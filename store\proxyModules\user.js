import { showInformations } from "@/api/proxyUser.js";
import { isLogin, checkLoginStatus } from "@/utils/auth.js";
export default {
  namespaced: true,
  state: {
    userInfo: {
      adminRabk: "0",
      headPic: "",
      phone: "",
      userName: "",
      integral: 0,
      name: "",
      realName: "",
      myInvitationCode: "",
    },
  },
  getters: {
    userInfo(state) {
      return state.userInfo;
    },
  },
  mutations: {
    SETUSERINFO(state, data) {
      state.userInfo = { ...data };
    },
    DEFAULTUSERINFO(state) {
      state.userInfo = {
        adminRabk: "0",
        headPic: "",
        phone: "",
        userName: "",
        integral: 0,
        name: "",
        realName: "",
        myInvitationCode: "",
      };
    },
  },
  actions: {
    async getUserInfo({ commit }) {
      // 先检查登录状态是否有效
      const loginStatus = await checkLoginStatus();
      if (!loginStatus.valid) {
        console.log(
          "[ProxyUser] 登录状态无效，无法获取用户信息:",
          loginStatus.reason
        );
        return Promise.reject(new Error("登录状态无效"));
      }

      try {
        const res = await showInformations();
        console.log("[ProxyUser] 获取用户信息成功:", res);
        if (res.data.code !== 200) {
          return Promise.reject(
            new Error(res.data.message || "获取用户信息失败")
          );
        }
        commit("SETUSERINFO", res.data.data);
        return res.data.data;
      } catch (error) {
        console.error("[ProxyUser] 获取用户信息失败:", error);
        return Promise.reject(error);
      }
    },
  },
};
