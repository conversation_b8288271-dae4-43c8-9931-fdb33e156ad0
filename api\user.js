import {
	post,
	get
} from '@/utils/requset.js'


const showInformations = function() {
	return post('/userinformation/showInformations')
}

const setUserName = function(data) {
	return post('/userinformation/updInfo', data)
}
const updUser = function(data) {
	return post('/userinformation/updT', data)
}

// 检查是否有权限进入各各权益
const ifGivePage = function() {
	return post('/gly/ifGivePage')
}

// 微信一键获取手机号
const getPhoneNoInfo = function(data) {
	return post('/WxGetInfo/getPhoneNoInfo', data)
}

// 绑定邀请码
const checkUserInvitation = function(data) {
	return post('/userinformation/checkUserInvitation', data)
}

// 获取权益商城订单
const getqyscOrder = function(data) {
	return get('/qyscfinishorder/loadManyByPramas', data)
}


// 用户手机号登录
const userPhoneLogin = function(data) {
	return post('/login/userPhoneLogin', data)
}







export {
	showInformations,
	setUserName,
	ifGivePage,
	updUser,
	getPhoneNoInfo,
	checkUserInvitation,
	getqyscOrder,
	userPhoneLogin
}