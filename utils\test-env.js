// 环境配置测试工具
// 这个文件用于测试环境配置是否正常工作
// 可以在开发时临时导入来验证配置

import config, { 
  BASE_URL, 
  ENV, 
  DEBUG, 
  VCONSOLE_ENABLED, 
  API_TIMEOUT,
  getCurrentEnv,
  isDevelopment,
  isProduction 
} from '@/config/index.js'

/**
 * 测试环境配置
 */
export function testEnvConfig() {
  console.log('=== 环境配置测试 ===')
  console.log('当前环境:', getCurrentEnv())
  console.log('是否开发环境:', isDevelopment)
  console.log('是否生产环境:', isProduction)
  console.log('完整配置:', config)
  console.log('BASE_URL:', BASE_URL)
  console.log('ENV:', ENV)
  console.log('DEBUG:', DEBUG)
  console.log('VCONSOLE_ENABLED:', VCONSOLE_ENABLED)
  console.log('API_TIMEOUT:', API_TIMEOUT)
  console.log('===================')
  
  return {
    env: getCurrentEnv(),
    isDev: isDevelopment,
    isProd: isProduction,
    config: config
  }
}

/**
 * 在页面中显示环境信息（用于调试）
 */
export function showEnvInfo() {
  const info = testEnvConfig()
  
  // 可以在页面中显示环境信息
  uni.showModal({
    title: '环境信息',
    content: `环境: ${info.env}\nAPI地址: ${BASE_URL}\nVConsole: ${VCONSOLE_ENABLED ? '启用' : '禁用'}`,
    showCancel: false
  })
  
  return info
}

// 自动执行测试（仅在开发环境）
if (isDevelopment) {
  // 延迟执行，确保应用已初始化
  setTimeout(() => {
    testEnvConfig()
  }, 1000)
}
