<template>
  <view class="bkcolor">
    <view class="wd" style="margin-top: 200px; transform: translateY(-50%)">
      <view
        v-if="inviterName !== ''"
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 20px;
        "
      >
        <view>
          {{ inviterName }}
        </view>
        <view style="font-size: 20px; margin-top: 10px"> 团队邀请你加入 </view>
      </view>
      <form>
        <view class="login-form-input">
          <input
            maxlength="11"
            placeholder="请输入手机号"
            v-model="registerForm.phone"
          />
          <view class="login-line"></view>
          <view class="login-verification">
            <input
              maxlength="6"
              placeholder="姓名"
              style="width: 70%"
              v-model="registerForm.name"
            />
          </view>
          <view class="login-line"></view>
          <view class="login-verification">
            <input
              maxlength="6"
              placeholder="验证码"
              style="width: 70%"
              v-model="registerForm.verificationCode"
            />
            <view
              v-if="verification === false"
              style="font-size: 14px"
              class="verification-buttom"
              @click="sendVerification"
              >获取验证码</view
            >
            <view style="font-size: 14px; color: #7f7f7f" v-else
              >还有{{ verificationSecond }}s可重发</view
            >
          </view>
        </view>
        <button
          form-type="submit"
          style="
            margin-top: 20px;
            border-radius: 20px;
            font-size: 15px;
            background: #4089fb;
            color: white;
          "
          @click="checkLogin"
        >
          注册
        </button>
      </form>
      <view style="text-align: center; margin-top: 20px; font-size: 14px">
        <view
          :class="agreement ? 'backcolorIsBlue' : ''"
          style="
            margin-right: 5px;
            border: 1px solid #7f7f7f;
            border-radius: 15px;
            width: 18px;
            height: 18px;
            display: inline-block;
            line-height: 18px;
          "
          ><uni-icons
            type="checkmarkempty"
            :color="agreement ? 'white' : '#F4F4F4'"
            size="15"
            @click="checkoutArg"
          ></uni-icons></view
        >我已阅读并协议<text @click="toUserProtocol" style="color: #4089fb"
          >用户协议</text
        >
      </view>
    </view>
  </view>
</template>

<script>
import { validPhone } from "@/utils/valid.js";
import { wxSendCode, getInfoByInvitationCode } from "@/api/proxyRegister.js";
import { checkLogin } from "@/api/proxyUser.js";
import { setToken, resetInfo, setUserId } from "@/utils/auth.js";

let timer;

export default {
  data() {
    return {
      agreement: false,
      isSend: false,
      verification: false,
      verificationSecond: 0,
      registerForm: {
        phone: "",
        name: "",
        adminRabk: "",
        invitationCode: "",
        verificationCode: "",
      },
      inviterName: "",
    };
  },
  methods: {
    checkoutArg() {
      this.agreement = !this.agreement;
    },
    sendVerification() {
      if (!this.agreement) {
        uni.showToast({
          icon: "error",
          title: "请勾选协议",
        });
        return;
      }
      if (this.isSend) {
        return;
      }
      if (!validPhone(this.registerForm.phone)) {
        uni.showToast({
          title: "手机号错误",
          icon: "error",
        });
        return;
      }
      if (this.registerForm.name === "") {
        uni.showToast({
          title: "请输入姓名",
          icon: "error",
        });
        return;
      }
      if (
        this.registerForm.adminRabk === "" ||
        this.registerForm.invitationCode === ""
      ) {
        uni.showToast({
          title: "请重新扫码进入",
          icon: "error",
        });
        return;
      }
      if (
        this.registerForm.adminRabk === "0" ||
        this.registerForm.adminRabk === "2" ||
        this.registerForm.adminRabk === "3"
      ) {
        uni.showToast({
          title: "二维码有误",
          icon: "error",
        });
        return;
      }
      this.isSend = true;
      wxSendCode(this.registerForm)
        .then((res) => {
          console.log(res);
        })
        .finally(() => {
          this.isSend = false;
          this.verificationSecond = 59;
          this.verification = true;
          clearInterval(timer);
          timer = setInterval(() => {
            this.verificationSecond = this.verificationSecond - 1;
            if (this.verificationSecond <= 0) {
              this.verification = false;
              clearInterval(timer);
            }
          }, 1000);
        });
    },
    checkLogin() {
      if (!this.agreement) {
        uni.showToast({
          icon: "error",
          title: "请勾选协议",
        });
        return;
      }
      if (this.isSend) {
        return;
      }
      if (!validPhone(this.registerForm.phone)) {
        uni.showToast({
          title: "手机号错误",
          icon: "error",
        });
        return;
      }
      if (this.registerForm.name === "") {
        uni.showToast({
          title: "请输入姓名",
          icon: "error",
        });
        return;
      }
      if (
        this.registerForm.adminRabk === "" ||
        this.registerForm.invitationCode === ""
      ) {
        uni.showToast({
          title: "请重新扫码进入",
          icon: "error",
        });
        return;
      }
      if (
        this.registerForm.adminRabk === "0" ||
        this.registerForm.adminRabk === "2" ||
        this.registerForm.adminRabk === "3"
      ) {
        uni.showToast({
          title: "二维码有误",
          icon: "error",
        });
        return;
      }
      if (this.registerForm.verificationCode.length !== 6) {
        uni.showToast({
          icon: "error",
          title: "验证码有误",
        });
        return;
      }
      const data = {
        phone: this.registerForm.phone,
        adminRabk: this.registerForm.adminRabk,
        verificationCode: this.registerForm.verificationCode,
      };
      uni.showLoading({
        mask: true,
        title: "加载中",
      });
      checkLogin(data)
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          const data = res.data.data;
          resetInfo();
          setToken(data.token);
          setUserId(data.userId);
          uni.hideLoading();
          uni.reLaunch({
            url: "/pages/proxyIndex/index",
          });
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    // 通过邀请码获取姓名
    getInfoByInvitationCode() {
      getInfoByInvitationCode({
        InvitationCode: this.registerForm.invitationCode,
      }).then((res) => {
        console.log(res);
        res = res.data;
        if (res.code !== 200) {
          return;
        }
        this.inviterName = res.data;
      });
    },
    toUserProtocol() {
      uni.navigateTo({
        url: "/pages/proxyUserProtocol/userProtocol",
      });
    },
  },
  watch: {
    isSend(c) {
      if (c) {
        uni.showLoading({
          mask: true,
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  onLoad(query) {
    const scene = decodeURIComponent(query.scene);
    if (scene) {
      const data = scene.split(",");
      console.log(data);
      this.registerForm = {
        ...this.registerForm,
        invitationCode: data[0],
        adminRabk: data[1],
      };
      console.log(this.registerForm);
      this.getInfoByInvitationCode();
    }
  },
};
</script>

<style>
.login-form-input {
  background: white;
  overflow: hidden;
  border-radius: 5px;
  padding: 0 10px;
}
.login-form-input input {
  margin: 10px 0;
  display: block;
  font-size: 14px;
}
.login-line {
  height: 3px;
  background-color: #f4f4f4;
}
.login-verification {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.verification-buttom {
  color: #4089fb;
}
.backcolorIsBlue {
  background-color: #4089fb;
}
</style>
