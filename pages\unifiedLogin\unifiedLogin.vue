<template>
  <view class="unified-login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle bg-circle-1"></view>
      <view class="bg-circle bg-circle-2"></view>
      <view class="bg-circle bg-circle-3"></view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 上半部分：Logo和标题 -->
      <view class="top-section">
        <!-- Logo和标题区域 -->
        <view class="header-section">
          <view class="logo-container">
            <view class="logo-icon">
              <uni-icons
                :type="isProxyMode ? 'person-filled' : 'contact'"
                color="#4089fb"
                size="40"
              ></uni-icons>
            </view>
          </view>
          <view class="welcome-text">{{
            isProxyMode ? "代理端登录" : "用户登录"
          }}</view>
          <view class="sub-text">{{
            isProxyMode
              ? "欢迎回来，请登录您的代理人账号"
              : "欢迎使用，请登录您的账号"
          }}</view>
        </view>

        <!-- 切换按钮 -->
        <view class="switch-section">
          <view class="switch-text" @click="toggleLoginMode">
            {{ isProxyMode ? "切换到用户登录" : "代理端登录通道" }}
          </view>
        </view>
      </view>

      <!-- 下半部分：登录表单 -->
      <view class="form-container">
        <!-- 用户端：微信授权登录 -->
        <view v-if="!isProxyMode" class="user-login-section">
          <!-- 协议勾选 -->
          <view class="agreement-section">
            <view class="checkbox-wrapper" @click="agreement = !agreement">
              <view class="checkbox" :class="{ checked: agreement }">
                <uni-icons
                  v-if="agreement"
                  type="checkmarkempty"
                  color="#fff"
                  size="12"
                ></uni-icons>
              </view>
              <text class="agreement-text">
                我已阅读并同意
                <text class="link-text" @click.stop="openUserProtocol"
                  >《用户协议》</text
                >
                和
                <text class="link-text" @click.stop="openPrivacyProtocol"
                  >《隐私政策》</text
                >
              </text>
            </view>
          </view>

          <!-- 微信授权登录按钮 -->
          <view class="login-btn-wrapper">
            <button
              class="login-btn"
              :class="{ loading: isMessage }"
              open-type="getPhoneNumber"
              @getphonenumber="getPhoneNumber"
            >
              <view class="btn-content">
                <uni-icons type="weixin" color="#fff" size="20"></uni-icons>
                <text v-if="!isMessage" style="margin-left: 8px"
                  >微信授权登录</text
                >
                <text v-else style="margin-left: 8px">登录中...</text>
              </view>
            </button>
          </view>
        </view>

        <!-- 代理端：手机号+验证码登录 -->
        <view v-if="isProxyMode" class="proxy-login-section">
          <view class="input-group">
            <view class="input-wrapper">
              <view class="input-icon">
                <uni-icons type="phone" color="#999" size="18"></uni-icons>
              </view>
              <input
                class="form-input"
                maxlength="11"
                placeholder="请输入手机号"
                v-model="loginForm.phone"
                type="number"
                placeholder-class="input-placeholder"
              />
            </view>
          </view>

          <view class="input-group">
            <view class="input-wrapper verification-wrapper">
              <view class="input-icon">
                <uni-icons type="locked" color="#999" size="18"></uni-icons>
              </view>
              <input
                class="form-input verification-input"
                maxlength="6"
                placeholder="请输入验证码"
                v-model="loginForm.verificationCode"
                type="number"
                placeholder-class="input-placeholder"
              />
              <view
                class="verification-btn"
                :class="{ disabled: verification || isMessage }"
                @click="sendVerification"
              >
                <text v-if="!verification">获取验证码</text>
                <text v-else>{{ verificationSecond }}s后重发</text>
              </view>
            </view>
          </view>

          <!-- 协议勾选 -->
          <view class="agreement-section">
            <view class="checkbox-wrapper" @click="agreement = !agreement">
              <view class="checkbox" :class="{ checked: agreement }">
                <uni-icons
                  v-if="agreement"
                  type="checkmarkempty"
                  color="#fff"
                  size="12"
                ></uni-icons>
              </view>
              <text class="agreement-text">
                我已阅读并同意
                <text class="link-text" @click.stop="openUserProtocol"
                  >《用户协议》</text
                >
                和
                <text class="link-text" @click.stop="openPrivacyProtocol"
                  >《隐私政策》</text
                >
              </text>
            </view>
          </view>

          <!-- 登录按钮 -->
          <view class="login-btn-wrapper">
            <button
              class="login-btn proxy"
              :class="{ loading: isMessage }"
              @click="checkLogin"
            >
              <text v-if="!isMessage">立即登录</text>
              <text v-else>登录中...</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { validPhone, validCode } from "@/utils/valid.js";
import {
  sendCode as userSendCode,
  checkLogin as userCheckLogin,
} from "@/api/login.js";
import {
  sendCode as proxySendCode,
  checkLogin as proxyCheckLogin,
} from "@/api/proxyUser.js";
import {
  setToken,
  setUserId,
  setProxyLoginStatus,
  resetInfo,
  clearProxyLoginStatus,
} from "@/utils/auth.js";
import { mapActions } from "vuex";
import {
  getPhoneNoInfo,
  userPhoneLogin,
  checkUserInvitation,
} from "@/api/user.js";

let timer;

export default {
  data() {
    return {
      isProxyMode: false, // 默认为用户登录模式
      verification: false,
      verificationSecond: 0,
      loginForm: {
        phone: "",
        verificationCode: "",
        adminRabk: "0",
      },
      isMessage: false,
      agreement: false,
    };
  },

  onLoad(options) {
    // 支持通过URL参数设置登录模式
    if (options.mode === "proxy") {
      this.isProxyMode = true;
      this.loginForm.adminRabk = "1";
    }
  },
  methods: {
    // 切换登录模式
    toggleLoginMode() {
      this.isProxyMode = !this.isProxyMode;
      // 切换模式时重置表单
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.loginForm = {
        phone: "",
        verificationCode: "",
        adminRabk: this.isProxyMode ? "1" : "0",
      };
      this.verification = false;
      this.verificationSecond = 0;
      this.agreement = false;
      clearInterval(timer);
    },

    // 发送验证码
    sendVerification() {
      if (this.verification === true || this.isMessage === true) {
        return;
      }

      if (!validPhone(this.loginForm.phone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none",
        });
        return;
      }

      const data = {
        phone: this.loginForm.phone,
        invitationCode: uni.getStorageSync("invitationCode"),
        adminRabk: this.isProxyMode ? 1 : 0,
      };

      this.isMessage = true;

      // 根据模式选择不同的API
      const sendCodeApi = this.isProxyMode ? proxySendCode : userSendCode;

      sendCodeApi(data)
        .then((res) => {
          if (res.data.code === 200) {
            uni.showToast({
              title: "验证码已发送",
              icon: "success",
            });

            this.startCountdown();
          } else {
            uni.showToast({
              title: res.data.message || "发送失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "网络错误",
            icon: "error",
          });
        })
        .finally(() => {
          this.isMessage = false;
        });
    },

    // 开始倒计时
    startCountdown() {
      this.verification = true;
      this.verificationSecond = 59;
      clearInterval(timer);
      timer = setInterval(() => {
        this.verificationSecond = this.verificationSecond - 1;
        if (this.verificationSecond <= 0) {
          clearInterval(timer);
          this.verification = false;
        }
      }, 1000);
    },

    // 代理端登录（手机号+验证码）
    checkLogin() {
      // 仅在代理端模式下才执行
      if (!this.isProxyMode) {
        return;
      }

      if (!this.agreement) {
        uni.showToast({
          title: "请勾选协议",
          icon: "none",
        });
        return;
      }

      if (this.isMessage) {
        return;
      }

      // 验证表单
      if (!validPhone(this.loginForm.phone)) {
        uni.showToast({
          icon: "error",
          title: "手机号格式有误",
        });
        return;
      }

      if (!validCode(this.loginForm.verificationCode)) {
        uni.showToast({
          icon: "error",
          title: "验证码格式有误",
        });
        return;
      }

      const data = {
        ...this.loginForm,
        adminRabk: "1", // 代理端固定为1
      };

      this.isMessage = true;

      // 代理端登录
      proxyCheckLogin(data)
        .then((res) => {
          if (res.data.code !== 200) {
            uni.showToast({
              icon: "error",
              title: "登录失败",
            });
            return;
          }

          const token = res.data.data.token;
          const userId = res.data.data.userId;

          // 清除之前的登录信息
          resetInfo();

          // 设置代理端登录信息
          setToken(token);
          setUserId(userId);
          setProxyLoginStatus(true);

          // 获取代理端用户信息
          this["proxyUser/getUserInfo"]();

          uni.showToast({
            title: "代理端登录成功",
            icon: "success",
          });

          // 跳转到代理端首页
          setTimeout(() => {
            uni.reLaunch({
              url: "/pages/proxyIndex/index",
            });
          }, 1000);
        })
        .finally(() => {
          this.isMessage = false;
        });
    },

    ...mapActions(["user/getUserInfo", "proxyUser/getUserInfo"]),

    // 微信授权获取手机号登录（用户端）
    getPhoneNumber(e) {
      console.log("获取手机号事件:", e);

      if (!this.agreement) {
        uni.showToast({
          title: "请先勾选协议",
          icon: "none",
        });
        return;
      }

      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        uni.showToast({
          title: "获取手机号失败",
          icon: "none",
        });
        return;
      }

      // 显示加载中
      this.isMessage = true;
      uni.showLoading({
        title: "登录中",
        mask: true,
      });

      // 直接使用微信登录进行授权
      uni.login({
        provider: "weixin",
        success: (loginRes) => {
          console.log("微信登录成功:", loginRes);

          // 准备获取手机号的参数
          const params = {
            code: e.detail.code, // 手机号获取的code
            loginCode: loginRes.code, // 登录的code
            encryptedData: e.detail.encryptedData,
            iv: e.detail.iv,
          };

          // 获取手机号
          getPhoneNoInfo(params)
            .then((res) => {
              console.log("获取手机号响应:", res);
              if (!res.data || res.data.code !== 200) {
                throw new Error(res.data?.message || "获取手机号失败");
              }

              let phone;
              // 如果后端返回的是加密的手机号数据
              if (
                typeof res.data.data === "object" &&
                res.data.data.phoneNumber
              ) {
                phone = res.data.data.phoneNumber;
              }
              // 如果后端直接返回手机号
              else if (
                typeof res.data.data === "string" &&
                res.data.data !== "null"
              ) {
                phone = res.data.data;
              } else {
                throw new Error("未能获取到有效的手机号");
              }

              console.log("获取到手机号:", phone);

              // 使用手机号登录
              return userPhoneLogin({
                phone: phone,
                loginCode: loginRes.code, // 添加登录code
              });
            })
            .then((res) => {
              console.log("手机号登录响应:", res);
              if (!res.data || res.data.code !== 200) {
                throw new Error(res.data?.message || "登录失败");
              }

              if (!res.data.data?.token) {
                throw new Error("登录返回数据格式错误");
              }

              // 清除之前的登录信息
              resetInfo();

              // 保存用户端登录信息
              setToken(res.data.data.token);
              setUserId(res.data.data.userId);
              clearProxyLoginStatus(); // 确保清除代理端标识

              // 检查邀请码
              const invitation = uni.getStorageSync("invitationCode");
              if (invitation) {
                return checkUserInvitation({
                  userId: res.data.data.userId,
                  invitation: invitation,
                });
              }
            })
            .then((invitationRes) => {
              if (invitationRes) {
                console.log("邀请码处理结果:", invitationRes);
              }

              // 刷新用户信息
              this["user/getUserInfo"]();

              uni.showToast({
                title: "登录成功",
                icon: "success",
              });

              // 跳转到用户端首页
              setTimeout(() => {
                uni.reLaunch({
                  url: "/pages/my/my",
                });
              }, 1000);
            })
            .catch((err) => {
              console.error("登录错误:", err);
              uni.showToast({
                title: err.message || "登录失败",
                icon: "error",
                duration: 2000,
              });
            })
            .finally(() => {
              this.isMessage = false;
              uni.hideLoading();
            });
        },
        fail: (err) => {
          console.error("微信登录失败:", err);
          this.isMessage = false;
          uni.hideLoading();
          uni.showToast({
            title: "微信登录失败",
            icon: "error",
          });
        },
      });
    },

    // 打开用户协议
    openUserProtocol() {
      let protocolUrl;
      // #ifdef H5
      protocolUrl = "/static/protocol/userProtocol.html";
      // #endif
      // #ifdef MP-WEIXIN || APP-PLUS
      protocolUrl = "/static/protocol/userProtocol.html";
      // #endif

      uni.navigateTo({
        url: `/pages/webPage/webPage?url=${encodeURIComponent(protocolUrl)}`,
      });
    },

    // 打开隐私政策
    openPrivacyProtocol() {
      let protocolUrl;
      // #ifdef H5
      protocolUrl = "/static/protocol/privacy.html";
      // #endif
      // #ifdef MP-WEIXIN || APP-PLUS
      protocolUrl = "/static/protocol/privacy.html";
      // #endif

      uni.navigateTo({
        url: `/pages/webPage/webPage?url=${encodeURIComponent(protocolUrl)}`,
      });
    },
  },

  onUnload() {
    // 清理定时器
    clearInterval(timer);
  },
};
</script>

<style lang="scss" scoped>
.unified-login-container {
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  /* 确保不出现滚动条 */
  box-sizing: border-box;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.bg-circle-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  right: -100px;
}

.bg-circle-2 {
  width: 150px;
  height: 150px;
  top: 50%;
  left: -75px;
}

.bg-circle-3 {
  width: 100px;
  height: 100px;
  bottom: 100px;
  right: 50px;
}

.main-content {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%;
  padding: 30px 30px 20px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* 恢复居中布局 */
}

.top-section {
  flex-shrink: 0;
  /* 防止压缩 */
  margin-bottom: 30px;
}

.header-section {
  text-align: center;
  flex-shrink: 0;
  margin-bottom: 20px;
}

.logo-container {
  margin-bottom: 15px;
}

.logo-icon {
  width: 70px;
  height: 70px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  backdrop-filter: blur(10px);
}

.welcome-text {
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 6px;
}

.sub-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.switch-section {
  text-align: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.switch-text {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  padding: 6px 14px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  display: inline-block;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.switch-text:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.form-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 25px;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
  /* 根据内容自适应高度，不占据剩余空间 */
}

.input-group {
  margin-bottom: 16px;
}

.input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 10px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #4089fb;
  background: #fff;
}

.verification-wrapper {
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px 0 40px;
  font-size: 15px;
  border: none;
  background: transparent;
  color: #333;
  box-sizing: border-box;
}

.verification-input {
  flex: 1;
  padding-right: 110px;
}

.input-placeholder {
  color: #999;
}

.verification-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  padding: 5px 10px;
  background: #4089fb;
  color: #fff;
  border-radius: 5px;
  font-size: 11px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.verification-btn.disabled {
  background: #ccc;
  color: #666;
}

.agreement-section {
  margin-bottom: 24px;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox.checked {
  background: #4089fb;
  border-color: #4089fb;
}

.agreement-text {
  font-size: 13px;
  color: #666;
  flex: 1;
  line-height: 1.4;
}

.link-text {
  color: #4089fb;
}

.login-btn-wrapper {
  margin-top: 0;
}

.login-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #4089fb 0%, #3d7bfa 100%);
  color: #fff;
  border: none;
  border-radius: 10px;
  font-size: 15px;
  font-weight: bold;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.login-btn.proxy {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
}

.login-btn.loading {
  opacity: 0.8;
}

.login-btn:active {
  transform: scale(0.98);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
