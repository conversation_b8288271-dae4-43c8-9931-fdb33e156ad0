// 生产环境配置
export default {
  // API基础URL
  BASE_URL: "https://elcxt.online:8011",

  // 环境标识
  ENV: "production",

  // 是否启用调试
  DEBUG: false,

  // 是否启用VConsole (生产环境启用以便移动端调试)
  VCONSOLE_ENABLED: true,

  // VConsole配置
  VCONSOLE_CONFIG: {
    defaultPlugins: ["system", "network", "element", "storage"],
    maxLogNumber: 1000,
    onReady: function () {
      console.log("VConsole 已准备就绪 - 生产环境");
    },
    onClearLog: function () {
      console.log("VConsole 日志已清除");
    },
  },

  // 其他生产环境特定配置
  API_TIMEOUT: 15000,

  // 日志级别
  LOG_LEVEL: "error",

  // 错误上报配置
  ERROR_REPORT: {
    enabled: true,
    maxErrors: 10,
    reportUrl: "/api/error/report",
  },
};
