// 环境配置管理
import developmentConfig from "./env.development.js";
import productionConfig from "./env.production.js";

/**
 * 获取当前环境
 * UniApp中通过process.env.NODE_ENV判断环境
 * 开发环境: development
 * 生产环境: production
 */
function getCurrentEnv() {
  // 在UniApp中，可以通过以下方式判断环境：
  // 1. process.env.NODE_ENV (推荐)
  // 2. 通过编译条件判断

  // #ifdef APP-PLUS
  // App端可以通过process.env.NODE_ENV判断
  if (typeof process !== "undefined" && process.env && process.env.NODE_ENV) {
    return process.env.NODE_ENV;
  }
  // #endif

  // #ifdef MP
  // 小程序端默认判断逻辑
  // 可以通过__wxConfig或其他方式判断，这里简化处理
  // #endif

  // #ifdef H5
  // H5端可以通过process.env.NODE_ENV判断
  if (typeof process !== "undefined" && process.env && process.env.NODE_ENV) {
    return process.env.NODE_ENV;
  }
  // #endif

  // 默认返回开发环境
  // 在实际部署时，可以通过构建工具设置环境变量
  return "development";
}

/**
 * 获取环境配置
 */
function getConfig() {
  const env = getCurrentEnv();

  switch (env) {
    case "production":
      return productionConfig;
    case "development":
    default:
      return developmentConfig;
  }
}

// 导出配置
const config = getConfig();

export default config;

// 也可以单独导出各个配置项
export const {
  BASE_URL,
  ENV,
  DEBUG,
  VCONSOLE_ENABLED,
  VCONSOLE_CONFIG,
  API_TIMEOUT,
  LOG_LEVEL,
  ERROR_REPORT,
} = config;

// 导出环境判断函数
export { getCurrentEnv };

// 便捷的环境判断
export const isDevelopment = config.ENV === "development";
export const isProduction = config.ENV === "production";
