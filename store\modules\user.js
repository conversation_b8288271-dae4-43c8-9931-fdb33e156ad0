import { showInformations } from "@/api/user.js";
import { isLogin } from "@/utils/auth.js";
export default {
  namespaced: true,
  state: {
    userInfo: {
      adminRabk: "0",
      headPic: "",
      phone: "",
      userName: "",
      integral: 0,
    },
  },
  getters: {},
  mutations: {
    SETUSERINFO(state, data) {
      state.userInfo = data
        ? { ...data }
        : {
            adminRabk: "0",
            headPic: "",
            phone: "",
            userName: "",
            integral: 0,
          };
    },
    DEFAULTUSERINFO(state) {
      state.userInfo = {
        adminRabk: "0",
        headPic: "",
        phone: "",
        userName: "",
        integral: 0,
      };
    },
  },
  actions: {
    getUserInfo({ commit }) {
      if (!isLogin()) {
        return;
      }
      showInformations().then((res) => {
        if (res.data.code !== 200) {
          return;
        }
        commit("SETUSERINFO", res.data.data);
      });
    },
  },
};
