<template>
  <view class="container">
    <!-- 收货地址 -->
    <view class="address-section" @click="goToAdressList">
      <view v-if="address" class="address-info">
        <view class="address-top">
          <text class="name">{{ address.recipientName }}</text>
          <text class="phone">{{ address.phoneNumber }}</text>
        </view>
        <view class="address-detail">
          {{ address.provinceCityDistrict + address.addressDetail }}
        </view>
      </view>
      <view v-else class="no-address">
        <text>请选择收货地址</text>
        <uni-icons type="right" size="20" color="#999"></uni-icons>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="goods-section" v-if="goodsInfo">
      <view class="goods-item">
        <image
          :src="getFirstImage(goodsInfo.goodsCover)"
          class="goods-img"
          mode="aspectFit"
        ></image>
        <view class="goods-info">
          <text class="goods-name">{{ goodsInfo.goodsName }}</text>
          <view class="goods-price-count">
            <text class="goods-price">¥{{ goodsInfo.goodsTotalPrice }}</text>
            <view class="goods-count">
              <view class="count-btn" @click="reduceCount">-</view>
              <input type="number" v-model="buyNum" class="count-input" />
              <view class="count-btn" @click="addCount">+</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-section" v-else>
      <text>正在加载商品信息...</text>
    </view>

    <!-- 支付方式 -->
    <view class="payment-section">
      <view class="section-title">
        <image src="/static/wx/wxpay.png" mode="widthFix" class="wxpay"></image
        >支付方式
      </view>
      <view class="payment-options">
        <view
          class="payment-item"
          :class="{ active: paymentType === 'wxpay' }"
          @click="selectPayment('wxpay')"
        >
          <!-- <image src="/static/wxpay-icon.png" class="payment-icon"></image> -->
          <text>微信支付</text>
          <view
            class="radio"
            :class="{ checked: paymentType === 'wxpay' }"
          ></view>
        </view>
        <!-- 				<view class="payment-item" :class="{'active': paymentType === 'yspay'}" @click="selectPayment('yspay')">
					<image src="/static/yspay-icon.png" class="payment-icon"></image>
					<text>银盛支付</text>
					<view class="radio" :class="{'checked': paymentType === 'yspay'}"></view>
				</view> -->
      </view>
    </view>

    <!-- 订单汇总 -->
    <view class="order-summary" v-if="goodsInfo">
      <view class="summary-item">
        <text>商品原金额</text>
        <text>¥{{ calculateOriginalPrice }}</text>
      </view>
      <view class="summary-item">
        <text class="deduction-text">积分抵扣</text>
        <text class="deduction-text">{{ calculateDeductionPoints }}</text>
      </view>
      <view class="summary-item">
        <text>实际金额</text>
        <text>¥{{ calculatePrice }}</text>
      </view>
      <view class="summary-item">
        <text>运费</text>
        <text>¥{{ goodsInfo.freight * buyNum }}</text>
      </view>
    </view>

    <!-- 提交订单 -->
    <view class="submit-section" v-if="goodsInfo">
      <view class="total-amount">
        <view class="amount-item">
          <text>商品金额：</text>
          <text class="amount">¥{{ calculatePrice }}</text>
        </view>
        <view class="amount-item">
          <text>运费：</text>
          <text class="amount">¥{{ goodsInfo.freight * buyNum }}</text>
        </view>
      </view>
      <view class="submit-btn" @click="submitOrder">提交订单</view>
    </view>
  </view>
</template>

<script>
import { fuioupay } from "@/api/fuiou.js";

export default {
  data() {
    return {
      goodsInfo: null,
      goodsId: "",
      buyNum: 1,
      address: null,
      paymentType: "wxpay",
      freight: 0,
      userId: uni.getStorageSync("userId"),
      userName: "",
    };
  },
  onLoad(options) {
    // 获取传递过来的商品ID
    if (options.id) {
      this.goodsId = options.id;
      this.loadGoodsInfo();
    } else {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }

    // 获取用户信息
    // this.getUserInfo();
  },
  onShow() {
    // 方式2：直接从页面栈获取（推荐）
    // const pages = getCurrentPages();
    // const currentPage = pages[pages.length - 1];
    // if (currentPage.$vm?.selectedAddress) {
    // 	this.address = currentPage.$vm.selectedAddress;
    // }
  },
  computed: {
    // 计算商品原价总额
    calculateOriginalPrice() {
      if (!this.goodsInfo) return "0.00";
      return (this.goodsInfo.goodsTotalPrice * this.buyNum).toFixed(2);
    },
    // 计算积分抵扣总额
    calculateDeductionPoints() {
      if (!this.goodsInfo) return 0;
      return this.goodsInfo.deductionPoints * this.buyNum;
    },
    // 计算实际支付金额（不含运费）
    calculatePrice() {
      if (!this.goodsInfo) return "0.00";
      return (this.goodsInfo.goodsRealPay * this.buyNum).toFixed(2);
    },
    // 计算总金额（含运费）
    totalAmount() {
      if (!this.goodsInfo) return "0.00";
      return (
        parseFloat(this.calculatePrice) +
        parseFloat(this.goodsInfo.freight * this.buyNum)
      ).toFixed(2);
    },
  },
  methods: {
    // 获取第一张图片作为封面
    getFirstImage(imageStr) {
      if (!imageStr || typeof imageStr !== "string") return "";
      try {
        // 将逗号分隔的图片字符串拆分，返回第一张图片URL
        const images = imageStr.split(",");
        return images && images.length > 0 ? images[0] : "";
      } catch (error) {
        console.error("处理图片字符串时出错:", error);
        return "";
      }
    },

    // 接收地址的方法
    setSelectedAddress(address) {
      this.address = address;
      // 这里可以执行其他操作，如更新UI等
      console.log("已选择地址:", address);
    },
    // 加载商品信息
    loadGoodsInfo() {
      try {
        // 这里应该从接口获取商品信息，但为了简化示例，从缓存中获取
        const pages = getCurrentPages();
        if (!pages || pages.length < 2) {
          console.warn("页面栈信息不足");
          return;
        }
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.$vm && prevPage.$vm.product) {
          this.goodsInfo = prevPage.$vm.product;
          // this.goodsInfo.goodsCover = JSON.parse(this.goodsInfo.goodsCover)
          console.log("获取到商品信息:", this.goodsInfo);
        } else {
          uni.showToast({
            title: "获取商品信息失败",
            icon: "none",
          });
        }
      } catch (error) {
        console.error("加载商品信息时出错:", error);
        uni.showToast({
          title: "加载商品信息失败",
          icon: "none",
        });
      }
    },

    // 获取用户信息
    // getUserInfo() {
    // 	// 从缓存中获取用户信息
    // 	try {
    // 		const userInfo = uni.getStorageSync('userInfo');
    // 		if (userInfo) {
    // 			this.userId = userInfo.id || '';
    // 			this.userName = userInfo.name || '';
    // 		} else {
    // 			// 未登录，跳转到登录页
    // 			uni.showToast({
    // 				title: '请先登录',
    // 				icon: 'none'
    // 			});
    // 			setTimeout(() => {
    // 				uni.navigateTo({
    // 					url: '/pages/login/login'
    // 				});
    // 			}, 1500);
    // 		}
    // 	} catch (e) {
    // 		console.error('获取用户信息失败:', e);
    // 	}
    // },

    // 选择收货地址
    chooseAddress() {
      uni.navigateTo({
        url: "/pages/address/address?from=order",
        events: {
          // 监听从地址选择页返回的事件
          addressSelected: (address) => {
            this.address = address;
          },
        },
      });
    },

    // 减少数量
    reduceCount() {
      if (this.buyNum > 1) {
        this.buyNum--;
      }
    },

    // 增加数量
    addCount() {
      this.buyNum++;
    },

    // 选择支付方式
    selectPayment(type) {
      this.paymentType = type;
    },

    // 提交订单
    submitOrder() {
      if (!this.address) {
        uni.showToast({
          title: "请选择收货地址",
          icon: "none",
        });
        return;
      }

      // 显示加载中
      uni.showLoading({
        title: "正在支付...",
      });

      // 构建支付参数
      const payParams = {
        userId: this.userId,
        name: this.goodsInfo.goodsName, // 商品名
        type: "3", // 固定值
        amt: this.totalAmount, // 总金额
        goodsId: this.goodsInfo.goodsId,
        buy_num: this.buyNum,
        addressId: this.address.addressId,
      };

      console.log("支付参数:", payParams);

      // 调用支付接口
      fuioupay(payParams)
        .then((res) => {
          uni.hideLoading();
          console.log("支付接口返回:", res);

          if (res.data && res.data.code === 200) {
            const payData = res.data.data;
            // 微信支付

            uni.requestPayment({
              appId: res.data.data.appid, // 公众号ID，由商户传入
              timeStamp: res.data.data.timestamp, // 时间戳，自1970年以来的秒数
              nonceStr: res.data.data.noncestr, // 随机串
              package: res.data.data.package,
              signType: res.data.data.signtype, // 微信签名方式
              paySign: res.data.data.paysign, // 微信签名
              success: (res) => {
                console.log("支付成功:", res);
                this.handlePaySuccess();
              },
              fail: (err) => {
                console.error("支付失败:", err);
                uni.showToast({
                  title: "支付取消或失败",
                  icon: "none",
                });
              },
            });
          } else {
            uni.showToast({
              title: res.data?.msg || "支付请求失败",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          uni.hideLoading();
          console.error("支付接口请求失败:", err);
          uni.showToast({
            title: "网络异常，请稍后再试",
            icon: "none",
          });
        });
    },

    // 处理支付成功
    handlePaySuccess() {
      uni.showToast({
        title: "支付成功",
        icon: "success",
      });

      setTimeout(() => {
        // 跳转到订单页面或其他页面
        uni.switchTab({
          url: "/pages/my/my",
        });
      }, 1500);
    },
    goToAdressList() {
      uni.navigateTo({
        url: "/pages/selfOrderBuy/addressList",
      });
    },
  },
};
</script>

<style>
.container {
  /* padding-bottom: 120rpx; */
  background-color: #f5f5f5;
  height: 100vh;
}

/* 地址样式 */
.address-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.no-address {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #666;
}

.address-info {
  position: relative;
}

.address-top {
  display: flex;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.phone {
  font-size: 30rpx;
  color: #333;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 商品信息样式 */
.goods-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

/* 加载状态样式 */
.loading-section {
  background-color: #ffffff;
  padding: 60rpx 30rpx;
  margin-bottom: 20rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.goods-item {
  display: flex;
}

.goods-img {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.goods-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  line-height: 40rpx;
  height: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-price-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.goods-price {
  font-size: 32rpx;
  color: #ff5490;
  font-weight: bold;
}

.goods-count {
  display: flex;
  align-items: center;
}

.count-btn {
  width: 50rpx;
  height: 50rpx;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

.count-input {
  width: 60rpx;
  text-align: center;
  height: 50rpx;
  line-height: 50rpx;
  margin: 0 5rpx;
  background-color: #f5f5f5;
}

/* 支付方式样式 */
.payment-section {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.payment-options {
  display: flex;
  flex-direction: column;
}

.payment-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.payment-item.active {
  color: #ff5490;
}

.payment-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.radio {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ccc;
  border-radius: 50%;
  margin-left: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.radio.checked {
  border: 2rpx solid #ff5490;
}

.radio.checked:after {
  content: "";
  width: 20rpx;
  height: 20rpx;
  background-color: #ff5490;
  border-radius: 50%;
}

/* 订单汇总样式 */
.order-summary {
  background-color: #ffffff;
  padding: 30rpx;
  margin-bottom: 190rpx;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.deduction-text {
  color: #ff0000;
}

/* 提交订单样式 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  border-top: 1rpx solid #f5f5f5;
}

.total-amount {
  flex: 1;
  padding-left: 30rpx;
  font-size: 28rpx;
}

.amount-item {
  display: flex;
}

.amount {
  color: #ff5490;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn {
  width: 240rpx;
  height: 100rpx;
  background-color: #ff5490;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
}

.wxpay {
  width: 40rpx;
  margin-right: 20rpx;
  margin-bottom: -10rpx;
}
</style>
