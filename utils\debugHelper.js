// 调试助手工具
import { isProduction, DEBUG } from "@/config/index.js";

// 调试助手类
class DebugHelper {
  constructor() {
    this.logs = [];
    this.maxLogs = 1000;
    this.enabled = DEBUG && !isProduction;

    this.setupDebugPanel();
  }

  // 设置调试面板
  setupDebugPanel() {
    if (!this.enabled) return;

    // 在开发环境中添加调试信息到页面
    this.addDebugInfo();
  }

  // 添加调试信息
  addDebugInfo() {
    if (typeof window === "undefined") return;

    // 添加系统信息
    const systemInfo = uni.getSystemInfoSync();
    this.log("系统信息", systemInfo);

    // 添加网络信息
    uni.getNetworkType({
      success: (res) => {
        this.log("网络信息", res);
      },
    });
  }

  // 记录日志
  log(label, data, level = "info") {
    const logEntry = {
      timestamp: new Date().toISOString(),
      label,
      data,
      level,
    };

    this.logs.push(logEntry);

    // 保持日志数量
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // 输出到控制台
    if (this.enabled) {
      const emoji = this.getLevelEmoji(level);
      console.log(`${emoji} [${label}]`, data);
    }
  }

  // 获取级别对应的emoji
  getLevelEmoji(level) {
    const emojis = {
      info: "ℹ️",
      warn: "⚠️",
      error: "❌",
      success: "✅",
      debug: "🐛",
      network: "🌐",
      user: "👤",
      performance: "⏱️",
    };
    return emojis[level] || "ℹ️";
  }

  // 网络请求调试
  logNetworkRequest(config, response, error = null) {
    const requestInfo = {
      url: config.url,
      method: config.method || "GET",
      headers: config.header,
      data: config.data,
      timestamp: new Date().toISOString(),
    };

    if (error) {
      this.log(
        `网络请求失败: ${config.url}`,
        {
          request: requestInfo,
          error: error.message || error,
        },
        "error"
      );
    } else if (response && response.statusCode !== undefined) {
      // 有响应数据时记录成功请求
      this.log(
        `网络请求成功: ${config.url}`,
        {
          request: requestInfo,
          response: {
            statusCode: response.statusCode,
            data: response.data,
          },
        },
        "network"
      );
    } else {
      // 请求开始时记录（response为null的情况）
      this.log(
        `网络请求开始: ${config.url}`,
        {
          request: requestInfo,
        },
        "info"
      );
    }
  }

  // 用户行为追踪
  logUserAction(action, page, data = {}) {
    this.log(
      `用户行为: ${action}`,
      {
        page,
        action,
        data,
        timestamp: new Date().toISOString(),
      },
      "user"
    );
  }

  // 性能监控
  logPerformance(label, startTime, endTime = Date.now()) {
    const duration = endTime - startTime;
    this.log(
      `性能监控: ${label}`,
      {
        duration: `${duration}ms`,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
      },
      "performance"
    );
  }

  // 错误追踪
  logError(error, context = {}) {
    this.log(
      "错误捕获",
      {
        message: error.message || error,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString(),
      },
      "error"
    );
  }

  // 微信环境检测
  checkWeixinEnvironment() {
    const isWeixin = /micromessenger/i.test(navigator.userAgent);
    const hasWeixinJSBridge =
      typeof window !== "undefined" && window.WeixinJSBridge;

    const weixinInfo = {
      isWeixin,
      hasWeixinJSBridge,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    };

    this.log("微信环境检测", weixinInfo, isWeixin ? "success" : "warn");

    return weixinInfo;
  }

  // 获取调试日志
  getLogs(level = null) {
    if (level) {
      return this.logs.filter((log) => log.level === level);
    }
    return [...this.logs];
  }

  // 清除日志
  clearLogs() {
    this.logs = [];
    console.clear();
  }

  // 导出日志
  exportLogs() {
    const logsData = {
      timestamp: new Date().toISOString(),
      systemInfo: uni.getSystemInfoSync(),
      logs: this.logs,
    };

    return JSON.stringify(logsData, null, 2);
  }

  // 显示调试信息摘要
  showDebugSummary() {
    if (!this.enabled) return;

    const summary = {
      totalLogs: this.logs.length,
      errorCount: this.logs.filter((log) => log.level === "error").length,
      warningCount: this.logs.filter((log) => log.level === "warn").length,
      networkRequests: this.logs.filter((log) => log.level === "network")
        .length,
      userActions: this.logs.filter((log) => log.level === "user").length,
    };

    console.group("🐛 调试摘要");
    console.table(summary);
    console.groupEnd();

    return summary;
  }
}

// 创建全局调试助手实例
const debugHelper = new DebugHelper();

// 便捷的调试函数
export const debug = {
  log: (label, data) => debugHelper.log(label, data, "debug"),
  info: (label, data) => debugHelper.log(label, data, "info"),
  warn: (label, data) => debugHelper.log(label, data, "warn"),
  error: (label, data) => debugHelper.log(label, data, "error"),
  success: (label, data) => debugHelper.log(label, data, "success"),

  network: (config, response, error) =>
    debugHelper.logNetworkRequest(config, response, error),
  user: (action, page, data) => debugHelper.logUserAction(action, page, data),
  performance: (label, startTime, endTime) =>
    debugHelper.logPerformance(label, startTime, endTime),

  checkWeixin: () => debugHelper.checkWeixinEnvironment(),
  summary: () => debugHelper.showDebugSummary(),
  export: () => debugHelper.exportLogs(),
  clear: () => debugHelper.clearLogs(),
};

// 性能监控装饰器
export function performanceMonitor(label) {
  return function (target, propertyKey, descriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args) {
      const startTime = Date.now();

      try {
        const result = await originalMethod.apply(this, args);
        debugHelper.logPerformance(`${label || propertyKey}`, startTime);
        return result;
      } catch (error) {
        debugHelper.logPerformance(`${label || propertyKey} (失败)`, startTime);
        throw error;
      }
    };

    return descriptor;
  };
}

export default debugHelper;
