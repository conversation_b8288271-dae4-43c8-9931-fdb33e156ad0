<template>
	<view class="page-carnumber-test">
		<view class="number-wrap">
			<view class="area-title">
				请输入您的车牌号
			</view>
			<view class="text">
				一辆车仅可申办一台ETC，暂仅支持蓝牌或绿牌，且9座及以下车辆办理
			</view>
			<car-number v-model="carNumber" :value="carNumber"></car-number>
		</view>

		<view class="number-wrap" style="margin-top: 2vh;">
			<view class="area-title">
				车主信息
			</view>
			<view class="userInfo">
				<view class="name">
					车主姓名
				</view>
				<input type="text" class="input" placeholder="请输入姓名" v-model="name" />
			</view>
			<view class="userInfo">
				<view class="name">
					车主手机号
				</view>
				<!-- <button v-if="phone == ''" open-type="getPhoneNumber" class="getphone-button"
					@getphonenumber="getPhoneNumber">点击授权获取手机号</button> -->
				<input type="text" class="input" placeholder="请输入手机号" v-model="phone" />

			</view>
		</view>


		<view class=" number-wrap" style="margin-top: 2vh;">
			<view class="area-title">
				邮寄地址
			</view>
			<textarea class="textarea" placeholder="请输入您的收货地址" v-model="address"></textarea>
		</view>

		<button type="default" @click="saveInfo" class="save-button">
			下一步
		</button>
	</view>
</template>

<script>
	import {
		mapGetters
	} from 'vuex'
	import CarNumber from '@/components/codecook-carnumber/codecook-carnumber.vue';
	import {
		ifGivePage,
		updUser,
		showInformations,
		getPhoneNoInfo
	} from '@/api/user.js'

	export default {
		components: {
			CarNumber
		},
		computed: {
			...mapGetters(['userInfo']),


		},
		data() {
			return {
				carNumber: '',
				name: '',
				phone: '',
				address: '',
				goods: {}
			}
		},
		watch: {
			carNumber(num) {
				console.log(num);
			},
		},
		methods: {
			saveInfo() {
				// 验证信息，
				if (this.carNumber == '' || this.carNumber == undefined || this.carNumber == null || this.carNumber
					.length < 7) {
					uni.showToast({
						title: "请完整填写信息",
						icon: 'error'
					})
					return
				}
				if (this.name == '' || this.name == undefined || this.name == null) {
					uni.showToast({
						title: "请完整填写信息",
						icon: 'error'
					})
					return
				}
				if (this.phone == '' || this.phone == undefined || this.phone == null) {
					uni.showToast({
						title: "请完整填写信息",
						icon: 'error'
					})
					return
				}
				if (this.address == '' || this.address == undefined || this.address == null) {
					uni.showToast({
						title: "请完整填写信息",
						icon: 'error'
					})
					return
				}
				console.log(this.userInfo)
				let data = {
					id: this.userInfo.id,
					carId: this.carNumber,
					phnumber: this.phone,
					realName: this.name,
					address: this.address

				}
				updUser(data).then(res => {
					console.log(res)
					if (res.data == "updSuccess") {
						uni.showToast({
							title: "添加成功",
							icon: 'success'
						})
						let that = this
						setTimeout(function() {
							console.log(that.goods)
							uni.navigateTo({
								url: '/pages/buy/buy?goods=' + JSON.stringify(that.goods) + '&carId=' + JSON.stringify(that.carNumber)

							})
						}, 1000)

					}
				})
			},
			// getPhoneNumber(e) {
			// 	let that = this;
			// 	//console.log("3 缓存的用户信息",  uni.getStorageSync("sessionUser"));
			// 	if (e.detail.errMsg == "getPhoneNumber:ok") { // 用户允许或去手机号
			// 		//获取到手机号码
			// 		let params = {
			// 			code: e.detail.code,
			// 			// encryptedData: e.detail.encryptedData,
			// 			// ivStr: e.detail.iv
			// 		};
			// 		console.log(params)
			// 		//调用后端接口getPhoneNoInfo方法
			// 		getPhoneNoInfo(params).then(res => {

			// 			if (res.data.code != 200) {
			// 				that.$msg.error(message)
			// 				return
			// 			}
			// 			//存入Storage
			// 			// uni.setStorageSync("sessionPhone",result.phoneNumber);
			// 			that.phone = res.data.data
			// 			console.log(that.phone)
			// 		})
			// 	}
			// },

			chuliData() {
				console.log(this.userInfo)
				this.carNumber = this.userInfo.carId
				this.name = this.userInfo.realName
				this.phone = this.userInfo.phnumber
				this.address = this.userInfo.address
				console.log(this.carNumber)
				if (this.userInfo.carId == null || this.userInfo.carId == '' || this.userInfo.carId ==
					undefined || this.userInfo.carId == 'null') {
					this.carNumber = ''
				}
				if (this.userInfo.realName == null || this.userInfo.realName == '' || this.userInfo.realName ==
					undefined || this
					.userInfo.realName == 'null') {
					this.name = ''
				}
				if (this.userInfo.phone == null || this.userInfo.phone == '' || this.userInfo.phone == undefined || this
					.userInfo.phone == 'null') {
					this.phone = ''
				}
				if (this.userInfo.address == null || this.userInfo.address == '' || this.userInfo.address == undefined ||
					this.userInfo.address == 'null') {
					this.address = ''
				}
			}

		},
		beforeMount() {},
		onLoad(options) {
			console.log(options.goods)
			this.goods = JSON.parse(options.goods)
			console.log(this.goods)

		},
		onShow() {
			this.chuliData()
		}
	}
</script>


<style scoped lang="less">
	.number-wrap {
		padding: 38rpx 30rpx;
		width: 86%;
		margin: 0 auto;
		background-color: #fff;
		border-radius: 10px;

	}

	.page-carnumber-test {
		height: 100vh;
		background-color: #f0f3f8;
		padding-top: 3vh;
		letter-spacing: 0.1rem;
	}

	.area-title {
		font-weight: 700;
		padding-bottom: 5px;
	}

	.text {
		margin-top: 10px;
		line-height: 1.2rem;
		font-size: 12px;
		color: #aaaaaa;
	}

	.userInfo {
		display: flex;
		align-items: center;
		width: 100%;
		margin: 0 auto;
		margin-top: 20px;
		height: 45px;
		border-radius: 5px;
		background-color: #f0f3f8;
	}

	.name {
		width: 30%;
		margin-left: 20px;
		letter-spacing: 0.1rem;
	}

	.input {
		margin-left: 20px;
	}

	.textarea {
		background-color: #f0f3f8;
		padding: 10px;
		width: 100%;
		box-sizing: border-box;
	}

	.save-button {
		margin-top: 40px;
		width: 50%;
		border-radius: 30px;
		background-color: #4ba6f8;
		color: #fff;
	}

	.getphone-button {
		height: 100%;
		width: 40%;
		line-height: 45px;
		font-size: 14px;
		margin-left: 30%;
		// background-color: #fff;
		color: #4ba6f8;
	}
</style>