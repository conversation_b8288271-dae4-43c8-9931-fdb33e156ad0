!function(){"use strict";var e={};let t,n;function o(){if(n)return n;const e=document.head.getAttribute("data-info");return n=((/f:\s*([^;]+)/i.exec(e)||{})[1]||"").toLowerCase(),n}e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),function(){if(void 0!==e){const t=e.e,n={};e.e=function(o){return t(o).catch((function(t){const i=n.hasOwnProperty(o)?n[o]:2;if(1===i&&(e.p=e.p.replace("/assets.","/assets2.")),i<1)throw t;return new Promise((function(t){setTimeout((function(){n[o]=i-1,t(e.e(o))}),100)}))}))}}}(),function(){if(void 0!==e&&void 0!==e.tu){const t=e.tu;e.tu=function(e){return t(function(e){if("string"!=typeof e||"undefined"==typeof document)return e;const t=document.head.getAttribute("data-info");if(!(t.indexOf("ntp-afdh3t")>=0||t.indexOf("ntp-afdh3c")>=0))return e;const n="afdprotocol=";var o=document.cookie.split("; ").find((e=>0===e.indexOf(n)));if(o){const t=o.split("=");if(t[1])return e+(e.indexOf("?")<0?"?":"&")+n+t[1]}return e}(e))}}}();const i={};function r(){return window?.__SSRUserConfigEarlyLog?.consistentMuid||window?.__muid||""}function s(e){try{return decodeURIComponent(e)}catch(e){}}let a,c;function d(e,t){if(!e)return null;if(t)return function(e){const t=document.cookie?.match(`\\b${e}=([^;]*)`);return t?s(t[1]):null}(e);if(!a){const e="undefined"!=typeof document&&document.cookie.split("; ");a={};const t=e&&e.length;for(let n=0;n<t;n++){const t=e[n].indexOf("=");a[e[n].slice(0,t).toLocaleLowerCase()]=s(e[n].slice(t+1))}}const n=e.toLocaleLowerCase();return a[n]||null}var l;!function(e){e[e.Alert=0]="Alert",e[e.Deprecated=1]="Deprecated",e[e.HighImpact=2]="HighImpact",e[e.Critical=3]="Critical"}(l||(l={}));const u=new class{constructor(e=20){this.maxLength=e,this.list=[]}push(e){this.list.push(e),this.list.length>this.maxLength&&this.list.shift()}get data(){return this.list}};function h(e,n,s,a=l.Alert,h,m,w){try{const f=function(){if(!t){const e=document.head.getAttribute("data-client-settings");e&&(t=JSON.parse(e))}return t}(),g=o(),p=function(e){if(e){const{pcsInfo:t,pageGenTime:n}=e,o=new Date(n).getTime(),i=!t||["prod","prod-ssr","prod-ssrntp"].includes(t.env);c=i?"browser.events.data.msn.com":"events-sandbox.data.msn.com";return{cors:"true","content-type":"application/x-json-stream","client-id":"NO_AUTH","client-version":"1DS-Web-JS-2.2.2",apikey:i?"0ded60c75e44443aa3484c42c1c43fe8-9fc57d3f-fdac-4bcf-b927-75eafe60192e-7279":"f8857dedc6f54ca8962cfb713e01e7d7-e9250191-fe0b-446f-95ae-07516262f98c-7028","upload-time":o,w:"0",anoncknm:"app_anon"}}return null}(f);let y="";p&&p.apikey&&""!==p.apikey&&(y=function(e){if(e){const t=e.indexOf("-");if(t>0)return e.substring(0,t)}return""}(p.apikey));const S=function(e,t,n,o,s,a,c=l.Alert,u=""){if(n){s=s||{};const{apptype:l,audienceMode:h,pagetype:m,pageGenTime:w,bundleInfo:f,deviceFormFactor:g="",fd_muid:p,os:y}=n;s.pageGenTime=w,s.build=f&&f.v,s.appType=l;const S=function(e,t,n){return t&&"enterprise"===t?"entnews":"anaheim"}(0,h),v=S||l,b=document.getElementsByTagName("html")[0].getAttribute("lang");let T,R="",x="muid";try{if("edgeChromium"===l&&"object"==typeof window&&window.location&&window.location.search){const e=new URLSearchParams(window.location.search);T=e.has("startpage")?"msedgdhp":"msedgntp","enterprise"===h?T="entnewsntp":"xbox"===y&&(T="xboxntp")}window&&window.getCookieConsentRequired&&"function"==typeof window.getCookieConsentRequired&&window.getCookieConsentRequired()||(R=r()||d("muid"))}catch{}R||(R=n.aid,x="aid");const E={name:"MS.News.Web.AppError",time:w,ver:"4.0",iKey:`o:${o}`,data:{baseData:{},baseType:"MS.News.Web.Base",page:{name:"default",product:v,type:m,content:i[l]??{category:"standaloneError"},ocid:T},browser:{clientId:R,clientIdType:x},flight:{id:a,tmpl:u},request:{activityId:n.aid,requestId:n.aid,afdMuid:p},locale:{mkt:b},extSchema:{id:e,severity:c,pb:s,message:t}}};return R&&"muid"===x&&(E.ext={...E?.ext,user:{...E?.ext?.user,localId:`t:${R}`}}),"object"==typeof window&&(window.isSSREnabled&&(E.data.flight.tmpl+=";ssr-enabled:1"),window.isSSRCompleted&&(E.data.flight.tmpl+=";ssr-completed:1")),E?JSON.stringify(E):null}return null}(n,e,f,y,s,g,a,w);if(m&&!function(e){if(null==e)return!1;return e.startsWith("1")||e.startsWith("2")||e.startsWith("3")||e.startsWith("4")}(f?.aid))return console.error(S),void console.error(`This App error Id: ${n} will not be sent due to app error sampling!`);if(p&&S){console.error(S),u.push(S);const e="https://"+c+"/OneCollector/1.0"+function(e){return"?"+Object.keys(e).map((function(t){return t+"="+encodeURIComponent(e[t])})).join("&")}(p);if(h&&!performance.getEntriesByType("visibility-state").some((e=>"visible"===e.name))){const t=()=>{"visible"===document.visibilityState&&navigator.sendBeacon(e,S)};document.addEventListener("visibilitychange",t,{once:!0})}else navigator.sendBeacon(e,S)}}catch{}}function m(){return!("undefined"==typeof window||!window.document||!window.document.createElement||window.isRenderServiceEnv)}let w=null;function f(){return m()?(null===w&&(w=Array.from(document.styleSheets).map((e=>{const t=new CSSStyleSheet,n=Array.from(e.cssRules).map((e=>e.cssText)).join(" ");return t.replaceSync(n),t}))),w):[]}function g(e,t,n=""){try{e.adoptedStyleSheets.push(t)}catch(e){h("Error setting adoptedStyleSheets",34030,{id:n,sheet:t,error:e?.message})}}var p;!function(e){e.JSON="application/json;charset=UTF-8",e.HTML="text/html;charset=UTF-8"}(p=p||(p={}));function y(){const e=performance.getEntriesByType("navigation")[0].serverTiming;if(e){const t=e.find((e=>"rsClusterName"===e.name));if(t)return t.description}}const S=function(){if(m())return window._clientSettings.apptype}();class v extends HTMLElement{connectedCallback(){if(this.previousSibling?.nodeType!==Node.COMMENT_NODE)return;const e=this.previousSibling.data;if(""!==e)try{this.jsonData=JSON.parse(e)}catch(t){h("Error parsing JSON from Comment Node",34029,{data:e,error:t?.message,tagName:this.tagName})}finally{this.previousSibling.data=""}}}customElements.get("fast-ssr-style")||customElements.define("fast-ssr-style",class e extends v{static#e=this.styleCache=new Map;connectedCallback(){super.connectedCallback();const t=this.getAttribute("style-ids"),n=this.getRootNode();"msnStudio"!==S&&"homePage"!==S||(f().forEach((e=>g(n,e))),function(e){const t=e.host;t&&(t.dataset.globalStylesAdded="1")}(n)),t&&(this.addToCache(),t.split(",").forEach((t=>{const o=e.styleCache.get(t);g(n,o,t)}))),this.remove()}addToCache(){this.jsonData&&Object.keys(this.jsonData).forEach((t=>{const n=this.createStyleSheet(t);n&&e.styleCache.set(t,n)}))}createStyleSheet(e){const t=new CSSStyleSheet,n=this.jsonData[e];try{return t.replace(n),t}catch(t){h("Error creating CSSStyleSheet",34030,{key:e,styles:n,error:t?.message})}}}),customElements.get("ttsr-marker")||customElements.define("ttsr-marker",class extends HTMLElement{constructor(e=Math.round(performance.now())){super(),this.initTime=e}connectedCallback(){const e=this.dataset.name;e&&(window.markTTSR(e,this.initTime),"true"===this.dataset.emitEvent&&window.dispatchEvent(new Event(e)),this.remove())}}),customElements.get("ssr-data-from-template")||customElements.define("ssr-data-from-template",class extends HTMLElement{connectedCallback(){const e=this.dataset.d;if(e)try{const t=JSON.parse(e);if(!t)return;Object.keys(t).forEach((e=>{window[e]=t[e]}))}catch(t){h("Error parsing JSON from SSRDataFromTemplate component",34029,{data:e,error:t?.message,tagName:this.tagName})}finally{this.remove()}}}),customElements.get("ssr-data")||customElements.define("ssr-data",class extends v{connectedCallback(){super.connectedCallback(),this.jsonData&&(Object.keys(this.jsonData).forEach((e=>{window[e]=this.jsonData[e]})),this.remove())}}),customElements.get("ssr-func-exp")||customElements.define("ssr-func-exp",class extends v{connectedCallback(){super.connectedCallback();const e=window[this.dataset.fnName];e&&"function"==typeof e&&e(this.jsonData),this.remove()}});const b="1.0.21";let T,R,x;(new Date).getTime();try{R="sessionStorage"in window&&window.sessionStorage}catch(e){}try{navigator&&navigator.userAgentData&&"Windows"===navigator.userAgentData.platform&&navigator.userAgentData.getHighEntropyValues&&navigator.userAgentData.getHighEntropyValues(["platformVersion"])&&navigator.userAgentData.getHighEntropyValues(["platformVersion"]).then((e=>{x=e.platformVersion}))}catch(e){}function E(e){if(e)return e.scrollTop||0;return A&&(window.pageYOffset||document.body.scrollTop||document.documentElement.scrollTop)||0}function k(){const e="undefined"!=typeof document&&document.cookie;if(!A&&!e)return"";const t=new RegExp("\\bMUID\\s*=\\s*([^;]*)","i").exec(e);return t&&t.length>1?t[1]:""}const A=!("undefined"==typeof window||!window.document||!window.document.createElement||window.isRenderServiceEnv);function C(){let e=!1;if(A)try{if(x&&parseInt(x.split(".")[0])>=11&&(e=!0),!e&&window&&window.external&&window.external.getHostEnvironmentValue("os-build")){const t=JSON.parse(window.external.getHostEnvironmentValue("os-build"))["os-build"].split(".");(t[0]>10||10===t[0]&&t[1]>0||t[2]>=22e3)&&(e=!0)}}catch(e){}return T=!0===e||null,T}const N="1.0.21";const L=()=>{const[e]=performance.getEntriesByType("navigation");if(!e)return!1;if("cache"===e.deliveryType||200===e.responseStatus&&0===e.transferSize)return!0;const t=e.serverTiming;return!(!t||!t.length)&&!!t.find((e=>e&&"cache"===e.name))};let q;const M=JSON.parse(document.head.dataset?.clientSettings||"{}");let I={};function D(e){I=e}let P,O=()=>"";function _(e){I.integration={...I.integration,joinIds:JSON.stringify(e)}}function U(e,t,n){const o=r()||k(),i=I||{},s=n||{};let a=i.custom;if(a)try{a=JSON.parse(a)}catch(e){}return{name:e,time:t.toISOString(),ver:"4.0",iKey:`o:${window.oneDSConfig?.key?.split("-")[0]}`,data:{...i,baseType:"MS.News.Web.Base",baseData:{},page:{...i.page,canvas:"Browser",appType:M.apptype,content:{...i.page?.content,title:document.title},config:P??i.page?.config},flight:{tmpl:`isswcached:${L()?"1":"0"};ssr-enabled:1;edge.mem:${navigator.deviceMemory+""};edge.concurrency:${navigator.hardwareConcurrency+""};edge.connection:${navigator.connection?.effectiveType+""};${O()}`},browser:{width:window.outerWidth,height:window.outerHeight,screenSize:[screen.width,screen.height].join("x"),clientId:o,scrollOffset:E(),anoncknm:"app_anon",cookieEnabled:navigator.cookieEnabled,isWin11:C(),muid:o},request:{activityId:M.aid,requestId:q||M.aid,afdMuid:M.fd_muid,tier:M&&M.detection&&M.detection.tier?M.detection.tier:""},timestamp:t.getTime(),sdk:{name:"peregrine-lite-telemetry",ver:N,schemaVer:b},build:M.bundleInfo?.v,custom:{...a,...s,"rs-cluster-name":y(),"ssr-enabled":1}},ext:{user:{localId:`t:${o}`}}}}const B="sign-in-active",H="sign-in-pending",j="sign-in-refreshed-page",V="sign-in-refresh-session";let $=new Map;const J=Object.freeze({set(e,t){return $.set(e,t),this},get:function(e,t){let n=$.get(e);return void 0===n&&t&&(n=t(),$.set(e,n)),n},clear(){$.clear()},delete:e=>$.delete(e),has:e=>$.has(e)});function F(){return m()?window._pageTimings||(window._pageTimings={}):J.get("__diagnostics_pageTimings",(()=>({})))}const W="MUID";const z="__RequestDataInstance__";class G{constructor(e,t){if(this.url=new URL(e.href),this.innerHeight=e.innerHeight,this.devicePixelRatio=e.devicePixelRatio,this.canUseCssGrid=e.canUseCssGrid,this.requestId=e.requestId,this.cookie=e.cookie,this.referer=e.referer,this.userAgent=e.userAgent,this.clientData=e.clientData,this.vpCetoToken=e.vpCetoToken,this.oneServiceHeaders=Y(e.oneServiceHeaders)||{},this.isPssrMode=t,t){const e=K("OSATE",this.cookie),t=!!e&&"1"===e,n=K("OSAT",this.cookie);if(t&&n||!e&&!n)return this.msalAuthReady=!0,void(n&&(this.oneServiceHeaders.Authorization=`Bearer ${n}`));this.msalAuthReady=!1,this.pssrRejectedReason=e&&!t?"interactiveLogin":e&&!n?"missOSAT":"missOSATE"}}static getInstance(){const t=J.get(z);return t||(e.g.TEST_ENV?X({href:"http://localhost:8080/",innerHeight:768,devicePixelRatio:1,canUseCssGrid:!1,requestId:"0",cookie:"",userAgent:"",referer:"",oneServiceHeaders:"",vpCetoToken:""}):X({href:"http://localhost:8080/",innerHeight:0,devicePixelRatio:0,canUseCssGrid:!1,requestId:"0",cookie:"",userAgent:"",referer:"",oneServiceHeaders:"",vpCetoToken:""}))}static resetInstance(e,t=!1){const n=new G(e,t);return J.set(z,n),n}}function K(e,t){if(t&&e){const n=new RegExp("\\b"+e+"\\s*=\\s*([^;]*)","i").exec(t);return n&&n.length>1?n[1]:null}return null}function Y(e){try{if(e)return JSON.parse(e)}catch(e){}}const X=G.resetInstance,Z=()=>G.getInstance();function Q(e){return function(e,t){if(e){const n=new RegExp("\\b"+t+"\\s*=\\s*([^;]*)","i").exec(e);return n&&n.length>1?n[1]:null}return null}(m()?window.document.cookie:Z().cookie,e)}var ee,te;!function(e){e.Lax="lax",e.Strict="strict",e.None="none"}(ee||(ee={})),function(e){e.High="High",e.Medium="Medium",e.Low="Low"}(te||(te={}));const ne="ntp.user_nurturing",oe="user_muid",ie=6,re=0,se=new Date("2010-01-01Z"),ae=192,ce=16383,de=240,le=192,ue=63,he="MUID";let me=!1,we=[];function fe(){const e=se,t=new Date;let n=-1;return(t.getDay()!=e.getDay()||t.getMonth()!=e.getMonth()||t.getFullYear()!=e.getFullYear())&&(n=Math.floor(Math.abs(t.getTime()-e.getTime())/864e5),n>ce)?0:n}function ge(e){let t=!0;if(32!=e?.length)return{isMuidValid:!1};const n=new Uint8Array(function(e){const t=[],n=[6,4,2,0,10,8,14,12,16,18,20,22,24,26,28,30];for(const o of n){const n=e.slice(o,o+2);t.push(parseInt(n,16))}return new Uint8Array(t)}(e));if(n.forEach((e=>{isNaN(e)&&(t=!1)})),!t)return{isMuidValid:!1};if(function(e){[e[0],e[3]]=[e[3],e[0]],[e[1],e[2]]=[e[2],e[1]],[e[4],e[5]]=[e[5],e[4]],[e[6],e[7]]=[e[7],e[6]]}(n),0!=(n[0]&ae))return{isMuidValid:!1};if((n[6]&de)!=ie<<4)return{isMuidValid:!1};if((n[8]&le)!=re<<6)return{isMuidValid:!1};const o=new Uint8Array(n);!function(e){!function(e){const t=e.length>>1;for(let n=0;n<t;n++)e[n+t]^=e[n]}(e)}(o);const i=function(e){const t=new Uint8Array([0]);for(let n=0;n<e.length-1;n++)t[0]+=e[n];return t[0]}(o);if(i!=o[15])return{isMuidValid:!1};let r=0;r|=(o[10]&ue)<<8,r|=o[11];fe();return r>fe()+1?{isMuidValid:!1}:{dateCreated:r,isMuidValid:t}}function pe(e,t){const n=new Date;n.setTime(n.getTime()+31536e6);return`MUID=${e};expires=${n.toUTCString()};domain=${t};path=/;SameSite=${ee.None};Secure;priority=${te.High};`}async function ye(e="",t=ve,n=Se,o=he){const i=Date.now();window.muidActionRecord={isMuidReverted:!1,muidRevertedFrom:"",signInPerfCost:0,perfCost:0,isRefreshMuidError:!1};try{if("psl"===e&&await Promise.race([new Promise((e=>{window?.chrome?.authPrivate?.getPrimaryAccountInfo?window.chrome.authPrivate.getPrimaryAccountInfo((t=>{e(!t||""===t.account_id)})):e(!0)})),new Promise((e=>{setTimeout((()=>{e(!0)}),50)}))]))return;window.muidActionRecord.signInPerfCost=Date.now()-i;const r=Q(W)||"",s=await t(o,(e=>e&&ge(e)?.isMuidValid),e);s!==r&&function(e,t,n,o){if(e){const i=window.location.hostname.toLowerCase().endsWith(".cn")?".msn.cn":".msn.com",{isMuidValid:r,dateCreated:s}=ge(e),{isMuidValid:a,dateCreated:c}=ge(t);if(!r&&a)return void n(o,t);if(r&&!a)return window.document.cookie=pe(e,i),window.muidActionRecord.isMuidReverted=!0,void(window.muidActionRecord.muidRevertedFrom=t);if(void 0!==s&&void 0!==c)s<=c?(window.document.cookie=pe(e,i),window.muidActionRecord.isMuidReverted=!0,window.muidActionRecord.muidRevertedFrom=t):n(o,t)}else n(o,t)}(s,r,n,o),window.muidActionRecord.perfCost=Date.now()-i}catch(e){window.muidActionRecord.isRefreshMuidError=!0}}const Se=(e,t)=>{window.localStorage.setItem(e,t),me&&function(e,t,n){const o=e.findIndex((e=>e?.key===t));if(o>-1)e[o]={...e[o],value:n};else{const o={key:t,value:n};e.push(o)}window.chrome.ntpSettingsPrivate.setPref(ne,e)}(we,oe,t)},ve=async(e,t,n)=>{me=!!window.chrome?.ntpSettingsPrivate?.getPref&&"psl"===n;let o=window.localStorage.getItem(e)||"";if(!o&&me){we=await function(e){return new Promise(((t,n)=>{window.chrome.ntpSettingsPrivate.getPref(e,(function(e){e&&e.value?t(e.value):t([])}))}))}(ne);const e=(we||[]).find((e=>e.key===oe));o=e?.value||""}return o};let be;const Te="data-t";let Re=e=>{try{const t=function(){if(!window.oneDSConfig?.endpoint)throw new Error("1DS endpoint not found");const e=new URL(window.oneDSConfig.endpoint);return e.searchParams.append("cors","true"),e.searchParams.append("content-type","application/x-json-stream"),e.searchParams.append("client-id","NO_AUTH"),e.searchParams.append("client-version",`peregrine-lite-telemetry-${M.bundleInfo?.v}`),window.oneDSConfig.key&&e.searchParams.append("apikey",window.oneDSConfig.key),e.searchParams.append("upload-time",Date.now().toString()),e.searchParams.append("w","0"),e.searchParams.append("anoncknm","app_anon"),e.searchParams.append("NoResponseBody","true"),e}();navigator.sendBeacon(t,JSON.stringify(e))}catch(e){h("Error sending Lite Telemetry",34019,{error:e.message})}};let xe=!0,Ee=!0;const ke={"TTVR.SearchBox":"TTSR.SearchBox",TTVR:"TTSR.Complete"};function Ae(e,t=!1,n=!0){const o=Object.assign({},ke,e);return()=>{if(n&&"hidden"!==document.visibilityState)return;const e=F();if(e.TTVR||!e["TTSR.SearchBox"])return;window.liteLoadTimeSentKey||(window.liteLoadTimeSentKey=[]);const i=U("MS.News.Web.LoadTime",new Date),r={};let s=!1;if(Object.entries(o).forEach((([t,n])=>{e[n]&&(r[t]=e[n],window.liteLoadTimeSentKey?.push(t),delete o[t],s=!0)})),!s)return;const a=function(){if(m()&&navigator)return{memory:navigator.deviceMemory,cpu:navigator.hardwareConcurrency,networkSpeed:navigator.connection&&navigator.connection.effectiveType}}()||{},c=t?r:{...e,...r};Object.assign(i.data,{extSchema:{markers:c,deviceCapabilities:a}}),Re(i)}}async function Ce(e,t){if(window.__peregrine_normalPVSent)return;if(Ee&&Me())return;"none"!==t&&await ye(t),window.__peregrine_litePVSent=!0;const n=U("MS.News.Web.PageView",new Date,e),o=()=>{F().TimeToSendPageView=Math.round(performance.now()),Re(n)};xe?(be||(be=new Promise((e=>{"visible"===document.visibilityState&&e(),document.addEventListener("visibilitychange",(function t(){"visible"===document.visibilityState&&(e(),document.removeEventListener("visibilitychange",t))}))}))),be).then(o):o()}const Ne=e=>{const t=[];let n=e;for(;n&&n!==document.body;)t.unshift(`${n.tagName.toLowerCase()}${n.id?"#"+n.id:""}${n.className?"."+n.className.replace(/\s+/g,"."):""}`),n=Oe(n);return t.join(" > ")};function Le(e,t){if(e)try{const n=U("MS.News.Web.PageAction",new Date);n.data.actionType=t,["click","rightclick"].includes(t)&&(n.data.gesture=t);const o=Pe(e);if(o){if("search_websearch"===o.n)return;if("submit"!==t&&["headersearch","searchinput"].includes(o.n))return;n.data.behavior=o.b,n.data.object={name:o.n,type:o.t,content:{id:o["c.i"],type:o["c.t"],headline:o["c.hl"],vertical:o["c.v"],brandId:o["c.bi"],category:o["c.c"]},ext:o.ext,destinationUrl:e.getAttribute("action")||e.getAttribute("destination-url")||e.href||""};let i=o.n||"",r=Oe(e);for(;r&&r!==document.body;){const e=Pe(r);e?.n&&(i=`${e.n}>${i}`),r=Oe(r)}n.data.object.placement={lineage:i},i||h("Empty lineage in Lite Action Telemetry",34020,{elementPath:Ne(e),elementType:e?.tagName,actionType:t})}else if("click"===t)return;Re(n)}catch(n){h("Error sending Lite Action Telemetry",34019,{error:n.message,stack:n.stack,elementPath:Ne(e),elementType:e?.tagName,actionType:t})}}function qe(e,t=!1){try{if("auxclick"===e.type&&1!==e.button)return;const n=Ie(e),o=n&&function(e){const t=20;for(let n=0;n<t&&e&&!De(e);n++)e=e.assignedSlot||e.parentElement||e.parentNode?.host;if(!De(e))return;return e}(n);if(o){const n=e.type;Le(o,_e[n]||"click"),t&&e.preventDefault()}}catch(t){let n;try{n=Ie(e)}catch(e){}h("Error sending Click Event Telemetry",34019,{error:t.message,stack:t.stack,type:e.type,button:e.button,elementPath:n&&Ne(n),elementType:n?.tagName})}}function Me(){try{return sessionStorage.getItem(B)||sessionStorage.getItem(H)||sessionStorage.getItem(j)||sessionStorage.getItem(V)}catch(e){return!1}}function Ie(e){if(e){if(!e.composedPath||"function"!=typeof e.composedPath)return e.target;{const t=e.composedPath();if(t.length>2&&"hasAttribute"in t[0]&&!t[0].hasAttribute(Te)&&"[object ShadowRoot]"===Object.prototype.toString.call(t[1])&&"hasAttribute"in t[2]&&t[2].hasAttribute(Te))return t[2];if(t.length>0)return t[0]}}}function De(e){if(!e)return!1;if("BUTTON"===e.nodeName||e.hasAttribute?.("href")||e.getAttribute&&"button"===e.getAttribute("role")||e.form)return!0;const t=Pe(e);return t&&(5===t.t||11===t.t||t.b&&e.tagName.includes("-"))}function Pe(e){try{const t=e.getAttribute(Te);return t&&JSON.parse(t)}catch(e){h("Error parsing telemetry attribute",34019,{error:e.message})}}function Oe(e){return e.assignedSlot||e.parentElement||e.parentNode?.host}const _e={auxclick:"middleclick",contextmenu:"rightclick"};let Ue=(new Date).getTime();const Be=e=>{const t=(Ue+16*Math.random())%16|0;return Ue=Math.floor(Ue/16),("x"===e?t:7&t|8).toString(16)},He=()=>"xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g,Be);const je=e=>{qe(e)},Ve=e=>{e.target&&Le(e.target,"submit")};class $e{constructor(e=-1){this._published=[],this._subscribers=[],this.maxSize=e}publish(e){this.maxSize>0&&this._published.length>=this.maxSize&&this._published.shift(),this._published.push(e),this._subscribers.forEach((t=>t(e)))}subscribe(e){return this._subscribers.push(e),this._published.forEach((t=>e(t))),{dispose:()=>{this._subscribers=this._subscribers.filter((t=>t!==e))}}}bulkPublish(e){e.forEach((e=>this.publish(e)))}}function Je(e){const t=performance.getEntriesByType("visibility-state").filter((t=>t.startTime<e));if(0===t.length)return e;let n=0;for(let o=0;o<t.length;o++){const i=t[o];"visible"===i.name&&(n+=(t[o+1]?.startTime??e)-i.startTime)}return n}const Fe=["vendors","microsoft","common","experience"];function We(){let e;const t=new Promise((t=>e=t));window.waitForTtsrComplete=()=>t,window.markTTSR=(t,n)=>{window._pageTimings[`${t}-Init`]=n||Math.round(performance.now()),requestAnimationFrame((()=>{window._pageTimings[`${t}-RAF`]=Je(Math.round(performance.now())),setTimeout((()=>{var n;window._pageTimings[t]=Je(Math.round(performance.now())),"TTSR.Complete"===t&&e(),"TTSR.Complete"===t&&window.ntpSsrEverything&&window.standbyLoadtimeSender&&(window.standbyLoadtimeSender(),delete window.standbyLoadtimeSender,delete window.ntpSsrEverything),"TTSR.Complete"==t&&(n=10,Math.floor(101*Math.random())<=n)&&function(){const e=window?.performance?.getEntriesByType("long-animation-frame");if(!e)return;window._longAnimationFrames=[],e.forEach((e=>{const t=e;t.scripts&&t.scripts.length>0&&t.scripts.forEach((e=>{for(let n=0;n<Fe.length;n++)if(e.sourceURL.includes(Fe[n])){window._longAnimationFrames.push({sourceURL:e.sourceURL,blockingDuration:t.blockingDuration});break}}))}))}()}))}))}}var ze;!function(e){e.visibility="visibility",e.adServed="adserved",e.click="click"}(ze||(ze={}));const Ge=["control icon-only","menu","ad-label","ad-choice"],Ke="cs-personalized-feed";function Ye(){const e=document.querySelector("edge-chromium-page")?.shadowRoot?.querySelector("grid-view-feed")?.shadowRoot?.querySelector("cs-super-container")?.shadowRoot?.querySelector(Ke)?.shadowRoot?.querySelector("cs-feed-layout");return e||document.querySelector("edge-chromium-page")?.shadowRoot?.querySelector("waterfall-view-feed")?.shadowRoot?.querySelector("cs-super-container")?.shadowRoot?.querySelector("cs-responsive-feed-layout")}function Xe(e,t){return e?.shadowRoot?.querySelectorAll(t)||[]}function Ze(e,t,n,o){if(!o)return e[n]?.shadowRoot?.querySelector(`#${t}`);for(let n=0;n<e?.length;n++){if("cs-native-ad-card"===e[n]?.localName||"cs-native-ad-card-bi"===e[n]?.localName||"cs-native-ad-card-va-phase2"===e[n]?.localName||"cs-native-ad-card-default"===e[n]?.localName){const o=e[n]?.shadowRoot?.querySelector("cs-card")?.querySelector("cs-content-card")?.getAttribute("data-t");if(o&&o.includes(t))return e[n]}else{const o=e[n]?.getAttribute("data-t");if(o&&o.includes(t))return e[n]}}}function Qe(){if(!window.isSSRCompleted)return;const e=Ye(),t=!!document.querySelector("edge-chromium-page")?.shadowRoot?.querySelector("waterfall-view-feed"),n=function(e){if("CS-RESPONSIVE-FEED-LAYOUT"===e?.tagName){const t=[];let n=Xe(e,"cs-native-ad-card, cs-native-ad-card-bi, cs-native-ad-card-va-phase2, cs-native-ad-card-default");return n.length>0?n:(n=Xe(e,"cs-responsive-card"),n.forEach((e=>{e?.id?.startsWith("nativead")&&t.push(e)})),t)}return e?.shadowRoot?.querySelectorAll("cs-native-ad-card, cs-native-ad-card-bi, cs-native-ad-card-va-phase2, cs-native-ad-card-default")||[]}(e);window.ssrAdData?.forEach(((o,i)=>{tt(o.beaconsJson,ze.adServed);let r=Ze(n,o.id,i,t);if(!r){if(window.customSectionRows)return;if(r=t?0==i?e?.shadowRoot?.querySelector("#nativead-river-2"):e?.shadowRoot?.querySelector("#nativead-river-1"):0==i?e?.shadowRoot?.querySelector("#nativead-river-1"):e?.shadowRoot?.querySelector("#nativead-resriver-1"),!r){const e=function(e){const t=[];return e?document.querySelector("edge-chromium-page")?.shadowRoot?.querySelector("waterfall-view-feed")?.shadowRoot?.querySelectorAll("cs-super-container")?.forEach((e=>{e.shadowRoot?.querySelectorAll("cs-responsive-feed-layout")?.forEach((e=>{let n=Xe(e,"cs-native-ad-card, cs-native-ad-card-va-phase2, cs-native-ad-card-default");n.length>0?n?.forEach((e=>{t.push(e)})):(n=Xe(e,"cs-responsive-card"),n?.forEach((e=>{e?.id?.startsWith("nativead")&&t.push(e)})))}))})):document.querySelector("edge-chromium-page")?.shadowRoot?.querySelector("grid-view-feed")?.shadowRoot?.querySelectorAll("cs-super-container")?.forEach((e=>{e.shadowRoot?.querySelectorAll(Ke)?.forEach((e=>{e.shadowRoot?.querySelectorAll("cs-feed-layout")?.forEach((e=>{e.shadowRoot?.querySelectorAll("cs-native-ad-card, cs-native-ad-card-va-phase2, cs-native-ad-card-default")?.forEach((e=>{t.push(e)}))}))}))})),t}(t);r=Ze(e,o.id,i,t)}if(!r){const r=e?.shadowRoot?.children?.length||0,s=[];for(let t=0;t<r;t++)s.push(e?.shadowRoot?.children?.item(t)?.tagName);const a=t?n[i]?.shadowRoot?.querySelector("cs-responsive-card")?.outerHTML||n[i]?.shadowRoot?.querySelector("cs-card")?.outerHTML||"":n[i]?.shadowRoot?.querySelector("cs-card")?.outerHTML||"";return void h("Ad render doesn't match ssr ad data",20193,{renderedCardTags:JSON.stringify(s),adDataId:o.id,renderedId:JSON.stringify(a)})}}window.adsListened?window.adsListened.push(r.id):window.adsListened=[r.id],r.addEventListener("click",(e=>{const t=e.composedPath();for(let e=0;e<7&&e<t.length;e++){const n=t[e];if(n.className&&Ge.includes(n.className.replace(/\s/g,"")))return}tt(o.beaconsJson,ze.click,o.clickBeaconJson)}));new IntersectionObserver(et(o.beaconsJson),{root:null,rootMargin:"0px",threshold:.5}).observe(r)}))}const et=e=>(t,n)=>{t&&t.forEach((t=>{t.intersectionRatio>=.5&&window.setTimeout((()=>{tt(e,ze.visibility)}),1e3,n,t.target)}))};function tt(e,t,n){if(e?.length)try{const o=JSON.parse(e);let i;switch(t){case ze.adServed:i=o&&o.trb;break;case ze.visibility:i=o&&o.tvb;break;case ze.click:i=n&&JSON.parse(n)?.tcb}for(let e=0;e<i.length;e++){const n=i[e];t===ze.visibility&&window.viewedBeaconUrls?.includes(n)||t===ze.adServed&&window.servedBeaconUrls?.includes(n)||nt(n,t,o.e)}}catch(t){h(`Fail to fire ad served call, Fail to parse beacon json tracking data: ${e}`,20179,{error:t.message})}}function nt(e,t,n){n||(e=decodeURIComponent(e)),e&&function(e,t){try{let t=new Image(1,1);t.src=e,t=null}catch(t){h(`Invalid beacon URL, Fail to parse beacon json tracking data: ${e}`,20189,{error:t.message})}finally{t===ze.visibility?window.viewedBeaconUrls?window.viewedBeaconUrls.push(e):window.viewedBeaconUrls=[e]:t==ze.adServed&&(window.servedBeaconUrls?window.servedBeaconUrls.push(e):window.servedBeaconUrls=[e])}}(e,t)}performance.mark("ssrExt"),(()=>{const e=location.search.toLowerCase().indexOf("ssronly=true")>-1;if(e&&(window.SSRONLY=e),window.isSSREnabled){history.scrollRestoration="manual",window.setLiteTelemetryContract=D,window.sendLitePV=Ce,window.standbyClickEventsHandler=je,window.addEventListener("click",window.standbyClickEventsHandler),window.addEventListener("auxclick",window.standbyClickEventsHandler),window.addEventListener("contextmenu",window.standbyClickEventsHandler),window.searchFormSubmitListener=Ve,(o().includes("ntp-sec-litelt")||window.location.href.includes("lite-loadtime=1"))&&(window.standbyLoadtimeSender=Ae({"TTVR.SearchBox":"TTSR.SearchBox",TTVR:"TTSR.Complete"},!0,!1),window.ntpSsrEverything=!0),window.sendAdBeacon=Qe,window.ssrLoadedExperience=new $e,window.ssrLoadedBundles=new $e,window.publishLoadedBundles=window.ssrLoadedExperience.bulkPublish.bind(window.ssrLoadedExperience),window.updateIdxId=_,We();try{let e="X";const n=L();n&&(e=He(),window.updatedRequestId=e,t=e.toUpperCase(),q=t);const o=window._clientSettings,i=o.aid||"X";window.swFeatTmplContents={aid:i,newRid:e,responseCached:Number(n)};const r=o.requestMuid;O=()=>`sw-rid-log:${i}|${e}|${Number(n)};requestMuid:${r};`}catch(e){h("Exception occurred while running the RID swap logic in the edgeNext SSR-extension",34031,{error:e.message})}var t;window.addEventListener("TTSR.SearchBox",(()=>{try{const e=(document.querySelector("edge-chromium-page")?.shadowRoot?.querySelector("cs-header-core")||document.querySelector("ruby-page")?.shadowRoot?.querySelector("cs-ruby-header-core")).shadowRoot.querySelector("common-search-box-edgenext").shadowRoot.querySelector("cs-common-search-box").shadowRoot.querySelector("form");if(!e)throw"Search box form element not found";e.addEventListener("submit",window.searchFormSubmitListener)}catch(e){h("Exception occurred while adding search box form submit event listener",34018,{error:e.message})}}),{once:!0}),window.addEventListener("TTSR.HeaderTag",(e=>{try{const e=["common-settings-edgenext"],t=(e,t)=>document.getElementsByTagName(e)[0]?.shadowRoot?.querySelector(t).shadowRoot,n=t("edge-chromium-page","cs-header-core")||t("ruby-page","cs-ruby-header-core");for(const t of e){const e=n.querySelector(t);e&&(e.removeAttribute("needs-hydration"),e.setAttribute("needs-recreation",""),o().includes("ntp-ttsrcsfix")&&e.setAttribute("use-ttsr",""))}}catch(e){h("SSR header components not found",20203,{error:e.message})}})),window.addEventListener("TTSR.TopSitesTag",(()=>{const e=document.getElementsByTagName("edge-chromium-page")[0]?.shadowRoot.querySelector("top-sites-edgenext")||document.getElementsByTagName("ruby-page")[0]?.shadowRoot.querySelector("top-sites-edgenext");if(!e)return;let t;e.removeAttribute("needs-hydration"),e.setAttribute("needs-recreation","");try{t=JSON.parse(localStorage["TSNP1.topsites"])}catch(e){return}const n=e.shadowRoot,o=n?.querySelectorAll("div.pill-top-site:not(.add-placeholder)");if(o?.length>0){const e=Math.min(o.length,t.length);for(let n=0;n<e;n++){const e=t[n],i=o[n];e.contentViewTelemetryTag&&i.setAttribute("data-t",e.contentViewTelemetryTag),i.setAttribute("title",e.title),i.setAttribute("aria-label",e.title),i.querySelector(".image img").setAttribute("src",e.faviconUrl);const r=i.querySelector("a.control");r.setAttribute("href",e.url),e.anchorTelemetryTag?r.setAttribute("data-t",e.anchorTelemetryTag):h("No anchorTelemetryTag",34021,{error:`contentViewTelemetryTag is ${i.getAttribute("data-t")}`}),i.querySelector("a.control .top-site-title span").innerText=e.title}if(o.length>t.length)for(let t=e;t<o.length;t++)o[t].remove();window.markTTSR("TTSR.TopSitesEdgeNextWC")}else;}),{once:!0}),window.addEventListener("TTSR.WelcomeGreetingLight",(()=>{try{const e=document.getElementsByTagName("edge-chromium-page")[0].shadowRoot,t=e.querySelector("cs-header-core").shadowRoot.querySelector("#WGPlaceHolder");if(t){const n=e.querySelector("#WelcomeGreetingHidden");t.replaceWith(n.querySelector("welcome-greeting-light")),n.remove()}}catch(e){h("SSR WelcomeGreetingLight components not found",34032,{error:e.message})}})),window.isResized=!1,window.addEventListener("resize",(()=>window.isResized=!0),{once:!0})}})(),performance.measure("ssrExt","ssrExt"),window.ssrExtensionLoaded=!0}();