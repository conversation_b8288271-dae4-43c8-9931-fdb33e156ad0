var OneTrustStub=(t=>{if(document.cookie){var e=function(t){return parseInt(t,2)||0};try{let t={};document.cookie.split(";").forEach((function(e){let[n,i]=e.split("=");t[n.trim()]=i}));var n=t["eupubconsent-v2"];if(n){n=function(t){for(var e="",n=0,i=t.length;n<i;n+=1){var o=t.charCodeAt(n).toString(2);e+="00000000".slice(0,8-o.length)+o}return e}(function(t){if("function"==typeof atob){var e=t.replace(/_/g,"/").replace(/-/g,"+");try{return atob(e)}catch(t){throw new Error("Unable to decode transparency and consent string")}}if("function"==typeof Buffer)return Buffer.from(t,"base64").toString("binary");throw new Error("Unable to detect base64 decoder")}(n.split(".")[0]));var i=e(n.slice(0,6));if(2!==i)throw new Error("Unsupported transparency and consent string version "+i);var o=n.slice(6,42),a=100*e(o),r=new Date(a);if(r){const t=31536e6;(new Date).getTime()-r.getTime()>=t&&(document.cookie="eupubconsent-v2=;Domain=.msn.com;Path=/;Expires="+new Date(0).toUTCString(),document.cookie="OptanonAlertBoxClosed=;Domain=.msn.com;Path=/;Expires="+new Date(0).toUTCString(),document.cookie="OptanonConsent=;Domain=.msn.com;Path=/;Expires="+new Date(0).toUTCString())}}}catch(s){console.log("Encountered Exception"),console.log(s)}}var s,u,c,p,l,d=new function(){this.optanonCookieName="OptanonConsent",this.optanonHtmlGroupData=[],this.optanonHostData=[],this.genVendorsData=[],this.vendorsServiceData=[],this.IABCookieValue="",this.oneTrustIABCookieName="eupubconsent",this.oneTrustIsIABCrossConsentEnableParam="isIABGlobal",this.isStubReady=!0,this.geolocationCookiesParam="geolocation",this.EUCOUNTRIES=["BE","BG","CZ","DK","DE","EE","IE","GR","ES","FR","IT","CY","LV","LT","LU","HU","MT","NL","AT","PL","PT","RO","SI","SK","FI","SE","GB","HR","LI","NO","IS"],this.stubFileName="otSDKStub",this.DATAFILEATTRIBUTE="data-domain-script",this.bannerScriptName="otBannerSdk.js",this.domPurifyScriptName="otDomPurify.js",this.mobileOnlineURL=[],this.isMigratedURL=!1,this.migratedCCTID="[[OldCCTID]]",this.migratedDomainId="[[NewDomainId]]",this.userLocation={country:"",state:"",stateName:""}},m=((l=s=s||{})[l.Days=1]="Days",l[l.Weeks=7]="Weeks",l[l.Months=30]="Months",l[l.Years=365]="Years",(l=T=T||{}).GDPR="GDPR",l.CCPA="CCPA",l.IAB2="IAB2",l.IAB2V2="IAB2V2",l.GENERIC="GENERIC",l.LGPD="LGPD",l.GENERIC_PROMPT="GENERIC_PROMPT",l.CPRA="CPRA",l.CDPA="CDPA",l.DELAWARE="DELAWARE",l.IOWA="IOWA",l.NEBRASKA="NEBRASKA",l.USNATIONAL="USNATIONAL",l.CUSTOM="CUSTOM",l.FLORIDA="FLORIDA",l.COLORADO="COLORADO",l.CONNECTICUT="CTDPA",l.MONTANA="MONTANA",l.TEXAS="TEXAS",l.OREGON="OREGON",l.TENNESSEE="TENNESSEE",l.NEWJERSEY="NEWJERSEY",l.NEWHAMPSHIRE="NEWHAMPSHIRE",l.UCPA="UCPA",T.CPRA,T.CDPA,T.COLORADO,T.OREGON,T.CONNECTICUT,T.FLORIDA,T.MONTANA,T.TEXAS,T.DELAWARE,T.IOWA,T.NEBRASKA,T.TENNESSEE,T.NEWJERSEY,T.NEWHAMPSHIRE,T.UCPA,(l=y=y||{}).Name="OTGPPConsent",l[l.ChunkSize=4e3]="ChunkSize",l.ChunkCountParam="GPPCookiesCount",(T=u=u||{}).CPRA="usca",T.CCPA="usca",T.CDPA="usva",T.OREGON="usor",T.USNATIONAL="usnat",T.COLORADO="usco",T.FLORIDA="usfl",T.CTDPA="usct",T.MONTANA="usmt",T.TEXAS="ustx",T.DELAWARE="usde",T.IOWA="usia",T.NEBRASKA="usne",T.TENNESSEE="ustn",T.NEWJERSEY="usnj",T.NEWHAMPSHIRE="usnh",T.UCPA="usut",T.IAB2V2="tcfeuv2",(l=c=c||{})[l.CPRA=8]="CPRA",l[l.CCPA=8]="CCPA",l[l.CDPA=9]="CDPA",l[l.OREGON=15]="OREGON",l[l.USNATIONAL=7]="USNATIONAL",l[l.COLORADO=10]="COLORADO",l[l.FLORIDA=13]="FLORIDA",l[l.MONTANA=14]="MONTANA",l[l.TEXAS=16]="TEXAS",l[l.DELAWARE=17]="DELAWARE",l[l.IOWA=18]="IOWA",l[l.NEBRASKA=19]="NEBRASKA",l[l.NEWHAMPSHIRE=20]="NEWHAMPSHIRE",l[l.NEWJERSEY=21]="NEWJERSEY",l[l.TENNESSEE=22]="TENNESSEE",l[l.UCPA=11]="UCPA",l[l.CTDPA=12]="CTDPA",l[l.IAB2V2=2]="IAB2V2","geo"),h="otpreview",g=(y.Name,"PRODUCTION"),A=((T={})[s.Days]="PCenterVendorListLifespanDay",T[s.Weeks]="LfSpnWk",T[s.Months]="PCenterVendorListLifespanMonth",T[s.Years]="LfSpnYr",f.prototype.camelize=function(t){return(t=t.replace("--","")).split("-").map((function(t,e){var n=t?t[0].toUpperCase()+t.slice(1):"";return 0===e?t:n})).join("")},f.prototype.strToObj=function(t){for(var e={},n=t.split(";").map((function(t){return t.trim()})),i=0,o=void 0;i<n.length;++i)if(/:/.test(n[i])){if(!(o=n[i].split(/:(.+)/))[1])return null;e[this.camelize(o[0])]=o[1].trim()}return e},f);function f(){var t=this;this.implementThePolyfill=function(){var e=t,n=Element.prototype.setAttribute;return Element.prototype.setAttribute=function(t,i){if("style"!==t.toLowerCase()&&n.apply(this,[t,i]),"style"!==t.toLowerCase()||i||this.removeAttribute("style"),"style"===t.toLowerCase()&&i){this.removeAttribute("style");var o,a=e.strToObj(i);for(o in a)this.style[o]=a[o]}},!0}}(l=p=p||{}).ping="ping",l.addEventListener="addEventListener",l.removeEventListener="removeEventListener",l.hasSection="hasSection",l.getSection="getSection",l.getField="getField",l.getGPPData="getGPPData";var E=new function(){var t=this;this.LOCATOR_NAME="__gppLocator",this.win=window,this.customInit="CUSTOMINIT",this.init=function(){t.win.__gpp&&"function"==typeof t.win.__gpp||(t.win.__gpp=t.executeGppApi,window.addEventListener("message",t.messageHandler,!1),t.addFrame(t.LOCATOR_NAME))},this.removeGppApi=function(){delete t.win.__gpp;var e=document.querySelectorAll("iframe[name="+t.LOCATOR_NAME+"]")[0];e&&e.parentElement.removeChild(e)},this.executeGppApi=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var i=null==(i=t.win)?void 0:i.__gpp;if(i.queue=i.queue||[],i.events=i.events||[],!e.length||1===e.length&&"queue"===e[0])return i.queue;if(1===e.length&&"events"===e[0])return i.events;var o=e[0],a=1<e.length?e[1]:null,r=2<e.length?e[2]:null;switch(o){case p.ping:return t.getPingRequest(a);case p.addEventListener:return t.addEventListener(a,r);case p.removeEventListener:return t.removeEventListener(r);default:return void t.addToQueue(o,a,r)}},this.getPingRequest=function(t){var e,n,i={gppVersion:1.1,cmpStatus:"stub",cmpDisplayStatus:"hidden",signalStatus:"not ready",supportedAPIs:(e=[],n={},Object.keys(c).forEach((function(t){var e={};e[t]=c[t],t=Object.assign(e,n);n=t})),Object.keys(u).map((function(t){return{name:t,value:u[t]}})).forEach((function(t){t=n[t.name]+":"+t.value,e.push(t)})),e.filter((function(t,n){return e.indexOf(t)===n}))),currentAPI:"",cmpId:Number.parseInt("28"),sectionList:[],applicableSections:[0],gppString:"",parsedSections:{}};return t&&t(i,!0),i},this.addFrame=function(e){var n,i=t.win.document,o=Boolean(t.win.frames[e]);return o||(i.body?((n=i.createElement("iframe")).style.cssText="display:none",n.name=e,n.setAttribute("title","GPP Locator"),i.body.appendChild(n)):setTimeout((function(){t.addFrame(e)}),5)),!o},this.addEventListener=function(e,n){var i,o=t.win.__gpp;return o.events=o.events||[],null!=(i=o)&&i.lastId||(o.lastId=0),o.lastId++,o.events.push({id:o.lastId,callback:e,parameter:n}),{eventName:"listenerRegistered",listenerId:o.lastId,data:!0,pingData:t.getPingRequest()}},this.removeEventListener=function(e){var n=!1,i=t.win.__gpp;return i.events=i.events||[],i.events=i.events.filter((function(t){return t.id.toString()!==e.toString()||!(n=!0)})),{eventName:"listenerRemoved",listenerId:e,data:n,pingData:t.getPingRequest()}},this.addToQueue=function(e,n,i){var o=t.win.__gpp;o.queue=o.queue||[],o.queue.push([e,n,i])},this.messageHandler=function(e){var n,i,o="string"==typeof e.data;try{n=o?JSON.parse(e.data):e.data}catch(t){n=null}n&&n.__gppCall&&(i=n.__gppCall,(0,t.win.__gpp)(i.command,(function(t,n){t={__gppReturn:{returnValue:t,success:n,callId:i.callId}},e&&e.source&&e.source.postMessage&&e.source.postMessage(o?JSON.stringify(t):t,e.origin||"*")}),i.parameter))},this.customInit||this.init()},v=(S.initCSPTrustedType=function(t){var e=new URL(t);window.DOMPurify&&window.trustedTypes&&window.trustedTypes.createPolicy&&(window.OtTrustedType.TrustedTypePolicy=window.trustedTypes.createPolicy("ot-trusted-type-policy",{createHTML:function(t){return window.DOMPurify.sanitize(t)},createScript:function(t){return window.DOMPurify.sanitize(t)},createScriptURL:function(t){var n,i=[document.location.hostname,e.hostname];try{n=new URL(t,location.origin)}catch(t){return"about:blank#error"}return n.hostname&&!i.includes(n.hostname)?"about:blank#blocked":n.href}}))},S.isCspTrustedType=function(){var t;return(null==(t=window.OtTrustedType)?void 0:t.isCspTrustedTypeEnabled)&&(null==(t=window.OtTrustedType)?void 0:t.TrustedTypePolicy)},S.createScriptURL=function(t){return S.isCspTrustedType()?window.OtTrustedType.TrustedTypePolicy.createScriptURL(t):t},S.checkAndAssignCspTrustedTypeEnabled=function(t){return t=null==(t=t.TenantFeatures)?void 0:t.CookieV2CSPTrustedType,window.OtTrustedType={isCspTrustedTypeEnabled:t},t},S);function S(){}b.prototype.initConsentSDK=function(){this.initCustomEventPolyfill(),this.ensureHtmlGroupDataInitialised(),this.setStubScriptElement(),this.setOTDataLayer(),this.getParam(),this.fetchBannerSDKDependency(),this.captureNonce()},b.prototype.captureNonce=function(){this.nonce=d.stubScriptElement.nonce||d.stubScriptElement.getAttribute("nonce")||null},b.prototype.fetchBannerSDKDependency=function(){this.setDomainDataFileURL(),this.crossOrigin=d.stubScriptElement.getAttribute("crossorigin")||null,this.previewMode="true"===d.stubScriptElement.getAttribute("data-preview-mode"),this.otFetch(d.bannerDataParentURL,this.getLocation.bind(this))},b.prototype.setDomainIfBulkDomainEnabled=function(t){var e=t&&t.TenantFeatures,n=window.location.hostname,i=t.Domain,o=t.BulkDomainCheckUrl;e&&e.CookieV2BulkDomainManagement&&n!==i&&t.ScriptType===g&&((e=window.sessionStorage)&&e.getItem("bulkDomainMgmtEnabled")?this.handleBulkDomainMgmt({isValid:"true"===window.sessionStorage.getItem("bulkDomainMgmtEnabled")},t):(i={location:d.storageBaseURL.replace(/^https?:\/\//,""),domainId:this.domainId,url:n},this.otFetch(o,this.handleBulkDomainMgmt,!1,i,t)))},b.prototype.getLocation=function(t){if(this.setDomainIfBulkDomainEnabled(t),this.updateVersion(t),(t.TenantFeatures&&t.TenantFeatures.CookieV2CSP||t.CookieV2CSPEnabled)&&this.nonce&&(this.setAttributePolyfillIsActive=!0,(new A).implementThePolyfill()),v.checkAndAssignCspTrustedTypeEnabled(t)&&v.initCSPTrustedType(d.storageBaseURL),!t.RuleSet[0].Type)return this.iabTypeAdded=!1,window.__tcfapi=this.executeTcfApi,this.intializeIabStub(),this.addBannerSDKScript(t);var e,n=window;n.OneTrust&&n.OneTrust.geolocationResponse?(n=n.OneTrust.geolocationResponse,this.setGeoLocation(n.countryCode,n.stateCode,n.stateName),this.addBannerSDKScript(t)):(n=this.readCookieParam(d.optanonCookieName,d.geolocationCookiesParam))||t.SkipGeolocation?(e=n.split(";")[0],n=n.split(";")[1],this.setGeoLocation(e,n),this.addBannerSDKScript(t)):this.getGeoLocation(t)},b.prototype.handleBulkDomainMgmt=function(t,e){window.sessionStorage&&window.sessionStorage.setItem("bulkDomainMgmtEnabled",JSON.stringify(t.isValid)),t.isValid&&(e.Domain=window.location.hostname)},b.prototype.getGeolocationURL=function(t){t.TenantFeatures;var e=""+d.stubScriptElement.getAttribute("src").split(d.stubFileName)[0]+t.Version;return new RegExp("^file://","i").test(e)&&t.MobileSDK?(e="/"+t.GeolocationUrl.replace(/^(http|https):\/\//,"").split("/").slice(1).join("/")+".js",d.storageBaseURL+e):t.GeolocationUrl},b.prototype.geoLocationJsonCallback=function(t,e){e&&this.setGeoLocation(e.country,e.state,e.stateName),this.addBannerSDKScript(t)},b.prototype.getGeoLocation=function(t){var e=this.getGeolocationURL(t);this.otFetch(e,this.geoLocationJsonCallback.bind(this,t),!0)},b.prototype.setOTDataLayer=function(){var t="data-dLayer-ignore",e=d.stubScriptElement.hasAttribute(t);t=d.stubScriptElement.getAttribute(t);this.otDataLayer={ignore:e&&"true"===t||e&&""===t,name:d.stubScriptElement.getAttribute("data-dLayer-name")||"dataLayer"}},b.prototype.setGeoLocation=function(t,e,n){d.userLocation={country:t,state:e=void 0===e?"":e,stateName:n=void 0===n?"":n}},b.prototype.otFetch=function(t,e,n,i,o){void 0===n&&(n=!1),void 0===i&&(i=null);var a=window.sessionStorage&&window.sessionStorage.getItem("otPreviewData");if(new RegExp("^file://","i").test(t))this.otFetchOfflineFile(t,e);else if(0<=t.indexOf("/consent/")&&this.previewMode&&a)a=JSON.parse(a).domainJson,e(a);else{d.mobileOnlineURL.push(t);var r=new XMLHttpRequest;if(r.onload=function(t){var n;this&&this.responseText?n=this.responseText:t&&t.target&&(n=t.target.responseText),o?e(JSON.parse(n),o):e(JSON.parse(n))},r.onerror=function(){e()},r.open("GET",t),r.withCredentials=!1,n&&r.setRequestHeader("accept","application/json"),i)for(var s in i)r.setRequestHeader(s,i[s]);r.send()}},b.prototype.otFetchOfflineFile=function(t,e){var n=(t=t.replace(".json",".js")).split("/"),i=n[n.length-1].split(".js")[0];this.jsonp(t,(function(){e(window[i])}))},b.prototype.jsonp=function(t,e){var n=document.createElement("script"),i=v.createScriptURL(t);n.setAttribute("src",i),this.nonce&&n.setAttribute("nonce",this.nonce),n.async=!0,n.type="text/javascript",this.crossOrigin&&n.setAttribute("crossorigin",this.crossOrigin),document.getElementsByTagName("head")[0].appendChild(n),new RegExp("^file://","i").test(t)||d.mobileOnlineURL.push(t),e&&(n.onload=n.onerror=function(){e()})},b.prototype.getRegionSet=function(t){var e,n,i,o=d.userLocation,a=t.RuleSet.filter((function(t){return!0===t.Default}));if(!o.country&&!o.state)return a&&0<a.length?a[0]:null;for(var r=o.state.toLowerCase(),s=o.country.toLowerCase(),u=0;u<t.RuleSet.length;u++)if(!0===t.RuleSet[u].Global)i=t.RuleSet[u];else{var c=t.RuleSet[u].States;if(c[s]&&0<=c[s].indexOf(r)){n=t.RuleSet[u];break}0<=t.RuleSet[u].Countries.indexOf(s)&&(e=t.RuleSet[u])}return n||e||i},b.prototype.ensureHtmlGroupDataInitialised=function(){this.initializeIABData(),this.initializeGroupData(),this.initializeHostData(),this.initializeGenVenData()},b.prototype.initializeGroupData=function(){var t=this.readCookieParam(d.optanonCookieName,"groups");t&&(d.optanonHtmlGroupData=this.deserialiseStringToArray(t))},b.prototype.initializeHostData=function(){var t=this.readCookieParam(d.optanonCookieName,"hosts");t&&(d.optanonHostData=this.deserialiseStringToArray(t))},b.prototype.initializeGenVenData=function(){var t=this.readCookieParam(d.optanonCookieName,"genVendors");t&&(d.genVendorsData=this.deserialiseStringToArray(t))},b.prototype.initializeIABData=function(){this.validateIABGDPRApplied(),this.validateIABGlobalScope()},b.prototype.validateIABGlobalScope=function(){var t=this.readCookieParam(d.optanonCookieName,d.oneTrustIsIABCrossConsentEnableParam);t?"true"===t?(d.hasIABGlobalScope=!0,d.isStubReady=!1):(d.hasIABGlobalScope=!1,d.IABCookieValue=this.getCookie(d.oneTrustIABCookieName)):d.isStubReady=!1},b.prototype.validateIABGDPRApplied=function(){var t=this.readCookieParam(d.optanonCookieName,d.geolocationCookiesParam).split(";")[0];t?this.isBoolean(t)?d.oneTrustIABgdprAppliesGlobally="true"===t:d.oneTrustIABgdprAppliesGlobally=0<=d.EUCOUNTRIES.indexOf(t):d.isStubReady=!1},b.prototype.isBoolean=function(t){return"true"===t||"false"===t},b.prototype.readCookieParam=function(t,e){var n,i,o,a;if(t=this.getCookie(t)){for(i={},o=t.split("&"),n=0;n<o.length;n+=1)a=o[n].split("="),i[decodeURIComponent(a[0])]=decodeURIComponent(a[1]).replace(/\+/g," ");return e&&i[e]?i[e]:e&&!i[e]?"":i}return""},b.prototype.getCookie=function(t){if(this.isAmp){var e=JSON.parse(window.localStorage.getItem(this.domainId))||{};if(e)return e[t]||null}for(var n,i=t+"=",o=document.cookie.split(";"),a=0;a<o.length;a+=1){for(n=o[a];" "==n.charAt(0);)n=n.substring(1,n.length);if(0==n.indexOf(i))return n.substring(i.length,n.length)}return null},b.prototype.updateGtmMacros=function(){for(var t=[],e=d.optanonHtmlGroupData.length,n=0;n<e;n++)this.endsWith(d.optanonHtmlGroupData[n],":1")&&t.push(d.optanonHtmlGroupData[n].replace(":1",""));for(e=d.optanonHostData.length,n=0;n<e;n++)this.endsWith(d.optanonHostData[n],":1")&&t.push(d.optanonHostData[n].replace(":1",""));for(e=d.genVendorsData.length,n=0;n<e;n++)this.endsWith(d.genVendorsData[n],":1")&&t.push(d.genVendorsData[n].replace(":1",""));for(e=d.vendorsServiceData.length,n=0;n<e;n++)this.endsWith(d.vendorsServiceData[n],":1")&&t.push(d.vendorsServiceData[n].replace(":1",""));var i,o=","+this.serialiseArrayToString(t)+",",a=(window.OnetrustActiveGroups=o,window.OptanonActiveGroups=o,window),r=(this.otDataLayer.ignore||void 0===a[this.otDataLayer.name]?this.otDataLayer.ignore||(a[this.otDataLayer.name]=[{event:"OneTrustLoaded",OnetrustActiveGroups:o},{event:"OptanonLoaded",OptanonActiveGroups:o}]):a[this.otDataLayer.name].constructor===Array&&(a[this.otDataLayer.name].push({event:"OneTrustLoaded",OnetrustActiveGroups:o}),a[this.otDataLayer.name].push({event:"OptanonLoaded",OptanonActiveGroups:o})),new CustomEvent("consent.onetrust",{detail:t}));!this.otDataLayer.ignore&&t.length&&(a[this.otDataLayer.name].constructor===Array&&a[this.otDataLayer.name].push({event:"OneTrustGroupsUpdated",OnetrustActiveGroups:o}),i=new CustomEvent("OneTrustGroupsUpdated",{detail:t})),setTimeout((function(){t.length&&window.dispatchEvent(r),i&&window.dispatchEvent(i)}))},b.prototype.deserialiseStringToArray=function(t){return t?t.split(","):[]},b.prototype.endsWith=function(t,e){return-1!==t.indexOf(e,t.length-e.length)},b.prototype.serialiseArrayToString=function(t){return t.toString()},b.prototype.getStubQueryParam=function(t,e){return!t||(t=t.split("?")).length<2?null:new URLSearchParams(t[1]).get(e)},b.prototype.setStubScriptElement=function(){d.stubScriptElement=document.querySelector("script[src*='"+d.stubFileName+"']");var t=(t=d.stubScriptElement&&d.stubScriptElement.getAttribute("src"))&&this.getStubQueryParam(t,"did");d.stubScriptElement&&d.stubScriptElement.hasAttribute(d.DATAFILEATTRIBUTE)?this.domainId=d.stubScriptElement.getAttribute(d.DATAFILEATTRIBUTE).trim():t?this.domainId=t:d.stubScriptElement||(d.stubScriptElement=document.querySelector("script[src*='"+d.migratedCCTID+"']"),d.stubScriptElement&&(d.isMigratedURL=!0,this.domainId=d.migratedDomainId.trim()))},b.prototype.setDomainDataFileURL=function(){var t=d.stubScriptElement.getAttribute("src"),e=-1<t.indexOf("/consent");t&&(d.isMigratedURL?d.storageBaseURL=t.split("/consent/"+d.migratedCCTID)[0]:d.storageBaseURL=(e?t.split("/consent"):t.split("/scripttemplates/"+d.stubFileName))[0]),this.storageBaseURL=d.storageBaseURL,this.isPreview&&-1===this.domainId.indexOf("test")?this.domainId=this.domainId+"-test":this.isPreview=!1,d.bannerBaseDataURL=d.storageBaseURL&&d.storageBaseURL+"/consent/"+this.domainId,d.bannerDataParentURL=d.bannerBaseDataURL+"/"+this.domainId+".json"},b.prototype.initCustomEventPolyfill=function(){if("function"==typeof window.CustomEvent)return!1;function t(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var n=document.createEvent("CustomEvent");return n.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),n}t.prototype=window.Event.prototype,window.CustomEvent=t},b.prototype.removeTcf=function(){delete window.__tcfapi;var t=document.querySelectorAll("iframe[name='__tcfapiLocator']")[0];t&&t.parentElement.removeChild(t)},b.prototype.getParamForIE=function(){return{get:function(t){return null===(t=new RegExp("[?&]"+t+"=([^&#]*)").exec(window.location.search))?null:decodeURI(t[1])||""}}},b.prototype.getParam=function(){window.document.documentMode||!window.URLSearchParams?this.urlParams=this.getParamForIE():this.urlParams=new URLSearchParams(window.location.search);var t="true"===this.urlParams.get("otreset"),e="true"===this.urlParams.get("otpreview"),n=(this.geoFromUrl=(this.urlParams.get("otgeo")||"").toLowerCase(),this.readCookieParam(h,"expiry")),i=this.readCookieParam(h,m);this.isReset=t||n&&new Date(n)<new Date,this.isPreview=!this.isReset&&(e||n&&new Date(n)>new Date),this.setGeoParam(this.geoFromUrl||i)},b.prototype.setGeoParam=function(t){var e;t&&((e=window).OneTrust||(e.OneTrust={}),t=t.split(","),e.OneTrust.geolocationResponse={countryCode:t[0],stateCode:t[1]})},b.prototype.updateVersion=function(t){"debug"!==this.buildType&&"cybuild"!==this.buildType||(t.Version="202502.1.0")};var y=b;function b(){var t=this;this.iabType=null,this.iabTypeAdded=!0,this.crossOrigin=null,this.isAmp=!1,this.domainId="",this.isReset=!1,this.isPreview=!1,this.geoFromUrl="",this.nonce="",this.setAttributePolyfillIsActive=!1,this.storageBaseURL="",this.charset=null,this.buildType="undefined",this.addBannerSDKScript=function(e){var n=t.getRegionSet(e),i=(n.GCEnable||(t.updateGtmMacros(),t.gtmUpdated=!0),t.iabTypeAdded&&("IAB2"!==n.Type&&"IAB2V2"!==n.Type||(t.iabType=n.Type,t.intializeIabStub()),"IAB2"!==n.Type)&&"IAB2V2"!==n.Type&&t.removeTcf(),n.IsGPPEnabled?E.init():E.removeGppApi(),d.stubScriptElement.cloneNode(!0)),o=e.UseSDKRefactor?(d.isMigratedURL&&(i.src=d.storageBaseURL+"/scripttemplates/new/scripttemplates/"+d.stubFileName+".js"),d.storageBaseURL+"/scripttemplates/new/scripttemplates/"+e.Version+"/"+d.bannerScriptName):"5.11.0"===e.Version?(d.isMigratedURL&&(i.src=d.storageBaseURL+"/scripttemplates/old/scripttemplates/"+d.stubFileName+".js"),d.storageBaseURL+"/scripttemplates/old/scripttemplates/5.11.0/"+d.bannerScriptName):(d.isMigratedURL&&(i.src=d.storageBaseURL+"/scripttemplates/"+d.stubFileName+".js"),d.storageBaseURL+"/scripttemplates/"+e.Version+"/"+d.bannerScriptName);["charset","data-language","data-document-language","data-domain-script","crossorigin","data-ignore-ga"].forEach((function(t){d.stubScriptElement.getAttribute(t)&&i.setAttribute(t,d.stubScriptElement.getAttribute(t))})),t.charset=d.stubScriptElement.getAttribute("charset"),t.isAmp=!!d.stubScriptElement.getAttribute("amp"),window.otStubData={bannerBaseDataURL:d.bannerBaseDataURL,crossOrigin:t.crossOrigin,domainData:e,domainId:t.domainId,geoFromUrl:t.geoFromUrl,isAmp:t.isAmp,isPreview:t.isPreview,isReset:t.isReset,mobileOnlineURL:d.mobileOnlineURL,nonce:t.nonce,otDataLayer:t.otDataLayer,regionRule:n,setAttributePolyfillIsActive:t.setAttributePolyfillIsActive,storageBaseURL:t.storageBaseURL,stubElement:i,urlParams:t.urlParams,userLocation:d.userLocation,gtmUpdated:t.gtmUpdated,previewMode:t.previewMode,charset:t.charset,stubUrl:d.stubScriptElement.getAttribute("src")},t.jsonp(o,null)},this.intializeIabStub=function(){var e=window;t.iabTypeAdded?(void 0===e.__tcfapi&&(window.__tcfapi=t.executeTcfApi),t.addIabFrame()):t.addBackwardIabFrame(),e.receiveOTMessage=t.receiveIabMessage,(e.attachEvent||window.addEventListener)("message",e.receiveOTMessage,!1)},this.addIabFrame=function(){var e=window,n="__tcfapiLocator";!e.frames[n]&&(e.document.body?t.addLocator(n,"CMP"):setTimeout(t.addIabFrame,5))},this.addBackwardIabFrame=function(){var e=window,n="__tcfapiLocator";!e.frames[n]&&(e.document.body?t.addLocator(n,"TCF"):setTimeout(t.addIabFrame,5))},this.addLocator=function(t,e){var n=window,i=n.document.createElement("iframe");(function(t,e,n){function i(t){return t?(";"!==(t=t.trim()).charAt(t.length-1)&&(t+=";"),t.trim()):null}void 0===n&&(n=!1);var o=i(t.getAttribute("style")),a=i(e);e="",e=n&&o?(()=>{for(var t=o.split(";").concat(a.split(";")).filter((function(t){return 0!==t.length})),e="",n="",i=t.length-1;0<=i;i--){var r=t[i].substring(0,t[i].indexOf(":")+1).trim();e.indexOf(r)<0&&(e+=r,n+=t[i]+";")}return n})():a,t.setAttribute("style",e)})(i,"display: none;",!0),i.name=t,i.setAttribute("title",e+" Locator"),n.document.body.appendChild(i)},this.receiveIabMessage=function(e){var n,i,o,a="string"==typeof e.data,r={};try{r=a?JSON.parse(e.data):e.data}catch(o){}r.__cmpCall&&"IAB2"===t.iabType&&console.log("Expecting IAB TCF v2.0 vendor iFrame call; Received IAB TCF v1.1"),r.__tcfapiCall&&"IAB2"===t.iabType&&(n=r.__tcfapiCall.callId,i=r.__tcfapiCall.command,o=r.__tcfapiCall.parameter,r=r.__tcfapiCall.version,t.executeTcfApi(i,o,(function(t,o){t={__tcfapiReturn:{returnValue:t,success:o,callId:n,command:i}},e&&e.source&&e.source.postMessage&&e.source.postMessage(a?JSON.stringify(t):t,"*")}),r))},this.executeTcfApi=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(t.iabType="IAB2",!e.length)return window.__tcfapi.a||[];var i=e[0],o=e[1],a=e[2],r=e[3];"function"==typeof a&&i&&("ping"===i?t.getPingRequest(a):t.addToQueue(i,o,a,r))},this.addToQueue=function(t,e,n,i){var o=window,a="__tcfapi";o[a].a=o[a].a||[],o[a].a.push([t,e,n,i])},this.getPingRequest=function(e,n){var i;void 0===n&&(n=!1),e&&(i=!(n={}),"IAB2"!==t.iabType&&"IAB2V2"!==t.iabType||(n={gdprApplies:d.oneTrustIABgdprAppliesGlobally,cmpLoaded:!1,cmpStatus:"stub",displayStatus:"stub",apiVersion:"2.0",cmpVersion:void 0,cmpId:void 0,gvlVersion:void 0,tcfPolicyVersion:void 0},i=!0),e(n,i))},this.getConsentDataRequest=function(t){t&&d.IABCookieValue&&t({gdprApplies:d.oneTrustIABgdprAppliesGlobally,hasGlobalScope:d.hasIABGlobalScope,consentData:d.IABCookieValue},!0)},this.initConsentSDK()}var T=new y;return t.OtSDKStub=y,t.otSdkStub=T,t})({});