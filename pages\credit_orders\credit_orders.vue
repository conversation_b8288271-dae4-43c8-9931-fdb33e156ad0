<template>
	<view class="bkcolor" style="background-color: #F8F8F8;">
		<!-- 订单外容器 -->
		<view class="fl-card wd">
			<view v-for="(item,index) in orederList" :key="index">
				<view class="order-info">
					<view>
						{{item.productTitle}}
					</view>
					<view>
						订单号: {{item.outTradeNum}}
					</view>
					<view>
						充值账号: {{item.mobile}}
					</view>
					<view>
						创建时间:{{dateString(item.payTime)}}
					</view>
					<view style="display: flex; align-items: center;">
						订单状态: <view style="margin: 0 5px; font-size: 14px; padding: 2px 10px; border-radius: 5px;"
							:class="item.orderStatus">{{orderStatuDirct[item.orderStatus]}}</view>
					</view>
				</view>
				<view class="fl-line" v-if="orederList.length - 1 != index">

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getAllUserCreditOrders
	} from '@/api/credit.js'
	import {
		mapGetters
	} from 'vuex'
	export default {
		data() {
			return {
				orederList: [],
				orderStatuDirct: {
					WAIT_PAY: '待支付',
					WAIT_TOPUP: '充值中',
					FAIL_ORDER: '充值失败',
					SUCCESS: "充值成功"
				}
			}
		},
		methods: {
			getAllUsOrders() {
				const data = {
					userid: this.userInfo.phone
				}
				/* getAllUsOrders(data).then(res => {
					res = res.data
					console.log(res)
					if (res.code !== 200) {
						return
					}
					const data = res.data
					data.sort((a, b) => {
						return b.id - a.id
					})
					this.orederList = data
				}) */
				getAllUserCreditOrders(data).then(res => {
					res = res.data
					console.log(res)

					const data = res
					data.sort((a, b) => {
						return b.id - a.id
					})
					this.orederList = data
				})

			},
			dateString(str) {
				let res = ''
				if (!str) {
					return '未知'
				}
				const arr1 = str.split('/')
				const arr2 = arr1[2].split(' ')
				return arr1[0] + '年' + arr1[1] + '月' + arr2[0] + '日 '
			}
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		onLoad() {
			this.getAllUsOrders()
		}
	}
</script>

<style>
	.fl-card {
		background-color: white;
		padding: 20px 10px;
		border-radius: 15px;
	}

	.fl-line {
		height: 1px;
		width: 90%;
		background-color: rgba(0, 0, 0, 0.1);
		margin: 15px auto;
	}

	.order-info>view {
		margin: 5px 0;
	}

	.WAIT_PAY {
		background-color: #ECF5FF;
		color: #4F9FFF;
		border: 1px #4F9FFF solid;
	}

	.WAIT_TOPUP {
		background-color: #FDF6EC;
		color: #E7A849;
		border: 1px solid #E7A849;
	}

	.SUCCESS {
		background-color: #F0F9EB;
		color: #70C44C;
		border: 1px solid #70C44C;
	}

	.FAIL_ORDER {
		background-color: #FEF0F0;
		color: #F79191;
		border: 1px solid #F79191;
	}

	.untreated {
		background-color: #F4F4F5;
		color: #96999E;
		border: 1px solid #96999E;
	}
</style>