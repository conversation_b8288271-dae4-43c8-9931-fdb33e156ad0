import { isLogin, isProxyLogin, checkLoginStatus } from "./auth.js";
import sessionManager from "./sessionManager.js";

/**
 * 检查是否需要登录认证的页面
 */
const AUTH_REQUIRED_PAGES = [
  // 用户端需要登录的页面
  "/pages/my/my",
  "/pages/setUserInfo/setUserInfo",
  "/pages/myOrder/myOrder",
  "/pages/buy/buy",
  "/pages/goodInfo/goodInfo",
  "/pages/flOrder/flOrder",
  "/pages/bindingCarId/bindingCarId",
  "/pages/withdrawal/withdrawal",
  "/pages/credit/credit",
  "/pages/credit_warn/credit_warn",
  "/pages/credit_orders/credit_orders",
  "/pages/selfOrderBuy/selfOrderBuy",
  "/pages/selfOrderBuy/addressList",
  "/pages/selfOrderBuy/editAddress",

  // 代理端需要登录的页面
  "/pages/proxyHome/proxyHome",
  "/pages/proxyIndex/index",
  "/pages/proxySetUserInfo/setUserInfo",
  "/pages/proxyUserList/userList",
  "/pages/proxyOrderList/orderList",
  "/pages/proxyPerformance/performance",
  "/pages/proxyProxyPerformance/proxyPerformance",
  "/pages/proxyDevelopment/development",
  "/pages/proxyUserProtocol/userProtocol",
];

/**
 * 代理端专属页面
 */
const PROXY_ONLY_PAGES = [
  "/pages/proxyHome/proxyHome",
  "/pages/proxyIndex/index",
  "/pages/proxyLogin/proxyLogin",
  "/pages/proxyRegister/register",
  "/pages/proxySetUserInfo/setUserInfo",
  "/pages/proxyUserList/userList",
  "/pages/proxyOrderList/orderList",
  "/pages/proxyPerformance/performance",
  "/pages/proxyProxyPerformance/proxyPerformance",
  "/pages/proxyDevelopment/development",
  "/pages/proxyUserProtocol/userProtocol",
];

/**
 * 用户端专属页面
 */
const USER_ONLY_PAGES = [
  "/pages/my/my",
  "/pages/index/index",
  "/pages/equities/equities",
  "/pages/ETCService/ETCService",
  "/pages/login/login",
  "/pages/setUserInfo/setUserInfo",
  "/pages/service/service",
  "/pages/myOrder/myOrder",
  "/pages/buy/buy",
  "/pages/webPage/webPage",
  "/pages/goodInfo/goodInfo",
  "/pages/flOrder/flOrder",
  "/pages/bindingCarId/bindingCarId",
  "/pages/withdrawal/withdrawal",
  "/pages/credit/credit",
  "/pages/credit_warn/credit_warn",
  "/pages/credit_orders/credit_orders",
  "/pages/goodsfilter/goodsList",
  "/pages/goodsfilter/goodsDetails",
  "/pages/selfOrderBuy/selfOrderBuy",
  "/pages/selfOrderBuy/addressList",
  "/pages/selfOrderBuy/editAddress",
];

/**
 * 公共页面（两端都可以访问）
 */
const PUBLIC_PAGES = [
  "/pages/wecome/wecome",
  "/pages/unifiedLogin/unifiedLogin",
];

/**
 * 路由守卫 - 页面跳转前的权限检查
 * @param {string} url - 要跳转的页面路径
 * @returns {Object} 检查结果 { canAccess: boolean, redirectUrl?: string, message?: string }
 */
export function routeGuard(url) {
  // 移除查询参数，只保留路径
  const path = url.split("?")[0];

  console.log("[RouteGuard] 检查页面访问权限:", path);

  // 检查是否为公共页面
  if (PUBLIC_PAGES.includes(path)) {
    return { canAccess: true };
  }

  // 检查是否需要登录
  const needAuth = AUTH_REQUIRED_PAGES.includes(path);
  const userLoggedIn = isLogin();
  const isProxyUser = isProxyLogin();

  console.log("[RouteGuard] 登录状态:", {
    userLoggedIn,
    isProxyUser,
    needAuth,
  });

  // 需要登录但未登录
  if (needAuth && !userLoggedIn) {
    return {
      canAccess: false,
      redirectUrl: "/pages/unifiedLogin/unifiedLogin",
      message: "请先登录",
    };
  }

  // 代理端用户访问用户端页面
  if (isProxyUser && USER_ONLY_PAGES.includes(path)) {
    return {
      canAccess: false,
      redirectUrl: "/pages/proxyIndex/index",
      message: "代理端用户无法访问用户端页面",
    };
  }

  // 用户端用户访问代理端页面
  if (userLoggedIn && !isProxyUser && PROXY_ONLY_PAGES.includes(path)) {
    return {
      canAccess: false,
      redirectUrl: "/pages/my/my",
      message: "普通用户无法访问代理端页面",
    };
  }

  return { canAccess: true };
}

/**
 * 安全的页面跳转函数 - 带权限检查
 * @param {Object} options - 跳转参数
 * @param {string} options.url - 目标页面路径
 * @param {string} options.method - 跳转方法 (navigateTo|redirectTo|reLaunch|switchTab)
 * @param {Object} options.params - 额外参数
 */
export function safeNavigate(options) {
  const { url, method = "navigateTo", ...params } = options;

  const guardResult = routeGuard(url);

  if (!guardResult.canAccess) {
    // 显示提示信息
    if (guardResult.message) {
      uni.showToast({
        title: guardResult.message,
        icon: "none",
        duration: 2000,
      });
    }

    // 重定向到正确的页面
    if (guardResult.redirectUrl) {
      setTimeout(() => {
        uni.reLaunch({
          url: guardResult.redirectUrl,
        });
      }, 2000);
    }

    return false;
  }

  // 执行跳转
  const navigationMethods = {
    navigateTo: uni.navigateTo,
    redirectTo: uni.redirectTo,
    reLaunch: uni.reLaunch,
    switchTab: uni.switchTab,
  };

  const navigationMethod = navigationMethods[method];
  if (navigationMethod) {
    navigationMethod({
      url,
      ...params,
    });
    return true;
  } else {
    console.error("[RouteGuard] 无效的导航方法:", method);
    return false;
  }
}

/**
 * 检查当前页面是否有访问权限
 * 用于页面的 onLoad 生命周期中
 */
export function checkCurrentPageAccess() {
  const pages = getCurrentPages();
  if (pages.length === 0) return true;

  const currentPage = pages[pages.length - 1];
  const currentPath = "/" + currentPage.route;

  const guardResult = routeGuard(currentPath);

  if (!guardResult.canAccess) {
    console.warn("[RouteGuard] 当前页面无访问权限:", currentPath);

    // 显示提示信息
    if (guardResult.message) {
      uni.showToast({
        title: guardResult.message,
        icon: "none",
        duration: 2000,
      });
    }

    // 重定向到正确的页面
    if (guardResult.redirectUrl) {
      setTimeout(() => {
        uni.reLaunch({
          url: guardResult.redirectUrl,
        });
      }, 2000);
    }

    return false;
  }

  return true;
}

/**
 * 获取默认首页（根据登录状态）
 */
export function getDefaultHomePage() {
  if (!isLogin()) {
    return "/pages/unifiedLogin/unifiedLogin";
  }

  if (isProxyLogin()) {
    return "/pages/proxyIndex/index";
  }

  return "/pages/my/my";
}

/**
 * 登出后的页面重定向
 */
export function redirectAfterLogout() {
  uni.reLaunch({
    url: "/pages/unifiedLogin/unifiedLogin",
  });
}
