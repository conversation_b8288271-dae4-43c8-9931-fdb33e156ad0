// 开发环境配置
export default {
  // API基础URL
  BASE_URL: "http://drtjza50.beesnat.com",

  // 环境标识
  ENV: "development",

  // 是否启用调试
  DEBUG: true,

  // 是否启用VConsole (开发环境可选择启用)
  VCONSOLE_ENABLED: true,

  // VConsole配置
  VCONSOLE_CONFIG: {
    defaultPlugins: ["system", "network", "element", "storage"],
    maxLogNumber: 1000,
    onReady: function () {
      console.log("VConsole 已准备就绪 - 开发环境");
    },
    onClearLog: function () {
      console.log("VConsole 日志已清除");
    },
  },

  // 其他开发环境特定配置
  API_TIMEOUT: 10000,

  // 日志级别
  LOG_LEVEL: "debug",

  // 错误上报配置
  ERROR_REPORT: {
    enabled: false,
    maxErrors: 50,
    reportUrl: "/api/error/report",
  },
};
