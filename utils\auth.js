// token的key
const TOKEN_NAME = "token";
// userid的key
const USER_ID = "user_id";
// appversion的key
const APP_VERSION = "1.0.3";
// 代理端登录状态的key
const PROXY_LOGIN_STATUS = "proxy_login_status";
// 微信session的key
const WX_SESSION_KEY = "wx_session_key";
// 登录时间戳的key
const LOGIN_TIMESTAMP = "login_timestamp";

const setToken = function (data) {
  uni.setStorageSync(TOKEN_NAME, data);
  // 同时记录登录时间戳
  setLoginTimestamp(Date.now());
};

const getToken = function () {
  return uni.getStorageSync(TOKEN_NAME);
};

const removeToken = function () {
  uni.removeStorageSync(TOKEN_NAME);
  removeLoginTimestamp();
};

const setUserId = function (data) {
  uni.setStorageSync(USER_ID, data);
};

const removeUserId = function () {
  uni.removeStorageSync(USER_ID);
};

const getUserId = function () {
  return uni.getStorageSync(USER_ID);
};

// 设置登录时间戳
const setLoginTimestamp = function (timestamp) {
  uni.setStorageSync(LOGIN_TIMESTAMP, timestamp);
};

// 获取登录时间戳
const getLoginTimestamp = function () {
  return uni.getStorageSync(LOGIN_TIMESTAMP) || 0;
};

// 移除登录时间戳
const removeLoginTimestamp = function () {
  uni.removeStorageSync(LOGIN_TIMESTAMP);
};

// 设置微信session key
const setWxSessionKey = function (sessionKey) {
  uni.setStorageSync(WX_SESSION_KEY, sessionKey);
};

// 获取微信session key
const getWxSessionKey = function () {
  return uni.getStorageSync(WX_SESSION_KEY);
};

// 移除微信session key
const removeWxSessionKey = function () {
  uni.removeStorageSync(WX_SESSION_KEY);
};

// 检查登录状态是否过期（基于时间）
const isLoginExpired = function () {
  const loginTime = getLoginTimestamp();
  if (!loginTime) return true;

  // 24小时过期（可配置）
  const expireTime = 24 * 60 * 60 * 1000;
  return Date.now() - loginTime > expireTime;
};

// 检查微信session是否有效
const checkWxSession = function () {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    uni.checkSession({
      success: () => {
        console.log("[Auth] 微信session有效");
        resolve(true);
      },
      fail: () => {
        console.log("[Auth] 微信session已失效");
        resolve(false);
      },
    });
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境，直接返回true
    resolve(true);
    // #endif
  });
};

// 综合检查登录状态
const isLogin = function () {
  const token = getToken();
  return token !== "" && token !== null && token !== undefined;
};

// 深度检查登录状态（包含session检查）
const checkLoginStatus = async function () {
  try {
    // 1. 检查是否有token
    if (!isLogin()) {
      console.log("[Auth] 没有token，未登录");
      return { valid: false, reason: "no_token" };
    }

    // 2. 检查登录是否过期
    if (isLoginExpired()) {
      console.log("[Auth] 登录已过期");
      return { valid: false, reason: "expired" };
    }

    // 3. 检查微信session（仅在微信小程序中）
    // #ifdef MP-WEIXIN
    const wxSessionValid = await checkWxSession();
    if (!wxSessionValid) {
      console.log("[Auth] 微信session失效");
      return { valid: false, reason: "wx_session_expired" };
    }
    // #endif

    console.log("[Auth] 登录状态有效");
    return { valid: true };
  } catch (error) {
    console.error("[Auth] 检查登录状态出错:", error);
    return { valid: false, reason: "check_error" };
  }
};

// 设置代理端登录状态
const setProxyLoginStatus = function (status) {
  uni.setStorageSync(PROXY_LOGIN_STATUS, status);
};

// 获取代理端登录状态
const getProxyLoginStatus = function () {
  return uni.getStorageSync(PROXY_LOGIN_STATUS);
};

// 检查是否为代理端登录
const isProxyLogin = function () {
  return getProxyLoginStatus() === true && isLogin();
};

// 清除代理端登录状态
const clearProxyLoginStatus = function () {
  uni.removeStorageSync(PROXY_LOGIN_STATUS);
};

// 刷新登录状态（更新时间戳）
const refreshLoginStatus = function () {
  if (isLogin()) {
    setLoginTimestamp(Date.now());
    console.log("[Auth] 登录状态已刷新");
  }
};

const resetInfo = function () {
  return uni.clearStorageSync();
};

export {
  setToken,
  getToken,
  removeToken,
  isLogin,
  resetInfo,
  setUserId,
  getUserId,
  removeUserId,
  APP_VERSION,
  setProxyLoginStatus,
  getProxyLoginStatus,
  isProxyLogin,
  clearProxyLoginStatus,
  setLoginTimestamp,
  getLoginTimestamp,
  removeLoginTimestamp,
  setWxSessionKey,
  getWxSessionKey,
  removeWxSessionKey,
  isLoginExpired,
  checkWxSession,
  checkLoginStatus,
  refreshLoginStatus,
};
