<template>
  <view class="set-user-info-container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="page-title">个人信息</view>
    </view>

    <!-- 用户头像区域 -->
    <view class="avatar-section">
      <image
        :src="userInfo.headPic === '' ? defaultHead : userInfo.headPic"
        class="user-avatar"
      ></image>
    </view>

    <!-- 信息编辑区域 -->
    <view class="info-section">
      <view class="info-item">
        <view class="item-label">用户名</view>
        <input
          type="text"
          v-model="userName"
          placeholder="请输入用户名"
          maxlength="20"
          class="custom-input"
        />
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="action-section">
      <button class="save-btn" @click="setUserName">保存修改</button>
      <button class="logout-btn" @click="logout">退出登录</button>
    </view>
  </view>
</template>

<script>
import { showInformations, setUserName } from "@/api/user.js";
import { resetInfo } from "@/utils/auth.js";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      userName: "",
      isSend: false,
      defaultHead:
        "https://elcxt.online:8199/files/javaForARResource/img/default_head.png",
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  methods: {
    getUserInfo() {
      if (this.isSend) {
        return;
      }
      this.isSend = true;
      showInformations()
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          this.userName = res.data.data.userName;
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    logout() {
      uni.showModal({
        title: "退出登录",
        content: "确定要退出登录吗？",
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            resetInfo();
            this.$store.commit("user/DEFAULTUSERINFO");
            uni.reLaunch({
              url: "/pages/unifiedLogin/unifiedLogin",
            });
          }
        },
      });
    },
    setUserName() {
      if (this.userName.length === 0) {
        uni.showToast({
          title: "请输入用户名",
          icon: "error",
        });
        return;
      }
      if (this.isSend) {
        return;
      }
      this.isSend = true;
      const data = { username: this.userName };
      setUserName(data)
        .then(
          (res) => {
            if (res.data.code !== 200) {
              return;
            }
            uni.showToast({
              title: "修改成功",
              icon: "success",
            });
            this.$store.dispatch("user/getUserInfo");
          },
          (e) => {
            console.log(e);
          }
        )
        .finally(() => {
          this.isSend = false;
        });
    },
  },
  onLoad() {
    this.getUserInfo();
  },
  watch: {
    isSend(c) {
      if (c) {
        uni.showLoading({
          title: "加载中",
          mask: true,
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  onHide() {
    uni.hideLoading();
  },
};
</script>

<style lang="scss">
.set-user-info-container {
  min-height: 100vh;
  background: #f5f6fa;
  padding: 0 24px;
}

// 顶部标题
.header {
  padding: 60px 0 40px 0;
  text-align: center;

  .page-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }
}

// 用户头像区域
.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;

  .user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 40px;
    border: 3px solid #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
}

// 信息编辑区域
.info-section {
  margin-bottom: 40px;

  .info-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

    .item-label {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
    }

    .custom-input {
      width: 100%;
      height: 44px;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 0 16px;
      font-size: 16px;
      border: 1px solid #e9ecef;
      box-sizing: border-box;

      &:focus {
        border-color: #007aff;
        background: white;
      }
    }
  }
}

// 操作按钮区域
.action-section {
  display: flex;
  flex-direction: column;
  gap: 16px;

  button {
    height: 48px;
    border-radius: 8px;
    border: none;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .save-btn {
    background: #007aff;
    color: white;

    &:active {
      background: #0056cc;
    }
  }

  .logout-btn {
    background: #ff3b30;
    color: white;

    &:active {
      background: #d70015;
    }
  }
}
</style>
