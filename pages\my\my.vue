<template>
  <view class="bkcolor">
    <!-- 第一行 - 简化的顶部区域 -->
    <view class="my-top-wrap">
      <!-- 顶部功能栏 -->
      <view class="top-header">
        <view class="function-btn" @click="openScan">
          <view class="btn-circle">
            <image
              src="@/static/my_image/scan.png"
              style="width: 16px; height: 16px"
            ></image>
          </view>
        </view>
        <view class="header-title">个人中心</view>
        <view class="placeholder"></view>
      </view>

      <!-- 用户信息卡片 - 简化布局 -->
      <view class="user-info-card">
        <view class="user-avatar-section">
          <image
            :src="userInfo.headPic === '' ? defaultHead : userInfo.headPic"
            class="user-avatar"
            @click="toSetUserInfo"
          ></image>
        </view>

        <view class="user-details" v-if="isLogin">
          <view class="user-name">{{ userInfo.userName }}</view>
          <view class="balance-info">
            <text>余额: ¥{{ userInfo.integral }}</text>
            <view class="withdraw-btn" @click="openToast()">提现</view>
          </view>
        </view>

        <view v-else class="login-section">
          <view class="welcome-text">欢迎使用ETC服务</view>
          <button
            class="login-btn"
            open-type="getPhoneNumber"
            @getphonenumber="getPhoneNumber"
          >
            立即登录
          </button>
        </view>
      </view>
    </view>

    <u-toast ref="uToast"></u-toast>

    <!-- 第二行 - 修复快捷服务样式 -->
    <view class="quick-services">
      <view class="service-item" @click="getOilUrl">
        <view class="service-icon">
          <image src="@/static/my_image/cheer.png" mode="heightFix"></image>
        </view>
        <view class="service-text">加油服务</view>
      </view>
      <view class="service-item" @click="getRlectricityUrl">
        <view class="service-icon">
          <image src="@/static/my_image/charge.png" mode="heightFix"></image>
        </view>
        <view class="service-text">充电服务</view>
      </view>
    </view>

    <!-- 第三行 - 修复功能菜单样式 -->
    <view class="function-menu">
      <view class="menu-title">便民服务</view>
      <view class="menu-grid">
        <!-- 加载状态 -->
        <view v-if="menuLoading" class="menu-loading">
          <text>菜单加载中...</text>
        </view>
        <!-- 菜单项 -->
        <view
          v-else
          v-for="(data, index) in visibleNavTar"
          :key="index"
          class="menu-item"
          @click="doMethod(index, data)"
        >
          <view class="menu-icon">
            <image :src="data.icon" mode="heightFix"></image>
          </view>
          <view class="menu-text">{{ data.text }}</view>
        </view>
      </view>
    </view>

    <!-- 第四行 - 修复产品推荐样式 -->
    <view class="product-section">
      <view class="section-title">ETC产品推荐</view>
      <view class="product-card" v-for="(data, index) in goods" :key="index">
        <view class="product-header">
          <view class="product-info">
            <view class="product-name">{{ data.name }}</view>
            <view class="product-desc">{{ data.msg }}</view>
          </view>
          <view class="action-btn" @click="toBuy(data)">立即办理</view>
        </view>

        <!-- 修复服务权益展示样式 -->
        <view class="benefits-row">
          <view class="benefit-item">
            <view class="benefit-icon">
              <image src="@/static/goodsIcon/ETC.png" mode="widthFix"></image>
            </view>
            <view class="benefit-text">ETC服务</view>
          </view>
          <view class="benefit-item">
            <view class="benefit-icon">
              <image src="@/static/goodsIcon/hospital.png" mode="widthFix"></image>
            </view>
            <view class="benefit-text">ETC商城</view>
          </view>
          <view class="benefit-item">
            <view class="benefit-icon">
              <image src="@/static/goodsIcon/car.png" mode="widthFix"></image>
            </view>
            <view class="benefit-text">加油服务</view>
          </view>
          <view class="benefit-item">
            <view class="benefit-icon">
              <image src="@/static/goodsIcon/sos.png" mode="widthFix"></image>
            </view>
            <view class="benefit-text">极速办理</view>
          </view>
        </view>
      </view>
    </view>

    <view>
      <!-- 提示窗示例 -->
      <uni-popup ref="showBinding" type="dialog">
        <uni-popup-dialog
          :type="msgType"
          cancelText="取消"
          confirmText="绑定"
          title="绑定"
          content="确认绑定该邀请码吗？"
          @confirm="dialogConfirm"
          @close="dialogClose"
        ></uni-popup-dialog>
      </uni-popup>
    </view>

    <!-- 固定联系按钮 -->
    <view class="contact-buttons">
      <view class="contact-item" @click="toPhone()">
        <image src="../../static/phone.png" mode="aspectFill"></image>
      </view>
      <view class="contact-item" @click="towxcustomer">
        <image src="../../static/customer.png" mode="aspectFill"></image>
      </view>
    </view>
  </view>
</template>

<script>
import { isLogin, isProxyLogin } from "@/utils/auth.js";
import { wxlogin } from "@/api/login.js";
import { allcommodity } from "@/api/shop.js";
import { mapGetters } from "vuex";
import { getActivities } from "@/api/sc.js";
import { getOilUrl } from "@/api/nlqf.js";
import {
  ifGivePage,
  updUser,
  showInformations,
  checkUserInvitation,
  getPhoneNoInfo,
  userPhoneLogin,
} from "@/api/user.js";
import { checkCurrentPageAccess } from "@/utils/routeGuard.js";
import { getMenuConfig } from "@/api/menu.js";

import { getLoginInfo } from "@/api/login.js";

export default {
  data() {
    return {
      navTar: [
        {
          text: "发票管理",
          icon: "https://elcxt.online:8199/files/javaForARResource/img/3.png",
          click: () => {
            console.log("点击etc资格查询");
            // 验证是否有权限
            uni.showLoading({
              title: "加载中",
            });
            ifGivePage()
              .then(
                (res) => {
                  uni.hideLoading();
                  res = res.data;
                  if (res.code !== 200) {
                    uni.showToast({
                      icon: "error",
                      title: "暂无开通权益",
                      duration: 1500,
                    });
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
                () => {
                  uni.hideLoading();
                }
              )
              .then((_) => {
                // 跳转小程序
                uni.showModal({
                  title: "提示",
                  content: "是否跳转微信小程序",
                  success: (res) => {
                    if (res.confirm) {
                      console.log("跳转微信小程序");
                      uni.navigateToMiniProgram({
                        // appid  写你要跳转的小程序的 appid
                        appId: "wx1f1ea04b716771be",
                        // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
                        path: "pages/index",
                        extraData: {
                          type: "out",
                        },
                        // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
                        envVersion: "release",
                        success(res) {
                          // 打开成功
                          uni.showToast({
                            title: "跳转成功",
                          });
                        },
                        fail(err) {},
                      });
                    } else if (res.cancel) {
                    }
                  },
                });
              });
          },
        },
        {
          text: "我的订单",
          icon: "https://elcxt.online:8199/files/javaForARResource/img/4.png",
          click: () => {
            uni.navigateTo({
              url: "/pages/myOrder/myOrder",
            });
          },
        },
        {
          text: "话费充值",
          icon: "https://elcxt.online:8199/files/javaForARResource/img/5.png",
          click: () => {
            console.log("点击话费快充");
            uni.navigateTo({
              url: "/pages/credit_warn/credit_warn",
            });
            // uni.showToast({
            // 	title: "敬请期待~",
            // 	icon: 'exception'
            // })
            return;
          },
        },
        {
          text: "ETC办理",
          icon: "https://elcxt.online:8199/files/javaForARResource/img/6.png",
          click: () => {
            // this.toUrl()
            uni.navigateTo({
              url: "/pages/my/etcHandle",
            });
          },
        },

        // {
        // 	text: '优惠洗车',
        // 	icon: 'https://elcxt.online:8199/files/javaForARResource/img/6.png',
        // 	click: () => {
        // 		this.toUrl()
        // 	}
        // },
        // {
        // 	text: '设备升级',
        // 	icon: 'https://elcxt.online:8199/files/javaForARResource/img/6.png',
        // 	click: () => {
        // 		this.toUrl()
        // 	}
        // },
        // {
        // 	text: '快递权益',
        // 	icon: 'https://elcxt.online:8199/files/javaForARResource/img/6.png',
        // 	click: () => {
        // 		this.toUrl()
        // 	}
        // },
        // {
        // 	text: '保险服务',
        // 	icon: 'https://elcxt.online:8199/files/javaForARResource/img/6.png',
        // 	click: () => {
        // 		this.toUrl()
        // 	}
        // },
      ],
      visibleNavTar: [], // 可见的菜单项
      menuLoading: true, // 菜单加载状态
      isLogin: false,
      tabbarHeigth: 0,
      defaultHead: require("@/static/tab/phone.png"),
      goods: [],
      isSend: false,
      weixinShare: null,
      msgType: "success",
      myInvitationCode: "",
      haveInvitationCode: true,
    };
  },
  methods: {
    // 获取菜单配置并过滤显示
    async getMenuConfigAndFilter() {
      try {
        this.menuLoading = true;
        const res = await getMenuConfig();

        if (res.data && res.data.code === 200) {
          const menuConfig = res.data.data || [];

          // 根据接口配置过滤菜单项
          this.visibleNavTar = this.navTar.filter((navItem) => {
            // 查找对应的配置项
            const configItem = menuConfig.find(
              (config) => config.cornName === navItem.text
            );

            // 如果找到对应配置，则根据status决定是否显示
            if (configItem) {
              return configItem.status === 1;
            }

            // 如果没有找到对应配置，默认显示
            return true;
          });

          console.log("菜单配置获取成功", menuConfig);
          console.log("过滤后的菜单", this.visibleNavTar);
        } else {
          // 接口调用失败，显示所有菜单项
          this.visibleNavTar = [...this.navTar];
          console.warn("菜单配置获取失败，显示默认菜单");
        }
      } catch (error) {
        console.error("获取菜单配置出错:", error);
        // 出错时显示所有菜单项
        this.visibleNavTar = [...this.navTar];
        uni.showToast({
          title: "菜单加载失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.menuLoading = false;
      }
    },

    dialogClose() {
      console.log("点击关闭");
    },
    dialogConfirm() {
      console.log("点击确认，开始绑定");
      // 修改用户信息
      let data = {
        invitationCode: this.myInvitationCode,
        id: this.userInfo.id,
      };
      updUser(data)
        .then((res) => {
          console.log("绑定成功");
          uni.showToast({
            icon: "success",
            title: "绑定成功",
          });
          this.haveInvitationCode = false;
        })
        .catch((error) => {
          uni.showToast({
            icon: "error",
            title: "绑定失败",
          });
        });
    },
    dialogInputConfirm(value) {
      console.log(this.userInfo);
      // 验证信息，
      if (value == "" || value == undefined || value == null) {
        return;
      }
      console.log(this.userInfo);
      let data = {
        id: this.userInfo.id,
        carId: value,
      };
      updUser(data).then((res) => {
        console.log(res);
        if (res.data == "updSuccess") {
          // 获取user数据
          showInformations().then((res) => {
            this.userInfo = res.data.data;
          });
        }
      });
      // 提交修改
    },
    showCar() {
      // this.$refs.popup.open()
      // 跳转绑定车牌信息界面
      uni.navigateTo({
        url: "/pages/bindingCarId/bindingCarId",
      });
    },
    toLookCar() {
      uni.navigateTo({
        url:
          "/pages/bindingCarId/bindingCarId?carNumber=" +
          this.userInfo.carId +
          "&name=" +
          this.userInfo.realName +
          "&phone=" +
          this.userInfo.phnumber +
          "&address=" +
          this.userInfo.address,
      });
    },

    // 跳转个人信息
    toSetUserInfo() {
      uni.navigateTo({
        url: "/pages/setUserInfo/setUserInfo",
      });
    },

    // 妈的臭傻逼
    // 打开二维码
    openScan() {
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }
      let that = this;
      uni.scanCode({
        success(res) {
          console.log(res);
          console.log(res.path);
          let url = res.path;
          // 获取携带的数据
          const match = url.match(/scene=(\d+)/);
          if (match) {
            const sceneValue = match[1];
            console.log(sceneValue); // 输出: 123457
            // 开始绑定数据
            let invitation = sceneValue;
            uni.setStorageSync("invitationCode", sceneValue);
            let userId = uni.getStorageSync("userId");
            if (
              invitation == null ||
              invitation == undefined ||
              invitation == ""
            ) {
              console.log("invitation为null");
            } else {
              // 调用接口，获取用户信息，并绑定code
              let useData = {
                userId: userId,
                invitation: invitation,
              };

              console.log("开始发送检查请求");
              checkUserInvitation(useData).then((res) => {
                console.log(res);
                if (res.data.data == "绑定失败，重复绑定") {
                  uni.showToast({
                    icon: "error",
                    title: "重复绑定",
                  });
                } else if (res.data.data == "绑定成功") {
                  uni.showToast({
                    icon: "error",
                    title: "绑定成功",
                  });
                } else {
                  uni.showToast({
                    icon: "error",
                    title: "绑定失败",
                  });
                }
              });
            }
          } else {
            console.log("No match found");
          }
        },
      });
    },
    // 获取商品
    allcommodity() {
      if (!isLogin()) {
        return;
      }
      allcommodity().then((res) => {
        if (res.data.code !== 200) {
          return;
        }
        this.goods = res.data.data;
      });
    },
    // 跳转快递
    toUrl(act_id = "52") {
      console.log(this.userInfo);
      const phone = this.userInfo.phone;
      // if (phone === '') {
      // 	if (isLogin()) {
      // 		uni.showToast({
      // 			title: '获取信息失败',
      // 			icon: 'error'
      // 		})
      // 		this.$store.dispatch("user/getUserInfo")
      // 	} else {
      // 		uni.showToast({
      // 			title: '请登录',
      // 			icon: 'error'
      // 		})
      // 	}
      // 	return
      // }
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }
      let toPath =
        "pages/empty_hair/new_module/select_etc_handle_type/select_etc_handle_type?isNewTrucks=0&shopId=1303036541465796608&vehPlates=" +
        this.userInfo.carId +
        '&extend={"merchantCode":"PPVMZIYXEXGDRCZOG"}';
      uni.navigateToMiniProgram({
        // appid  写你要跳转的小程序的 appid
        appId: "wxddb3eb32425e4a96",
        // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
        path: toPath,
        extraData: {
          type: "out",
        },
        // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
        envVersion: "release",
        success(res) {
          // 打开成功
          uni.showToast({
            title: "跳转成功",
          });
        },
        fail(err) {
          // 打开失败/取消
          // uni.showToast({
          // 	title: '取消跳转',
          // 	icon: 'error'
          // })
        },
      });

      // if (this.isSend) {
      // 	return
      // }
      // this.isSend = true
      // 获取url
      // getActivities({
      // 	phone,
      // 	act_id
      // }).then(res => {
      // 	if (res.data.code !== 200) {
      // 		uni.showToast({
      // 			title: '你没有权限访问',
      // 			icon: 'error'
      // 		})
      // 		return
      // 	}
      // 	const url = res.data.data
      // 	console.log(url)
      // 	// uni.switchTab({
      // 	// 	url: '/pages/my/my',
      // 	// 	success() {
      // 	// 		plus.runtime.openURL(url)
      // 	// 	}
      // 	// })
      // 	uni.navigateTo({
      // 		url: '/pages/webPage/webPage?url=' + url
      // 	})
      // }).finally(() => {
      // 	this.isSend = false
      // })
    },
    // 获取链接
    getOilUrl() {
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }
      uni.navigateToMiniProgram({
        // appid  写你要跳转的小程序的 appid
        appId: "wx1f1ea04b716771be",
        // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
        path: "pages/index",
        extraData: {
          type: "out",
        },
        // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
        envVersion: "release",
        success(res) {
          // 打开成功
          uni.showToast({
            title: "跳转成功",
          });
        },
        fail(err) {
          // 打开失败/取消
          // uni.showToast({
          // 	title: '取消跳转',
          // 	icon: 'error'
          // })
        },
      });

      // this.isSend = true
      // getOilUrl().then((res) => {
      // 	if (res.data.code !== 200) {
      // 		uni.showToast({
      // 			title: '你没有权限访问',
      // 			icon: 'error'
      // 		})
      // 		return
      // 	}
      // 	const data = res.data.data
      // 	// 打开浏览器
      // 	// plus.runtime.openURL(data)
      // 	// 打开内置浏览器
      // 	uni.navigateTo({
      // 		url: '/pages/webPage/webPage?url=' + data
      // 	})
      // }).finally(() => {
      // 	this.isSend = false
      // })
    },
    // 购买商品
    toBuy(goods) {
      console.log(goods);
      uni.navigateTo({
        url: "/pages/bindingCarId/bindingCarId?goods=" + JSON.stringify(goods),
      });
    },
    // 获取电url
    getRlectricityUrl() {
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }
      uni.navigateToMiniProgram({
        // appid  写你要跳转的小程序的 appid
        appId: "wx01440835f1987e8b",
        // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
        path: "pages/index/index",
        extraData: {
          type: "out",
        },
        // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
        envVersion: "release",
        success(res) {
          // 打开成功
          uni.showToast({
            title: "跳转成功",
          });
        },
        fail(err) {
          // 打开失败/取消
          // uni.showToast({
          // 	title: '跳转不成功'
          // })
        },
      });
    },
    //执行方法
    doMethod(i, menuItem) {
      // 如果传入了menuItem，直接调用其click方法
      if (menuItem && menuItem.click && typeof menuItem.click === "function") {
        menuItem.click();
        return;
      }

      // 兼容旧的索引方式（保留原有逻辑）
      if (i == 0) {
        if (!isLogin()) {
          uni.showToast({
            duration: 1000,
            title: "请先登录",
            icon: "error",
          });
          return;
        }
        uni.navigateToMiniProgram({
          // appid  写你要跳转的小程序的 appid
          appId: "wx9040bb0d3f910004",
          // 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
          path: "pages/index",
          extraData: {
            type: "out",
          },
          // 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
          envVersion: "release",
          success(res) {
            // 打开成功
            uni.showToast({
              title: "跳转成功",
            });
          },
          fail(err) {
            // 打开失败/取消
            // uni.showToast({
            // 	title: '取消跳转',
            // 	icon: 'error'
            // })
          },
        });
      } else if (i == 1) {
        uni.navigateTo({
          url: "/pages/myOrder/myOrder",
        });
      } else if (i == 2) {
        // 验证是否有权限
        console.log("点击话费快充");
        uni.navigateTo({
          url: "/pages/credit_warn/credit_warn",
        });
        return;
      } else if (i == 3) {
        // this.toUrl()
        uni.navigateTo({
          url: "/pages/my/etcHandle?carId=" + this.userInfo.carId,
        });
      } else {
        uni.showToast({
          title: "暂未开放",
          icon: "error",
        });
      }
    },
    // 微信获取用户信息
    /* getUserInfoByWx() {
				let that = this
				wx.getUserInfo({
					success: function(res) {
						console.log(res)
						that.jm(res)
					}
				})
			}, */

    // 解密
    /* jm(res) {
				var WXBizDataCrypt = require('./WXBizDataCrypt')
				// 通过服务端获取sessionkey
				// let d = {
				// 	phone: this.userInfo.phone
				// }
				// getLoginInfo(d).then(res => {
				// 	console.log(res)
				// })
				var appId = 'wx5445ea5d582832c2'
				var sessionKey = '11'
				var encryptedData = res.encryptedData
				var iv = res.iv

				var pc = new WXBizDataCrypt(appId, sessionKey)

				var data = pc.decryptData(encryptedData, iv)

				console.log('解密后 data: ', data)
			}, */
    openToast() {
      this.$refs.uToast.show({
        title: "登录成功",
        message: "您尚未达到36个使用月，暂时无法领取",
        type: "default",
      });
    },
    getPhoneNumber(e) {
      let that = this;
      console.log("获取手机号事件:", e);

      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        uni.showToast({
          title: "获取手机号失败",
          icon: "none",
        });
        return;
      }

      // 显示加载中
      uni.showLoading({
        title: "登录中",
        mask: true,
      });

      // 直接使用微信登录进行授权
      uni.login({
        provider: "weixin",
        success: function (loginRes) {
          console.log("微信登录成功:", loginRes);

          // 准备获取手机号的参数
          const params = {
            code: e.detail.code, // 手机号获取的code
            loginCode: loginRes.code, // 登录的code
            encryptedData: e.detail.encryptedData,
            iv: e.detail.iv,
          };

          // 获取手机号
          getPhoneNoInfo(params)
            .then((res) => {
              console.log("获取手机号响应:", res);
              if (!res.data || res.data.code !== 200) {
                throw new Error(res.data?.message || "获取手机号失败");
              }

              // 如果后端返回的是加密的手机号数据
              if (
                typeof res.data.data === "object" &&
                res.data.data.phoneNumber
              ) {
                that.phone = res.data.data.phoneNumber;
              }
              // 如果后端直接返回手机号
              else if (
                typeof res.data.data === "string" &&
                res.data.data !== "null"
              ) {
                that.phone = res.data.data;
              } else {
                throw new Error("未能获取到有效的手机号");
              }

              console.log("获取到手机号:", that.phone);

              // 使用手机号登录
              return userPhoneLogin({
                phone: that.phone,
                loginCode: loginRes.code, // 添加登录code
              });
            })
            .then((res) => {
              console.log("手机号登录响应:", res);
              if (!res.data || res.data.code !== 200) {
                throw new Error(res.data?.message || "登录失败");
              }

              if (!res.data.data?.token) {
                throw new Error("登录返回数据格式错误");
              }

              // 保存登录信息
              uni.setStorageSync("token", res.data.data.token);
              uni.setStorageSync("userId", res.data.data.userId);
              uni.setStorageSync("phone", res.data.data.phone);

              // 检查邀请码
              const invitation = uni.getStorageSync("invitationCode");
              if (invitation) {
                return checkUserInvitation({
                  userId: res.data.data.userId,
                  invitation: invitation,
                });
              }
            })
            .then((invitationRes) => {
              if (invitationRes) {
                console.log("邀请码处理结果:", invitationRes);
              }

              // 刷新用户信息
              that.$store.dispatch("user/getUserInfo");
              that.isLogin = true;

              uni.showToast({
                title: "登录成功",
                icon: "success",
              });

              // 刷新页面
              uni.reLaunch({
                url: "/pages/my/my",
              });
            })
            .catch((err) => {
              console.error("登录错误:", err);
              uni.showToast({
                title: err.message || "登录失败",
                icon: "error",
                duration: 2000,
              });
            })
            .finally(() => {
              uni.hideLoading();
            });
        },
        fail: function (err) {
          console.error("微信登录失败:", err);
          uni.hideLoading();
          uni.showToast({
            title: "微信登录失败",
            icon: "error",
          });
        },
      });
    },

    // 获取用户openId
    toLogin(id) {
      let that = this;
      uni.login({
        provider: "weixin",
        onlyAuthorize: true, // 微信登录仅请求授权认证
        success: function (event) {
          const { code } = event;
          console.log(event);
          //客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
          let data = {
            code: event.code,
            id: id,
          };
          wxlogin(data).then((res) => {
            console.log("登录返回信息");
            console.log(res);
          });
        },
        fail: function (err) {
          console.log(err);
          // 登录授权失败
          // err.code是错误码
        },
      });
    },
    toPhone() {
      uni.makePhoneCall({
        phoneNumber: "**********", //要拨打的手机号
        success: (res) => {
          console.log("跳转通话");
        },
        fail: (res) => {
          console.log("取消通话");
        },
      });
    },
    towxcustomer() {
      try {
        wx.openCustomerServiceChat({
          extInfo: {
            url: "https://work.weixin.qq.com/kfid/kfce70043c37d280aec", //客服ID
          },
          corpId: "wwdacecfcf786c160e", //企业微信ID
          success(res) {},
        });
      } catch (error) {
        showToast("请更新至微信最新版本");
      }
    },
  },
  onLoad(options) {
    // 检查页面访问权限
    if (!checkCurrentPageAccess()) {
      return; // 如果无权限访问，路由守卫会自动重定向
    }

    // 检查登录状态（确保不是代理端用户）
    if (isProxyLogin()) {
      uni.showToast({
        title: "代理端用户请使用代理端页面",
        icon: "none",
        duration: 2000,
      });
      setTimeout(() => {
        uni.reLaunch({
          url: "/pages/proxyIndex/index",
        });
      }, 2000);
      return;
    }

    if (uni.getStorageSync("token") == "") {
      this.isLogin = false;
    } else {
      this.isLogin = true;
    }

    // 只有在已登录状态下才获取用户信息
    if (this.isLogin) {
      showInformations().then((res) => {
        if (res.data.code == 841) {
          this.isLogin = false;
        }
      });
      // 获取个人信息
      this.$store.dispatch("user/getUserInfo");
    }

    console.log(options);
    this.tabbarHeigth = uni.getSystemInfoSync().statusBarHeight;
    uni.showLoading({
      title: "加载中",
    });

    // 获取菜单配置
    this.getMenuConfigAndFilter();

    this.allcommodity();

    console.log("App Launch");
    uni.hideLoading();

    // 获取微信分享对象
    // plus.share.getServices(services => {
    // 	for (let i in services) {
    // 		if (services[i].id === 'weixin') {
    // 			this.weixinShare = services[i]
    // 			break
    // 		}
    // 	}
    // })
  },
  onShow() {
    if (uni.getStorageSync("token") == "") {
      this.isLogin = false;
    } else {
      this.isLogin = true;
    }
    if (uni.getStorageSync("pay_type_info") == 1) {
      uni.removeStorageSync("pay_type_info");
      uni.showToast({
        title: "话费充值成功",
      });
    }
    // 只有在已登录状态下才刷新用户信息
    if (this.isLogin) {
      this.$store.dispatch("user/getUserInfo");
    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
    styleObject() {
      return {
        position: "absolute",
        top: `${this.tabbarHeight + 20}px`,
        right: "20px",
      };
    },
  },
  watch: {
    isSend(c) {
      if (c) {
        uni.showLoading({
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },
};
</script>

<style lang="scss">
@import "@/styles/design-system.scss";

.bkcolor {
  background: #f5f6fa;
  min-height: 100vh;
}

// 简化的顶部样式
.my-top-wrap {
  background: linear-gradient(135deg, #4a69bd 0%, #6c5ce7 100%);
  padding-top: 50px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  padding-bottom: 20px;
}

.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px 15px;

  .function-btn {
    .btn-circle {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .header-title {
    color: white;
    font-size: 16px;
    font-weight: 600;
  }

  .placeholder {
    width: 36px;
  }
}

// 简化的用户信息卡片
.user-info-card {
  background: white;
  margin: 0 20px;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;

  .user-avatar-section {
    margin-right: 15px;

    .user-avatar {
      width: 60px;
      height: 60px;
      border-radius: 15px;
      border: 2px solid #4a69bd;
    }
  }

  .user-details {
    flex: 1;

    .user-name {
      font-size: 18px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 8px;
    }

    .balance-info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      text {
        color: #7f8c8d;
        font-size: 14px;
      }

      .withdraw-btn {
        background: #4a69bd;
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 12px;
      }
    }
  }

  .login-section {
    flex: 1;
    text-align: center;

    .welcome-text {
      font-size: 14px;
      color: #7f8c8d;
      margin-bottom: 15px;
    }

    .login-btn {
      background: #4a69bd;
      color: white;
      border: none;
      border-radius: 20px;
      padding: 10px 30px;
      font-size: 14px;
    }
  }
}

// 修复快捷服务样式
.quick-services {
  padding: 20px;
  display: flex;
  gap: 15px;

  .service-item {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 24px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    min-height: 80px;

    .service-icon {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 32px;
        height: 32px;
      }
    }

    .service-text {
      font-size: 14px;
      color: #2c3e50;
      font-weight: 500;
      text-align: center;
      line-height: 1.2;
    }
  }
}

// 修复功能菜单样式
.function-menu {
  margin: 0 20px 20px;

  .menu-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
  }

  .menu-grid {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;

    .menu-loading {
      grid-column: 1 / -1;
      text-align: center;
      padding: 30px 0;
      color: #7f8c8d;
      font-size: 14px;
    }

    .menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: 8px 4px;

      .menu-icon {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;

        image {
          width: 32px;
          height: 32px;
          object-fit: contain;
        }
      }

      .menu-text {
        font-size: 12px;
        color: #2c3e50;
        font-weight: 500;
        line-height: 1.2;
        word-break: keep-all;
        white-space: nowrap;
      }
    }
  }
}

// 修复产品推荐样式
.product-section {
  margin: 0 20px 100px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
  }

  .product-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

    .product-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .product-info {
        flex: 1;

        .product-name {
          font-size: 16px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 5px;
        }

        .product-desc {
          font-size: 13px;
          color: #7f8c8d;
        }
      }

      .action-btn {
        background: #4a69bd;
        color: white;
        padding: 8px 16px;
        border-radius: 15px;
        font-size: 13px;
      }
    }

    .benefits-row {
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding-top: 15px;
      border-top: 1px solid #f0f0f0;

      .benefit-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        flex: 1;
        padding: 8px 4px;

        .benefit-icon {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;

          image {
            width: 28px;
            height: 28px;
            object-fit: contain;
          }
        }

        .benefit-text {
          font-size: 11px;
          color: #7f8c8d;
          font-weight: 400;
          line-height: 1.2;
          word-break: keep-all;
          white-space: nowrap;
        }
      }
    }
  }
}

// 简化的联系按钮
.contact-buttons {
  position: fixed;
  right: 15px;
  bottom: 20vh;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .contact-item {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
          font-size: 14px;
          font-weight: 600;
          box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }
      }
    }

    .benefits-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 16px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;

      .benefit-item {
        text-align: center;

        .benefit-icon {
          width: 40px;
          height: 40px;
          background: #f8f9fa;
          border-radius: 10px;
          margin: 0 auto 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          image {
            width: 24px;
            height: 24px;
          }
        }

        .benefit-text {
          font-size: 11px;
          color: #7f8c8d;
        }
      }
    }
  }
}

// 联系按钮
.contact-buttons {
  position: fixed;
  right: 20px;
  bottom: 25vh;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .contact-item {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
