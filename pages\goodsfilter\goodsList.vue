<template>
  <view class="container">
    <view class="header">
      <view class="search-container">
        <uni-search-bar
          placeholder="请输入关键字"
          bgColor="#EEEEEE"
          @confirm="search"
          radius="100"
          style="width: 80%"
          cancelButton="none"
          v-model="searchKeyword"
          @click="goToSearch"
        />
        <view class="search-button" @click="handleSearch">
          <button>搜索</button>
        </view>
      </view>
      <!-- 标题栏 -->
      <view class="tabs">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ active: activeTab === index }"
          @click="switchTab(index)"
        >
          {{ tab.title }}

          <!-- 右侧箭头操作区 -->
          <!-- <view class="arrow-container" v-if="activeTab === 1 && index === activeTab">
						
						<text class="arrow" :class="{ active: sortOrder === 'asc' }">
							▲
						</text>

						
						<text class="arrow" :class="{ active: sortOrder === 'desc' }" style="  
				display: inline-block;
				transform: rotateX(180deg);">
							▲
						</text>
					</view> -->
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <!-- 综合 -->
    <scroll-view scroll-y class="content-scroll" v-if="activeTab === 0">
      <view
        class="product-section"
        v-for="(product, index) in products"
        :key="index"
        @click="goToDetails(product.id)"
      >
        <!-- 商品图片 -->
        <image
          class="product-image"
          :src="getFirstImage(product.goodsCover)"
          mode="aspectFit"
        />

        <!-- 商品信息 -->
        <view class="product-info">
          <text class="product-title">{{ product.goodsName }}</text>

          <view class="product-bought">
            <!-- 价格标签 -->
            <view class="price-tag">
              <text class="price">￥{{ product.goodsTotalPrice }}</text>
            </view>

            <!-- 购物车按钮 -->
            <!-- <view class="cart-btn" @click.stop="addToCart">
							<uni-icons type="cart" size="30" color="#ff5490"></uni-icons>
						</view> -->
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 价格排序 -->
    <scroll-view scroll-y class="content-scroll" v-if="activeTab === 1">
      <view
        class="product-section"
        v-for="(product, index) in sortedProducts"
        :key="index"
        @click="goToDetails(product.id)"
      >
        <!-- 商品图片 -->
        <image
          class="product-image"
          :src="getFirstImage(product.goodsCover)"
          mode="aspectFit"
        />

        <!-- 商品信息 -->
        <view class="product-info">
          <text class="product-title">{{ product.goodsName }}</text>

          <view class="product-bought">
            <!-- 价格标签 -->
            <view class="price-tag">
              <text class="price">￥{{ product.goodsTotalPrice }}</text>
            </view>

            <!-- 购物车按钮 -->
            <!-- <view class="cart-btn" @click.stop="addToCart">
							<uni-icons type="cart" size="30" color="#ff5490"></uni-icons>
						</view> -->
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import {
  getGoodsList,
  queryGoodsByName,
  getAllGoodsList,
  getGoodsDetail,
} from "@/api/goods";
export default {
  data() {
    return {
      activeTab: 0, // 默认显示第一个标签
      tabs: [
        {
          title: "综合",
        },
        {
          title: "价格",
        },
      ],
      activeArrow: "up", // 记录当前激活的箭头
      sortOrder: "asc", // 默认升序排序
      clickCount: 0, // 点击次数
      searchKeyword: "", // 搜索关键词
      // 添加默认测试数据，如果接口正常将被覆盖
      products: [],
    };
  },
  created() {
    this.loadProducts();
  },
  methods: {
    // 获取第一张图片作为封面
    getFirstImage(imageStr) {
      if (!imageStr) return "";
      // 将逗号分隔的图片字符串拆分，返回第一张图片URL
      const images = imageStr.split(",");
      return images[0] || "";
    },
    // 加载商品数据
    loadProducts() {
      uni.showLoading({
        title: "加载中...",
      });

      // 根据当前激活的标签选择不同的处理逻辑
      if (this.activeTab === 0) {
        // 综合接口不传参数
        let data = {
          logicalDel: 0,
        };
        getGoodsDetail(data)
          .then((res) => {
            uni.hideLoading();
            console.log("综合接口返回数据:", res);

            if (res.data) {
              // 综合接口直接返回数组
              if (Array.isArray(res.data)) {
                this.products = res.data;
                console.log("处理后的商品列表:", this.products);
              } else {
                this.products = [];
                console.error("综合接口返回数据格式不正确");
                uni.showToast({
                  title: "数据格式错误",
                  icon: "none",
                });
              }
            } else {
              uni.showToast({
                title: "数据加载失败",
                icon: "none",
              });
            }
          })
          .catch(this.handleError);
      } else {
        // 价格接口需要传sortOrder参数
        const params = {
          sortOrder: this.sortOrder,
        };
        getGoodsList(params)
          .then((res) => {
            uni.hideLoading();
            console.log("价格接口返回数据:", res);

            if (res.data && res.data.code === 200) {
              // 价格接口返回嵌套结构
              if (res.data.data && Array.isArray(res.data.data)) {
                this.products = res.data.data;
                console.log("处理后的商品列表:", this.products);
              } else {
                this.products = [];
                console.error("接口返回数据格式不正确");
                uni.showToast({
                  title: "数据格式错误",
                  icon: "none",
                });
              }
            } else {
              uni.showToast({
                title: "数据加载失败",
                icon: "none",
              });
            }
          })
          .catch(this.handleError);
      }
    },

    // 处理接口错误
    handleError(err) {
      console.error("请求失败:", err);
      uni.hideLoading();
      uni.showToast({
        title: "网络连接失败",
        icon: "none",
      });
    },
    // 切换标签
    switchTab(index) {
      this.activeTab = index;
      // 切换标签后重新加载数据
      this.loadProducts();
    },
    // 去商品详情
    goToDetails(id) {
      uni.navigateTo({
        url: `/pages/goodsfilter/goodsDetails?id=${id}`,
      });
    },
    // 去搜索页面
    goToSearch() {
      console.log("聚焦搜索框");
    },
    // 搜索确认
    search(e) {
      if (!e.value || e.value.trim() === "") {
        return;
      }

      uni.showLoading({
        title: "搜索中...",
      });

      // 调用queryGoodsByName方法搜索商品
      queryGoodsByName({
        goodsName: e.value,
      })
        .then((res) => {
          uni.hideLoading();
          console.log("搜索结果:", res);

          if (res.data && res.data.code === 200) {
            // 根据API返回结构获取数据
            if (res.data.data && Array.isArray(res.data.data)) {
              this.products = res.data.data;
            } else if (
              res.data.data &&
              res.data.data.list &&
              Array.isArray(res.data.data.list)
            ) {
              this.products = res.data.data.list;
            } else {
              this.products = [];
              uni.showToast({
                title: "未找到相关商品",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: "搜索失败",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.error("搜索请求失败:", err);
          uni.hideLoading();
          uni.showToast({
            title: "网络连接失败",
            icon: "none",
          });
        });
    },
    // 添加到购物车
    addToCart() {
      uni.showToast({
        title: "已加入购物车",
        icon: "success",
      });
    },
    // 箭头点击处理
    handleArrowClick(direction) {
      // this.activeArrow = direction

      // 修改排序逻辑：在asc和desc之间交替
      if (this.sortOrder === "asc") {
        this.sortOrder = "desc";
      } else {
        this.sortOrder = "asc";
      }

      // 重新加载数据
      // this.loadProducts()
    },
    // 处理搜索按钮点击
    handleSearch() {
      if (!this.searchKeyword || this.searchKeyword.trim() === "") {
        // 查所有
        this.loadProducts();
        return;
      }

      // 调用搜索方法
      this.search({
        value: this.searchKeyword,
      });
    },
  },
  computed: {
    // 计算属性：处理价格排序
    sortedProducts() {
      return Array.isArray(this.products) ? this.products : [];
    },
  },
};
</script>

<style>
.container {
  height: 100vh;
  /* 必须设置容器高度 */
  display: flex;
  flex-direction: column;
}

.header {
  align-items: center;
  z-index: 10;
  /* 确保在内容上方 */
}

.tabs {
  display: flex;
  border-bottom: 2rpx solid #eee;
}

.tab-item {
  text-align: center;
  width: 50%;
  padding: 20rpx 40rpx;
  margin-right: 30rpx;
  position: relative;
  font-size: 27rpx;
}

.tab-item.active {
  color: #ff5490;
  font-weight: bold;
  display: inline-flex;
  justify-content: center;
}

.content {
  padding: 20rpx;
}

.content-scroll {
  flex: 1;
  /* 占据剩余空间 */
  overflow: hidden;
}

.product-list {
  min-height: 101%;
  /* 修复滚动区域高度问题 */
}

.content-item {
  padding: 30rpx;
  min-height: 200rpx;
  line-height: 1.6;
}

.container {
  background: #fff;
  padding: 20rpx;
}

/* 商品展示区样式 */
.product-section {
  display: flex;
  padding: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  background: #fff;
}

.product-image {
  width: 240rpx;
  height: 240rpx;
  margin-right: 30rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

.product-bought {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.price-tag {
  display: flex;
  align-items: baseline;
  margin-bottom: 30rpx;
}

.price {
  font-size: 35rpx;
  color: #ff5490;
  font-weight: 500;
  margin-right: 10rpx;
}

.cart-btn {
  float: right;
}

.arrow-container {
  display: flex;
  flex-direction: column;
  float: right;
  margin: -10rpx 0 0 10rpx;
}

.arrow {
  font-size: 10rpx;
  color: #666;
  line-height: 2;
  padding-top: 12rpx;
  transition: all 0.3s;
}

.arrow.active {
  color: #ff5490;
  /* transform: scale(1.2); */
}

.arrow:active {
  opacity: 0.7;
}

.search-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10rpx;
}

.search-container :deep(.uni-searchbar) {
  flex: 1;
}

.search-button {
  margin-left: 15rpx;
  background-color: #ff5490;
  border-radius: 50%;
}

.search-button button {
  background-color: #ff5490;
  width: 120rpx;
  height: 70rpx;
  text-align: center;
  color: white;
  font-size: 30rpx;
  font-weight: 600;
}
</style>
