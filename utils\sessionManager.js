import {
  checkLoginStatus,
  refreshLoginStatus,
  removeToken,
  removeUserId,
  clearProxyLoginStatus,
  removeWxSessionKey,
} from "./auth.js";
import { logout } from "./logout.js";
import { getDefaultHomePage } from "./routeGuard.js";
import store from "@/store";

/**
 * 登录状态管理器
 * 负责登录状态的检查、保活和自动恢复
 */
class SessionManager {
  constructor() {
    this.checkInterval = null;
    this.isChecking = false;
    this.lastCheckTime = 0;
    this.checkFrequency = 5 * 60 * 1000; // 5分钟检查一次
    this.retryCount = 0;
    this.maxRetries = 3;
  }

  /**
   * 初始化session管理器
   */
  init() {
    console.log("[SessionManager] 初始化登录状态管理器");

    // 在应用启动时检查登录状态
    this.checkAndRestoreLoginStatus();

    // 启动定时检查
    this.startPeriodicCheck();

    // 监听页面显示事件，用于刷新登录状态
    this.setupPageVisibilityListener();

    // 监听应用前后台切换
    this.setupAppStateListener();
  }

  /**
   * 检查并恢复登录状态
   */
  async checkAndRestoreLoginStatus() {
    if (this.isChecking) return;

    this.isChecking = true;
    console.log("[SessionManager] 开始检查登录状态");

    try {
      const loginStatus = await checkLoginStatus();

      if (loginStatus.valid) {
        console.log("[SessionManager] 登录状态有效，刷新时间戳");
        refreshLoginStatus();

        // 恢复用户信息到store
        await this.restoreUserInfo();

        this.retryCount = 0; // 重置重试次数
      } else {
        console.log("[SessionManager] 登录状态无效:", loginStatus.reason);
        await this.handleInvalidLogin(loginStatus.reason);
      }
    } catch (error) {
      console.error("[SessionManager] 检查登录状态时出错:", error);

      // 重试机制
      if (this.retryCount < this.maxRetries) {
        this.retryCount++;
        console.log(
          `[SessionManager] 重试检查登录状态 (${this.retryCount}/${this.maxRetries})`
        );
        setTimeout(() => {
          this.checkAndRestoreLoginStatus();
        }, 2000 * this.retryCount); // 递增重试间隔
      }
    } finally {
      this.isChecking = false;
      this.lastCheckTime = Date.now();
    }
  }

  /**
   * 恢复用户信息到store
   */
  async restoreUserInfo() {
    try {
      // 恢复用户端用户信息
      if (store.state.user && store.dispatch) {
        try {
          await store.dispatch("user/getUserInfo");
        } catch (error) {
          console.log(
            "[SessionManager] 恢复用户端用户信息失败:",
            error.message
          );
          // 如果是登录状态无效，不需要抛出错误，让后续逻辑处理
          if (error.message === "登录状态无效") {
            console.log("[SessionManager] 用户端登录状态无效，将清除登录数据");
          }
        }
      }

      // 恢复代理端用户信息
      if (store.state.proxyUser && store.dispatch) {
        try {
          await store.dispatch("proxyUser/getUserInfo");
        } catch (error) {
          console.log(
            "[SessionManager] 恢复代理端用户信息失败:",
            error.message
          );
          // 如果是登录状态无效，不需要抛出错误，让后续逻辑处理
          if (error.message === "登录状态无效") {
            console.log("[SessionManager] 代理端登录状态无效，将清除登录数据");
          }
        }
      }

      console.log("[SessionManager] 用户信息恢复尝试完成");
    } catch (error) {
      console.error("[SessionManager] 恢复用户信息过程出错:", error);
    }
  }

  /**
   * 处理无效登录状态
   */
  async handleInvalidLogin(reason) {
    console.log(`[SessionManager] 处理无效登录: ${reason}`);

    switch (reason) {
      case "wx_session_expired":
        // 微信session过期，需要重新登录
        await this.handleWxSessionExpired();
        break;

      case "expired":
        // 登录过期
        console.log("[SessionManager] 登录已过期，清除登录状态");
        this.clearLoginData();
        break;

      case "no_token":
        // 没有token，可能是首次使用
        console.log("[SessionManager] 未找到登录令牌");
        break;

      default:
        // 其他错误，清除登录状态
        console.log("[SessionManager] 登录状态检查出错，清除登录状态");
        this.clearLoginData();
        break;
    }
  }

  /**
   * 处理微信session过期
   */
  async handleWxSessionExpired() {
    console.log("[SessionManager] 处理微信session过期");

    // #ifdef MP-WEIXIN
    try {
      // 重新登录获取新的session
      const loginRes = await uni.login();
      console.log(
        "[SessionManager] 重新登录成功，获取新的code:",
        loginRes.code
      );

      // 这里需要调用您的后端接口，用新的code换取新的token
      // 暂时先刷新登录状态
      refreshLoginStatus();

      // TODO: 调用后端接口更新session
      // await this.updateSessionWithNewCode(loginRes.code);
    } catch (error) {
      console.error("[SessionManager] 重新登录失败:", error);
      this.clearLoginData();
    }
    // #endif
  }

  /**
   * 清除登录数据
   */
  clearLoginData() {
    removeToken();
    removeUserId();
    clearProxyLoginStatus();
    removeWxSessionKey();

    // 清除store中的用户信息
    if (store.state.user) {
      store.commit("user/DEFAULTUSERINFO");
    }
    if (store.state.proxyUser) {
      store.commit("proxyUser/DEFAULTUSERINFO");
    }

    console.log("[SessionManager] 已清除所有登录数据");
  }

  /**
   * 启动定时检查
   */
  startPeriodicCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(() => {
      // 避免频繁检查
      if (Date.now() - this.lastCheckTime < this.checkFrequency) {
        return;
      }

      console.log("[SessionManager] 定时检查登录状态");
      this.checkAndRestoreLoginStatus();
    }, this.checkFrequency);

    console.log(
      "[SessionManager] 已启动定时检查，间隔:",
      this.checkFrequency / 1000,
      "秒"
    );
  }

  /**
   * 停止定时检查
   */
  stopPeriodicCheck() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log("[SessionManager] 已停止定时检查");
    }
  }

  /**
   * 设置页面可见性监听
   */
  setupPageVisibilityListener() {
    // #ifdef H5
    if (typeof document !== "undefined") {
      document.addEventListener("visibilitychange", () => {
        if (!document.hidden) {
          console.log("[SessionManager] 页面重新可见，检查登录状态");
          this.checkAndRestoreLoginStatus();
        }
      });
    }
    // #endif
  }

  /**
   * 设置应用状态监听
   */
  setupAppStateListener() {
    // #ifdef MP-WEIXIN
    // 监听小程序显示
    wx.onAppShow(() => {
      console.log("[SessionManager] 小程序重新显示，检查登录状态");
      this.checkAndRestoreLoginStatus();
    });
    // #endif
  }

  /**
   * 手动刷新登录状态
   */
  async refresh() {
    console.log("[SessionManager] 手动刷新登录状态");
    await this.checkAndRestoreLoginStatus();
  }

  /**
   * 销毁session管理器
   */
  destroy() {
    this.stopPeriodicCheck();
    console.log("[SessionManager] Session管理器已销毁");
  }
}

// 创建全局实例
const sessionManager = new SessionManager();

export default sessionManager;
