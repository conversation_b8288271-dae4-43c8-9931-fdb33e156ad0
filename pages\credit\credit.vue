<template>
	<view class="container">
		<view class="box">
			<view class="contact">
				<view class="contact_font mobile_title">联系电话：</view>
				<u--input v-if="!loadPage" v-model="phone" type="number" :maxlength="11" placeholder="请输入手机号"
					border="none"></u--input>
			</view>
		</view>
		<view class="box" style="margin-top: 20rpx;">
			<view class="contact">
				<view class="contact_font mobile_title">真实姓名：</view>
				<u--input v-if="!loadPage" v-model="name" type="text" :maxlength="11" placeholder="请输入手机号绑定姓名"
					border="none"></u--input>
			</view>
		</view>

		<view class="box" style="margin-top: 20rpx;">
			<view class="credit_title">
				移动联通话费充值
			</view>
			<view class="credit_package">
				<view class="single_package" v-for="(data,index) in credit_package.creditGoods1" :key="index">
					<image :src="data.image" mode="aspectFit"
						:class="[data.id == active_credit.id ? 'active_package_image' : 'default_package_image']"
						@click="getActiveCreditInfo(data,index)">
					</image>
				</view>
			</view>
		</view>

		<view class="box" style="margin-top: 20rpx;">
			<view class="credit_title">
				电信充值通道专用
			</view>
			<view class="credit_package">
				<view class="single_package" v-for="(data,index) in credit_package.creditGoods2" :key="index">
					<image :src="data.image" mode="aspectFit"
						:class="[data.id == active_credit.id  ? 'active_package_image' : 'default_package_image']"
						@click="getActiveCreditInfo(data,index)">
					</image>
				</view>
			</view>
		</view>

		<view class="box" style="margin-top: 20rpx;">
			<view class="credit_title">
				套餐详情
			</view>

			<view class="details_header">
				<image :src="active_credit.image" mode="aspectFit" class="details_image"
					@click="clickImg(active_credit.image)"></image>
				<view class="other_details">
					<view class="credit_title">
						{{active_credit.title}}
					</view>
					<view class="details_msg">
						规格：{{active_credit.facePrice}}
					</view>
					<view class="details_price">
						￥{{active_credit.userPay}}
					</view>
				</view>
			</view>

			<view class="details_single" style="flex-direction: column;align-items: start;">
				<view class="details_title">
					配送方式
				</view>
				<view
					style="padding: 10rpx 20rpx; background-color: #fadbd6; border-radius: 56rpx; width: auto;margin-top: 20rpx;color: #f9554d;font-size: 24rpx;">
					自动发货
				</view>
			</view>
			<view class="details_single">
				<view class="details_title">
					商品金额
				</view>
				<view style="font-weight: 700;">
					￥{{active_credit.userPay}}
				</view>
			</view>
			<view class="details_single">
				<view class="details_title">
					运费
				</view>
				<view style="font-weight: 700;">
					+￥0
				</view>
			</view>
			<view class="details_single">
				<view class="details_title">
					抵扣券
				</view>
				<view style="color: #9d9d9d;">
					无可用抵扣券
				</view>
			</view>
		</view>

		<view class="box" style="margin-top: 20rpx; margin-bottom: 10vh;">
			<view style="letter-spacing: 1px;">
				余额可抵
				<span
					style="color: #ed6b69; font-size: 40rpx;font-weight: 700; margin: 0 2px;">{{active_credit.integral}}</span>
				元
			</view>
		</view>

		<!--  底部 -->
		<view style="position: fixed; bottom: 0; width: 100vw; height: 17vw; background-color: white;">
			<view
				style="height: 100%;width: 90vw; padding: 0 5vw; display: flex; align-items: center; justify-content: space-between;">
				<view style="display: flex; padding: 5px 0;">
					<view>
						总计：
						<span class="details_price">￥{{active_credit.userPay}}</span>
					</view>
				</view>
				<view style="padding: 26rpx 10vw; background-color: #3F85FC;border-radius: 62rpx; color: white;"
					@click="showPop()">
					提交订单
				</view>
			</view>
		</view>



		<u-popup :show="show" :round="10" mode="center" @close="close" @open="open" :safeAreaInsetBottom="false">
			<view style="height: 60vh; width: 80vw; background-color: #fff;border-radius: 20rpx; ">
				<scroll-view scroll-y="true" style="height: 50vh; width: 90%; margin: 0 auto;">
					<view>
						<view
							style="width: 100%; text-align: center;padding: 20rpx 0 ; font-size: 40rpx; color: #fa514a;">
							充值温馨提示!
						</view>
						<view class="paragraph red">
							1.充值安全:本次充值未到账前。严禁同一号码在任何平台再次下单，如果有下单无售后。
						</view>

						<view class="paragraph">
							2.防范诈骗:我们不会主动致电，如遇陌生来电需警惕。
						</view>

						<view class="paragraph">
							3.客户支持:如有疑问，请通过官方平台联系在线客服。

						</view>

						<view class="paragraph">
							请严格遵守以上安全准则，共同维护信息安全。如需帮助，随时联系我们。
						</view>

						<view class="paragraph ">
							4.充值到账时间为0-48小时，建议月中充值，因为月初月尾会有延迟（不超过72小时），介意勿拍！
						</view>
						<view class="paragraph red">
							5.[充值失败]网络充值存在10%-20%的失败率充值失败订单会在未来24小时内款项会原路退回，请注意查收;
						</view>
						<view class="paragraph red">
							6.携号转网，充值号码错误，空号，虚商号码，企业号，号码停机，未实名号码勿拍，因为这些原因导致的充值失败，概不负责。
						</view>
						<view class="paragraph red">
							7.不支持充值后撤销订单，充值之后等待到账即可。
						</view>



						<view class="paragraph red" style="margin: 20rpx 0; margin-top: 40rpx;">
							注意⚠️
						</view>
						<view class="paragraph red">


							电信手机号请月中进行充值！月初月尾充值会非常慢到账！介意慎拍！！！电信已更新支付13个地区： 广东， 江苏， 河北， 江西， 河南， 甘肃， 湖北， 四川， 福建， 吉林， 辽宁，
							山东， 贵州
							开启时间为每日7: 30-晚22: 00)
							其他地区在更新中
							移动、联通支持全国！
						</view>
					</view>
				</scroll-view>
				<!-- 操作栏 -->
				<view class="popup_operate">
					<view class="popup_operate_button" @click="close()">
						取消
					</view>
					<view class="popup_operate_button" v-if="time != 0">
						{{time}}
						<span style="margin-left: 2rpx;">S</span>
					</view>
					<view class="popup_operate_button" style="background-color: #3f85fc; color: #fff;" @click="pay"
						v-else>
						确定
					</view>
				</view>
			</view>
		</u-popup>


		<!-- 订单按钮 -->
		<view
			style="position: fixed; bottom: 15vh; right: 5vw; display: flex; flex-direction: column; align-items: center; background-color: #3f85fc; border: 1px solid rgba(0, 0, 0, 0.1); width: 50px; height: 50px; border-radius: 25px; justify-content: center; box-shadow: 0px 0px 1px 2px rgba(149, 146, 162, 0.1)"
			@click="toCerditOrder">
			<view>
				<uni-icons type="list" color="#fff"></uni-icons>
			</view>
			<view class="" style="color: #fff; font-size: 12px; font-weight: bolder;">
				订单
			</view>
		</view>


		<u-loading-page :loading="loadPage" loading-text="loading..."></u-loading-page>
	</view>
</template>

<script>
	import {
		getCreditPackageIngoAPI,
		getCreditGoods
	} from "@/api/credit.js"
	import {
		isLogin
	} from '@/utils/auth.js'
	import {
		fuioupay
	} from '@/api/fuiou.js'
	export default {
		data() {
			return {
				credit_package: {},
				active_credit: {},
				active_index: 0,
				show: false,
				phone: '',
				name: '',
				time: 5,
				kg: true, //倒计时按钮是否可以点击 true不能点击
				loadPage: true,
				timer: null, // 用于存储定时器

			}
		},
		methods: {
			// 获取话费套餐数据
			getCreditPackageIngo() {
				// getCreditPackageIngoAPI().then(res => {
				// 	console.log(res)
				// 	res.data.splice(0, 1)
				// 	this.credit_package = res.data
				// 	// 初始化active_credit
				// 	this.active_credit = res.data[0]
				// 	console.log(this.credit_package)
				// 	// uni.hideLoading()
				// 	this.loadPage = false
				// })
				getCreditGoods().then(res => {
					console.log(res)
					// res.data.data.creditGoods1.splice(0, 1)
					this.credit_package = res.data.data
					this.active_credit = this.credit_package.creditGoods1[0]
					this.loadPage = false
				})


			},
			//点击商品，切换选中商品数据
			getActiveCreditInfo(e, i) {
				this.active_credit = e
				this.active_index = i
			},
			// 点击图片放大
			clickImg(e) {
				uni.previewImage({
					urls: [e], //需要预览的图片http链接列表，多张的时候，url直接写在后面就行了
					current: '', // 当前显示图片的http链接，默认是第一个
					success: function(res) {},
					fail: function(res) {},
					complete: function(res) {},
				})
			},
			// 打开弹窗
			showPop() {
				// 验证是否登录
				if (!isLogin()) {
					uni.showToast({
						duration: 1000,
						title: '请先登录',
						icon: 'error'
					})
					return
				}
				let str = /^(1[3-9]\d{9})$|^([69]\d{7})$|^([6]\d{5})$|^(09\d{8})$/
				//验证是否填写手机号
				if (!str.test(this.phone)) {
					uni.showToast({
						title: "手机号格式错误",
						icon: 'error'
					})
					return
				}
				// 验证是否填写姓名
				if (this.name == '') {
					uni.showToast({
						title: "请填写姓名",
						icon: 'error'
					})
					return
				}

				this.show = true
			},
			open() {
				// console.log('open');
				this.countdown()
			},
			close() {
				this.show = false
				// console.log('close');
				// 清除计时器
				clearInterval(this.timer)
			},

			// 按钮的倒计时
			countdown() {
				//修改倒计时时间 这里写了10s 
				// 先清除可能存在的旧定时器
				this.time = 5
				if (this.timer) {
					clearInterval(this.timer)
				}

				this.timer = setInterval(() => {
					this.time--
					if (this.time === 0) {
						this.kg = false
						clearInterval(this.timer)
					}
				}, 1000)
			},
			// 下单
			pay() {
				let data = {
					userId: uni.getStorageSync("userId"),
					id: this.active_credit.cerditId,
					type: "2",
					phone: this.phone,
					people_name: this.name,
				}
				console.log(data)
				// 调起富友支付
				console.log("发起支付")
				fuioupay(data).then(res => {
					// uni.hideLoading()
					// 如果返回code == 200
					if (res.data.code == "200") {
						console.log("请求成功")
						let redata = res.data

						console.log("开始调起微信支付")

						// 如果已经在微信环境中，直接调用  
						console.log(redata)
						uni.requestPayment({
							appId: res.data.data.appid, // 公众号ID，由商户传入
							timeStamp: res.data.data.timestamp, // 时间戳，自1970年以来的秒数  
							nonceStr: res.data.data.noncestr, // 随机串  
							package: res.data.data.package,
							signType: res.data.data.signtype, // 微信签名方式  
							paySign: res.data.data.paysign, // 微信签名  
							success: function(res) {
								console.log(res); //res.errMsg == "requestPayment:ok"
								console.log('success:' + JSON.stringify(res));
								// 完成，返回首页
								uni.setStorageSync('pay_type_info', 1);
								uni.switchTab({
									// 携带充值成功信息
									url: '/pages/my/my'
								})
							},
							fail: function(err) {
								console.log('fail:' + JSON.stringify(err));
								// 判断是否是用户主动取消支付
								if (err.errMsg === "requestPayment:fail cancel") {
									uni.showToast({
										title: '您已取消支付',
										icon: 'none',
										duration: 2000
									});
								} else {
									uni.showToast({
										title: '支付失败，请重试',
										icon: 'none',
										duration: 2000
									});
								}
							},
							complete(err) {
								console.log('complete:' + JSON.stringify(err));

							}
						})
					} else {
						if (res.data.data === '用户积分不足') {
							uni.showToast({
								title: "积分不足",
								icon: "error"
							})
						} else {
							uni.showToast({
								title: "请求失败",
								icon: "error"
							})
						}

					}


				})




			},

			// 去话费充值订单页面
			toCerditOrder() {
				if (!isLogin()) {
					uni.showToast({
						duration: 1000,
						title: '请先登录',
						icon: 'error'
					})
					return
				}
				uni.navigateTo({
					url: '/pages/credit_orders/credit_orders'
				})
			},
		},
		mounted() {
			// uni.showLoading({
			// 	title: "加载中"
			// })
			this.loadPage = true
			this.getCreditPackageIngo()
		}

	}
</script>

<style scoped>
	.container {
		background-color: #ededed;
		width: 100vw;
		min-height: 100vh;
		padding-top: 20rpx;
		height: auto;
	}

	.box {
		width: 88vw;
		margin: 0 auto;
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx 30rpx;
	}

	.contact {
		width: 100%;
		display: flex;
		align-items: center;
	}

	.contact_font {
		font-size: 26rpx;
	}

	.mobile_title {
		margin-right: 10%;
	}

	.credit_title {
		font-weight: 700;
	}

	.credit_package {
		margin-top: 20rpx;
		width: 100%;
		height: 24vw;
		display: grid;
		grid-template-columns: 1fr 1fr 1fr;
		grid-column-gap: 2vw;
	}

	.single_package {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.details_header {
		width: 100%;
		height: 30vw;
		border-bottom: 1px solid #d6d6d6;
		display: flex;
		align-items: center;
	}

	.details_image {
		width: 20vw;
		height: 20vw;
		margin-right: 5vw;
		border-radius: 20rpx;
	}

	.other_details {
		width: 70vw;
		height: 20vw;
		display: grid;
		grid-template-rows: repeat(3, 1fr);
	}

	.details_msg {
		font-size: 26rpx;
		color: #9d9d9d;
	}

	.details_price {
		color: #ed6b69;
		font-size: 30rpx;
		font-weight: 700;
	}

	.details_single {
		padding: 28rpx 0;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.details_title {
		font-size: 30rpx;
	}

	.default_package_image {
		width: 20vw;
		height: 20vw;
		border-radius: 20rpx;
	}

	.active_package_image {
		width: 23vw;
		height: 23vw;
		border-radius: 20rpx;
	}

	.customStyle {
		height: 50vh;
		width: 60vw;
	}

	.paragraph {
		letter-spacing: 1px;
		line-height: 1rem;
		margin-bottom: 10rpx;
		font-size: 22rpx;
	}

	.red {
		color: #fa514a;
	}

	.popup_operate {
		width: 100%;
		height: 8vh;
		display: flex;
		align-items: center;
		justify-content: space-around;

	}

	.popup_operate_button {
		height: 5vh;
		width: 40%;
		background-color: #d4d4d4;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 2.5vh;
		color: #818181;
	}
</style>