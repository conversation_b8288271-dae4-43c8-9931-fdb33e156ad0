<template>
	<view  style="font-size: 18px;">
		<view>
			用户名:<text style="margin-left: 5px;">{{treeNode.self.userName}}</text>
		</view>
		<view class="line"></view>
		<view>
			代理等级: <text style="margin-left: 5px;">{{ rankName }}</text>
		</view>
		<view class="line"></view>
		<view>
			<view>业绩列表:</view>
			<view style="margin-top: 5px;">
				<view v-for="(value, key) in treeNode.count" :key="key" style="margin-top: 5px;">
					{{key}}: {{value}}
				</view>
			</view>
		</view>
		<view class="line"></view>
		<view>
			<view style="display: flex; justify-content: space-between;" @click="isShow = !isShow">
				<view>
					直属代理:
				</view>
				<view>
					<uni-icons :type="isShow ? 'bottom' : 'right'" size="18"></uni-icons>
				</view>
			</view>
			<view v-if="isShow && treeNode.childTree.length !== 0">
				<view v-for="(data, index) in treeNode.childTree" :key="index"  style="padding-left: 15px; margin-top: 30px; border-left: 1px solid #7f7f7f;">
					<PerformanceTree :treeNode="data"></PerformanceTree>
				</view>
			</view>
			<view v-if="isShow && treeNode.childTree.length === 0" style="text-align: center; margin-top: 15px; color: #7f7f7f" >
				暂无发展成员
			</view>
		</view>
	</view>
</template>

<script>
	const proxyDict = ['', '1级代理', '2级代理', '管理员']
	export default {
		name: 'PerformanceTree',
		props: {
			treeNode: {
				type: Object,
				require: false
			}
		},
		data() {
			return {
				isShow: false
			}
		},
		methods: {
			
		},
		computed: {
			rankName() {
				const name = proxyDict[parseInt(this.treeNode.self.adminRabk)]
				return name
			}
		}
	}
</script>

<style>
	.line {
		margin: 10px 5px;
		height: 1px;
		background: rgba(0, 0, 0, 0.1);
	}
</style>