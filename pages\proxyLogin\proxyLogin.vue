<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle bg-circle-1"></view>
      <view class="bg-circle bg-circle-2"></view>
      <view class="bg-circle bg-circle-3"></view>
    </view>

    <!-- 返回按钮 -->
    <view class="back-btn" @click="goBackToUser">
      <uni-icons type="back" color="#fff" size="20"></uni-icons>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- Logo和标题区域 -->
      <view class="header-section">
        <view class="logo-container">
          <view class="logo-icon">
            <uni-icons
              type="person-filled"
              color="#4089fb"
              size="40"
            ></uni-icons>
          </view>
        </view>
        <view class="welcome-text">代理端登录</view>
        <view class="sub-text">欢迎回来，请登录您的代理人账号</view>
      </view>

      <!-- 登录表单 -->
      <view class="form-container">
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <uni-icons type="phone" color="#999" size="18"></uni-icons>
            </view>
            <input
              class="form-input"
              maxlength="11"
              placeholder="请输入手机号"
              v-model="loginForm.phone"
              type="number"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper verification-wrapper">
            <view class="input-icon">
              <uni-icons type="locked" color="#999" size="18"></uni-icons>
            </view>
            <input
              class="form-input verification-input"
              maxlength="6"
              placeholder="请输入验证码"
              v-model="loginForm.verificationCode"
              type="number"
              placeholder-class="input-placeholder"
            />
            <view
              class="verification-btn"
              :class="{ disabled: verification || isMessage }"
              @click="sendVerification"
            >
              <text v-if="!verification">获取验证码</text>
              <text v-else>{{ verificationSecond }}s后重发</text>
            </view>
          </view>
        </view>

        <!-- 协议勾选 -->
        <view class="agreement-section">
          <view class="checkbox-wrapper" @click="agreement = !agreement">
            <view class="checkbox" :class="{ checked: agreement }">
              <uni-icons
                v-if="agreement"
                type="checkmarkempty"
                color="#fff"
                size="12"
              ></uni-icons>
            </view>
            <text class="agreement-text">
              我已阅读并同意
              <text class="link-text" @click.stop="toUserProtocol"
                >《用户协议》</text
              >
            </text>
          </view>
        </view>

        <!-- 登录按钮 -->
        <view class="login-btn-wrapper">
          <button
            class="login-btn"
            :class="{ loading: isMessage }"
            @click="checkLogin"
          >
            <text v-if="!isMessage">立即登录</text>
            <text v-else>登录中...</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { validPhone, validCode } from "@/utils/valid.js";
import { sendCode, checkLogin } from "@/api/proxyUser.js";
import { setToken, setUserId, setProxyLoginStatus } from "@/utils/auth.js";

let timer;

export default {
  data() {
    return {
      verification: false,
      verificationSecond: 0,
      loginForm: {
        phone: "",
        verificationCode: "",
        adminRabk: "0",
      },
      isMessage: false,
      // 协议按钮
      agreement: false,
      invitationCode: null,
    };
  },
  methods: {
    // 返回用户端
    goBackToUser() {
      uni.navigateBack({
        delta: 1,
      });
    },

    // 发送手机验证码
    sendVerification() {
      console.log("发起验证码");
      if (this.verification === true || this.isMessage === true) {
        return;
      }

      let data = {
        phone: this.loginForm.phone,
        invitationCode: uni.getStorageSync("invitationCode"),
        adminRabk: 1,
      };

      if (!validPhone(data.phone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none",
        });
        return;
      }

      this.isMessage = true;

      // 发送验证码
      sendCode(data)
        .then((res) => {
          if (res.data.code === 200) {
            uni.showToast({
              title: "验证码已发送",
              icon: "success",
            });

            this.verification = true;
            this.verificationSecond = 59;
            clearInterval(timer);
            timer = setInterval(() => {
              this.verificationSecond = this.verificationSecond - 1;
              if (this.verificationSecond <= 0) {
                clearInterval(timer);
                this.verification = false;
              }
            }, 1000);
          } else {
            uni.showToast({
              title: res.data.message || "发送失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "网络错误",
            icon: "error",
          });
        })
        .finally(() => {
          this.isMessage = false;
        });
    },

    // 登录
    checkLogin() {
      if (!this.agreement) {
        uni.showToast({
          title: "请勾选协议",
          icon: "none",
        });
        return;
      }
      if (this.isMessage) {
        return;
      }

      // 验证表单
      const data = {
        ...(this.loginForm || {}),
      };

      if (!validPhone(data.phone)) {
        uni.showToast({
          icon: "error",
          title: "手机号格式有误",
        });
        return;
      }

      if (!validCode(data.verificationCode)) {
        uni.showToast({
          icon: "error",
          title: "验证码格式有误",
        });
        return;
      }

      this.isMessage = true;

      // 代理端登录
      checkLogin(data)
        .then((res) => {
          if (res.data.code === 200) {
            uni.showToast({
              title: "登录成功",
              icon: "success",
            });

            // 存储代理端登录状态
            const userData = res.data.data;
            setToken(userData.token);
            setUserId(userData.userId);
            setProxyLoginStatus(true);

            // 更新代理端用户信息
            this.$store.dispatch("proxyUser/getUserInfo");

            // 跳转到代理端主页
            uni.reLaunch({
              url: "/pages/proxyIndex/index",
            });
          } else {
            uni.showToast({
              title: res.data.message || "登录失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "网络错误",
            icon: "error",
          });
        })
        .finally(() => {
          this.isMessage = false;
        });
    },

    toUserProtocol() {
      uni.navigateTo({
        url: "/pages/proxyUserProtocol/userProtocol",
      });
    },

    // 拨打客服电话
    callService() {
      uni.makePhoneCall({
        phoneNumber: "4000689196",
      });
    },
  },

  watch: {
    isMessage(c) {
      if (c) {
        uni.showLoading({
          mask: true,
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },

  onUnload() {
    clearInterval(timer);
    uni.hideLoading();
  },

  onLoad(config) {
    console.log(config);
    if (config.myInvitationCode !== undefined) {
      this.invitationCode = config.myInvitationCode;
    }
  },
};
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.bg-circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.bg-circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 返回按钮 */
.back-btn {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  position: fixed;
  top: 100rpx;
  left: 30rpx;
  z-index: 999;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.back-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
}

.back-btn text {
  font-size: 24rpx;
  color: #fff;
  margin-left: 8rpx;
  font-weight: 500;
}

/* 主要内容区域 */
.main-content {
  padding: 120rpx 60rpx 60rpx;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo-container {
  margin-bottom: 40rpx;
}

.logo-icon {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.welcome-text {
  font-size: 56rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.sub-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 表单容器 */
.form-container {
  flex: 1;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-wrapper {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 0 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(64, 137, 251, 0.2);
  border-color: #4089fb;
}

.input-icon {
  margin-right: 24rpx;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  height: 96rpx;
  font-size: 30rpx;
  color: #333;
  border: none;
  outline: none;
  background: transparent;
}

.input-placeholder {
  color: #999;
  font-size: 28rpx;
}

.verification-wrapper {
  padding-right: 0;
}

.verification-input {
  padding-right: 24rpx;
}

.verification-btn {
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #4089fb, #5a67d8);
  color: #fff;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 16rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.verification-btn:active {
  transform: scale(0.95);
}

.verification-btn.disabled {
  background: #ccc;
  color: #666;
}

/* 协议区域 */
.agreement-section {
  margin: 40rpx 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  border-radius: 6rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #4089fb;
  border-color: #4089fb;
}

.agreement-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

.link-text {
  color: #fff;
  text-decoration: underline;
  font-weight: 500;
}

/* 登录按钮 */
.login-btn-wrapper {
  margin: 60rpx 0 40rpx;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #fff, #f8f9ff);
  color: #4089fb;
  border: none;
  border-radius: 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.login-btn.loading {
  background: #ccc;
  color: #666;
}

/* 底部区域 */
.footer-section {
  margin-top: auto;
  padding-top: 40rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider-line {
  flex: 1;
  height: 1rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  margin: 0 32rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.contact-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 32rpx;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-title {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.contact-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  color: #4089fb;
  padding: 20rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.contact-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 1);
}

.contact-btn text {
  margin-left: 12rpx;
}
</style>
