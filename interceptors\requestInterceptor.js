import { resetInfo, isLogin } from "@/utils/auth.js";
import store from "@/store/index.js";
import { debug } from "@/utils/debugHelper.js";
import { handleNetworkError } from "@/utils/errorHandler.js";

// 防止重复跳转的标志
let isRedirecting = false;
// 防止重复初始化的标志
let isInitialized = false;

// 判断当前是否在代理端页面
const isProxyPage = function () {
  const pages = getCurrentPages();
  if (pages.length === 0) return false;
  const currentPage = pages[pages.length - 1];
  return currentPage.route && currentPage.route.includes("proxy");
};

//  请求成功拦截器
const success = function (res) {
  // 记录成功的请求
  debug.network(res.config || {}, res);

  if (res.data === undefined || res.data.code === undefined) {
    return;
  }

  // 处理特定的业务错误码
  if (res.data.code === 841) {
    debug.warn("用户认证失效", {
      code: res.data.code,
      message: res.data.message,
    });

    // 防止重复跳转
    if (isRedirecting) {
      debug.info("已在跳转中，忽略重复的认证失效处理");
      return;
    }

    isRedirecting = true;
    resetInfo();

    // 根据当前页面类型清除对应的store
    if (isProxyPage()) {
      store.commit("proxyUser/DEFAULTUSERINFO");
    } else {
      store.commit("user/DEFAULTUSERINFO");
    }

    // 根据当前页面类型跳转到对应的登录页面
    const loginUrl = isProxyPage()
      ? "/pages/proxyLogin/proxyLogin"
      : "/pages/login/login";

    uni.reLaunch({
      url: loginUrl,
      complete() {
        isRedirecting = false; // 重置标志
        setTimeout(() => {
          uni.showToast({
            title: "请先登录",
            icon: "error",
          });
        }, 100);
      },
    });
  }
};

// 请求失败拦截器
const fail = function (err) {
  debug.error("请求失败", err);
  handleNetworkError(err, { source: "interceptor" });
};

// 请求前拦截器
const invoke = function (args) {
  // 记录请求开始
  debug.network(args, null);

  // 自动获取用户信息逻辑 - 添加更多条件检查
  if (
    args.url.indexOf("/userinformation/showInformations") === -1 &&
    isLogin() &&
    !isRedirecting // 不在跳转过程中
  ) {
    // 根据当前页面类型调用对应的store
    if (isProxyPage()) {
      // 代理端页面，检查代理端用户信息
      if (
        store.getters.proxyUserInfo &&
        store.getters.proxyUserInfo.phone === ""
      ) {
        debug.info("自动获取代理端用户信息", { url: args.url });
        store.dispatch("proxyUser/getUserInfo");
      }
    } else {
      // 用户端页面，检查用户端用户信息
      if (store.getters.userInfo && store.getters.userInfo.phone === "") {
        debug.info("自动获取用户端用户信息", { url: args.url });
        store.dispatch("user/getUserInfo");
      }
    }
  }
};

const requestInterceptor = function () {
  // 防止重复初始化
  if (isInitialized) {
    debug.info("请求拦截器已经初始化过，跳过重复初始化");
    return;
  }

  uni.addInterceptor("request", {
    success,
    fail,
    invoke,
  });

  isInitialized = true;
  debug.info("请求拦截器已初始化", {
    handlers: ["success", "fail", "invoke"],
    supportProxy: true,
  });
};

export { requestInterceptor };
