!function(){var e,t,o,n,i,a,r={29395:function(e,t,o){"use strict";o.d(t,{S:function(){return Q}});var n=o(33940),i=o(45751),a=o(63070),r=o(2785),s=o(85469),c=o(20158);var d=o(28904),l=o(99452),p=o(42590),h=o(94537),u=o(12184),g=o(97390),m=o(64591),f=o(13975),b=o(97021),y=o(82229),w=o(37802);const x="HideDialogs";class v extends d.H{constructor(){super(...arguments),this.useFastAnchoredRegion=!0,this.useDetailPageStyles=!1,this.useKumoDesign=!1,this.useWAISTStyles=!1,this.fixedPosition=!1,this.defaultVerticalPosition="bottom",this.defaultHorizontalPosition="end",this.autoFocus=!0,this.isWidgetRegion=!1,this.isUpdatingPosition=!1,this.isBingHp="bingHomepage"===g.jG.AppType,this.isViews="views"===g.jG.AppType,this.isHomepage="homePage"===g.jG.AppType,this.isWinWidget="winWidgets"===g.jG.AppType,this.fixedParentSpace=!1,this.closeMenu=e=>{this.referrerButton&&this.referrerButton.focus(),this.$emit("dismiss-menu"),e?.stopPropagation()},this.keyDownHandler=e=>{if(e.key===h.CX||this.isWinWidget&&e.key===h.oM)this.closeMenu(e);else if(!this.isWinWidget&&e.key===h.oM){if(!this.customTabEventHandler)return this.tabEventHandler(e),!1;this.customTabEventHandler(this,e)}return!0},this.updatePositionManually=()=>{this.updatePosition()},this.tabEventHandler=e=>{if(this.fluentMenu){const t=this.fluentMenu.children.length,o=e.target;Array.from(this.fluentMenu.children).map(((n,i)=>{if(n===o){const o=(i+(e.shiftKey?-1:1)+t)%t;this.fluentMenu.children[o].focus()}}))}},this.initMenuPositioning=()=>{this.setMenuPosition(),document.addEventListener("scroll",this.updatePosition),window.addEventListener("resize",this.updatePosition)},this.setMenuPosition=()=>{const e=this.referrerButton.getBoundingClientRect(),t=e.top,o=e.bottom,n=this.offsetParent?.offsetTop||0;let i=0;this.isBingHp&&(i=this.offsetParent.getBoundingClientRect().top+window.scrollY-n);let a=0;if(this.isViews&&(a=this.offsetParent.getBoundingClientRect().top+window.scrollY-n),this.fluentMenu){let r;r="top"===this.defaultVerticalPosition?`top:${t+window.scrollY-2-this.fluentMenu.clientHeight-n}px;`:o+this.fluentMenu.clientHeight>window.innerHeight?`top:${t+window.scrollY-2-this.fluentMenu.clientHeight-n-i-a}px;`:`top:${o+window.scrollY+1-n-i-a}px;`,this.setAttribute("style",`position:absolute;${r}${this.getHorizontalPosition(e)}`),this.isUpdatingPosition=!1}this.useDetailPageStyles&&(this.setAttribute("style",`position:absolute;top:${e.top+window.scrollY}px;${this.getHorizontalPosition(e)}`),this.isUpdatingPosition=!1)},this.updatePosition=(0,w.Z)((()=>{this.isUpdatingPosition||(window.requestAnimationFrame((()=>{this.setMenuPosition()})),this.isUpdatingPosition=!0)}),300)}onKeyDownHandlerMenuItem(e,t){return e.key!==h.kL&&e.key!==h.BI?this.keyDownHandler(e):(t.onClick&&t.onClick(e),!0)}connectedCallback(){if(this.useFastAnchoredRegion&&this.isWidgetRegion){const e=(0,y.K0)(u.RL).getCurrentState().selectedFeedDisplaySetting;this.viewportElement="headingsonly"===e?(0,f.b_)(b._.backgroundOuterHolder):null,this.fixedPosition="off"===e}super.connectedCallback(),this.isDarkMode=m.k.appInDarkMode(),document.addEventListener(x,this.closeMenu),this.fluentMenu&&(this.useFastAnchoredRegion||this.initMenuPositioning(),this.autoFocus&&this.fluentMenu&&(window.setTimeout((()=>{this.fluentMenu.children&&this.fluentMenu.children[0]&&this.fluentMenu.children[0].focus()}),100),this.$emit("shown-menu")))}onMouseEnter(e,t){const o=e.currentTarget,n=o.shadowRoot?.querySelector?.('[part="content"]');if(n){n.scrollWidth>n.clientWidth?o.setAttribute("title",t||""):o.removeAttribute("title")}}disconnectedCallback(){super.disconnectedCallback(),document.removeEventListener(x,this.closeMenu),this.useFastAnchoredRegion||(document.removeEventListener("scroll",this.updatePosition),window.removeEventListener("resize",this.updatePosition))}getHorizontalPosition(e){const t=document.documentElement.clientWidth,o=e.right,n=e.left,i=this.fixedParentSpace?this.offsetParent.getBoundingClientRect():null;if("rtl"===document.dir||"start"===this.defaultHorizontalPosition){let e=0;if(this.isViews||this.isHomepage){const{left:o=0,width:n=0}=this.offsetParent.getBoundingClientRect()||{};e=t-o-n}return this.useDetailPageStyles?`right:${t-o-e-(i?i.right-i.width:0)-245}px;`:t-o+this.fluentMenu.clientWidth<=window.innerWidth?`right:${t-o-e-(i?i.right-i.width:0)}px`:`left:${n-(i?.left??0)}px;`}let a=0;return(this.isViews||this.isHomepage||this.isBingHp)&&(a=this.offsetParent.getBoundingClientRect().left),this.useDetailPageStyles?`left:${n-a-(i?.left??0)-245}px;`:n+this.fluentMenu.clientWidth<=window.innerWidth?`left:${n-a-(i?.left??0)}px;`:`right:${t-o-(i?i.right-i.width:0)}px;`}}(0,n.gn)([l.LO],v.prototype,"menuItems",void 0),(0,n.gn)([l.LO],v.prototype,"isDarkMode",void 0),(0,n.gn)([l.LO],v.prototype,"referrerButton",void 0),(0,n.gn)([l.LO],v.prototype,"useFastAnchoredRegion",void 0),(0,n.gn)([l.LO],v.prototype,"useDetailPageStyles",void 0),(0,n.gn)([l.LO],v.prototype,"useKumoDesign",void 0),(0,n.gn)([l.LO],v.prototype,"useWAISTStyles",void 0),(0,n.gn)([(0,p.Lj)({mode:"boolean"})],v.prototype,"fixedPosition",void 0),(0,n.gn)([(0,p.Lj)({mode:"boolean",attribute:"dynamic-width"})],v.prototype,"dynamicWidth",void 0),(0,n.gn)([p.Lj],v.prototype,"defaultVerticalPosition",void 0),(0,n.gn)([p.Lj],v.prototype,"defaultHorizontalPosition",void 0),(0,n.gn)([(0,p.Lj)({mode:"boolean"})],v.prototype,"autoFocus",void 0),(0,n.gn)([(0,p.Lj)({mode:"boolean"})],v.prototype,"isWindowsDashboard",void 0),(0,n.gn)([l.LO],v.prototype,"isWidgetRegion",void 0),(0,n.gn)([l.LO],v.prototype,"isGlyphUrlsPicture",void 0),(0,n.gn)([l.LO],v.prototype,"fixedParentSpace",void 0);var _=o(78923),S=o(22798),C=o(29717),T=o(80260),k=o(42689),A=o(22674),$=o(28632);const E=_.i` .menu.detail-page{background:#424242;border-radius:6px}.menu.detail-page fluent-menu-item,.menu.detail-page fluent-menu-item:hover{background:transparent;color:${T.Qp};fill:${T.Qp}}.item-selected{font-weight:600;border-radius:4px;border-left-color:var(--accent-fill-focus);border-left-width:5px;background:var(--neutral-fill-active)}@media (prefers-color-scheme:dark){.menu.detail-page{background:#424242;border-radius:6px}.menu.detail-page fluent-menu-item,.menu.detail-page fluent-menu-item:hover{background:transparent;color:${k.C};fill:${k.C}}}`,P=_.i`
.menu-item.kumo-menu-item:hover{background:${S.H.Highlight};color:${S.H.HighlightText}}`,I=_.i` .kumo-menu{min-width:248px;box-sizing:border-box;padding:8px;box-shadow:0px 0px 8px rgba(0,0,0,0.12),0px 8px 16px rgba(0,0,0,0.14);border-radius:12px;background:#FAFAFA}.kumo-menu-item{display:flex;justify-content:flex-start;align-items:center;gap:6px;margin:8px 0px 0px 0px;max-width:100%;padding:6px 8px;border-radius:12px}.kumo-menu-item:first-of-type{margin:0px}fluent-menu-item.kumo-menu-item::part(content){width:min-content;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.menu-item.kumo-menu-item:hover{background:#E3E8EF}`.withBehaviors((0,C.Uu)(_.i` .kumo-menu{background:rgba(27,29,31,1)}.menu-item.kumo-menu-item:hover{background:var(--neutral-fill-stealth-hover)}`)),B=_.i`
.kumo-menu-item.waist-item{color:#8D9198}.icon.waist-glyph{background:#292C33;color:#DBDBDB}.menu-item.kumo-menu-item.waist-item:hover{background:#1B1D1F}.kumo-menu-item.waist-item:first-of-type:hover{.icon.waist-glyph{background:#2F333A}background:#292C33}.kumo-menu-item.waist-item:first-of-type{.icon.waist-glyph{background:#292C33;color:#DBDBDB}color:#DBDBDB;background:#292C33}`,F=_.i`
.kumo-menu-item.waist-item:hover{color:${S.H.ButtonText} !important}`,L=_.i`
.waist-menu{border-radius:20px;padding:0px}fluent-menu-item.kumo-menu-item.waist-item::part(content){width:auto;white-space:normal;min-width:192px}.menu-item.kumo-menu-item.waist-item:hover{background:#FAFAFA}.kumo-menu-item.waist-item:first-of-type{.icon.waist-glyph{background:#EFF4FA}font-weight:550;color:black;border-radius:20px 20px 0px 0px;background:#EFF4FA;height:48px;padding:10px}.kumo-menu-item.waist-item:first-of-type:hover{.icon.waist-glyph{background:#E7EBF1}background:#EFF4FA}.icon.waist-glyph{background:#EFF4FA;border-radius:10px;padding:6px;color:black}.kumo-menu-item.waist-item{color:#6E7279;padding:25px 10px 25px 10px;gap:8px;margin-bottom:6px;font-size:12px;line-height:16px;padding:16px 10px}`.withBehaviors((0,C.Uu)(B),(0,C.vF)(F)),D=_.i` .copilot-menu-item{display:flex;align-items:center;justify-content:space-between;margin-bottom:12px}.copilot-input-with-icon{position:relative;display:inline-block}.copilot-input-with-icon input{padding:10px 12px 11px 42px;box-sizing:border-box;width:240px;height:44px;border-radius:16px;background:rgba(123,117,114,0.08);box-shadow:0px 1px 2px 0px rgba(0,0,0,0.08) inset,0px 1px 1px 0px #FFF;border:none;color:#23272b;text-overflow:ellipsis;font-family:inherit;font-size:16px;font-style:normal;font-weight:400;line-height:145%;letter-spacing:-0.16px;outline:none}.copilot-input-with-icon input::placeholder{color:#65696f}.copilot-input-with-icon img{position:absolute;left:10px;top:50%;transform:translateY(-50%);pointer-events:none;width:20px;height:20px;z-index:2}.copilot-voice-btn{--card-actions-button-size:48px;align-items:center;box-sizing:border-box;color:#242424;cursor:pointer;display:flex;fill:#242424;font-family:inherit;height:var(--card-actions-button-size);justify-content:center;min-width:var(--card-actions-button-size);outline:none;transition:all 0.1s ease 0s;width:var(--card-actions-button-size);border:none}`.withBehaviors((0,C.Uu)(_.i` .copilot-input-with-icon input{box-shadow:0px 6px 12px 0px #001E441A;color:#fff}.copilot-input-with-icon input::placeholder{color:#8e9398}`),(0,C.vF)(P)),M=_.i` .kumo-menu-item.toggle-item{height:36px;cursor:default}.kumo-menu-item.toggle-item:hover{background:transparent}.toggle-button{display:inline;right:8px;top:5px;position:absolute;cursor:pointer}.toggle-option-container,.toggle-option{height:24px;display:flex;align-items:center;padding:6px 12px;box-sizing:border-box;border-radius:12px}.toggle-option-container{justify-content:space-between;border:1px solid #C6CBD1;background:#FFF;color:#474B50;width:88px}.toggle-option{position:absolute;top:0;width:51px;justify-content:center;background:#23272B;color:#FFF}.toggle-selected-1{left:-1px}.toggle-selected-2{right:-1px}`.withBehaviors(new A.O(_.i``,_.i` .toggle-button{left:8px}`),(0,C.Uu)(_.i` .kumo-menu-item.toggle-item:hover{background:transparent}.toggle-option{background:#3A3D42}`),(0,C.vF)(_.i``)),R=_.i` .menu-item:hover{background:${S.H.Highlight};color:${S.H.HighlightText}}fluent-menu-item[aria-checked="true"]{background:${S.H.Highlight};color:${S.H.HighlightText} !important}fluent-menu-item:focus-visible{color:${S.H.Highlight}}fluent-menu-item:hover>img{filter:invert(1)}fluent-menu-item[aria-checked="true"]>img{filter:invert(1) !important}`,O=_.i` ${E} ${I} ${M} ${L} :host{z-index:var(--menu-z-index,700);--control-corner-radius:4}:host(:focus){outline:none}:host([isWindowsDashboard]){--control-corner-radius:8}:host([dynamic-width]) .menu-item{min-width:max-content}fluent-menu{box-shadow:0 2.4px 7.2px rgba(0,0,0,0.18),0 12.8px 28.8px rgba(0,0,0,0.22);max-width:var(--menu-max-width,275px);min-width:var(--menu-min-width,240px)}fluent-anchored-region{z-index:700}svg{fill:currentColor}.icon{align-items:center;display:flex}.rotate-icon-down svg{-ms-transform:rotate(180deg);transform:rotate(180deg)}:host(:focus){outline:none}fluent-menu-item::part(content){width:100%;overflow:none}.menu-item{grid-template-columns:minmax(32px,auto) 1fr minmax(32px,auto);background:none}.menu-item:hover{background:var(--neutral-fill-stealth-hover)}.menu-item::part(content){grid-column-start:2;margin-inline-start:0}.menu-item::part(input-container){display:none}.menu-item [slot="start"]{grid-column:1}.menu-item-divider{margin-bottom:6px;border-top-color:${$.c1}}.menu-item-text{padding-inline-start:12px;opacity:0.786;font-size:12px}${D}
`.withBehaviors((0,C.vF)(R));var j=o(49218),U=o(93703),H=o(41472),N=o(54256),z=o(89150);const W=j.dy` ${(e,t)=>e.glyphUrls&&Object.keys(e.glyphUrls).length?t.parent.isGlyphUrlsPicture?j.dy`<picture slot="start" part="icon" aria-hidden="true"><source srcset="${e.glyphUrls.darkMode}" media="(prefers-color-scheme: dark)"><img part="icon-img" src=${e.glyphUrls.lightMode}></picture>`:t.parent.isDarkMode?j.dy`<img class="dark" slot="start" part="icon" src=${e.glyphUrls.darkMode} aria-hidden="true"/>`:j.dy`<img class="light" slot="start" part="icon" src=${e.glyphUrls.lightMode} aria-hidden="true"/>`:e.glyph?j.dy`<span slot="start" class="icon ${(e,t)=>t.parent.useWAISTStyles?"waist-glyph":""}" part="icon" aria-hidden="true">${e=>"string"==typeof e.glyph?j.dy`${j.dy.partial(e.glyph)}`:e.glyph}</span>`:void 0}`,G=j.dy`<div class="copilot-menu-item"><div class="copilot-input-with-icon"><img alt="" aria-hidden="true" src="${(0,g.Yq)()?.StaticsUrlWithBuild}/common/icons/copilot_color.svg"></img><input class="copilot-input" type="text" placeholder="${e=>e.copilotInputPlaceholder}"/></div><div class="copilot-voice-btn"><img alt="" aria-hidden="true" src="${(0,g.Yq)()?.StaticsUrlWithBuild}/common/icons/microPhone${(e,t)=>t.parent.isDarkMode?"DarkMode":""}.svg"></img></div></div>`,q=j.dy`<fluent-menu-item aria-label="${e=>`${e.ariaLabel||e.title} menu item`}" class="menu-item kumo-menu-item toggle-item" part="menu-item" tabindex="0" @keydown=${(e,t)=>t.parent.onKeyDownHandlerMenuItem(t.event,e)} role="${e=>e.role||"menuitem"}" exportparts="start: actions-menu-start menu-item icon-img" id="${e=>e.id}" title="${e=>e.tooltip}">${W} ${e=>e.title}<div class="toggle-button" @click=${(e,t)=>e.onClick(t.event)} data-t="${e=>e.telemetryTag}"><div class="toggle-option-container"><div>${e=>e.toggle.option1}</div><div>${e=>e.toggle.option2}</div></div><div class="toggle-selected-${e=>e.toggle.isOption1Selected?"1":"2"} toggle-option">${e=>e.toggle.isOption1Selected?e.toggle.option1:e.toggle.option2}</div></div></fluent-menu-item>`,V=j.dy`
${(0,U.g)((e=>e.isCopilot),G)}
${(0,U.g)((e=>e.isDivider),j.dy`<fluent-divider class="menu-item-divider"></fluent-divider>`)}
${(0,U.g)((e=>e.isText),j.dy`<div class="menu-item-text" role="presentation">${e=>e.title}</div>`)}
${(0,U.g)((e=>e.toggle),q)}
${(0,U.g)((e=>!(e.isDivider||e.isText||e.isCopilot||e.toggle)),j.dy`<fluent-menu-item aria-label="${e=>`${e.ariaLabel||e.title} menu item`}" :checked="${e=>"menuitemradio"===e.role&&Boolean(e.checked)}" class="menu-item ${(e,t)=>t.parent.useKumoDesign?"kumo-menu-item":""} ${e=>e.isSelected?"item-selected":""} ${(e,t)=>t.parent.useWAISTStyles?"waist-item":""}" part="menu-item" @click=${(e,t)=>e.onClick(t.event)} @keydown=${(e,t)=>t.parent.onKeyDownHandlerMenuItem(t.event,e)} tabindex="0" role="${e=>e.role||"menuitem"}" exportparts="start: actions-menu-start menu-item icon-img" id="${e=>e.id}" ?disabled="${e=>e.disabled}" isNavigation="${e=>e.isNavigation}" data-t="${e=>e.telemetryTag}" data-customhandled=${e=>e.dataCustomHandled||void 0} title @mouseenter=${(e,t)=>t.parent.onMouseEnter(t.event,e.ariaLabel)}>${W} ${e=>e.title} ${(e,t)=>e.suffixGlyphUrls&&Object.keys(e.suffixGlyphUrls).length?t.parent.isGlyphUrlsPicture?j.dy`<picture slot="end" aria-hidden="true"><source srcset="${e.suffixGlyphUrls.darkMode}" media="(prefers-color-scheme: dark)"><img src=${e.suffixGlyphUrls.lightMode}></picture>`:t.parent.isDarkMode?j.dy`<img class="dark" slot="end" src=${e.suffixGlyphUrls.darkMode} aria-hidden="true"/>`:j.dy`<img class="light" slot="end" src=${e.suffixGlyphUrls.lightMode} aria-hidden="true"/>`:e.suffixGlyph?j.dy`<span slot="end" class="icon" aria-hidden="true">${e=>"string"==typeof e.suffixGlyph?j.dy`${j.dy.partial(e.suffixGlyph)}`:e.suffixGlyph}</span>`:void 0}</fluent-menu-item>`)}
`,K=j.dy`<fluent-menu ${(0,H.i)("fluentMenu")} @keydown=${(e,t)=>e.keyDownHandler(t.event)} class=${e=>(0,N.A)("menu",["detail-page",!!e.useDetailPageStyles],["kumo-menu",!!e.useKumoDesign],["waist-menu",!!e.useWAISTStyles])} part="menu">${(0,z.rx)((e=>e.menuItems),V)}</fluent-menu>`,Y=j.dy`<template id="actions-menu">${(0,U.g)((e=>e.useFastAnchoredRegion),j.dy`<fluent-anchored-region part="menu-region" :anchorElement=${e=>e.referrerButton||document.body} :viewportElement=${e=>e.viewportElement||document.documentElement} fixed-placement="${e=>e.fixedPosition}" vertical-positioning-mode="dynamic" vertical-default-position="${e=>e.defaultVerticalPosition}" horizontal-positioning-mode="dynamic" horizontal-default-position="${e=>e.defaultHorizontalPosition}" horizontal-inset="true">${K}</fluent-anchored-region>`)} ${(0,U.g)((e=>!e.useFastAnchoredRegion),j.dy`${K}`)}</template>`;i.D.define(a.H.registry),r.D.define(a.H.registry),s.D.define(a.H.registry),c.D.define(a.H.registry);let Q=class extends v{};Q=(0,n.gn)([(0,d.M)({name:"msn-actions-menu",template:Y,styles:O,shadowOptions:{delegatesFocus:!0}})],Q)},32276:function(e,t,o){"use strict";o.d(t,{il:function(){return gt}});var n=o(33940),i=o(53597),a=o(81736),r=o(64831),s=o(97390),c=o(15588),d=o(99690),l=o(65669),p=o(28368),h=o(74597),u=o(20291),g=o(56175),m=o(53241),f=o(86701),b=o(89181),y=o(25514),w=o(96697),x=o(28904),v=o(99452),_=o(45997),S=o(92133),C=o(94718),T=o(34223),k=o(29476);const A="contextualSuggestionChange",$="SearchBoxFocus",E="csb_tqsk";let P=class extends x.H{constructor(){super(...arguments),this.options=null,this.parameterMap={},this.formParameters=[],this.disableSubmit=!1,this.searchVisible=!0,this.usePageBreakpoints=!1,this.isBingUrlWarmed=!1,this.isRuby=!1,this.isAutoSuggestInitialized=!1,this.autosuggestShown=!1,this.showAPIAutoSuggestBox=!1,this.enableLoadAPIAutoSuggestBox=!1,this.delayStartInitAPIAutoSuggestBox=!1,this.enableAPIAutoSuggestions=!1,this.isInputFocused=!1,this.actionElementFocused=!1,this.searchBoxTelemetryTags="",this.buttonTelemetryTag="",this.deepSearchButtonTelemetryTag="",this.inputTelemetryTag="",this.contextualSuggestionsInAS=[],this.selectBoxOptionTelemetryTag="",this.value="",this.searchButtonOnLeft=!1,this.placeholderColor=0,this.hoverFocusDeeperShadowStyle=!1,this.searchIconTreatment=0,this.searchIconColor=0,this.brightSearchInDarkMode=0,this.enableDeepSearch=!1,this.enableDefaultDeepSearchQry=!1,this.enableDimMask=!1,this.dimMaskOpacity=.6,this.hasInitialized=!1,this.keyDownScrollHappened=!1,this.firstClickLog=!1,this.firstKeyPressLog=!1,this.bingUpsellFocused=!1,this.firstTypeToSearchLog=!1,this.trendingSearchesRequested=!1,this.onAutosuggestRendered=e=>{const t=e.target.getAttribute("SuggMode");this.isNextWordASMode="1"===t},this.onAutosuggestShown=()=>{this.autosuggestShown=!0,(0,r.Gg)(r.tk.search,!0),d.M0&&d.M0.addOrUpdateTmplProperty("CHASShow","1")},this.onAutosuggestHidden=()=>{this.autosuggestShown&&((0,r.Gg)(r.tk.search,!1),this.autosuggestShown=!1)},this.onAPIAutosuggestShown=()=>{this.showAPIAutoSuggestBox=!0,(0,r.Gg)(r.tk.search,!0),d.M0&&d.M0.addOrUpdateTmplProperty("CHASShow","1")},this.onAPIAutosuggestHidden=()=>{this.showAPIAutoSuggestBox&&((0,r.Gg)(r.tk.search,!1),this.showAPIAutoSuggestBox=!1)},this.onAPIAutosuggestRendered=()=>{const e=this.formElement.getAttribute("SuggMode");this.isNextWordASMode="1"===e},this.onDocumentKeypress=e=>{const t=e.composedPath(),o=t&&t[0]&&t[0],n=o&&o.tagName.toLowerCase(),a=o&&o.isContentEditable||e.target&&e.target.isContentEditable;["input","fluent-text-field","fluent-text-area","textarea","cs-feed-layout","msft-feed-layout","card-action","qna-feed-list","select","fluent-select","cs-responsive-feed-layout"].includes(n)||e.target.id===i.cN||a||e.charCode>32&&e.charCode<=126&&(this.inputElement.focus(),e.stopPropagation(),this.options?.ntpTypeToSearchFocusOnly&&e.preventDefault(),this.options?.ntpTypeToSearchLog&&!this.firstTypeToSearchLog&&(d.M0&&d.M0.addOrUpdateTmplProperty("NTPTypeToSearch","1"),this.firstTypeToSearchLog=!0))},this.inputOnFocus=()=>{this.isInputFocused=!0,window.dispatchEvent(new CustomEvent($,{detail:!0}))},this.inputOnBlur=(e=void 0)=>{this.isInputFocused=!1,window.dispatchEvent(new CustomEvent($,{detail:!1}))},this.inputOnMouseEnter=()=>{window.dispatchEvent(new CustomEvent("SearchBoxHover",{detail:!1}))},this.inputOnClick=()=>{!1===this.firstClickLog&&(d.M0&&d.M0.addOrUpdateTmplProperty("CHInputClick","1"),this.firstClickLog=!0),!this.delayStartInitAPIAutoSuggestBox||this.isAutoSuggestInitialized||this.enableLoadAPIAutoSuggestBox||this.initAPIAutoSuggest()},this.onKeyDown=e=>("Tab"!==e.key&&"Escape"!==e.key&&(!1===this.firstKeyPressLog&&(d.M0&&d.M0.addOrUpdateTmplProperty("CHInputTp","1"),this.firstKeyPressLog=!0),!this.delayStartInitAPIAutoSuggestBox||this.isAutoSuggestInitialized||this.enableLoadAPIAutoSuggestBox||this.initAPIAutoSuggest()),this.searchOptionsElement&&"Tab"===e.key&&this.inputElement&&""===this.inputElement.value&&(this.searchOptionsElement.focus(),this.searchOptionsElement.setAttribute("style","")),!0),this.resetFormAfterSubmit=(e=!1)=>{this.options?.contextualSuggestFormCode&&this.formElement.form?.value===this.options.contextualSuggestFormCode?.toString()&&this.addOrUpdateSearchParameter("form",this.formParameters.find((e=>"form"===e.name))?.value||""),(this.options&&this.options.target&&"_self"!==this.options.target||e)&&window.setTimeout((()=>{if(this.options){if(this.searchActionUrl=this.options.searchActionUrl,e&&!this.options.notResetWhenOpenInNewTab&&(this.inputElement.value="",this.value="",this.inputElement.dispatchEvent(new Event("input"))),this.formElement){const e=this.formElement.getElementsByTagName("input");for(const t of e)"q"===t.name||this.parameterMap[t.name]||t.remove()}this.formParameters=[],this.populateFormParameters(),this.searchBoxTelemetryTags="",this.populateTelemetryTags(),this.inputElement.blur()}}),0)},this.keyDownHandler=e=>{!this.options?.disableArrowUpDownAS||this.keyDownScrollHappened||"ArrowDown"!==e.key&&"ArrowUp"!==e.key&&"PageUp"!==e.key&&"PageDown"!==e.key||!this.options||!this.options.autofocus||!this.isInputFocused||this.autosuggestShown||this.showAPIAutoSuggestBox||this.scrollPageAndMarkDone()},this.inputKeyDownHandler=e=>{if(!this.bingUpsellFocused&&"ArrowDown"===e.key){const t=this.shadowRoot?.getElementById?.("bing-as-upsell");if(!t)return;e.preventDefault(),e.stopImmediatePropagation(),e.stopPropagation(),t.focus(),this.bingUpsellFocused=!0}},this.onKeypress=e=>{if("Enter"!==e.key||!this.formElement)return!0;this.onSubmit(!!this.options?.searchKeyPressOpenInNewTab)},this.onContextualSuggestionChange=e=>{if(s.jG.LocaleDisplayLanguageString!==s.jG.LocaleContentMarketString)return;const t=e?.detail;t&&this.updateContextualSuggestion(t)},this.onVisibilitychange=()=>{"visible"===document.visibilityState&&this.contextualSuggestionList?.length&&(this.contextualSuggestionText=this.shuffleContextualSuggestionText()||this.contextualSuggestionText)},this.updateContextualSuggestion=e=>{this.options&&this.options.contextSuggestAsPlaceholder&&(this.contextualSuggestionText=e.suggestion,this.contextualSuggestionList=e.allSuggestions||[],e.isAds?this.contextualSuggestionSlug=this.options.localizedStrings.adLabel||"Ad":this.options?.contextualSuggestRotate&&this.contextualSuggestionList.length&&(this.contextualSuggestionText=this.shuffleContextualSuggestionText()||e.suggestion)),this.options&&this.options.enableContextualAutoSuggest&&(this.contextualSuggestionsInAS=e.suggestions||[])},this.shiftNextContextualSuggestionText=()=>{if(!(0,S.D)()||!this.contextualSuggestionList)return this.contextualSuggestionText;const e=this.contextualSuggestionList[0]??this.contextualSuggestionText,t=(0,C.$o)(),o=t.getObject(E),n=o?.expire,i=Date.now();let a=e;if(!n||n>i-864e5){const t=o?.previousText,n=t?this.contextualSuggestionList.indexOf(t):-1;a=-1===n||n>=this.contextualSuggestionList.length-1?e:this.contextualSuggestionList[n+1]}return t.setObject(E,{previousText:a,expire:i}),a},this.shuffleContextualSuggestionText=()=>{let e=this.contextualSuggestionText;if(!this.contextualSuggestionList?.length||this.contextualSuggestionList.length<=1)return e;if(this.options?.suggestRotateInOrder)return this.shiftNextContextualSuggestionText();let t=0;do{const t=Math.floor(Math.random()*this.contextualSuggestionList.length);e=this.contextualSuggestionList[t]}while(e===this.contextualSuggestionText&&++t<=5);return e},this.handActionFocusEvent=()=>{this.actionElementFocused=!0},this.handActionUnfocusEvent=()=>{this.actionElementFocused=!1}}enableWarmBingUrlChanged(){this.enableWarmBingUrl&&!this.isBingUrlWarmed&&this.delayWarmBingConnections()}enableAutoSuggestionChanged(){this.enableAutoSuggestion&&!this.isAutoSuggestInitialized&&!this.enableAPIAutoSuggestions&&this.formElement&&this.delayInitAutosuggest(),this.enableAutoSuggestion&&!this.isAutoSuggestInitialized&&this.formElement&&this.enableAPIAutoSuggestions&&!this.enableLoadAPIAutoSuggestBox&&this.delayInitAPIAutosuggest()}inputElementChanged(e,t){void 0===e&&t&&t.value!==this.value&&(this.value=t.value)}connected(){window.isSSREnabled&&window.dispatchEvent(new CustomEvent("SearchBoxHydrated")),this.enableWarmBingUrl&&!this.isBingUrlWarmed&&this.delayWarmBingConnections(),this.disableSubmit||document.body.addEventListener("keypress",this.onDocumentKeypress,{capture:!0}),this.options&&this.options.showCodexButtonOutside&&!this.isRuby&&(this.outsideCodexBtnFullPageLayout=!0),this.isRuby&&(this.largerSearchBox=!1,this.largerSearchBoxTweak=0),!this.enableAutoSuggestion||this.isAutoSuggestInitialized||this.enableAPIAutoSuggestions||this.delayInitAutosuggest(),this.options&&this.options.autofocus&&this.inputElement&&this.inputElement.focus(),this.enableAutoSuggestion&&!this.isAutoSuggestInitialized&&this.enableAPIAutoSuggestions&&!this.enableLoadAPIAutoSuggestBox&&this.delayInitAPIAutosuggest(),this.delayInitTrendingQuery()}optionsChanged(e,t){if(!this.options)return;this.hasInitialized||(this.initialize(),this.hasInitialized=!0),this.options.autofocus&&this.inputElement&&this.inputElement.focus();const o=s.jG&&s.jG.CurrentRequestTargetScope&&"kids"===s.jG.CurrentRequestTargetScope.audienceMode;!0===this.options.enableWebAPISuggetion&&!o&&!this.isMobile()&&!this.isTablet()&&this.options.enableMSNSuggestions&&this.options.enableMSNSuggestions>0?1===this.options.enableMSNSuggestions?this.placeholder=this.placeholder||this.options.localizedStrings.placeholderWithMSN||this.options.localizedStrings.placeholderText:2===this.options.enableMSNSuggestions&&(this.placeholder=this.placeholder||this.options.localizedStrings.placeholderWithMSNv2||this.options.localizedStrings.placeholderText):this.placeholder=this.placeholder||this.options.localizedStrings.placeholderText,this.defaultPlaceholder=this.placeholder,t?.additionalFormParamMap!==e?.additionalFormParamMap&&this.populateFormParameters();let n=this.options.enableDeepSearch||!1;this.options.enableDeepSearchForNonBingDSE&&(n=n&&"0"===(0,c.L)().get("dsp")),this.enableDeepSearch=n,this.enableDefaultDeepSearchQry=this.options.enableDefaultDeepSearchQry||!1,this.enableTrendingQueryClearable=this.options.enableTrendingQueryClearable||!1,this.enableConditionalSchBtn=this.options.enableConditionalSchBtn||!1,this.enableHighlightTrendingQuery=this.options.enableHighlightTrendingQuery||!1,this.largerSearchBox=this.options.largerSearchBox||!1,this.largerSearchBoxTweak=this.options.largerSearchBoxTweak||0,this.isRuby&&(this.largerSearchBox=!1,this.largerSearchBoxTweak=0)}get getPlaceholder(){if(this.options&&this.options.enableContextualSuggest&&this.contextualSuggestionText){if(this.contextualSuggestionSlug)return"";const e=!this.isInputFocused||this.options.contextualSuggestOnFocus;return e&&d.M0.addOrUpdateTmplProperty("ContextualSB","1"),e?this.contextualSuggestionText:""}return this.isInputFocused?"":this.placeholder}get getEnableTrendingQueryClearable(){return!!(this.options?.enableTrendingQueryClearable&&this.contextualSuggestionText&&this.isInputFocused)}get getEnableConditionalSearchButtonOnRight(){return!this.isRuby&&!!(this.options?.enableConditionalSchBtn&&this.searchButtonOnLeft&&(this.contextualSuggestionText&&this.isInputFocused||""!==this.value))}get getEnableHighlightTrendingQuery(){return!!(this.options?.enableHighlightTrendingQuery&&this.contextualSuggestionText&&this.isInputFocused)}get enableSearchSuggestionGhostTemplate(){if(this.options&&this.options.enableContextualSuggest&&this.contextualSuggestionText&&!this.value){const e=!this.isInputFocused||this.options.contextualSuggestOnFocus;return e&&d.M0.addOrUpdateTmplProperty("ContextualSB","2"),e}return!1}initialize(){if(!this.options)return;if(this.searchActionUrl=this.options.searchActionUrl,this.isRuby?this.brightSearchInDarkMode=0:this.brightSearchInDarkMode=this.options.brightSearchInDarkMode?this.options.brightSearchInDarkMode:0,this.hoverBgHighlight=this.options.hoverBgHighlight,this.populateTelemetryTags(),this.populateFormParameters(),(0,u.BW)("SearchBox"),(0,g.c)((()=>{const e=(0,m.Ou)(),t=e[u.nz+"SearchBox"],o=t&&void 0===e.isSSRAborted;(0,u.o_)("SearchBox",!1,o?t:void 0,!o)})),this.options.disableArrowUpDownAS&&document.addEventListener("keydown",this.keyDownHandler,{capture:!0}),this.options.enableContextualSuggest&&!this.options.trendingSearchConfig){document.addEventListener(A,this.onContextualSuggestionChange);const e=new CustomEvent("commonSearchBoxConnecteState",{detail:{connected:!0}});document.dispatchEvent(e),this.options?.contextualSuggestRotate&&document.addEventListener("visibilitychange",this.onVisibilitychange)}this.isInputFocused=this.options.autofocus||!1,this.searchButtonOnLeft=!!this.isRuby||!this.options.searchButtonOnRight;const e=s.jG.CurrentRequestTargetScope&&"kids"===s.jG.CurrentRequestTargetScope.audienceMode;this.enableAPIAutoSuggestions=this.options&&!0===this.options.enableWebAPISuggetion&&!e,this.options.enableAPIASonMobile||(this.enableAPIAutoSuggestions=this.enableAPIAutoSuggestions&&!this.isMobile()&&!this.isTablet())}handleTextInput(){this.inputElement&&null!==this.inputElement.value&&(this.value=this.inputElement.value)}populateTelemetryTags(){const e={content:{headline:"",type:l.uH.TextSearch},behavior:l.wu.TextSearch,type:l.c9.SearchBox};this.searchBoxTelemetryTags=new p.D({...e,name:"headersearch"}).getMetadataTag(),this.inputTelemetryTag=new p.D({...e,name:"search_websearch",action:l.Aw.Submit}).getMetadataTag(),this.buttonTelemetryTag=new p.D({name:"searchinput",behavior:l.wu.TextSearch}).getMetadataTag(),this.deepSearchButtonTelemetryTag=new p.D({name:"deepsearchinput",behavior:l.wu.Undefined}).getMetadataTag(),this.clearTelemetryTag=new p.D({name:"clear",behavior:l.wu.Close,content:{type:l.uH.TextSearch}}).getMetadataTag()}async delayInitAutosuggest(){await(0,_.F_)(),window.requestAnimationFrame((()=>{this.initAutosuggest()}))}async delayInitTrendingQuery(){await(0,_.F_)(),window.requestAnimationFrame((()=>{this.getTrendingSearchData()}))}initAutosuggest(){if(!this.options||!this.options.autoSuggest||!this.options.searchActionUrl)return;const e="AutoSuggest.Service.Elements.";(0,y.Z)(window,e+"Input",this.inputElement),(0,y.Z)(window,e+"Form",this.formElement),this.formElement.addEventListener("autosuggestRendered",this.onAutosuggestRendered),this.formElement.addEventListener("autosuggestShown",this.onAutosuggestShown),this.formElement.addEventListener("autosuggestHide",this.onAutosuggestHidden),(0,a.fF)(this.getAutoSuggestInitProps()),this.isAutoSuggestInitialized=!0}getAutoSuggestInitProps(){return{autoSuggestProperties:this.options&&this.options.autoSuggest||{},requestId:d.M0.getRequestId(),telemetryCallback:d.M0&&d.M0.sendActionEvent.bind(d.M0),suggestionClickCallback:this.resetFormAfterSubmit,action:this.options&&this.options.searchActionUrl||"",searchBoxContext:{inputRef:this.inputElement,formRef:this.formElement},isDarkMode:!this.options?.disableDarkTheme&&!this.options?.brightSearchInDarkMode&&matchMedia("(prefers-color-scheme: dark)").matches,enableDetailLogs:this.options&&this.options.enableASDetailLog||!1}}async delayInitAPIAutosuggest(){this.isAutoSuggestInitialized||this.enableLoadAPIAutoSuggestBox||this.options?.autoSuggest?.delayBind!==i.pb.OnPageLoad?(await(0,_.F_)(),window.requestAnimationFrame((()=>{this.delayStartInitAPIAutoSuggestBox=!0,!this.isAutoSuggestInitialized&&!this.enableLoadAPIAutoSuggestBox&&this.options&&this.options.autoSuggest&&3===this.options.autoSuggest.delayBind&&this.options.autoSuggest.businessSearchResultsEnabled&&this.options.enablePreLoadEnterpriseAS&&this.initAPIAutoSuggest()}))):this.initAPIAutoSuggest()}initAPIAutoSuggest(){this.formElement.addEventListener("renderAPIAutoSuggest",this.onAPIAutosuggestRendered),this.formElement.addEventListener("showAPIAutoSuggest",this.onAPIAutosuggestShown),this.formElement.addEventListener("cleanAPIAutoSuggest",this.onAPIAutosuggestHidden),this.isAutoSuggestInitialized=!0,this.enableLoadAPIAutoSuggestBox=!0}addOrUpdateSearchParameter(e,t){if(!this.formElement||!e||!t)return;const o=this.formElement.getElementsByTagName("input").namedItem(e);if(o)o.value=t;else{const o=document.createElement("input");o.type="hidden",o.name=e,o.value=t,this.formElement.appendChild(o)}}onSubmit(e=!1,t=!1){const o=!this.inputElement.value&&this.contextualSuggestionText&&this.options?.contextualSuggestAsDefault;if(this.disableSubmit&&!o)return;if(o){this.value=this.contextualSuggestionText||"",this.inputElement.value=this.contextualSuggestionText||"";const e=this.contextualSuggestionSlug&&this.options?.adContextualSuggestFormCode?this.options.adContextualSuggestFormCode:this.options?.contextualSuggestFormCode;e&&this.addOrUpdateSearchParameter("form",e.toString())}t&&(this.options?.enableDefaultDeepSearchQry&&!this.inputElement.value&&(this.value=this.options.localizedStrings.deepSearchQueryText||"",this.inputElement.value=this.options.localizedStrings.deepSearchQueryText||""),this.addOrUpdateSearchParameter("shm","cr"),this.addOrUpdateSearchParameter("form","NTDPSH")),this.inputElement.value||d.M0&&d.M0.sendActionEvent(this.formElement,l.Aw.Click,l.wu.NoSearchQuerySubmit);const n=h.u.get(this.formElement);if(n&&(n.contract.destinationUrl=this.options&&this.options.searchActionUrl||"",o&&(n.contract.content={...n.contract.content,headline:"Contextual Search"}),h.u.setAttribute(this.formElement,n.contract)),d.M0&&d.M0.sendActionEvent(this.formElement,l.Aw.Submit,l.wu.TextSearch),e){const e=this.formElement.target;this.formElement.target="_blank",this.formElement.submit(),window.setTimeout((()=>{this.formElement.target=e}),0)}else this.formElement.submit();this.resetFormAfterSubmit(e)}onClear(e){d.M0.sendActionEvent(e.detail)}clearSearch(e){e.stopPropagation(),this.options?.enableTrendingQueryClearable&&this.contextualSuggestionText&&this.value&&this.contextualSuggestionText===this.value?(this.inputElement.value="",this.value="",this.contextualSuggestionText=""):this.value?(this.inputElement.value="",this.value=""):this.options?.enableTrendingQueryClearable&&this.contextualSuggestionText&&(this.contextualSuggestionText=""),this.inputElement.dispatchEvent(new Event("input")),this.inputElement.focus(),d.M0.sendActionEvent(e.currentTarget)}scrollPageAndMarkDone(){this.inputElement.blur(),this.keyDownScrollHappened=!0}async delayWarmBingConnections(){await(0,_.F_)(),this.warmBingConnections()}warmBingConnections(){if(!this.options)return;const{warmWwwBing:e,warmRBingStatics:t}=this.options;if(!t&&!e)return;const o=(e,t=!1)=>{const o=document.createElement("link");o.rel="preconnect",o.href=e,t&&o.setAttribute("crossorigin",""),document.head.appendChild(o)};t&&(o("https://r.bing.com"),o("https://r.bing.com",!0),o("https://r.msftstatic.com"),o("https://r.msftstatic.com",!0)),e&&o("https://www.bing.com"),this.isBingUrlWarmed=!0}populateFormParameters(){this.options&&(this.parameterMap={},this.options.formParameters&&this.options.formParameters.forEach&&this.options.formParameters.forEach((e=>{e&&(this.parameterMap[e.name]=e.value)})),this.options.additionalFormParamMap&&Object.assign(this.parameterMap,this.options.additionalFormParamMap),this.formParameters=Object.entries(this.parameterMap).map((e=>{const[t,o]=e;return{name:t,value:o}})))}onCodexBingClick(e){if(!this.options)return;const t=this.options.target||"_blank";this.inputElement?.value&&this.options.codexBingSearchUrl?(d.M0.sendActionEvent(e.currentTarget,l.Aw.Click,l.wu.TextSearch),window.open(`${this.options.codexBingSearchUrl}&q=${encodeURIComponent(this.inputElement.value)}`,t)):this.options.codexBingUrl&&(d.M0.sendActionEvent(e.currentTarget,l.Aw.Click,l.wu.Navigate),window.open(this.options.codexBingUrl,t))}isMobile(){return"phone"===(T.Al&&T.Al.ClientSettings&&T.Al.ClientSettings.deviceFormFactor)}isTablet(){return"tablet"===(T.Al&&T.Al.ClientSettings&&T.Al.ClientSettings.deviceFormFactor)}getTrendingSearchData(){if(!this.options?.enableContextualSuggest||!this.options?.trendingSearchConfig||this.trendingSearchesRequested||s.jG.LocaleDisplayLanguageString!==s.jG.LocaleContentMarketString)return;this.trendingSearchesRequested=!0;(0,k.K)(this.options.trendingSearchConfig).getTrendingSearches().then((e=>{if(!e||!e.length||!e[0])return;let t=e[0];this.options?.enableRandomTrendingSearch&&(t=e[Math.floor(Math.random()*e.length)]),this.updateContextualSuggestion({suggestion:t.query,isAds:t.isAds,suggestions:e.map(((e,t)=>({id:t+"",suggestionText:e.query})))})})).catch((e=>(0,f.H)(b.QWk,"Fetch trending searches promise failed",e)))}disconnectedCallback(){super.disconnectedCallback(),this.disableSubmit||document.body.removeEventListener("keypress",this.onDocumentKeypress,{capture:!0}),this.options&&this.options.disableArrowUpDownAS&&document.removeEventListener("keydown",this.keyDownHandler,{capture:!0}),this.options&&this.options.enableContextualSuggest&&!this.options.trendingSearchConfig&&(document.removeEventListener(A,this.onContextualSuggestionChange),this.options.contextualSuggestRotate&&document.removeEventListener("visibilitychange",this.onVisibilitychange)),this.enableAutoSuggestion&&this.formElement&&!this.enableAPIAutoSuggestions&&(this.formElement.removeEventListener("autosuggestRendered",this.onAutosuggestRendered),this.formElement.removeEventListener("autosuggestShown",this.onAutosuggestShown),this.formElement.removeEventListener("autosuggestHide",this.onAutosuggestHidden)),this.enableAutoSuggestion&&this.formElement&&this.enableLoadAPIAutoSuggestBox&&(this.formElement.removeEventListener("showAPIAutoSuggest",this.onAPIAutosuggestShown),this.formElement.removeEventListener("cleanAPIAutoSuggest",this.onAPIAutosuggestHidden),this.formElement.removeEventListener("renderAPIAutoSuggest",this.onAPIAutosuggestRendered))}};(0,n.gn)([v.LO],P.prototype,"options",void 0),(0,n.gn)([v.LO],P.prototype,"formParameters",void 0),(0,n.gn)([v.LO],P.prototype,"fakeFocused",void 0),(0,n.gn)([v.LO],P.prototype,"searchVisible",void 0),(0,n.gn)([v.LO],P.prototype,"usePageBreakpoints",void 0),(0,n.gn)([v.LO],P.prototype,"isRuby",void 0),(0,n.gn)([v.LO],P.prototype,"searchIcon",void 0),(0,n.gn)([v.LO],P.prototype,"enableWarmBingUrl",void 0),(0,n.gn)([v.LO],P.prototype,"enableAutoSuggestion",void 0),(0,n.gn)([v.LO],P.prototype,"autosuggestShown",void 0),(0,n.gn)([v.LO],P.prototype,"showAPIAutoSuggestBox",void 0),(0,n.gn)([v.LO],P.prototype,"enableLoadAPIAutoSuggestBox",void 0),(0,n.gn)([v.LO],P.prototype,"isInputFocused",void 0),(0,n.gn)([v.LO],P.prototype,"actionElementFocused",void 0),(0,n.gn)([v.LO],P.prototype,"inputElement",void 0),(0,n.gn)([v.LO],P.prototype,"clearElement",void 0),(0,n.gn)([v.LO],P.prototype,"searchElement",void 0),(0,n.gn)([v.LO],P.prototype,"searchOptionsElement",void 0),(0,n.gn)([v.LO],P.prototype,"searchBoxTelemetryTags",void 0),(0,n.gn)([v.LO],P.prototype,"buttonTelemetryTag",void 0),(0,n.gn)([v.LO],P.prototype,"deepSearchButtonTelemetryTag",void 0),(0,n.gn)([v.LO],P.prototype,"inputTelemetryTag",void 0),(0,n.gn)([v.LO],P.prototype,"clearTelemetryTag",void 0),(0,n.gn)([v.LO],P.prototype,"bingASUpsellTelemetryTag",void 0),(0,n.gn)([v.LO],P.prototype,"isNextWordASMode",void 0),(0,n.gn)([v.LO],P.prototype,"searchActionUrl",void 0),(0,n.gn)([v.LO],P.prototype,"placeholder",void 0),(0,n.gn)([v.LO],P.prototype,"defaultPlaceholder",void 0),(0,n.gn)([v.LO],P.prototype,"contextualSuggestionText",void 0),(0,n.gn)([v.LO],P.prototype,"contextualSuggestionSlug",void 0),(0,n.gn)([v.LO],P.prototype,"contextualSuggestionList",void 0),(0,n.gn)([v.LO],P.prototype,"contextualSuggestionsInAS",void 0),(0,n.gn)([v.LO],P.prototype,"selectBoxOptionTelemetryTag",void 0),(0,n.gn)([v.LO],P.prototype,"value",void 0),(0,n.gn)([v.LO],P.prototype,"searchButtonOnLeft",void 0),(0,n.gn)([v.LO],P.prototype,"placeholderColor",void 0),(0,n.gn)([v.LO],P.prototype,"hoverFocusDeeperShadowStyle",void 0),(0,n.gn)([v.LO],P.prototype,"searchIconTreatment",void 0),(0,n.gn)([v.LO],P.prototype,"searchIconColor",void 0),(0,n.gn)([v.LO],P.prototype,"brightSearchInDarkMode",void 0),(0,n.gn)([v.LO],P.prototype,"enableDeepSearch",void 0),(0,n.gn)([v.LO],P.prototype,"enableDefaultDeepSearchQry",void 0),(0,n.gn)([v.LO],P.prototype,"enableDimMask",void 0),(0,n.gn)([v.LO],P.prototype,"dimMaskOpacity",void 0),(0,n.gn)([v.LO],P.prototype,"hoverBgHighlight",void 0),(0,n.gn)([v.LO],P.prototype,"enableConditionalSchBtn",void 0),(0,n.gn)([v.LO],P.prototype,"enableTrendingQueryClearable",void 0),(0,n.gn)([v.LO],P.prototype,"enableHighlightTrendingQuery",void 0),(0,n.gn)([v.LO],P.prototype,"outsideCodexBtnFullPageLayout",void 0),(0,n.gn)([v.LO],P.prototype,"largerSearchBox",void 0),(0,n.gn)([v.LO],P.prototype,"largerSearchBoxTweak",void 0),(0,n.gn)([v.lk],P.prototype,"getPlaceholder",null),(0,n.gn)([v.lk],P.prototype,"getEnableTrendingQueryClearable",null),(0,n.gn)([v.lk],P.prototype,"getEnableConditionalSearchButtonOnRight",null),(0,n.gn)([v.lk],P.prototype,"getEnableHighlightTrendingQuery",null),(0,n.gn)([v.lk],P.prototype,"enableSearchSuggestionGhostTemplate",null),P=(0,n.gn)([w.b],P);var I=o(67830),B=o(11252),F=o(958),L=o(27460),D=o(42689),M=o(74449),R=o(26738),O=o(17993),j=o(22674),U=o(53131),H=o(62734),N=o(40009),z=o(78923),W=o(81239),G=o(29717),q=o(22134),V=o(78493),K=o(91689),Y=o(69393);const Q=`${(0,s.Yq)().StaticsUrl}/latest/icons/search-box-icons/search_maximal_light.png`,J=`${(0,s.Yq)().StaticsUrl}/latest/icons/search-box-icons/search_maximal_dark.png`,X=`${(0,s.Yq)().StaticsUrl}/latest/icons/search-box-icons/search_bing.svg`,Z=`${(0,s.Yq)().StaticsUrl}/latest/icons/search-box-icons/search_g.svg`,ee=`${(0,s.Yq)().StaticsUrl}/latest/icons/search-box-icons/search_icons_light.png`,te=`${(0,s.Yq)().StaticsUrl}/latest/icons/search-box-icons/search_icons_dark.png`,oe=z.i`
@media (max-width:343px){.core{width:var(--search-box-width,310px)}}@media (min-width:343px) and (max-width:644px){.core{width:var(--search-box-width,343px)}}@media (min-width:644px) and (max-width:956px){.core{width:var(--search-box-width,400px)}.core.core-info-bc{width:343px}}@media (min-width:956px) and (max-width:1268px){.core{width:var(--search-box-width,528px)}}@media (min-width:1268px){.core{width:var(--search-box-width,760px)}.core.wd_search{width:940px}}`.withBehaviors(new W.w("outsideCodexBtnFullPageLayout",!0,z.i` @media (max-width:644px){#srchfrm{width:343px}.core{width:283px}}@media (min-width:644px) and (max-width:956px){#srchfrm{width:400px}.core{width:340px}}@media (min-width:956px) and (max-width:1268px){#srchfrm{width:528px}.core{width:468px}}@media (min-width:1268px){#srchfrm{width:760px}.core{width:700px}}@media (max-width:480px){.core{width:343px}}`),new W.w("largerSearchBox",!0,z.i` @media (min-width:644px) and (max-width:956px){.core{width:440px}}@media (min-width:956px) and (max-width:1268px){.core{width:568px}}@media (min-width:1268px){.core{width:800px}.core.wd_search{width:940px}}`.withBehaviors(new W.w("outsideCodexBtnFullPageLayout",!0,z.i` @media (min-width:644px) and (max-width:956px){#srchfrm{width:440px}.core{width:380px}}@media (min-width:956px) and (max-width:1268px){#srchfrm{width:568px}.core{width:508px}}@media (min-width:1268px){#srchfrm{width:800px}.core{width:740px}}`),new W.w("largerSearchBoxTweak",1,z.i` @media (min-width:956px) and (max-width:1060px){.core{width:548px}}@media (min-width:644px) and (max-width:920px){.core{width:420px}}`.withBehaviors(new W.w("outsideCodexBtnFullPageLayout",!0,z.i` @media (min-width:956px) and (max-width:1060px){#srchfrm{width:548px}.core{width:488px}}@media (min-width:644px) and (max-width:920px){#srchfrm{width:420px}.core{width:360px}}`)))))),ne=z.i`
${(0,I.vW)(null,I.K$.c1)}{.core{width:343px}}${(0,I.eH)(I.K$.c2)}{.core{width:400px}.core.core-info-bc{width:343px}}${(0,I.eH)(I.K$.c3)}{.core{width:528px}}${(0,I.vW)(I.K$.c4,null)}{.core{width:760px}.core.wd_search{width:940px}}`.withBehaviors(new W.w("outsideCodexBtnFullPageLayout",!0,z.i` ${(0,I.vW)(null,I.K$.c1)}{#srchfrm{width:343px}.core{width:283px}}${(0,I.eH)(I.K$.c2)}{#srchfrm{width:400px}.core{width:340px}}${(0,I.eH)(I.K$.c3)}{#srchfrm{width:528px}.core{width:468px}}${(0,I.vW)(I.K$.c4,null)}{#srchfrm{width:760px}.core{width:700px}}@media (max-width:480px){.core{width:343px}}`),new W.w("largerSearchBox",!0,z.i` ${(0,I.eH)(I.K$.c2)}{.core{width:440px}}${(0,I.eH)(I.K$.c3)}{.core{width:568px}}${(0,I.vW)(I.K$.c4,null)}{.core{width:800px}.core.wd_search{width:940px}}`.withBehaviors(new W.w("outsideCodexBtnFullPageLayout",!0,z.i` ${(0,I.eH)(I.K$.c2)}{#srchfrm{width:440px}.core{width:380px}}${(0,I.eH)(I.K$.c3)}{#srchfrm{width:568px}.core{width:508px}}${(0,I.vW)(I.K$.c4,null)}{#srchfrm{width:800px}.core{width:740px}}`),new W.w("largerSearchBoxTweak",1,z.i` @media ${(0,I.aK)(I.K$.c3,null)} and (max-width:1060px){.core{width:548px}}@media ${(0,I.aK)(I.K$.c2,null)} and (max-width:920px){.core{width:420px}}`.withBehaviors(new W.w("outsideCodexBtnFullPageLayout",!0,z.i` @media ${(0,I.aK)(I.K$.c3,null)} and (max-width:1060px){#srchfrm{width:548px}.core{width:488px}}@media ${(0,I.aK)(I.K$.c2,null)} and (max-width:920px){#srchfrm{width:420px}.core{width:360px}}`)))))),ie=z.i`
form.autosuggestShown.as .core .search-btn{border-radius:0 var(--search-box-radius) 0 0}.core .search-btn{border-radius:0 var(--search-box-radius) var(--search-box-radius) 0}form.autosuggestShown.as .core.search-btn-on-left .search-btn{border-radius:var(--search-box-radius) 0 0 0}.core.search-btn-on-left .search-btn{border-radius:var(--search-box-radius) 0 0 var(--search-box-radius);background:transparent}.core.search-btn-on-left .conditional-sh-container .search-btn{border-radius:0 var(--search-box-radius) var(--search-box-radius) 0}form.autosuggestShown.as .core.search-btn-on-left .conditional-sh-container .search-btn{border-radius:0 var(--search-box-radius) 0 0}.search-btn-on-right.core:not(.mobile){background:linear-gradient(to left,${F.Av} 10px,${L.s} 50px)}`,ae=z.i`
form.autosuggestShown.as .core .search-btn{border-radius:var(--search-box-radius) 0 0 0}.core .search-btn{border-radius:var(--search-box-radius) 0 0 var(--search-box-radius)}form.autosuggestShown.as .core.search-btn-on-left .search-btn{border-radius:0 var(--search-box-radius) 0 0;}.core.search-btn-on-left .search-btn{border-radius:0 var(--search-box-radius) var(--search-box-radius) 0;background:transparent}.core.search-btn-on-left .conditional-sh-container .search-btn{border-radius:var(--search-box-radius) 0 0 var(--search-box-radius)}form.autosuggestShown.as .core.search-btn-on-left .conditional-sh-container .search-btn{border-radius:var(--search-box-radius) 0 0 0}.bing-upsell-container{right:unset;left:-54px}.search-btn-on-right.core:not(.mobile){background:linear-gradient(to right,${F.Av} 10px,${L.s} 50px)}`,re=z.i`
.core{border:1px solid ${(0,B.Y)()?Y.YP.forcedColorLink:Y.YP.selectedBackground}}@-moz-document url-prefix(){.core .search-btn::part(control){border:none}.clear-btn::part(control){border:none}}`,se=z.i` .core #q::placeholder{color:#919191}`.withBehaviors((0,G.Uu)(z.i` .core #q:not(.disable-dk)::placeholder{color:#a19f9d;opacity:unset}`)),ce=z.i` .core #q.highlight:focus::placeholder{color:${D.C}}`,de=z.i`
${ce}
`.withBehaviors((0,G.Uu)(z.i` .core #q:not(.disable-dk)::placeholder{color:#ccc}.core #q.highlight:not(.disable-dk):focus::placeholder{color:${D.C}}`)),le=z.i` form:not(.autosuggestShown) .core:hover,form:not(.autosuggestShown) .core:focus-within{box-shadow:0 0 0 1px rgba(0,0,0,.1),0 2px 4px 1px rgba(0,0,0,.18)}`.withBehaviors((0,G.Uu)(z.i` form:not(.autosuggestShown) .core:hover,form:not(.autosuggestShown) .core:focus-within{box-shadow:0 2px 4px 1px rgba(0,0,0,.35)}`)),pe=z.i` .search-icon-asset-svg{display:block;width:24px;height:24px;background-color:${M.Q};mask:url(${X}) no-repeat center;-webkit-mask:url(${X}) no-repeat center},`.withBehaviors((0,G.Uu)(z.i` .search-icon-asset-svg{background-color:${D.C}}`)),he=z.i` .search-icon-asset-svg{display:block;width:24px;height:24px;background-color:${M.Q};mask:url(${Z}) no-repeat center;-webkit-mask:url(${Z}) no-repeat center}`.withBehaviors((0,G.Uu)(z.i` .search-icon-asset-svg{background-color:${D.C}}`)),ue=z.i` .search-icon-asset-img{content:url(${Q})}`.withBehaviors((0,G.Uu)(z.i` .search-icon-asset-img{content:url(${J})}`)),ge=z.i` .core.search-btn-on-left .search-btn.stealth > svg{fill:#174ae4}.core.search-btn-on-left .search-btn.stealth > .search-icon-asset-svg{background-color:#174ae4}`.withBehaviors((0,G.Uu)(z.i` .core.search-btn-on-left .search-btn.stealth > svg{fill:#a2b7f4}.core.search-btn-on-left .search-btn.stealth > .search-icon-asset-svg{background-color:#a2b7f4}`)),me=` :host{--accent-fill-rest:#0078d4;--neutral-layer-floating:#fff}.core{background:#ffffff}.core.search-btn-on-left .search-btn.stealth > .search-icon-asset-img{content:url(${Q})}.clear-container #clear_srch svg{fill:#262626}`,fe=`\n.deep-search-btn{background-color:#fff;border:1px solid rgba(0,0,0,.1);color:#174ae4}.deep-search-btn:hover{background-color:#f9f9f9;box-shadow:0 3.2px 7.2px 0 rgba(0,0,0,.13),0 .6px 1.8px 0 rgba(0,0,0,.1)}.deep-search-btn #b_sh_btn_isprt{background-image:url(${ee});\n}\n`,be=z.i`
.core .conditional-sh-container .search-btn.conditional > svg{fill:${R.go}}.core .conditional-sh-container .search-btn.conditional > .search-icon-asset-svg{background-color:${R.go};height:24px;width:24px}`,ye=z.i`
.core.search-btn-on-left .clear-container{margin-inline-end:2px}.divider{border-left:1px solid #dadce0;height:100%;margin-inline-start:10px}.conditional-sh-container{display:flex;align-items:center;z-index:2;height:inherit}.conditional{background:transparent;height:30px;min-width:20px}.core .conditional-sh-container .search-btn.conditional::part(control){padding-right:0 !important;padding-left:0 !important;padding-inline-start:8px !important;padding-inline-end:16px !important}.core .conditional-sh-container .search-btn.conditional > svg,.core .conditional-sh-container .search-btn.conditional > .search-icon-asset-svg{height:20px;width:20px}${be}
`.withBehaviors((0,G.Uu)(z.i` .core .conditional-sh-container .search-btn.conditional:not(.disable-dk) > svg{fill:${D.C}}.core .conditional-sh-container .search-btn.conditional:not(.disable-dk) > .search-icon-asset-svg{background-color:${D.C}}`)),we=z.i`
`.withBehaviors((0,G.Uu)(z.i` ${me} ${fe} ${ce} ${be} .core.search-btn-on-left .search-btn.stealth > svg{fill:#707070}.core.search-btn-on-left .search-btn.stealth > .search-icon-asset-svg{background-color:#707070}.core #q{color:#262626}.core #q:not(.disable-dk)::placeholder{color:#757575;opacity:1}.core #q.highlight:not(.disable-dk):focus::placeholder{color:#444}.core .conditional-sh-container .search-btn.conditional:not(.disable-dk) > svg{fill:${R.go}}.core .conditional-sh-container .search-btn.conditional:not(.disable-dk) > .search-icon-asset-svg{background-color:${R.go}}`)),xe=z.i`
`.withBehaviors((0,G.zw)(z.i` .core:hover:not(:focus-within){background-color:#f5f5f5}`),new W.w("brightSearchInDarkMode",1,z.i` .core:hover:not(:focus-within){background-color:#f5f5f5}`),new W.w("brightSearchInDarkMode",2,z.i` .core:hover:not(:focus-within){background-color:#f5f5f5}`)),ve=z.i`
`.withBehaviors((0,G.Uu)(z.i` ${me} ${fe} ${ce} ${be} .core.search-btn-on-left .search-btn.stealth > svg{fill:#5f6368}.core.search-btn-on-left .search-btn.stealth > .search-icon-asset-svg{background-color:#5f6368}.core #q{color:#000000}.core #q:not(.disable-dk)::placeholder{color:#5f6368;opacity:1}.core #q.highlight:not(.disable-dk):focus::placeholder{color:#444}.core .conditional-sh-container .search-btn.conditional:not(.disable-dk) > svg{fill:#262626}.core .conditional-sh-container .search-btn.conditional:not(.disable-dk) > .search-icon-asset-svg{background-color:#262626}`)),_e=z.i` ${fe} :host{position:relative}.dpsb-wrp{position:absolute;top:4px;right:-147px;z-index:601}.deep-search-btn::part(control){height:24px;padding:0}.deep-search-btn::part(content){display:flex}.deep-search-btn{position:relative;height:auto;background-color:#fff;border:1px solid rgba(0,0,0,.1);white-space:nowrap;padding:6px 16px 6px 12px;border-radius:20px;display:flex;margin:0 0 0 16px;vertical-align:top;cursor:pointer;color:#174ae4}.deep-search-btn:hover{background-color:#f9f9f9;box-shadow:0 3.2px 7.2px 0 rgba(0,0,0,.13),0 .6px 1.8px 0 rgba(0,0,0,.1)}.deep-search-btn #b_sh_btn_isprt{background-image:url(${ee});background-size:515px 46px;background-repeat:no-repeat;background-position:-457px 0;width:24px;height:24px;margin-right:6px;display:inline-block}.deep-search-btn #b_sh_btn_text{line-height:24px;font-size:14px}`.withBehaviors((0,G.Uu)(z.i` .deep-search-btn{background:#4a4a4a;color:#a2b7f4}.deep-search-btn #b_sh_btn_isprt{background-image:url(${te})}.deep-search-btn:hover{border-color:rgba(255,255,255,.3);background:#484644;box-shadow:0 3.2px 7.2px 0 rgba(0,0,0,.13),0 .6px 1.8px 0 rgba(0,0,0,.1)}`)),Se=z.i` .core.core-search-kumo .search-btn.conditional > .search-icon-asset-img{filter:${q.MK} !important}form.kumo.autosuggestShown.as .kumo-wrapper{background:${q.ZZ}}.form.kumo{box-shadow:0px 8px 32px 0px ${q.o2}}form.kumo.autosuggestShown{background:${q.ZZ};outline:1px solid ${q.Sd};z-index:${K.K.Above.toString()}}.core-search-kumo .search-btn::part(control){color:${q.kt}}.core.core-search-kumo{border:8px solid rgba(0,0,0,0.4);background:${q.H1};box-shadow:0px 1px 2px 0px ${q.nP} inset}form.kumo.autosuggestShown .core.core-search-kumo{border-color:rgba(0,0,0,1)}.core.core-search-kumo svg{fill:${q.kt}}.core.core-search-kumo .search-btn.stealth > .search-icon-asset-img{filter:${q.MK} !important;background-color:transparent;content:unset}.core.search-btn-on-left.core-search-kumo #q{caret-color:${q.n9};color:${q.Vq}}.core.core-search-kumo .clear-container #clear_srch svg{fill:${q.kt}}}`,Ce=z.i`
${(0,I.vW)(null,I.K$.c1)}{.core.core-search-kumo{width:336px}}${(0,I.eH)(I.K$.c2)}{.core.core-search-kumo{width:424px}}${(0,I.eH)(I.K$.c3)}{.core.core-search-kumo{width:512px}}${(0,I.vW)(I.K$.c4,null)}{.core.core-search-kumo{width:600px}}`,Te=z.i`
@media (max-width:343px){.core.core-search-kumo{width:var(--search-box-width,248px)}}@media (min-width:343px) and (max-width:644px){.core.core-search-kumo{width:var(--search-box-width,336px)}}@media (min-width:644px) and (max-width:956px){.core.core-search-kumo{width:var(--search-box-width,424px)}}@media (min-width:956px) and (max-width:1268px){.core.core-search-kumo{width:var(--search-box-width,512px)}}@media (min-width:1268px){.core.core-search-kumo{width:var(--search-box-width,600px)}}`,ke=z.i`
.core.search-btn-on-right.core-search-kumo .search-btn,form.autosuggestShown.as .core.core-search-kumo .search-btn{border-radius:var(--search-box-radius) 0 0 var(--search-box-radius);background:transparent}.core.search-btn-on-left.core-search-kumo .search-btn{border-radius:var(--search-box-radius) 0 0 var(--search-box-radius);background:transparent;height:48px}.search-btn-on-right.core:not(.core-search-kumo).not(.mobile){background:linear-gradient(to left,${F.Av} 10px,${L.s} 50px)}`,Ae=z.i`
.core.search-btn-on-right.core-search-kumo .search-btn,form.autosuggestShown.as .core.core-search-kumo .search-btn{border-radius:0 var(--search-box-radius) var(--search-box-radius) 0;background:transparent}.search-btn-on-right.core:not(.core-search-kumo).not(.mobile){background:linear-gradient(to right,${F.Av} 10px,${L.s} 50px)}`,$e=z.i`
.core.core-search-kumo .conditional-sh-container{margin-inline:0px}.core.core-search-kumo .conditional-sh-container .search-btn.conditional::part(control){border-radius:0px 24px 24px 0px;min-width:44px}.core.core-search-kumo .search-btn.conditional > .search-icon-asset-img{filter:${q.f9} !important}`,Ee=z.i`
.form.kumo{height:64px;box-shadow:0px 8px 32px 0px ${q.Jh};border-radius:32px;backdrop-filter:blur(60px)}.kumo-wrapper{height:64px}form.kumo.autosuggestShown.nwas{border-radius:24px}form.kumo.autosuggestShown.as .kumo-wrapper{height:64px;border-radius:32px 32px 0px 0px;backdrop-filter:blur(60px);background:${q.Pj};position:relative;z-index:${K.K.Flyout.toString()};clip-path:inset(-1px round 24px 24px 0px 0px) !important}.core-search-kumo .search-btn::part(control){color:${q.pt}}.core-search-kumo .search-btn::part(control):active,.core-search-kumo .search-btn:active{background-color:initial}.core.core-search-kumo{border-radius:50px;--search-box-radius:44px;border:8px solid rgba(255,255,255,0.4);background:${q.HU};box-shadow:0px 1px 2px 0px ${q.Jh} inset;box-sizing:border-box}form.kumo.autosuggestShown .core.core-search-kumo{border-color:rgba(255,255,255,1)}.core.core-search-kumo .clear-btn{min-width:20px;height:32px;background:transparent;margin-top:1px}.core.core-search-kumo .clear-container{margin-inline-end:16px;height:32px}.core.core-search-kumo .clear-container #clear_srch svg{fill:${q.pt}}.core.core-search-kumo .search-btn.stealth > .search-icon-asset-img{filter:${q.f9}}.core.core-search-kumo .search-btn::part(control):focus-visible{box-shadow:0 0 0 2px inset ${O.yG}}.core.search-btn-on-left.core-search-kumo .search-btn::part(control){color:${q.pt};border:none;height:48px;padding-top:2px}.core.search-btn-on-right.core-search-kumo .search-btn::part(control){padding-inline-start:14px !important;padding-inline-end:8px !important;border-radius:unset}.core.search-btn-on-left.core-search-kumo #q{padding-inline-start:6px;font-weight:${V.TU};line-height:${V.oK};font-size:${V.Rt};caret-color:${q.KG};color:${q.qd}}.core.search-btn-on-right.core-search-kumo #q:not(:placeholder-shown){color:var(--Neutral-Stroke-Focus-1-Rest)}.core.search-btn-on-right.core-search-kumo #q{font-size:18px;box-shadow:0px 1px 2px 0px ${q.Jh} inset;border-radius:50px;background:#F6F9FC;margin:8px;margin-right:initial;caret-color:rgba(0,30,68,0.10);padding-left:24px}.core.core-search-kumo .search-btn > svg{height:20px;width:20px;color:${q.eH}}.core.core-search-kumo svg{fill:${q.eH}}form.kumo.autosuggestShown{background:${q.Pj};border-radius:24px 24px 0px 0px;outline:1px solid ${q.hL};z-index:${K.K.Above.toString()}}form.kumo.autosuggestShown.as .core{border-radius:24px;--search-box-radius:18px;z-index:${K.K.Overlay.toString()}}form.kumo.autosuggestShown.as #q{border-radius:16px}.core-search-kumo .cc-pa{padding-inline:0}.core.core-search-kumo .search-btn.conditional > .search-icon-asset-img{filter:${q.f9} !important}`.withBehaviors((0,G.Uu)(Se),new j.O(ke,Ae),new W.w("usePageBreakpoints",!1,Te),new W.w("usePageBreakpoints",!0,Ce),new W.w("enableConditionalSchBtn",!0,$e)),Pe=z.i`
:host{display:block}.form{position:relative;height:46px}.search-hidden{visibility:hidden}@keyframes blink{0%{opacity:1}61.55%{opacity:0}}.fake-cursor{background:${D.C};position:absolute;width:1px;top:12px;bottom:12px;margin-inline-start:56px;margin-inline-end:16px;animation:blink 1.3s step-end infinite}.search-btn-on-right.fake-cursor{margin-inline-start:16px}.search-icon{display:flex;justify-content:center;align-items:center}.core{display:flex;height:100%;--search-box-radius:22px;user-select:none;--elevation:4;border-radius:var(--search-box-radius);transition:none;position:relative;overflow:hidden;background:${L.s};font-family:${U.S};${H.XC}}.core #q{-webkit-appearance:none;outline:none;background:transparent;border:0;margin-top:auto;margin-bottom:auto;border:none;padding:calc(${N._5} * 2px + 2px) 12px calc(${N._5} * 2px + 2px) 12px;color:${D.C};font-size:16px;font-weight:400;line-height:24px;width:100%;font-family:inherit;flex:1;padding-inline-start:16px}.core #q:placeholder-shown{text-overflow:ellipsis}.core .search-btn .search-btn-txt{font-size:16px;font-weight:400}.search-btn-on-right.core .search-btn::part(control):active{color:rgb(255,255,255)}.core .search-btn{height:inherit;min-width:auto}.core .search-btn::part(control){width:100%;padding-right:22px !important;padding-left:22px !important}.core.search-btn-on-left .search-btn::part(control){padding-inline-start:16px !important;padding-inline-end:8px !important}.core.search-btn-on-left #q{padding-inline-start:6px}.core.search-btn-on-left .search-btn.stealth > svg{fill:${M.Q}}.core .search-btn.stealth{background:transparent}.core .search-btn > svg{height:24px;width:24px}.core .search-btn.stealth > svg{fill:var(--search-svg-fill,${R.go})}form.autosuggestShown.as .core{border-radius:var(--search-box-radius) var(--search-box-radius) 0 0}form.autosuggestShown.nwas .core{border-radius:var(--search-box-radius)}.core input[type="search"]::-webkit-search-decoration,.core input[type="search"]::-webkit-search-cancel-button,.core input[type="search"]::-webkit-search-results-button,.core input[type="search"]::-webkit-search-results-decoration{display:none}.clear-container{display:flex;align-items:center;z-index:2;height:46px}.bing-upsell-container{position:absolute;right:-54px;top:0px;cursor:pointer}.bing-upsell-btn{background:transparent}.bing-upsell-btn > svg{width:48px;height:48px}@media (max-width:500px){.core.core-bing-chat{width:316px}}.clear-btn{background:transparent;height:30px;min-width:20px}.clear-btn > svg{width:20px;height:20px}.clear-container{display:flex;align-items:center;z-index:2;height:30px;margin-top:auto;margin-bottom:auto;margin-inline-end:21px}.ad-ghost-container{display:flex;position:absolute;width:calc(100% - 70px);padding:calc(${N._5} * 2px + 2px) 12px calc(${N._5} * 2px + 2px) 12px;padding-inline-start:16px;height:100%;box-sizing:border-box;align-items:center;background:transparent;pointer-events:none;-webkit-appearance:none}.search-btn-on-left .ad-ghost-container{margin-inline-start:50px;padding-inline-start:6px;width:calc(100% - 90px)}.ad-ghost-text{color:#767676;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex-shrink:1}.ad-ghost-slug{color:#767676;border:1px solid #767676;margin-left:5px;padding:0 4px;width:fit-content;height:16px;font-size:11px;background-color:transparent;border-radius:3px;text-align:center;flex-shrink:0}.search-mask{position:fixed;left:0;top:0;width:100%;height:100%;pointer-events:none;background-color:transparent;transition:background-color .2s}.search-mask.dim{pointer-events:all;background-color:rgba(0,0,0,var(--bg-opacity))}`.withBehaviors(new W.w("brightSearchInDarkMode",0,z.i` `.withBehaviors((0,G.Uu)(z.i` .core .search-btn.stealth > svg{fill:${D.C}}.core.search-btn-on-left .search-btn.stealth > svg{fill:${D.C}}.core #q:not(.disable-dk)::placeholder{color:#FFFFFF;opacity:0.7}.text-area::placeholder{color:#FFFFFF;opacity:0.7}#footer{color:#F9F9F9}.ad-ghost-text{color:#c9c9c9}.ad-ghost-slug{color:#c9c9c9;border:1px solid #c9c9c9}`))),new j.O(ie,ae),(0,G.vF)(re),new W.w("usePageBreakpoints",!1,oe),new W.w("usePageBreakpoints",!0,ne),new W.w("placeholderColor",5,se),new W.w("hoverFocusDeeperShadowStyle",!0,le),new W.w("searchIconTreatment",1,pe),new W.w("searchIconTreatment",2,he),new W.w("searchIconTreatment",3,ue),new W.w("searchIconColor",1,ge),new W.w("enableDeepSearch",!0,_e),new W.w("enableHighlightTrendingQuery",!0,de),new W.w("enableConditionalSchBtn",!0,ye),new W.w("brightSearchInDarkMode",1,we),new W.w("brightSearchInDarkMode",2,ve),new W.w("hoverBgHighlight",!0,xe),new W.w("isRuby",!0,Ee));var Ie=o(49218),Be=o(93703),Fe=o(41472),Le=o(89150),De=o(39978),Me=o(23423),Re=o.n(Me),Oe=o(22091),je=o.n(Oe),Ue=o(41067),He=o.n(Ue);const Ne=`${(0,s.Yq)().StaticsUrl}/latest/icons/search-box-icons/search_maximal_light.png`,ze=`${(0,s.Yq)().StaticsUrl}latest/icons/search-box-icons/search_ruby.svg`,We=Ie.dy`<input name=${e=>e.name} type="hidden" value=${e=>e.value} />`,Ge=Ie.dy`<div class="fake-cursor ${e=>e.searchButtonOnLeft?"search-btn-on-left":"search-btn-on-right"}"></div>`,qe=Ie.dy`<div class="ad-ghost-container" aria-label="${e=>e.contextualSuggestionText}"><div class="ad-ghost-text" aria-hidden="true">${e=>e.contextualSuggestionText}</div><button class="ad-ghost-slug" aria-hidden="true" tabindex="-1">${e=>e.contextualSuggestionSlug}</button></div>`,Ve=Ie.dy`<span class="divider"></span>`,Ke=Ie.dy`${(0,Be.g)((e=>e?.isRuby),Ie.dy`${Ie.dy.partial(He())}`,Ie.dy`${Ie.dy.partial(je())}`)}`,Ye=Ie.dy`<div class="clear-container cc-pa" style="${e=>""!==e.value||e.getEnableTrendingQueryClearable?"":"display: none"}" ${(0,Fe.i)("clearElement")}><fluent-button class="clear-btn" id="clear_srch" data-targetid="ClearSearchButton" appearance="stealth" title=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.clearButtonTitle} aria-label=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.clearButtonTitle} data-customhandled="true" @mousedown="${e=>e.handActionFocusEvent()}" @mouseup="${e=>e.handActionUnfocusEvent()}" @click="${(e,t)=>e.clearSearch(t.event)}" @keypress=${(e,t)=>e.clearSearch(t.event)} data-t="${e=>e.clearTelemetryTag}" icon-only>${Ke}</fluent-button>${(0,Be.g)((e=>e.getEnableConditionalSearchButtonOnRight),Ve)}</div>`,Qe=Ie.dy` ${e=>e.options&&e.options.childExperienceReferencesWC?.CommonAutoSuggest&&(0,De.Ot)(e.options&&e.options.childExperienceReferencesWC.CommonAutoSuggest,{properties:{formElement:e.formElement,inputElement:e.inputElement,searchIconOnLeft:e.searchButtonOnLeft,suggestionClickCallback:e.resetFormAfterSubmit,contextualSuggestions:e.contextualSuggestionsInAS,enablePreLoadEnterpriseAS:e.options&&e.options.enablePreLoadEnterpriseAS,openInNewTab:e.options?.asOpenInNewTab,showBothCxtAndTrending:e.options?.showBothCxtAndTrending,enableNewAsFooter:e.options?.autoSuggest?.enableNewAsFooter,enableBlueDeepSearch:e.options?.autoSuggest?.enableBlueDeepSearch,enableDeepSearch:e.options?.autoSuggest?.enableDeepSearch,searchBtnOpenInNewTab:e.options?.searchBtnOpenInNewTab,isCNMkt:e.options?.autoSuggest?.isCNMkt,placeholder:e.getPlaceholder,defaultPlaceholder:e.defaultPlaceholder,isRuby:e.isRuby,outsideCodexBtnFullPageLayout:e.outsideCodexBtnFullPageLayout,largerSearchBox:e.largerSearchBox,largerSearchBoxTweak:e.largerSearchBoxTweak}})}
`,Je=Ie.dy`<img class="search-icon-asset-img" src="${Ne}" aria-hidden="true" width="24" height="24" />`,Xe=Ie.dy`<img class="search-icon-asset-img" src="${ze}" aria-hidden="true" width="20" height="20" />`,Ze=Ie.dy`<span class="search-icon-asset-svg"></span>`,et=Ie.dy`${e=>e.searchIcon}`,tt=Ie.dy`${Ie.dy.partial(Re())}`,ot=Ie.dy`${e=>e.searchButtonOnLeft&&3===e.searchIconTreatment?Je:!e.searchButtonOnLeft||1!==e.searchIconTreatment&&2!==e.searchIconTreatment?e.isRuby?Xe:e.searchIcon?et:tt:Ze}`,nt=Ie.dy`<fluent-button class="search-btn" part="button" title=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.buttonText} aria-label=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.buttonText} appearance="${e=>e.searchButtonOnLeft||e.options?.mobileStyle?"stealth":"accent"}" @click="${e=>{let t=!!e.options?.searchBtnOpenInNewTab;e.options?.fixSpyGlassNewTabBehavior&&(t&&=!e.value||""==e.value),e.onSubmit(t)}}" @keypress="${e=>e.onSubmit()}" data-t="${e=>e.buttonTelemetryTag}" data-customhandled="true" icon-only>${ot}</fluent-button>`,it=Ie.dy`<div class="conditional-sh-container"><fluent-button class="search-btn conditional ${e=>e.options?.disableDarkTheme?"disable-dk":""}" ${(0,Fe.i)("searchElement")} part="button" title=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.buttonText} aria-label=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.buttonText} appearance="stealth" @mousedown="${e=>e.handActionFocusEvent()}" @mouseup="${e=>e.handActionUnfocusEvent()}" @click="${e=>e.onSubmit()}" @keypress="${e=>e.onSubmit()}" data-t="${e=>e.buttonTelemetryTag}" data-customhandled="true" icon-only>${ot}</fluent-button></div>`,at=Ie.dy`<div class="dpsb-wrp" style="${e=>e.enableDefaultDeepSearchQry||""!==e.value?"":"display: none"}"><fluent-button class="deep-search-btn" title=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.deepSearchButtonDescText} aria-label=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.deepSearchButtonText} @click="${e=>e.onSubmit(!0,!0)}" @keypress="${e=>e.onSubmit(!0,!0)}" data-t="${e=>e.deepSearchButtonTelemetryTag}" data-customhandled="true"><span id="b_sh_btn_isprt"></span><span id="b_sh_btn_text">${e=>e.options?.localizedStrings.deepSearchButtonText}</span></fluent-button></div>`,rt=Ie.dy`<div class="core ${e=>e.searchButtonOnLeft?"search-btn-on-left":"search-btn-on-right"} ${e=>e.options?.mobileStyle?"mobile":""} ${e=>e.options?.showCodexBingIcon?"core-bing-chat":""} ${e=>e.options?.enableCodexBingIconStyles?"core-info-bc":""} ${e=>e.options?.enableDoublePane&&!e.isRuby?"wd_search":""} ${e=>e.isRuby?"core-search-kumo":""}" part="searchcore"><slot name="search-options-left"></slot>${e=>e.searchButtonOnLeft?nt:""} ${(0,Be.g)((e=>e.contextualSuggestionSlug&&e.enableSearchSuggestionGhostTemplate),qe)}<input class="control ${e=>e.options?.disableDarkTheme?"disable-dk":""} ${e=>e.getEnableHighlightTrendingQuery?"highlight":""}" type="search" id="q" title=${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.inputTitle} name="q" @input=${e=>e.handleTextInput()} @focus=${e=>e.inputOnFocus()} @blur=${e=>e.inputOnBlur()} @mouseenter=${e=>e.inputOnMouseEnter()} @keypress=${(e,t)=>e.onKeypress(t.event)} @click=${e=>e.inputOnClick()} @keydown=${(e,t)=>e.onKeyDown(t.event)} aria-label="${e=>e.options&&e.options.localizedStrings&&e.options.localizedStrings.inputTitle}" placeholder=${e=>e.getPlaceholder} autocomplete="off" maxlength="250" ${(0,Fe.i)("inputElement")} :value="${e=>e.value}" data-t="${e=>e.inputTelemetryTag}" data-customhandled="true" />${(0,Be.g)((e=>!e.options?.hideClearButton),Ye)} ${(0,Be.g)((e=>e.getEnableConditionalSearchButtonOnRight),it)}<slot name="search-options" ${(0,Fe.i)("searchOptionsElement")} style="${e=>e.options?.enableConditionalSchBtn&&(e.isInputFocused||e.actionElementFocused||""!==e.value)?"display: none;":""}"></slot>${e=>e.searchButtonOnLeft?"":nt}</div>`,st=Ie.dy`${(0,Be.g)((e=>e?.isRuby),Ie.dy`<div class="kumo-wrapper">${rt}</div>`,rt)}`,ct=Ie.dy`<form ${(0,Fe.i)("formElement")} id="srchfrm" role="search" part="root" action=${e=>e.searchActionUrl} target=${e=>e.options&&e.options.target||"_self"} method="get" data-customhandled="true" data-clarity-mask=${e=>e.options&&e.options.enableClarity} data-t="${e=>e.searchBoxTelemetryTags}" class=${e=>`form ${e.isRuby?"kumo":""} ${e.searchVisible?"":"search-hidden"} ${e.isNextWordASMode?"nwas":"as"} ${e.autosuggestShown||e.showAPIAutoSuggestBox?"autosuggestShown":""}`}>${(0,Be.g)((e=>e.enableDimMask),Ie.dy`<div style="--bg-opacity:${e=>e.dimMaskOpacity}" class="search-mask ${e=>e.showAPIAutoSuggestBox?"dim":""}"></div>`)} ${st} ${e=>e.isInputFocused&&e.options?.hideCodexButtonOnFocus?"":Ie.dy`<slot name="codex-bing-chat"></slot>`}<slot></slot>${(0,Le.rx)((e=>e.formParameters),We)} ${(0,Be.g)((e=>e.fakeFocused),Ge)} ${(0,Be.g)((e=>e.enableLoadAPIAutoSuggestBox),Qe)}</form>${(0,Be.g)((e=>e.enableDeepSearch),at)}
`;var dt=o(35883),lt=o(63070),pt=o(18449),ht=o(79659),ut=o(45900);dt.D.define(lt.H.registry),pt.D.define(lt.H.registry),ht.D.define(lt.H.registry),ut.D.define(lt.H.registry);const gt=P.compose({name:"cs-common-search-box",template:ct,styles:Pe,shadowOptions:{delegatesFocus:!0}})},56099:function(e,t,o){"use strict";o.d(t,{q:function(){return k}});var n=o(33940),i=o(28904),a=o(42590),r=o(63521);class s{constructor(e){this.response=e}get WebUrl(){return this.response?.webUrl}}var c=o(86027),d=o(56322),l=o(89181);const p={id:r.o2.Graph,friendlyName:r.H2.Graph};async function h(){const e=new URL("https://graph.microsoft.com/v1.0/sites/root");e.searchParams.append("$select","webUrl");const t=(0,d.pW)();return function(e){if(!e||200!==e.status)return null;const t=new s(e.content);return t}(await(0,c.h)({apiName:r.jf.SharePointSites,name:"GetSiteRootUrl",url:e,headers:{"client-request-id":t,"content-type":"application/json"},method:"GET",authentication:p,errors:{authenticationError:l.Q$P,authenticationProviderNotSupported:l.vC9,authenticationUserInteractionRequired:l.g_R,unhandledException:l.$S3,tooManyRequests:l.h4Z,unsuccessfulStatusCode:l.OyK,unsupportedEnterpriseCloud:l.BW3}}))}var u=o(74566);class g extends i.H{checkSVGImage(){return this.image.indexOf("image/svg+xml")>-1||this.image.indexOf(".svg")>-1}connectedCallback(){super.connectedCallback(),this.imageElement=this.shadowRoot?.getElementById?.("company-logo");const e=new Image;e.onload=()=>{this.imageElement.setAttribute("width",e.width.toString()),this.imageElement.setAttribute("height",e.height.toString())},e.src=this.image,this.enableClickableCompanyLogo&&this.fetchCompanyUrl().then((e=>{this.companySiteUrl=e}))}async fetchCompanyUrl(){return(await h()).WebUrl}onCompanyLogoClick(){this.enableClickableCompanyLogo&&((0,u.Ay)()(),(0,u.Rc)()())}}(0,n.gn)([(0,a.Lj)({attribute:"companylogo-image"})],g.prototype,"image",void 0),(0,n.gn)([(0,a.Lj)({attribute:"backplate-color"})],g.prototype,"backplateColor",void 0),(0,n.gn)([(0,a.Lj)({mode:"boolean",attribute:"backplate-image"})],g.prototype,"backplateImage",void 0),(0,n.gn)([(0,a.Lj)({mode:"boolean",attribute:"horizontal-flip"})],g.prototype,"horizontalFlip",void 0),(0,n.gn)([(0,a.Lj)({mode:"boolean"})],g.prototype,"showFullPageBlur",void 0),(0,n.gn)([(0,a.Lj)({attribute:"companySite-url"})],g.prototype,"companySiteUrl",void 0),(0,n.gn)([(0,a.Lj)({mode:"boolean",attribute:"enable-clickable-company-logo"})],g.prototype,"enableClickableCompanyLogo",void 0);var m=o(28632),f=o(22674),b=o(78923);const y=b.i`
.company-logo-backplate{left:88px}.company-logo-flip{left:initial !important;right:88px}`,w=b.i`
.company-logo-backplate{right:88px}.company-logo-flip{left:88px;right:initial !important}`,x=b.i` .company-logo-backplate{min-width:100px;background:${m.c1};border-radius:100px;justify-content:center;display:flex;padding:12px 17px}.company-logo-backplate.blurbg{background:rgba(0,0,0,0.3)}.company-logo-image{max-height:48px;max-width:180px;height:auto;width:auto;z-index:0}.company-logo-svg-image{max-height:24px;max-width:114px;height:24px;width:auto;z-index:0}`.withBehaviors(new f.O(y,w));var v=o(49218),_=o(93703);const S=e=>`company-logo-backplate ${e.horizontalFlip?" company-logo-flip":""} ${e.showFullPageBlur?"blurbg":""}`,C=e=>e.backplateColor&&!e.showFullPageBlur?`background: ${e.backplateColor}`:"",T=v.dy` ${(0,_.g)((e=>e.backplateImage&&e.enableClickableCompanyLogo&&e.companySiteUrl),v.dy`<div class="${S}" style="${C}"><a id="company-logo-url" href=${e=>e.companySiteUrl} target="_blank"> <img id="company-logo" class=${e=>e.checkSVGImage()?"company-logo-svg-image":"company-logo-image"} src='${e=>e.image}' alt='No Image'></img></a></div>`)} ${(0,_.g)((e=>e.backplateImage&&e.enableClickableCompanyLogo&&!e.companySiteUrl),v.dy`<div class="${S}" style="${C}"><img id="company-logo" class=${e=>e.checkSVGImage()?"company-logo-svg-image":"company-logo-image"} src='${e=>e.image}' alt='No Image' @click=${e=>e.onCompanyLogoClick()}></img></div>`)} ${(0,_.g)((e=>e.backplateImage&&!e.enableClickableCompanyLogo),v.dy`<div class="${S}" style="${C}"><img id="company-logo" class=${e=>e.checkSVGImage()?"company-logo-svg-image":"company-logo-image"} src='${e=>e.image}' alt='No Image'></img></div>`)} ${(0,_.g)((e=>!e.backplateImage&&e.enableClickableCompanyLogo&&e.companySiteUrl),v.dy`<a id="company-logo-url" href=${e=>e.companySiteUrl} target="_blank"><img id="company-logo" class=${e=>e.checkSVGImage()?"company-logo-svg-image":"company-logo-image"} src='${e=>e.image}' alt='No Image'></img></a>`)} ${(0,_.g)((e=>!e.backplateImage&&e.enableClickableCompanyLogo&&!e.companySiteUrl),v.dy`<img id="company-logo" class=${e=>e.checkSVGImage()?"company-logo-svg-image":"company-logo-image"} src='${e=>e.image}' alt='No Image'}" @click=${e=>e.onCompanyLogoClick()}></img>`)} ${(0,_.g)((e=>!e.backplateImage&&!e.enableClickableCompanyLogo),v.dy`<img id="company-logo" class=${e=>e.checkSVGImage()?"company-logo-svg-image":"company-logo-image"} src='${e=>e.image}' alt='No Image'></img>`)}
`;let k=class extends g{};k=(0,n.gn)([(0,i.M)({name:"enterprise-company-logo",template:T,styles:x})],k)},92837:function(e,t,o){"use strict";o.d(t,{S:function(){return l},s:function(){return p}});var n=o(49218),i=o(20284),a=o(93703),r=o(90042),s=o(40359),c=o(25203),d=o(54192);const l=n.dy`<div class="${e=>`${(0,d.S)(e)} ${e.enableAdSponsoredText?"sponsored-text":""}`} ${(e,t)=>m(e,t)}"><a class="${e=>`${(0,r.$H)(e.enableSafeAds,"ad-label-text")}`}" href="${e=>e.privacyUrl}" @click="${(e,t)=>!e.enableAdCardClickCbInAdSlug||!e.adCardClickCallback||e.adCardClickCallback(t.event,e.privacyUrl)}" target="${i.P._blank}" title="${e=>e.localizedStrings&&e.localizedStrings.nativeAdAdLabelText}" data-t="${e=>e.telemetryMetadata||e.adTelemetryContext?.adLabel?.getMetadataTag()}">${e=>e.adLabelText}</a></div>`,p=n.dy`<div class="${e=>h(e)} ${e=>u(e)} ${e=>(0,r.$H)(e.enableSafeAds,"ad-slug")}" slot="${(e,t)=>g(e,t)}">${(0,a.g)((e=>e.showAdLabel&&e.adLabelText),l)} ${(0,a.g)((e=>e.providerName&&!e.enableAdCardProviderAlignment&&!e.enableProviderAlignmentV2),n.dy`<div class="${e=>(0,r.$H)(e.enableSafeAds,"ad-provider")}"><a class="${e=>(0,r.$H)(e.enableSafeAds,"ad-provider-text")}" href="${e=>e.destinationUrl}" target="${i.P._blank}" title="${e=>e.providerName}" ${(0,s.lq)(!0,!1)} data-t="${e=>e.telemetryMetadata||e.adTelemetryContext?.destination?.getMetadataTag()}">${e=>(0,c.vy)(e.providerName)}</a></div>`)}</div>`,h=e=>e.hideAdLabelAndAdChoice?"ad-slug-extended":"",u=e=>e.enableBlendInAdSlugStyle?"blend-in-ad-slug":"",g=(e,t)=>{switch(e.enableBlendInAdSlugStyle){case 2:case 3:case 4:return"";default:return(0,r.$H)(e.enableSafeAds,"footer-start")}},m=(e,t)=>{switch(e.enableBlendInAdSlugStyle){case 1:return(0,r.$H)(e.enableSafeAds,"bi-ad-slug1");case 2:return(0,r.$H)(e.enableSafeAds,"bi-ad-slug2");case 3:return(0,r.$H)(e.enableSafeAds,"bi-ad-slug3");case 4:return(0,r.$H)(e.enableSafeAds,"bi-ad-slug4");default:return(0,r.$H)(e.enableSafeAds,"ad-label")}}},9004:function(e,t,o){"use strict";o.d(t,{Oj:function(){return _}});var n=o(33940),i=o(89181),a=o(69183),r=o(42590),s=o(82954);let c=class extends a.F{constructor(){super(...arguments),this.privacyUrl="",this.id="",this.adLabelText="Ad",this.nativeAdAdLabelText="",this.type="greenLabel",this.isDynamicRendering=!0,this.shouldAdjustGap=!1,this.cardLayout="default",this.telemetryMetadata="",this.adLabelTextOpacity="0.7",this.color="var(--neutral-foreground-rest)",this.isFeedV2=!1,this.isSeasonal=!1,this.isMsnHPAdSlug=!1,this.adLabelFontSize="var(--type-ramp-minus-2-font-size)",this.enableSafeAds=!1,this.hideAdLabelAndAdChoice=!1,this.enableAdSponsoredText=!1}connectedCallback(){super.connectedCallback()}};(0,n.gn)([(0,r.Lj)({attribute:"privacy-url"})],c.prototype,"privacyUrl",void 0),(0,n.gn)([r.Lj],c.prototype,"id",void 0),(0,n.gn)([(0,r.Lj)({attribute:"ad-label-text"})],c.prototype,"adLabelText",void 0),(0,n.gn)([(0,r.Lj)({attribute:"native-ad-ad-label-text"})],c.prototype,"nativeAdAdLabelText",void 0),(0,n.gn)([(0,r.Lj)({attribute:"type"})],c.prototype,"type",void 0),(0,n.gn)([(0,r.Lj)({mode:"boolean",attribute:"is-dynamic-rendering"})],c.prototype,"isDynamicRendering",void 0),(0,n.gn)([(0,r.Lj)({mode:"boolean",attribute:"should-adjust-gap"})],c.prototype,"shouldAdjustGap",void 0),(0,n.gn)([(0,r.Lj)({attribute:"layout"})],c.prototype,"layout",void 0),(0,n.gn)([(0,r.Lj)({attribute:"card-layout"})],c.prototype,"cardLayout",void 0),(0,n.gn)([(0,r.Lj)({attribute:"tel-metadata"})],c.prototype,"telemetryMetadata",void 0),(0,n.gn)([(0,r.Lj)({attribute:"ad-label-text-opacity"})],c.prototype,"adLabelTextOpacity",void 0),(0,n.gn)([(0,r.Lj)({attribute:"color"})],c.prototype,"color",void 0),(0,n.gn)([(0,r.Lj)({mode:"boolean",attribute:"is-feed-v2"})],c.prototype,"isFeedV2",void 0),(0,n.gn)([(0,r.Lj)({mode:"boolean",attribute:"is-seasonal"})],c.prototype,"isSeasonal",void 0),(0,n.gn)([(0,r.Lj)({mode:"boolean",attribute:"is-msn-hp-ad-slug"})],c.prototype,"isMsnHPAdSlug",void 0),(0,n.gn)([(0,r.Lj)({attribute:"ad-label-font-size"})],c.prototype,"adLabelFontSize",void 0),(0,n.gn)([(0,r.Lj)({mode:"boolean",attribute:"enable-safe-ads"})],c.prototype,"enableSafeAds",void 0),(0,n.gn)([(0,r.Lj)({mode:"boolean",attribute:"hide-ad-label-and-ad-choice"})],c.prototype,"hideAdLabelAndAdChoice",void 0),(0,n.gn)([(0,r.Lj)({mode:"boolean",attribute:"enable-ad-sponsored-text"})],c.prototype,"enableAdSponsoredText",void 0),c=(0,n.gn)([(0,s.uj)(i.EoX,"msn-native-ad-ad-label")],c);var d=o(28904),l=o(90346),p=o(60002),h=o(78923),u=o(90042),g=o(97650);const m=h.i` .native-ad-label-dr-wrapper,.${(0,u.lj)("native-ad-label-dr-wrapper")}{--ad-label-fill-grey:#595959;--ad-label-color-grey:#FFF;--ad-label-fill-green:#1E6525;--ad-label-color-white:#FFF;--outline-width:1;--control-corner-radius:2;outline:none;pointer-events:auto;font-family:var(--body-font);font-size:var(--type-ramp-minus-2-font-size);display:flex}.ad-label-hidden{max-width:0px;min-width:0px;visibility:hidden;width:0;width:0;padding:0;margin:0;border:0;margin-bottom:-12px}.ndr,.gap{margin-inline-end:8px}.native-ad-label-dr,.${(0,u.lj)("native-ad-label-dr")}{box-sizing:border-box;padding:0 6px;display:inline-block;line-height:14px;border-radius:calc(var(--control-corner-radius) * 1px);text-decoration:none;border:calc(var(--stroke-width) * 1px) solid transparent}.grey{background:var(--ad-label-fill-grey);color:var(--ad-label-color-grey)}.green{background:var(--ad-label-fill-green);color:var(--ad-label-color-white)}.white{background:white;color:lightgrey;border:calc(var(--stroke-width) * 1px) solid white}.transparent-infopane{color:white;border:calc(var(--stroke-width) * 1px) solid white}.transparent-river{color:lightgrey;border:calc(var(--stroke-width) * 1px) solid lightgrey}.ad-badge-topsite{background:white;border:1px solid grey;border-radius:30px;color:grey;padding:0px 4px;text-decoration:none}.ad-label-topsite{box-sizing:border-box;display:inline-block;text-decoration:none;color:var(--top-site-color)}.ad-txt-small{font-size:9px;line-height:18px}.ad-slug1{color:var(--neutral-foreground-rest);border:0.5px solid var(--neutral-foreground-rest)}.ad-slug2{color:transparent;opacity:0.3;border:0.5px solid var(--neutral-foreground-rest);position:absolute}.ad-slug2-text.is-feed-v2-text::before{border-color:#292929}.ad-slug2-text.is-feed-v2-text{color:#292929}.ad-slug2-text.is-seasonal-style::before{border-color:#000000;opacity:1}.ad-slug2-text.is-seasonal-style{color:#000000}.ad-slug2-text:focus-visible{outline-offset:-2px}@media (prefers-color-scheme:dark){.ad-slug2-text.is-feed-v2-text::before{border-color:#FFFFFF}.ad-slug2-text.is-feed-v2-text{color:#FFFFFF}.ad-slug2-text.is-seasonal-style::before{border-color:#FFFFFF;opacity:1}.ad-slug2-text.is-seasonal-style{color:#FFFFFF}}${g.f1} @media (forced-colors:active){.ad-slug .ad-label,.${(0,u.lj)(".ad-slug")} .${(0,u.lj)(".ad-label")}{border:0.5px solid buttontext;opacity:1}.ad-slug .ad-label-text,.${(0,u.lj)(".ad-slug")} .${(0,u.lj)(".ad-label-text")}{color:buttontext;opacity:1;background:buttonface}.ad-slug .ad-provider-text,.${(0,u.lj)(".ad-slug")} .${(0,u.lj)(".ad-provider-text")}{color:buttontext;opacity:1;background:buttonface}}`.withBehaviors(new l.g("layoutStyle"),new p.j(["adLabelTextOpacity","color","adLabelFontSize"],(e=>h.i` .ad-slug2-text{--control-corner-radius:4;position:relative;padding:1px 5px;text-decoration:none;line-height:16px;opacity:${e.adLabelTextOpacity};color:${e.color};font-size:${e.adLabelFontSize}px}.ad-slug2-text::before{content:"";width:100%;height:100%;position:absolute;top:0;left:0;opacity:0.3;box-sizing:border-box;border-radius:calc(var(--control-corner-radius) * 1px);border:0.5px solid ${e.color}}.is-msn-hp-style{color:#2b2b2b;font-weight:400;font-size:11px;margin-inline-end:6px}.is-msn-hp-style::before{border:0.5px solid #2b2b2b}@media (forced-colors:active){.ad-slug2-text::before{border:0.5px solid buttontext;opacity:1}.ad-slug2-text{color:buttontext;opacity:1;background:buttonface}}`)));var f,b=o(49218),y=o(93703),w=o(92837),x=o(54192);!function(e){e.default="default",e.condensed="condensed",e.infoPane="infoPane",e.infoPane1x3y="infoPane1x3y",e.infoPaneSplitVertical="infoPaneSplitVertical",e.infoPaneSplitHorizontal="infoPaneSplitHorizontal"}(f||(f={}));const v=b.dy` ${(0,y.g)((e=>e.type),b.dy` ${(0,y.g)((e=>"greyLabel"===e.type),b.dy`<div class="${e=>(0,x.S)(e)} ${e=>(0,u.$H)(e.enableSafeAds,"native-ad-label-dr-wrapper")}"><a class="${e=>(0,u.$H)(e.enableSafeAds,"native-ad-label-dr")} grey ${e=>e.shouldAdjustGap?"gap":""}" href="${e=>e.privacyUrl}" target="_blank" title="${e=>e.nativeAdAdLabelText}" data-t="${e=>e.telemetryMetadata}">${e=>e.adLabelText}</a></div>`)} ${(0,y.g)((e=>"greenLabel"===e.type),b.dy`<div class="${e=>(0,x.S)(e)} ${e=>(0,u.$H)(e.enableSafeAds,"native-ad-label-dr-wrapper")}"><a class="${e=>(0,u.$H)(e.enableSafeAds,"native-ad-label-dr")} green ${e=>e.shouldAdjustGap?"gap":""}" href="${e=>e.privacyUrl}" target="_blank" title="${e=>e.nativeAdAdLabelText}" data-t="${e=>e.telemetryMetadata}">${e=>e.adLabelText}</a></div>`)} ${(0,y.g)((e=>"defaultLabel"===e.type),b.dy`<div class="${e=>(0,x.S)(e)} ${e=>(0,u.$H)(e.enableSafeAds,"ad-slug")}">${w.S}</div>`)} ${(0,y.g)((e=>"topSiteAdTxtSmall"===e.type),b.dy`<div class="${e=>(0,u.$H)(e.enableSafeAds,"native-ad-label-dr-wrapper")}"><a class="ad-label-topsite ad-txt-small" href="${e=>e.privacyUrl}" target="_blank" title="${e=>e.nativeAdAdLabelText}" data-t="${e=>e.telemetryMetadata}">${e=>e.adLabelText}</a></div>`)} `)}
`;let _=class extends c{};_=(0,n.gn)([(0,d.M)({name:"msn-native-ad-ad-label",template:v,styles:m})],_)},54192:function(e,t,o){"use strict";o.d(t,{S:function(){return n}});const n=e=>e.hideAdLabelAndAdChoice?"ad-label-hidden":""},42097:function(e,t,o){"use strict";o.d(t,{J0:function(){return i},Vy:function(){return a},pl:function(){return r}});let n=!1;const i=e=>e.hoverState&&!e.hoverState.isHovered&&e.hoverState.isDecorationLinesExpanded?65:25,a=(e,t)=>{e.hoverState&&e.hoverState.isDecorationLinesExpanded!==t&&(e.hoverState.isDecorationLinesExpanded=t)},r=async e=>{if(n)return;n=!0;let t=[];return(e?.adTemplateConfig?.configType||e?.adTemplateConfig?.allowedAssets)&&(t=t.concat([o.e("nas-highlight-v3v4").then(o.bind(o,82747))])),t=t.concat([Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("nativeadstemplates")]).then(o.bind(o,70172)),Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("nativeadstemplates")]).then(o.bind(o,5029)),Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("nativeadstemplates")]).then(o.bind(o,94611)),Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("nativeadstemplates")]).then(o.bind(o,89498)),Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("nativeadstemplates")]).then(o.bind(o,313))]),await Promise.all(t)}},69183:function(e,t,o){"use strict";o.d(t,{F:function(){return d}});var n=o(33940),i=o(28904),a=o(78923),r=o(99452),s=o(51961);const c=new Map([["flex","MsnFlexLayout"],["pixel","MsnPixelLayout"],["block","MsnBlockLayout"]]);class d extends i.H{constructor(){super(...arguments),this._displayName="MsnUberLayout"}connectedCallback(){super.connectedCallback();const e=this.layoutConfig?.mode;if(c.has(e)&&c.get(e)!==this._displayName)return;const t=Object.keys(this.layoutConfig||{}).filter((e=>"mode"!==e)).map((e=>(0,s._t)(e,this.layoutConfig?.[e]))).filter((e=>""!==e));this.layoutStyle=a.i`${t.length>0?`:host {${t.join(";")}}`:""}`}}(0,n.gn)([r.LO],d.prototype,"layoutConfig",void 0),(0,n.gn)([r.LO],d.prototype,"layoutStyle",void 0)},39231:function(e,t,o){"use strict";o.d(t,{h:function(){return te}});var n=o(33940),i=o(45900),a=o(63070);var r=o(28904),s=o(65669),c=o(42590),d=o(99452),l=JSON.parse('[{"id":"ABT8CC4C5C8EF6DB879FDCA1E27C109E624D3C67C7AC0275024110E030793FEDEAA","width":100},{"id":"ABTA100D2A1ABBB6E08F5121E0CDD0B0731945F2261F671B5EAD2A08D81F7B0B345","width":100},{"id":"ABTA33D1D299F01BDE36ED6EF79C9DC773893CBB69D094F6ABB3CD089924ECDC95C","width":100},{"id":"ABTF48C79733EFF6A118D03B1EAF32A7B8BC252E8FE9D92D96A0F8262F7D15EEFC2","width":100},{"id":"ABT5EDEA901E9DF7E86234E1A64FBBADC3D37782438AC16CEFCED401A471A919351","width":100},{"id":"ABT9B4903828CE76AECCAC5EBD2B63228A986B8CC00D1A1F212B616B63C193A31DF","width":100},{"id":"ABT24E8E2F9DA405997800FA6A344691C02DE64592C6E629B0EAFE1228673A27CEF","width":100},{"id":"ABT5AC94A41F0390D13C03A92D408B2DE263C71029C89BFB9388AA166628216166D","width":100},{"id":"ABTB3663F96377300AEB6F45F7ED1EF081780F62AEE01F573976EDAA1C66DF871A2","width":100},{"id":"ABT77BA972574F660ACE13D5A01FDD34E27530473EB4CA888293DAC90500BE48BA1","width":100},{"id":"ABT7739BFCCF6A0BE1AB12B5FA8170278D9CD8190809820B3D49E8FD43DEE660BFC","width":100},{"id":"ABT0B2F4E7964579FF756A61366CEADC713F3EC180E8F8220CE0D06E8FB95957EC7","width":100},{"id":"ABTF191FC21D6A9645D72E255565FC98A3275058A9058ECC60CD55CC858A55EDB5B","width":100},{"id":"ABTA1D27F9BBA969B79C94253E96A44D2AFF4327BF4D6EAD364E4B7176F19DE1C82","width":100},{"id":"ABTD8B5FDADE899184EC0AD1292E58CFDD15A84F5B1B274D860C9B098968DE413DF","width":100},{"id":"ABT78AD0382918CD7CB6A36D9716742003F31322F1757DF66B21F13CAB4D6F208B4","width":100},{"id":"ABT2A1168BC37B9A3F063906FE413DF98B866C345F1341827070BD065370B71BCE0","width":100},{"id":"ABT8CC4C5C8EF6DB879FDCA1E27C109E624D3C67C7AC0275024110E030793FEDEAA","width":100},{"id":"ABTA93D6CFC4021227D8D44EEAADD7636EA14BD8942E7073C58EE486819C79F1D0F","width":100},{"id":"ABTA585DD8154694CF5513209B6AED85789D2C38F24C52F7F23EB196982C6AE8328","width":100},{"id":"ABT74A3C7F8F4B165BD89D496912ADB21C79E7DA27F7F62A9FD77231821397D36D5","width":100},{"id":"ABT8708C3FBC067DF8DF6C482574C680C7C46C00AA0F3777A8515503E39F832E1A5","width":100},{"id":"ABT6B4F78B1E3363136050B05149CF74C290A4D4BD10CDDE28D4F4B642110CB63C0","width":100},{"id":"ABTCECE00108E43C44846B3CE82F17676DA93108505D222E60D695895DD7773788E","width":200},{"id":"ABT775A81D3A34FEB45D5AF98F18DA09711D768B633A0BD63305F38BCCF0D10AAD1","width":200},{"id":"ABT1D9A1C69509CF367E2A66BC2F317B102277F4D951A11EFFB52F84A5AF1FB305E","width":200},{"id":"ABT5708E4312D4D6511227941417A4DDA092EF61C5267EAA4AEC1FD34795528BC79","width":200},{"id":"ABTDEC0BFBEC6406DCD08B4FC34D1825CD9F999D7250FD4E1CD68B4A8515C2E0274","width":200},{"id":"ABT5F8365ADD2441C59C098A2900A66C1DA0AF4B61A2D78A3FFBAF4E0D9568AD940","width":200},{"id":"ABT67DBBD761679B638F606F0130A36DDD661C68FD1385491EBC3D0030008A9AE71","width":200},{"id":"ABT8F10D1DC25BE2941DEC5A5F2F067376CEF509536A3035B9C54E5F147BEF18E65","width":200},{"id":"ABT54FBD263CD7AAF2C885059F613D512DCFB9217180592748585786FF38AB9D6C2","width":200}]');const p=['<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2048 2048">\n        <path d="M1871 1276l33 124-200 53 53 201-124 33-63-237-546-315v631l173 173-90 90-147-146-147 146-90-90 173-173v-631l-546 315-63 237-124-33 53-201-200-53 33-124 237 63 546-315-546-315-237 63-33-124 200-53-53-201 124-33 63 237 546 315V282L723 109l90-90 147 146 147-146 90 90-173 173v631l546-315 63-237 124 33-53 201 200 53-33 124-237-63-546 315 546 315 237-63z" />\n    </svg>','<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2048 2048">\n        <path d="M1024 2048q-27 0-45-18t-19-46v-392l-258 227q-19 16-42 16-27 0-46-18t-19-46q0-28 22-48l343-302v-333H649q-2 4-3 5l-321 364q-19 22-47 22-27 0-46-18t-19-46q0-23 16-42l251-285H64q-27 0-45-18t-19-46q0-26 19-45t45-19h368L229 728q-16-19-16-42 0-27 19-45t46-19q28 0 47 22l278 316h357V626L617 325q-11-9-16-21t-5-27q0-26 18-45t45-19q24 0 43 16l258 227V64q0-26 18-45t46-19q26 0 45 19t19 45v392l258-227q19-16 42-16 27 0 46 18t19 46q0 29-22 48l-343 302v333h333l302-343q19-21 48-21 26 0 45 18t19 45q0 24-16 43l-227 258h392q28 0 46 18t18 46q0 26-19 45t-45 19h-392l227 258q16 19 16 43 0 26-19 44t-45 19q-31 0-49-21l-301-343h-333v333l343 302q21 20 21 48 0 26-18 45t-45 19q-24 0-43-16l-258-227v392q0 27-18 45t-46 19z" />\n    </svg>','<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16">\n        <path d="M0 0h16v16h-16zm0 0h16v16h-16z" fill="none" />\n        <path d="M13.338 9.2l-.757.29-2.581-1.49 2.581-1.49.757.29a1 1 0 0 0 .716-1.867l-.418-.16-.007-.022-.015-.017.07-.442a1 1 0 0 0-1.975-.314l-.127.8-2.582 1.49v-2.981l.63-.51a1 1 0 0 0-1.26-1.554l-.347.277h-.046l-.347-.277a1 1 0 0 0-1.26 1.554l.63.51v2.981l-2.581-1.49-.127-.8a1 1 0 0 0-1.975.314l.07.442-.016.018-.008.022-.418.16a1 1 0 0 0 .717 1.866l.757-.29 2.581 1.49-2.581 1.49-.757-.29a1 1 0 1 0-.716 1.867l.418.16.008.022.016.018-.07.442a1 1 0 1 0 1.975.314l.127-.8 2.58-1.491v2.981l-.63.51a1 1 0 0 0 1.26 1.554l.347-.281h.046l.347.281a1 1 0 0 0 1.26-1.554l-.63-.51v-2.981l2.581 1.49.127.8a1 1 0 1 0 1.975-.314l-.07-.442.015-.017.007-.022.418-.16a1 1 0 0 0-.715-1.867z" />\n    </svg>'],h=l.map((e=>`<img width="${e.width}px" src="https://www.bing.com/th?id=${e.id}&w=${e.width}" alt="" />`)),u=['<img width="50px" height="50px" src="https://www.bing.com/th?id=OSS.BT1D902D82E13C1C9AB0211C789B5153A026404DF13B52837B2B3FDAAA752B2203" alt="" />'],g=['<img width="35px" height="35px" src="https://www.bing.com/th?id=OSS.BT22B9FE60ACAACC9352B2AA48B3007ADDA3B40C698D77070951543CACE5DCE6E8"/>'],m=['<img width="100px" height="100px" src="https://www.bing.com/th?id=ABT597A9F2855EB8E415CEB9082C22E30462441426D4A3524F51E1D37CB5641F017"/>'],f=['<img width="100px" src="https://www.bing.com/th?id=OSS.BT9DBFC000B690AD1F3FEE04CADA210CA58868A03D0D8CDB8BE04D4178A888E1FF"/>'],b=['<img width="63px" height="70px" src="https://ts4.cn.mm.bing.net/th?id=ABTC3328C7266ACC0B84E85FEC88930CA497118E91B20AA5E203AC7F728DD0A2706" />'],y=['<img width="63px" height="70px" src="https://ts4.cn.mm.bing.net/th?id=ABT1B5BDB7BBBBD4A52F1C8B4D4F38CA77B159EF316B27676EFE8C11505679B6358" />'],w=['<img width="50px" src="https://www.bing.com/th?id=OSS.BT23834136D7030366B40D378EB6C3007A5C5BBD74CE732FFB37545047349A9EA3" alt="" />'];var x;!function(e){e.Snow="snow",e.Heart="heart",e.Ghost="ghost",e.Fireworks="fireworks",e.Pancake="pancake",e.Redpackets="redpackets",e.Bell="bell",e.BlackFriday="black-friday",e.Confetti="confetti",e.Year="year"}(x||(x={}));const v=x.Snow;const _={density:60,variant:x.Heart,speed:6,yoyoYLowerbound:0,yoyoYUpperbound:0,useRandomDelay:!1,fallDurationLowerbound:20,fallDurationUpperbound:40,reverse:!0,scale:2,scaleLowerbound:.7,opacityLowerbound:.6,iteration:1,timingFunction:"ease-in"},S={density:100,variant:x.Ghost,speed:3,yoyoYLowerbound:0,yoyoYUpperbound:0,useRandomDelay:!1,fallDurationLowerbound:20,fallDurationUpperbound:50,reverse:!0,scale:2,scaleLowerbound:.7,opacityLowerbound:.6,iteration:1,timingFunction:"ease-in"},C={density:10,variant:x.Fireworks,useRandomDelay:!1,scale:2,scaleLowerbound:.7,iteration:1,timingFunction:"ease-in"},T={density:20,variant:x.Pancake,speed:4,yoyoYLowerbound:-10,yoyoYUpperbound:-10,useRandomDelay:!1,fallDurationLowerbound:10,fallDurationUpperbound:20,reverse:!1,scale:.8,scaleLowerbound:.4,opacityLowerbound:1,iteration:1,timingFunction:"linear",rotationEnabled:!0},k={density:25,variant:x.BlackFriday,speed:3,yoyoYLowerbound:-10,yoyoYUpperbound:-10,useRandomDelay:!1,fallDurationLowerbound:10,fallDurationUpperbound:20,reverse:!1,scale:1.5,scaleLowerbound:.5,opacityLowerbound:1,iteration:1,timingFunction:"linear",rotationEnabled:!0},A={variant:x.Snow,iteration:1,fallDuration:8},$={density:20,variant:x.Redpackets,speed:3,yoyoYLowerbound:0,yoyoYUpperbound:0,useRandomDelay:!1,fallDurationLowerbound:20,fallDurationUpperbound:50,reverse:!0,scaleLowerbound:.7,opacityLowerbound:.6,iteration:1,timingFunction:"ease-in"},E={density:300,variant:x.Confetti,speed:10,yoyoYLowerbound:0,yoyoYUpperbound:0,useRandomDelay:!0,fallDurationLowerbound:20,fallDurationUpperbound:40,scale:.5,scaleLowerbound:.3,opacityLowerbound:1,iteration:1,timingFunction:"ease-in"},P={density:30,variant:x.Year,speed:6,yoyoYLowerbound:0,yoyoYUpperbound:0,useRandomDelay:!1,fallDurationLowerbound:20,fallDurationUpperbound:40,reverse:!0,scale:2,scaleLowerbound:.7,opacityLowerbound:.6,iteration:1,timingFunction:"ease-in",endY:130};function I(e=100){return Math.floor(Math.random()*e)}function B(e,t,o=0){return+(Math.random()*(t-e)+e).toFixed(o)}var F;!function(e){e.center="center",e.random="Random"}(F||(F={}));class L{constructor(e,t,o){if(this.density=100,this.speed=1,this.scale=1,this.variant=v,this.elements=[],this.xOffSet=10,this.opacityLowerbound=.3,this.scaleLowerbound=.3,this.fallDurationLowerbound=15,this.fallDurationUpperbound=45,this.yoyoYLowerbound=20,this.yoyoYUpperbound=80,this.useRandomDelay=!0,this.reverse=!1,this.timingFunction="linear",this.maxDuration=0,this.rotationEnabled=!1,this.endY=100,!t)return void this.onVariantChange();const{variant:n}=t;n&&(this.variant=n,Object.assign(this,function(e){switch(e){case x.Heart:return _;case x.Ghost:return S;case x.Fireworks:return C;case x.Pancake:return T;case x.BlackFriday:return k;case x.Redpackets:return $;case x.Confetti:return E;case x.Year:return P;default:return A}}(n))),this.onVariantChange(),Object.assign(this,t),this.center=o?o/2*(100/o):e/2*(100/e)}onVariantChange(){this.className=function(e){switch(e){case x.Heart:return"heart";case x.Ghost:return"ghost";case x.Fireworks:return"fireworks";case x.Pancake:return"pancake";case x.Redpackets:return"redpackets";case x.BlackFriday:return"black-friday";case x.Confetti:return"confetti";case x.Year:return"ballon";default:return"snowflake"}}(this.variant),this.elements=function(e){switch(e){case x.Heart:return u;case x.Ghost:return g;case x.Fireworks:return m;case x.Pancake:return f;case x.Redpackets:return b;case x.BlackFriday:return y;case x.Confetti:return h;case x.Year:return w;default:return p}}(this.variant)}getEffectHtml(){if(!this.getHasElements())return"";let e="";for(let t=0;t<this.density;t++)e+=this.getRandomElement();return e}getEffectStyle(){switch(this.variant){case x.Ghost:return this.getGhostStyle();case x.Fireworks:return this.getFireworksStyle();case x.Pancake:case x.BlackFriday:return this.getPancakeStyle();default:return this.getSnowflakeStyle()}}getAnimationDuration(){return this.iteration?this.iteration*this.maxDuration:0}getRandomElement(){const e=this.elements.length;return`<div class="effect-element ${this.className}">${this.elements[I(e)]}</div>`}getHasElements(){return this.elements?.length>0}getSnowflakeStyle(){if(!this.getHasElements())return"";let e="";for(let t=1;t<=this.density;t++){const o=I(),n=B(-this.xOffSet,this.xOffSet),i=o+n,a=o+n/2,r=B(this.yoyoYLowerbound,this.yoyoYUpperbound),s=B(this.scaleLowerbound,1,2)*this.scale,c=B(this.fallDurationLowerbound,this.fallDurationUpperbound)*(1/this.speed),d=this.useRandomDelay?-1*I(this.fallDurationUpperbound)*(1/this.speed):0,l=B(this.opacityLowerbound,1,2);this.maxDuration=this.fallDuration?this.fallDuration:Math.max(this.maxDuration,c),e+=`\n                .${this.className}:nth-child(${t}) {\n                    opacity: ${l};\n                    transform: translate(${o}vw, -30px) scale(${s});\n                    animation: fall-${t} ${c}s ${d}s ${this.timingFunction} ${this.iteration||"infinite"};\n                }\n\n                @keyframes fall-${t} {\n                    ${r}% {\n                        transform: translate(${i}vw, ${r}vh) scale(${s});\n                    }\n\n                    to {\n                        transform: translate(${a}vw, ${this.reverse?-this.endY:this.endY}vh) scale(${s});\n                    }\n\n                }\n            `}return e}getGhostStyle(){if(!this.getHasElements())return"";let e="";for(let t=1;t<=this.density;t++){const o=I(),n=B(-this.xOffSet,this.xOffSet),i=this.spwan==F.random?o+n:this.center,a=o+n/2,r=B(this.yoyoYLowerbound,this.yoyoYUpperbound),s=B(this.scaleLowerbound,1,2)*this.scale,c=B(this.fallDurationLowerbound,this.fallDurationUpperbound)*(1/this.speed),d=this.useRandomDelay?-1*I(this.fallDurationUpperbound)*(1/this.speed):0,l=B(this.opacityLowerbound,1,2);this.maxDuration=Math.max(this.maxDuration,c),e+=`\n                .${this.className}:nth-child(${t}) {\n                    opacity: ${l};\n                    transform: translate(${o}vw, -10px) scale(${s});\n                    animation: fall-${t} ${c}s ${d}s ${this.timingFunction} ${this.iteration||"infinite"};\n                }\n\n                @keyframes fall-${t} {\n                    ${r}% {\n                        transform: translate(${i}vw, ${r}vh) scale(${s});\n                    }\n\n                    to {\n                        transform: translate(${a}vw, ${this.reverse?-100:100}vh) scale(${s});\n                    }\n\n                }\n            `}return e}getFireworksStyle(){if(!this.getHasElements())return"";let e="";for(let t=1;t<=this.density;t++){const o=I(),n=B(this.yoyoYLowerbound,this.yoyoYUpperbound),i=B(this.scaleLowerbound,1,2)*this.scale;e+=`\n                .${this.className}:nth-child(${t}) {\n                    transform: scale(${i});\n                    position: absolute;\n                    top: ${n}vh;\n                    left: ${o}vw;\n                }\n            `}return e}getPancakeStyle(){if(!this.getHasElements())return"";let e="";for(let t=1;t<=this.density;t++){const o=I(),n=B(-this.xOffSet,this.xOffSet),i=o+n,a=o+n/2,r=B(this.yoyoYLowerbound,this.yoyoYUpperbound),s=B(this.scaleLowerbound,1,2)*this.scale,c=B(this.fallDurationLowerbound,this.fallDurationUpperbound)*(1/this.speed),d=this.useRandomDelay?-1*I(this.fallDurationUpperbound)*(1/this.speed):0,l=B(this.opacityLowerbound,1,2),p=this.rotationEnabled?B(0,360):0,h=this.rotationEnabled?B(45,360):0;this.maxDuration=Math.max(this.maxDuration,c),e+=`\n                .${this.className}:nth-child(${t}) {\n                    opacity: ${l};\n                    transform: translate(${o}vw, -150px) scale(${s});\n                    animation: fall-${t} ${c}s ${d}s ${this.timingFunction} ${this.iteration||"infinite"};\n                    position: absolute;\n                }\n\n                @keyframes fall-${t} {\n                    ${r}% {\n                        transform: translate(${i}vw, ${r}vh) scale(${s}) rotate(${p}deg);\n                    }\n\n                    to {\n                        transform: translate(${a}vw, ${this.reverse?-100:100}vh) scale(${s}) rotate(${h}deg);\n                    }\n\n                }\n            `}return e}}var D=o(49218);const M=D.dy`
    <svg class="icon" slot="start" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2048 2048">
        <path d="M2018 1673l-135 36 36 135-124 33-45-171-214-123v246l125 126-90 90-99-99-99 99-90-90 125-126v-246l-214 123-45 171-124-33 36-135-135-36 33-123 171 45 214-123-214-123-171 45-33-123 135-36-36-135 124-33 45 171 214 123v-246l-125-126 90-90 99 99 99-99 90 90-125 126v246l214-123 45-171 124 33-36 135 135 36-33 123-171-45-214 123 214 123 171-45 33 123zM1291 425l181-105-181-105 64-110 181 104V0h128v209l181-104 64 110-181 105 181 105-64 110-181-104v209h-128V431l-181 104-64-110zM810 854l106 28-34 124-69-19-18 69-124-33 28-105-123-214-123 214 28 105-124 33-18-69-69 19-34-124 106-28 123-214H219l-78 77-90-90 51-51-51-51 90-90 78 77h246L342 298l-106-28 34-124 69 19 18-69 124 33-28 105 123 214 123-214-28-105 124-33 18 69 69-19 34 124-106 28-123 214h246l78-77 90 90-51 51 51 51-90 90-78-77H687l123 214z" />
    </svg>
`,R=D.dy`
    <svg class="heartBeat icon" slot="start" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.8199 5.57912L11.9992 6.40163L11.1759 5.57838C9.07688 3.47931 5.67361 3.47931 3.57455 5.57838C1.47548 7.67744 1.47548 11.0807 3.57455 13.1798L11.4699 21.0751C11.7628 21.368 12.2377 21.368 12.5306 21.0751L20.432 13.1783C22.5264 11.0723 22.53 7.67857 20.4306 5.57912C18.3277 3.47623 14.9228 3.47623 12.8199 5.57912Z" fill="#b01712" />
    </svg>
`,O=D.dy`
    <img width=100% src="https://th.bing.com/th?w=100&id=OSS.BT037D4049A6986225B3755062A118281CFAB10105131B847C0EDF04E926F33EA0"/>
`,j=D.dy`
    <img width=100% src="https://th.bing.com/th?id=OSS.BT4D396D134EF411AE90AD8B36A6A389D4D112A63332199A2CE04999AE30ECD64F"/>
`,U=D.dy``,H=D.dy`
    <img width=100% src="https://th.bing.com/th?id=ABTD9CC9298A17A75148B5C65015149FDFA84CAC4EA278E2AC99DF3E0A5DA482FA9"/>
`,N=D.dy`
    <img width="40px" style="position: relative; top: 12px;" src="https://th.bing.com/th?h=80&id=ABTC2AB266759C7A068DC812C45633A580850E6AE507D5AB989DD2C260D74555ADB" />
`,z=D.dy``,W=D.dy``,G=D.dy`
    <img width=100% src="https://th.bing.com/th?id=OSS.BTF1D42076CF74BC0373F0B780C59A94F7A6631C60BAA85ADF6E0E803E241AF0C9"/>
`;var q=o(53241);class V extends r.H{constructor(){super(...arguments),this.shouldAlwaysFade=!1,this.fadeTime=1500,this.start=!0,this.effectOn=!1,this.waitForContentView=!1,this.effectHtml="",this.effectStyle="",this.containerStyleClass="fade-in",this.effectFinished=!1,this.controlTelemtryTag="",this.controlTooltip="",this.controlText="",this.controlIcon="",this.topicName="holiday",this.useToggle=!1,this.controlTimer=null,this.durationTimer=null,this.animationDuration=0}connectedCallback(){this.useToggle=this.controlConfig?.useToggle,this.initializeControl(),super.connectedCallback(),this.attachIntersectionObserver(this.autoStartEffect.bind(this)),this.toogleEffect=this.toogleEffect.bind(this),window.addEventListener("clickToggleEffect",this.toogleEffect)}disconnectedCallback(){super.disconnectedCallback(),window.removeEventListener("clickToggleEffect",this.toogleEffect)}initializeControl(){this.controlConfig&&(this.updateControlTelemetryTag(),this.controlTooltip=this.getControlTooltip(),this.controlText=this.getControlText(),this.controlIcon=this.getControlIcon())}attachIntersectionObserver(e){if("IntersectionObserver"in window){const t=new IntersectionObserver((o=>{if(!o||o.length<1)return;o[0].isIntersecting&&(e(),t.unobserve(this))}),{threshold:this.controlConfig?.minIntersection||.9});t.observe(this)}else e()}autoStartEffect(){this.start&&this.startEffect()}async startEffect(){const e=new L(window.innerWidth,this.effectConfig,this.shadowRoot?.host.clientWidth);this.effectHtml=e.getEffectHtml(),this.effectStyle=e.getEffectStyle(),this.effectOn=!0,this.animationDuration=e.getAnimationDuration(),this.animationDuration>0&&(this.waitForContentView?window.addEventListener("contentViewTransported",(()=>{(0,q.Ou)().contentViewTransported=performance.now(),this.durationTimer=window.setTimeout((()=>{this.shouldAlwaysFade?this.toogleEffect():this.resetEffect()}),1e3*this.animationDuration)}),{once:!0}):this.durationTimer=window.setTimeout((()=>{this.shouldAlwaysFade?this.toogleEffect():this.resetEffect()}),1e3*this.animationDuration))}resetEffect(){this.durationTimer&&clearTimeout(this.durationTimer),this.start=!1,this.effectOn=!1,this.effectHtml="",this.effectStyle=""}toogleEffect(){this.containerStyleClass=this.effectOn?"fade-out":"fade-in",this.effectOn=!this.effectOn,this.updateControlTelemetryTag(),this.start||(this.start=!0,this.startEffect()),this.controlTimer&&(clearTimeout(this.controlTimer),this.controlTimer=null),this.controlTimer=window.setTimeout((()=>{this.effectOn||(this.containerStyleClass="fade-out-end",this.animationDuration>0&&this.resetEffect()),this.controlTimer=null}),this.fadeTime)}updateControlTelemetryTag(){if(!this.controlConfig)return"";const{telemetryContext:e}=this.controlConfig;if(!e)return"";e.updateContract({...e.contract,behavior:this.effectOn?s.wu.TurnOff:s.wu.TurnOn}),this.controlTelemtryTag=e.getMetadataTag()}getControlTooltip(){if(!this.controlConfig)return"";const{onText:e,offText:t}=this.controlConfig;return this.effectOn?t||e||"":e||""}getControlText(){return!this.controlConfig||this.controlConfig.iconOnly?"":this.getControlTooltip()}getControlIcon(){if(!this.controlConfig)return"";const e=function(e){switch(e){case x.Heart:return R;case x.Ghost:return O;case x.Fireworks:return W;case x.Pancake:return j;case x.Redpackets:return z;case x.Bell:return U;case x.BlackFriday:return H;case x.Confetti:return N;case x.Year:return G;default:return M}}(this.effectConfig?.variant),{onIcon:t,offIcon:o}=this.controlConfig;return this.effectOn?t||o||e:t||e}}(0,n.gn)([(0,c.Lj)({mode:"boolean"})],V.prototype,"shouldAlwaysFade",void 0),(0,n.gn)([c.Lj],V.prototype,"fadeTime",void 0),(0,n.gn)([(0,c.Lj)({mode:"boolean"})],V.prototype,"start",void 0),(0,n.gn)([(0,c.Lj)({mode:"boolean"})],V.prototype,"effectOn",void 0),(0,n.gn)([(0,c.Lj)({mode:"boolean"})],V.prototype,"waitForContentView",void 0),(0,n.gn)([d.LO],V.prototype,"controlConfig",void 0),(0,n.gn)([d.LO],V.prototype,"effectConfig",void 0),(0,n.gn)([d.LO],V.prototype,"effectHtml",void 0),(0,n.gn)([d.LO],V.prototype,"effectStyle",void 0),(0,n.gn)([d.LO],V.prototype,"containerStyleClass",void 0),(0,n.gn)([d.LO],V.prototype,"effectFinished",void 0),(0,n.gn)([d.LO],V.prototype,"controlTelemtryTag",void 0),(0,n.gn)([d.LO],V.prototype,"controlTooltip",void 0),(0,n.gn)([d.LO],V.prototype,"controlText",void 0),(0,n.gn)([d.LO],V.prototype,"controlIcon",void 0),(0,n.gn)([c.Lj],V.prototype,"topicName",void 0),(0,n.gn)([d.LO],V.prototype,"useToggle",void 0);var K=o(67830);const Y=o(78923).i` :host{position:fixed;pointer-events:none;top:0;left:0;width:100vw;height:100vh;z-index:2}:host([effectOn]) .switch-button{opacity:var(--stop-button-opacity-effect-on,--stop-button-opacity)}:host([effecton]) .hideControlWhenEffectOn{opacity:0;pointer-events:none}.effect-element.heart{position:absolute;bottom:-150px;left:0}.effect-element.ghost{position:absolute;bottom:-150px;left:0;height:30px;width:30px}.effect-element.redpackets{position:absolute;bottom:-150px;left:0;height:30px;width:20px}.effect-element.confetti{position:absolute;top:0px;left:0}.effect-element.ballon{position:absolute;bottom:-200px;left:0}.effect-container{-webkit-transition:opacity var(--effect-fade-time,1500ms) ease-in-out;transition:opacity var(--effect-fade-time,1500ms) ease-in-out}.fade-out,.fade-out-end{opacity:0}.fade-in{opacity:1}.fade-out-end .effect-element{animation-play-state:paused}.fade-in .effect-element{animation-play-state:running}.snowflake{position:absolute;fill:#fff;pointer-events:none;width:20px;height:20px}.stop-button{position:absolute;bottom:var(--shopping-effect-ctl-icon-bottom,40px);left:var(--shopping-effect-ctl-icon-left,0px);pointer-events:auto;border-radius:0px 16px 16px 0px;background-color:var(--shopping-effect-ctl-bg,var(--neutral-fill-rest));opacity:var(--stop-button-opacity,0.8);-webkit-transition:opacity 500ms ease-in-out;transition:opacity 500ms ease-in-out;transition:padding 300ms ease-in-out}.pumpkin{opacity:0.95}.stop-button:hover{opacity:1;padding-right:12px}.stop-button:not(.icon-only)::part(control){padding-left:4px;padding-right:10px}.icon-only.stop-button{width:var(--shopping-effect-icon-width,32px);height:var(--shopping-effect-icon-height,32px);border-radius:var(--shopping-effect-icon-border-radius,50%);left:calc(var(--shopping-effect-ctl-icon-left,40px) - var(--shopping-effect-icon-width,0)/2);background-size:contain;background-image:var(--shopping-effect-background-image);background-size:var(--shopping-effect-background-size)}.switch-button{background-repeat:no-repeat;align-items:end}.switch-button::part(switch){margin-bottom:10px;margin-left:15px;background-color:#C8C8C8}.switch-button::part(checked-indicator){background-color:#fff}.stop-button .icon{width:var(--shopping-effect-ctl-icon-size,16px);height:var(--shopping-effect-ctl-icon-size,16px)}.icon-only::part(control){padding:0}.stop-button::part(start){margin-inline-end:6px}.icon-only::part(start){margin-inline-end:0}@keyframes heartBeat{0%{transform:scale(1)}14%{transform:scale(1.3)}28%{transform:scale(1)}42%{transform:scale(1.3)}70%{transform:scale(1)}}.heartBeat{animation-name:heartBeat;animation-duration:1.3s;animation-iteration-count:infinite;animation-timing-function:ease-in-out}${(0,K.eH)(K.K$.c5)}{.icon-only.stop-button{left:calc(var(--shopping-effect-ctl-icon-left-c5,var(--shopping-effect-ctl-icon-left,40px)) - var(--shopping-effect-icon-width,0)/2)}}${(0,K.eH)(K.K$.c4)}{.icon-only.stop-button{left:calc(var(--shopping-effect-ctl-icon-left-c4,var(--shopping-effect-ctl-icon-left,40px)) - var(--shopping-effect-icon-width,0)/2)}}${(0,K.eH)(K.K$.c3)}{.icon-only.stop-button{left:calc(var(--shopping-effect-ctl-icon-left-c3,var(--shopping-effect-ctl-icon-left,40px)) - var(--shopping-effect-icon-width,0)/2)}}${(0,K.eH)(K.K$.c2)}{.icon-only.stop-button{left:calc(var(--shopping-effect-ctl-icon-left-c2,var(--shopping-effect-ctl-icon-left,40px)) - var(--shopping-effect-icon-width,0)/2)}}`;var Q=o(93703),J=o(57194),X=o(88393);const Z=D.dy` ${(0,Q.g)((e=>e.controlConfig&&!e.controlConfig.hideControl),D.dy`<slot name="effect-control">${(0,Q.g)((e=>e.useToggle),D.dy`<fluent-switch aria-label="${e=>e.controlTooltip}" role="button" title="${e=>e.controlTooltip}" @click=${e=>e.toogleEffect()} class="switch-button stop-button ${e=>e.controlConfig?.iconOnly?"icon-only":""} ${e=>e.controlConfig?.hideControlWhenEffectOn?"hideControlWhenEffectOn":""}" part="stop-button" style="${e=>e.controlConfig?.style||""}" data-t="${e=>e.controlTelemtryTag}"></fluent-switch>`)} ${(0,Q.g)((e=>!e.useToggle),D.dy`<fluent-button aria-label="${e=>e.controlTooltip}" role="button" title="${e=>e.controlTooltip}" @click=${e=>e.toogleEffect()} class="stop-button ${e=>e.controlConfig?.iconOnly?"icon-only":""} ${e=>e.controlConfig?.hideControlWhenEffectOn?"hideControlWhenEffectOn":""} ${e=>"ghost"==e.effectConfig?.variant?"pumpkin":""}" part="stop-button" style="${e=>e.controlConfig?.style||""}" data-t="${e=>e.controlTelemtryTag}">${e=>e.controlIcon} ${e=>e.controlText}</fluent-button>`)}</slot>`)}
`,ee=D.dy`<style>${e=>e.effectStyle}</style><div style="--effect-fade-time: ${e=>e.fadeTime}ms" class="effect-container ${e=>e.containerStyleClass}" part="effect-container" :innerHTML=${(0,J.v)((e=>e.effectHtml?e.effectHtml:""),X.h)}></div>${Z}
`;let te=class extends V{};te=(0,n.gn)([(0,r.M)({name:"msn-shopping-effect",template:ee,styles:Y})],te),i.D.define(a.H.registry)},72647:function(e,t,o){"use strict";o.d(t,{Q:function(){return s}});var n=o(49218),i=o(41472),a=o(99026),r=o(95185);const s=e=>n.dy`<div class="header-core" part="core" ${(0,i.i)("core")}>${e.search??n.dy`<slot name="search"></slot>`}<div class="header-start" part="header-start" ${(0,i.i)("start")} ${(0,a.N)({property:"startItems",filter:(0,r.R)()})}>${e.start??n.dy`<slot name="start"></slot>`}</div><div class="header-end" part="header-end" ${(0,i.i)("end")} ${(0,a.N)({property:"endItems",filter:(0,r.R)()})}>${e.end??n.dy`<slot name="end"></slot>`}</div></div><slot></slot>`},59225:function(e,t,o){"use strict";o.d(t,{j:function(){return B}});var n=o(33940);const i=["waffle-wc","#wafflePlaceholder"],a=["enterprise-company-logo"],r=["common-settings-edgenext","#settingsPlaceholder"],s=["market-language-toggle","#marketLanguageToggle","welcome-greeting-light","sign-in-control","#signInButton"];var c=o(58968),d=o(99452),l=o(42590),p=o(99690),h=o(65669),u=o(37802),g=o(69951),m=o(91731),f=o(67830),b=o(64831),y=o(7640),w=o(34223),x=o(28904);class v extends x.H{constructor(){super(...arguments),this.startItems=[],this.endItems=[]}}var _=o(28368),S=o(84560),C=o(96772),T=o(37059),k=o(11070),A=o(13975),$=o(97021),E=o(59081),P=o(82229),I=o(12184);class B extends v{get isTranslucent(){return!this.isOnImage&&!this.isDarkMode}constructor(){super(),this.isRuby=!1,this.spaceForGreeting=0,this.showFullPageBlur=!1,this.needDarkThemeIcons=!1,this.isOnM365Page=!1,this.itemGap=0,this.settingsWidth=-1,this.settingsMaxWidth=-1,this.greetingMaxWidth=243,this.greetingMinWidth=88,this.mltWidth=-1,this.isPlaceholderHeaderEnabled=!1,this.isOnImage=!1,this.forceHideMLToggle=!1,this.darkModeQuery=window.matchMedia("(prefers-color-scheme:dark)"),this.isInlineMLTButtonEnabled=!1,this.isSICFlightEnabled=!1,this.isSignInFlyoutVisible=!1,this.shortText=!1,this.setupResizeObserver=()=>{const e=new window.ResizeObserver((0,u.Z)((e=>{this.updateStartSectionAppearance(),this.updateEndSectionAppearance()}),200));this.core&&e.observe(this.core),this.start&&e.observe(this.start),this.end&&e.observe(this.end)},this.updateStartSectionAppearance=()=>{if(!this.search||!this.startItems||0===this.startItems.length)return;const e=this.search,t=Math.floor((this.core.offsetWidth-e.offsetWidth)/2-24),o=this.getRealItems(this.startItems,i),n=this.getRealItems(this.startItems,a);let r=0;const s=[];o.forEach((e=>{r+=e.offsetWidth+(s.length>0?this.itemGap:0),s.push(e)})),n.reverse().forEach((e=>{r+=e.offsetWidth+(s.length>0?this.itemGap:0),r<=t&&s.push(e)})),this.startItems.forEach((e=>{s.includes(e)?e.classList.remove("overflow"):e.classList.add("overflow")}))},this.updateEndSectionAppearance=()=>{if(!this.search||!this.endItems||0===this.endItems.length||0===this.core.offsetWidth)return;if((0,S.N)()&&window.isSSREnabled&&!this.endSection.signInControlRef)try{const e=this.endItems.find((e=>"SIGN-IN-CONTROL"===e.tagName||"signInButton"===e.id));e&&p.M0.addOrUpdateTmplProperty("SignInControlRemoved","1"),e?.remove()}catch(e){}const e=0+(this.searchSection.showCodexBingIcon?54:0),t=Math.floor((this.core.offsetWidth-this.search.offsetWidth)/2-24-e),o=this.getRealItems(this.endItems,r),n=this.getRealItems(this.endItems,s);let i=0;const a=[];o.forEach((e=>{const t=a.length>0?this.itemGap:0;("COMMON-SETTINGS-EDGENEXT"===e.tagName||"settingsPlaceholder"===e.id)&&this.settingsWidth>0?i+=this.settingsWidth+t:i+=e.offsetWidth+t,a.push(e)})),n.reverse().forEach((e=>{const o=a.length>0?this.itemGap:0;"WELCOME-GREETING-LIGHT"===e.tagName?(this.spaceForGreeting=Math.min(t-i-o,this.greetingMaxWidth),this.spaceForGreeting=Math.max(this.spaceForGreeting,this.greetingMinWidth),i+=this.spaceForGreeting+o):"MARKET-LANGUAGE-TOGGLE"===e.tagName&&this.mltWidth>0?i+=this.mltWidth+o:i+=e.offsetWidth+o,i<=t&&a.push(e)})),this.endItems.forEach((e=>{("MARKET-LANGUAGE-TOGGLE"!==e.tagName&&"marketLanguageToggle"!==e.id||this.endSection?.languageToggleHorizontalAlignment&&!(this.mltWidth>80))&&(a.includes(e)?e.classList.remove("overflow"):e.classList.add("overflow"))}))},this.updateGreetingWidth=(e,t)=>{e&&this.greetingMinWidth!==Math.floor(e)&&(this.greetingMinWidth=Math.floor(e)),t&&this.greetingMaxWidth!==Math.ceil(t)&&(this.greetingMaxWidth=Math.ceil(t))},this.updateSettingsWidth=(e,t)=>{const o=Math.floor(e);if(o!==this.settingsWidth&&(this.settingsWidth=o,this.updateEndSectionAppearance()),t&&t>0&&this.settingsMaxWidth!==Math.ceil(t)){this.settingsMaxWidth=Math.ceil(t);const e=Math.min(80,this.settingsMaxWidth-40);this.core.style.setProperty("--settings-shift",`${e}px`)}},this.updateMLTButtonWidth=e=>{const t=Math.floor(e);t!==this.mltWidth&&(this.mltWidth=t,this.updateEndSectionAppearance())},this.onSignInButtonClicked=e=>{e.preventDefault(),this.signInDataStateConnector.updateSignInFlyoutDisplayState(!this.isSignInFlyoutVisible),(0,b.Gg)(b.tk.signInControl,!this.isSignInFlyoutVisible)},this.getRealItems=(e,t)=>t.map((t=>e.find((e=>e.matches(t))))).filter((e=>!!e)),this.darkModeQueryEvent=e=>{this.isDarkMode=!!e?.matches},this.onScroll=()=>{this.forceHideMLToggle=(window.pageYOffset||document.documentElement.scrollTop)>30},this.isPlaceholderHeaderEnabled=this.isFlightEnabled("ntp-plch-hdr"),this.isInlineMLTButtonEnabled=this.isFlightEnabled("ntp-inln-mlt"),this.isSICFlightEnabled=this.isFlightEnabled("ntp-inln-sic"),this.isInlineMLTButtonEnabled&&(this.mltTelemetryObj=(0,T.u)("MarketLanguageToggleButton",h.wu.Customize,h.Aw.Click).getMetadataTag()),this.isSICFlightEnabled&&(this.signInButtonContentViewTag=(0,k.X)("EdgeSignInButton",{headline:"EdgeSignInButton"}).getMetadataTag(),this.signInButtonTelemetryTag=(0,k.X)("SignIn",{headline:"SignIn"},h.wu.Open,h.Aw.Click).getMetadataTag(),this.setColumnArrangementAttributes(),this.signInDataStateConnector=(0,P.K0)(I.HS)),window.addEventListener("scroll",this.onScroll),this.isDarkMode=this.darkModeQuery.matches,(0,y.N6)("backgroundImage",(e=>{c.H.enqueue((()=>{this.isOnImage="displayed"===e}))})),this.darkModeQuery.addEventListener("change",this.darkModeQueryEvent)}connectedCallback(){super.connectedCallback(),(0,S.N)()&&(this.endSection?.languageToggleHorizontalAlignment&&s.unshift(s.splice(2,1)[0]),(0,g.l_)(this).then((()=>{this.setupResizeObserver()})),this.isSICFlightEnabled&&(0,E.Uo)(I.HS,(e=>{this.isSignInFlyoutVisible=e.isFlyoutDisplayed})))}disconnectedCallback(){super.disconnectedCallback(),this.removeMarketLanguageToggleEvents()}getCompanyLogoTelemetryTag(){return new _.D({name:"companyLogo",action:h.Aw.Click,type:h.c9.ActionButton,behavior:h.wu.Navigate}).getMetadataTag()}getMarketLanguageToggleDisplayString(){let e="";const t=this.endSection.localeMarket;if(t&&t.displayString&&(e=t.displayString,e.indexOf("(")>-1&&e.indexOf(")")>-1)){const t=e.indexOf("("),o=e.indexOf(")");if(t>=o)return e;e=e.substring(t+1,o),e&&e.length>0&&(e=e.charAt(0).toUpperCase()+e.slice(1))}return e}handleMLToggleIcon(e,t){t&&p.M0.sendActionEvent(t.currentTarget,h.Aw.Click,h.wu.Click),p.M0.addOrUpdateTmplProperty("MarketLibraryUnification","1"),(0,C.ag)(e)}removeMarketLanguageToggleEvents(){this.darkModeQuery.removeEventListener("change",this.darkModeQueryEvent),window.removeEventListener("scroll",this.onScroll)}isFlightEnabled(e){return w.Al.CurrentFlightSet.has(e)}setColumnArrangementAttributes(){this.shortText=!((0,m.Bn)().currentColumnArrangement>=f.K$.c4),(0,m.Bn)().subscribe((e=>{this.shortText=!(e>=f.K$.c4)}))}signInButtonRefChanged(){this.signInButtonRef&&(0,A.FY)($._.signInButton,this.signInButtonRef)}}(0,n.gn)([d.LO],B.prototype,"startSection",void 0),(0,n.gn)([d.LO],B.prototype,"endSection",void 0),(0,n.gn)([d.LO],B.prototype,"searchSection",void 0),(0,n.gn)([d.LO],B.prototype,"chromiumPageSettingsState",void 0),(0,n.gn)([d.LO],B.prototype,"headerCoreTTVRTemplate",void 0),(0,n.gn)([d.LO],B.prototype,"spaceForGreeting",void 0),(0,n.gn)([l.Lj],B.prototype,"showFullPageBlur",void 0),(0,n.gn)([d.LO],B.prototype,"needDarkThemeIcons",void 0),(0,n.gn)([d.LO],B.prototype,"isOnM365Page",void 0),(0,n.gn)([d.LO],B.prototype,"isDarkMode",void 0),(0,n.gn)([d.LO],B.prototype,"isOnImage",void 0),(0,n.gn)([d.LO],B.prototype,"forceHideMLToggle",void 0),(0,n.gn)([d.LO],B.prototype,"isSignInFlyoutVisible",void 0),(0,n.gn)([d.LO],B.prototype,"shortText",void 0),(0,n.gn)([d.LO],B.prototype,"signInButtonRef",void 0),(0,n.gn)([d.lk],B.prototype,"isTranslucent",null)},94572:function(e,t,o){"use strict";o.d(t,{S6:function(){return P},k9:function(){return v},rw:function(){return I}});var n=o(49218),i=o(41472),a=o(93703),r=o(49854),s=o(84560),c=o(39978),d=o(76915),l=o(77669),p=o.n(l),h=o(14487),u=o.n(h);const g=(0,d.M)("TTSR.WaffleWC"),m=(0,d.M)("TTSR.CommonSettingsEdgeNext"),f=n.dy`<div id="signInButton" data-t="${e=>e.signInButtonContentViewTag}"><fluent-button ${(0,i.i)("signInButtonRef")} class=${e=>B(e,"sign-in-button")} appearance="stealth" aria-expanded=${e=>e.isSignInFlyoutVisible} aria-label=${e=>e.endSection.strings?.signInButtonTitle} role="button" title=${e=>e.endSection.strings?.signInButtonTitle} data-t="${e=>e.signInButtonTelemetryTag}" @click=${(e,t)=>e.onSignInButtonClicked(t.event)}><span class=${e=>B(e,"text")}>${e=>e.endSection.strings?.signInButtonText}</span></fluent-button>${(0,a.g)((e=>e.isSignInFlyoutVisible),n.dy`${e=>(0,c.Ot)({instanceId:"SignInFlyoutWC",configRef:{experienceType:"SignInFlyoutWC",instanceSrc:"default"}})}`)}</div>`,b=n.dy`<div id="marketLanguageToggle" class="${e=>e.isOnImage&&!e.showFullPageBlur||e.isDarkMode?"image-enable":""}">${(0,a.g)((e=>!e.forceHideMLToggle),n.dy`<fluent-button class="languageButton ${e=>e.isOnImage&&!e.showFullPageBlur||e.isDarkMode?"light":"dark"} ${e=>e.needDarkThemeIcons?"dark-theme":""}" title=${e=>e.endSection.localeMarket&&e.endSection.localeMarket.displayString} aria-label=${e=>e.endSection.localeMarket&&e.endSection.localeMarket.displayString} data-t="${e=>e.mltTelemetryObj}" @click=${(e,t)=>e.handleMLToggleIcon(e.endSection.localeMarket&&e.endSection.localeMarket.id,t.event)}>${e=>e.getMarketLanguageToggleDisplayString()}</fluent-button>`)}</div>`,y=n.dy`${e=>(0,c.Ot)(e.startSection.waffleRef,{properties:{showFullPageBlur:e.showFullPageBlur,needDarkThemeIcons:e.needDarkThemeIcons},ttvrMarkerAggregate:e.isPlaceholderHeaderEnabled?void 0:"Header"})}`,w=n.dy`<div class="header-icon-placeholder" id="wafflePlaceholder">${n.dy.partial(u())} ${(0,a.g)((e=>(0,s.N)()),y)} ${g}</div>`,x=n.dy` ${(0,a.g)((e=>!e.isPlaceholderHeaderEnabled),y)} ${(0,a.g)((e=>e.isPlaceholderHeaderEnabled),w)}
`,v=n.dy` ${(0,a.g)((e=>e.startSection),n.dy` ${(0,a.g)((e=>e.startSection.rubyPivotMenuItems&&e.startSection.rubyPivotMenuItems),(()=>((async()=>{const{RubyPivotManagerWC:e}=await o.e("web-components_ruby-pivot-manager_dist_index_js").then(o.bind(o,91789))})(),n.dy`<ruby-pivot-manager :rubyPivotManagerStrings=${e=>e.startSection.rubyPivotManagerStrings} :rubyPivotMenuItems=${e=>e.startSection.rubyPivotMenuItems} :enableRubyPivotNavMenu=${e=>e.startSection.enableRubyPivotNavMenu} :defaultRubyPivotMenuItems=${e=>e.startSection.rubyPivotMenuItems} :maxInnerWidth=${740} :isRubyNTP=${!0}></ruby-pivot-manager>`)))} ${(0,a.g)((e=>e.startSection.waffleRef),x)} ${(0,a.g)((e=>e.startSection.enableCompanyLogo),n.dy`<enterprise-company-logo data-t="${e=>e.getCompanyLogoTelemetryTag()}" :image=${e=>e.startSection.companyLogoImage} :backplateImage=${e=>e.startSection.showcompanyLogoBackplate||e.showFullPageBlur||e.needDarkThemeIcons} :backplateColor=${e=>e.startSection.companyLogoBackplateColor} :horizontalFlip=${e=>!0} :showFullPageBlur=${e=>e.showFullPageBlur} :enableClickableCompanyLogo=${e=>e.startSection.enableClickableCompanyLogo}></enterprise-company-logo>`)} `)}
`,_=n.dy` ${e=>e.endSection.marketLanguageToggleRef&&(0,c.Ot)(e.endSection.marketLanguageToggleRef,{properties:{onMarketValueChange:e.endSection.onMarketValueChange,localeMarket:e.endSection.localeMarket,horizontalAligned:e.endSection.languageToggleHorizontalAlignment,showFullPageBlur:e.showFullPageBlur,needDarkThemeIcons:e.needDarkThemeIcons,regionAndLanguageTitle:e.endSection.strings?.regionAndLanguageTitle,closeButtonText:e.endSection.strings?.closeButtonText,updateButtonWidth:e.updateMLTButtonWidth},ttvrMarkerAggregate:"Header"})}
`,S=n.dy`${(0,a.g)((e=>e.isInlineMLTButtonEnabled),b,_)}`,C=n.dy`<span id="WGPlaceHolder"></span>`,T=n.dy`${e=>(0,c.Ot)(e.endSection.settingsRef,{properties:{showFullPageBlur:e.showFullPageBlur,needDarkThemeIcons:e.needDarkThemeIcons,updateButtonWidth:e.updateSettingsWidth,isOnM365Page:e.isOnM365Page},ttvrMarkerAggregate:e.isPlaceholderHeaderEnabled?void 0:"Header"})}`,k=n.dy`${e=>(0,c.Ot)(e.endSection.signInControlRef,{properties:{showFullPageBlur:e.showFullPageBlur,needDarkThemeIcons:e.needDarkThemeIcons},ttvrMarkerAggregate:"Header"})}`,A=n.dy`<div class="header-icon-placeholder" id="settingsPlaceholder">${n.dy.partial(p())} ${(0,a.g)((e=>(0,s.N)()),T)} ${m}</div>`,$=n.dy` ${(0,a.g)((e=>!e.isSICFlightEnabled||e.isRuby),k)} ${(0,a.g)((e=>e.isSICFlightEnabled&&!e.isRuby),f)}
`,E=n.dy` ${(0,a.g)((e=>!e.isPlaceholderHeaderEnabled),T)} ${(0,a.g)((e=>e.isPlaceholderHeaderEnabled),A)}
`,P=n.dy` ${(0,a.g)((e=>e.endSection),n.dy` ${(0,a.g)((e=>e.endSection.marketLanguageToggleRef&&!e.endSection.languageToggleHorizontalAlignment),S)} ${e=>e.endSection.welcomeGreetingRef&&(e.endSection.showWGPlaceHolder?C:(0,c.Ot)(e.endSection.welcomeGreetingRef,{properties:{rootMaxWidth:e.spaceForGreeting,updateGreetingWidth:e.updateGreetingWidth,showFullPageBlur:e.showFullPageBlur,needDarkThemeIcons:e.needDarkThemeIcons},ttvrMarkerAggregate:"Header"}))} ${(0,a.g)((e=>e.endSection.marketLanguageToggleRef&&e.endSection.languageToggleHorizontalAlignment),_)} ${(0,a.g)((e=>e.endSection.settingsRef),E)} ${(0,a.g)((e=>e.endSection.signInControlRef),$)} `)}
`,I=n.dy`
${(0,a.g)((e=>e.searchSection),n.dy`<div class="header-search" part="header-search" ${(0,i.i)("search")} ${(0,r.XJ)()}>${e=>e.searchSection.searchConfigRef&&(0,c.Ot)(e.searchSection.searchConfigRef,{ttvrMarkerAggregate:"Header"})} ${(0,a.g)((()=>!(0,s.N)()),(e=>e.headerCoreTTVRTemplate))}</div>`)}
`,B=(e,t)=>(t+=e.isTranslucent||e.showFullPageBlur||e.needDarkThemeIcons?" translucent":"",t+=e.shortText?" short-text":"",t+=e.isSignInFlyoutVisible?" active":"")},51012:function(e,t,o){"use strict";o.d(t,{W:function(){return d}});var n=o(77615),i=o(94572),a=o(59225),r=o(72647),s=o(45914);const c=a.j.compose({name:"cs-header-core",template:(0,r.Q)({start:i.k9,end:i.S6,search:i.rw}),styles:s._}),d=()=>{c.define(n.s.registry)}},47709:function(e,t,o){"use strict";o.d(t,{Z:function(){return x}});var n=o(33940),i=o(45900),a=o(63070);var r=o(28904),s=o(99452),c=o(42590);class d extends r.H{constructor(){super(...arguments),this.hasAction=!1,this.hideOverflowText=!1,this.focusBack=!0}connectedCallback(){super.connectedCallback();let e=document.activeElement;for(;e?.shadowRoot?.activeElement;)e=e.shadowRoot.activeElement;this.lastActiveElement=e}disconnectedCallback(){super.disconnectedCallback(),this.focusBack&&this.lastActiveElement.focus()}handleActionAdded(){this.actions.assignedNodes().length>0?this.hasAction=!0:this.hasAction=!1}}(0,n.gn)([s.LO],d.prototype,"hasAction",void 0),(0,n.gn)([(0,c.Lj)({mode:"boolean"})],d.prototype,"hideOverflowText",void 0),(0,n.gn)([s.LO],d.prototype,"focusBack",void 0);var l=o(78923),p=o(27782),h=o(55135),u=o(79148),g=o(62734),m=o(27186);const f=l.i` ${(0,m.j)("flex")} :host{font-weight:400;font-size:${p.c};line-height:${p.R};--elevation:4}.toast-root{width:fit-content;max-width:var(--toast-max-width,420px);min-height:48px;height:fit-content;border-radius:calc(${h.rS} * 1px);background:${u.RJ};box-sizing:border-box;display:flex;${g.XC}}.text-overflow{display:inline-block;max-width:var(--toast-message-max-width,200px);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.content-container{margin-inline-start:16px;margin-top:8px;margin-bottom:8px;display:flex;flex-wrap:nowrap;align-items:center;min-width:0}.content-container span{text-overflow:ellipsis;overflow:hidden}.content-container > span:first-child{flex-shrink:1}.content-container > span:last-child{flex-shrink:0}.close-button{margin:8px}.with-action{margin-inline-end:13px}`;var b=o(49218),y=o(41472);const w=b.dy`<div class="toast-root" part="toast-root" tabindex="0"><div class="content-container" part="toast-content-container"><span class="${e=>e.hasAction?"with-action":""} ${e=>e.hideOverflowText?"text-overflow":""}"><slot></slot></span><span><slot name="actions" ${(0,y.i)("actions")} @slotchange=${e=>e.handleActionAdded()}></slot></span></div><div class="close-button" part="toast-close-button"><slot name="close-button"></slot></div></div>`;let x=class extends d{};x=(0,n.gn)([(0,r.M)({name:"msn-toast",template:w,styles:f,shadowOptions:{delegatesFocus:!0}})],x),i.D.define(a.H.registry)},61120:function(e,t,o){"use strict";o.d(t,{X:function(){return A}});var n=o(33940),i=o(45900),a=o(63070),r=o(4778),s=o(40894);var c=o(28904),d=o(58968),l=o(42590),p=o(99452);class h extends c.H{constructor(){super(...arguments),this.useKumo=!1}get isEmptyUrl(){return!this.newOrUpdatedTopSite?.url}get isEmptyName(){return!this.newOrUpdatedTopSite?.name}saveTopSite(){if(this.validator){const e=this.validator(this.newOrUpdatedTopSite);if(this.newOrUpdatedUrlError={isInvalid:e.isInvalid,errorMessage:e.errorMessage},e.isInvalid)return}this.$emit("save",this.newOrUpdatedTopSite),this.resetInputs()}closeTopSiteDialog(){this.$emit("close"),this.resetInputs()}closeButtonHandler(e){let t;const o=this.shadowRoot?.host&&this.shadowRoot.host.id,n=this.shadowRoot?.activeElement&&this.shadowRoot.activeElement.id;"Enter"===e.key||" "===e.key?this.closeTopSiteDialog():"Tab"===e.key?"closeButton"===n&&(t=e.shiftKey?"topSiteAddDialog"===o&&void 0!==this.shadowRoot.querySelector?.("suggestion-title")?this.shadowRoot.querySelector?.("suggestion-body")[-1]:this.shadowRoot.getElementById?.("Cancel"):this.shadowRoot.getElementById?.("nameInputControl")):"Shift"===e.key&&e.preventDefault(),t&&(e.preventDefault(),d.H.enqueue((()=>t.focus())))}nameInputKeyHandler(e){let t;const o=this.shadowRoot?.host&&this.shadowRoot.host.id,n=this.shadowRoot?.activeElement&&this.shadowRoot.activeElement.id;"Tab"===e.key?"nameInputControl"===n&&(e.shiftKey?t=this.shadowRoot.getElementById?.("closeButton"):"topSiteAddDialog"===o?t=this.shadowRoot.getElementById?.("urlInputControl"):"topSiteEditDialog"===o&&(t=this.shadowRoot.getElementById?.("Save"))):"Shift"===e.key&&e.preventDefault(),t&&(e.preventDefault(),d.H.enqueue((()=>t.focus())))}handleNameChange(){this.newOrUpdatedTopSite.name=this.nameInputControl?.value||""}handleUrlChange(){this.resetNewOrUpdatedUrlError(),this.newOrUpdatedTopSite={...this.newOrUpdatedTopSite,url:this.urlInputControl?.value||""}}resetInputs(){this.urlInputControl&&(this.urlInputControl.value=""),this.nameInputControl&&(this.nameInputControl.value=""),this.newOrUpdatedTopSite={name:"",url:""},this.resetNewOrUpdatedUrlError()}resetNewOrUpdatedUrlError(){this.newOrUpdatedUrlError={isInvalid:!1,errorMessage:""}}}(0,n.gn)([l.Lj],h.prototype,"headerTitle",void 0),(0,n.gn)([l.Lj],h.prototype,"saveText",void 0),(0,n.gn)([l.Lj],h.prototype,"cancelText",void 0),(0,n.gn)([l.Lj],h.prototype,"closeText",void 0),(0,n.gn)([l.Lj],h.prototype,"addDialogSubHeading",void 0),(0,n.gn)([p.LO],h.prototype,"validator",void 0),(0,n.gn)([p.LO],h.prototype,"newOrUpdatedUrlError",void 0),(0,n.gn)([p.LO],h.prototype,"saveButtonTelemetryTag",void 0),(0,n.gn)([p.LO],h.prototype,"cancelButtonTelemetryTag",void 0),(0,n.gn)([p.LO],h.prototype,"closeButtonTelemetryTag",void 0),(0,n.gn)([p.LO],h.prototype,"nameInputTelemetryTag",void 0),(0,n.gn)([p.LO],h.prototype,"urlInputTelemetryTag",void 0),(0,n.gn)([p.LO],h.prototype,"nameInputData",void 0),(0,n.gn)([p.LO],h.prototype,"urlInputData",void 0),(0,n.gn)([p.LO],h.prototype,"newOrUpdatedTopSite",void 0),(0,n.gn)([p.LO],h.prototype,"useKumo",void 0),(0,n.gn)([p.lk],h.prototype,"isEmptyUrl",null),(0,n.gn)([p.lk],h.prototype,"isEmptyName",null);var u=o(42689),g=o(27460),m=o(22674),f=o(22134),b=o(78923),y=o(29717);const w=b.i` .close-button{left:auto;right:16px}`,x=b.i` .close-button{left:16px;right:auto}`,v=b.i` .kumo-dialog fluent-text-field::part(root){border-color:${f.eH}}`,_=b.i` :host{--control-corner-radius:2;--dialog-z-index:0;--layer-corner-radius:4;position:relative}h1{cursor:default;font-weight:600;font-size:16px;line-height:24px;margin-top:0;margin-bottom:0;color:${u.C}}.kumo-dialog{--layer-corner-radius:12}.kumo-dialog h1{line-height:28px;font-size:20px}.dialog{--dialog-height:var(--top-site-dialog-height,auto);--dialog-width:var(--top-site-dialog-width,auto);position:absolute;z-index:var(--top-site-dialog-z-index);top:var(--dialog-top,auto);bottom:var(--dialog-bottom,auto);left:var(--dialog-left,auto);right:var(--dialog-right,auto);margin-top:var(--top-site-dialog-margin-top,0px)}.main-container{padding:12px 16px;display:grid;background-color:${g.s};border-radius:calc(var(--layer-corner-radius)* 1px)}.kumo-dialog .main-container{padding:16px}.header{display:grid;grid-template-columns:1fr auto;position:relative;width:100%}.close-button{position:absolute;min-width:24px;height:24px}.close-button::part(control){padding:0;margin:0}.close-button svg{width:10px;height:10px}.kumo-dialog .close-button{background:transparent}.kumo-dialog .close-button svg{width:13px;height:13px}.body{margin-top:4px;flex:1 1 0;font-size:12px;display:inline-flex;flex-direction:column;flex-wrap:wrap;gap:12px;color:${u.C};justify-content:space-between}.kumo-dialog .body{margin-top:20px}.input-container{display:grid;line-height:16px}.input-container.error fluent-text-field::part(label){color:rgba(209,52,56,1.0)}.input-container.error fluent-text-field::part(root){border-bottom-color:rgba(209,52,56,1.0)}.input-container--error{color:rgba(209,52,56,1.0)}fluent-text-field::part(label){line-height:32px;margin-top:auto;margin-bottom:auto;width:40px;text-align:start}fluent-text-field::part(control){margin:0;transition:all 0.2s ease-in-out;line-height:20px;font-weight:400;padding:0 11px;border-radius:2px;height:32px}.kumo-dialog fluent-text-field::part(label){line-height:26px}.kumo-dialog fluent-text-field::part(root){border-radius:4px;background:transparent;border-color:${f.vV};border-bottom-color:#616161;border-bottom-width:2px}.kumo-dialog .input-container.has-value fluent-text-field::part(root){border-bottom-color:#0F6CBD}.kumo-dialog fluent-text-field::part(root):focus,.kumo-dialog fluent-text-field::part(root):focus-within{box-shadow:none;border-bottom-color:#0F6CBD}.footer-actions{justify-content:center;box-sizing:border-box;display:grid;grid-template-columns:var(--top-site-dialog-button-width,140px) var(--top-site-dialog-button-width,140px);grid-gap:8px;width:100%;margin-top:24px}.kumo-dialog .footer-actions{margin-top:18px;justify-content:left;--type-ramp-base-font-size:16px;--base-height-multiplier:9;grid-template-columns:50px 65px}fluent-dialog::part(positioning-region){position:var(--dialog-position,relative);border-radius:calc(var(--layer-corner-radius)* 1px)}`.withBehaviors(new m.O(w,x),(0,y.Uu)(v));var S=o(49218),C=o(93703),T=o(41472);const k=S.dy`<fluent-dialog @dismiss=${e=>e.closeTopSiteDialog()} class="dialog  ${e=>e.useKumo?"kumo-dialog":""}" part="dialog" modal><div class="main-container"><div class="header"><h1 class="header-title">${e=>e.headerTitle}</h1></div><div class="body" part="main-container-body">${(0,C.g)((e=>e.nameInputData),S.dy`<div class="input-container ${e=>e.isEmptyName?"":"has-value"}"><fluent-text-field autofocus appearance="fill" tabindex="0" type="${e=>e.nameInputData.type||"text"}" id="${e=>e.nameInputData.id}" aria-label="${e=>e.nameInputData.ariaLabel}" value="${e=>e.nameInputData.value}" @input=${(e,t)=>e.handleNameChange()} data-t="${e=>e.nameInputTelemetryTag}" ${(0,T.i)("nameInputControl")}>${e=>e.nameInputData.name}</fluent-text-field></div>`)} ${(0,C.g)((e=>e.urlInputData),S.dy`<div class="input-container ${e=>e.newOrUpdatedUrlError?.isInvalid?"error":""} ${e=>e.isEmptyUrl||e.newOrUpdatedUrlError?.isInvalid?"":"has-value"} "><fluent-text-field tabindex="0" type="${e=>e.urlInputData.type||"url"}" id="${e=>e.urlInputData.id}" aria-label="${e=>e.urlInputData.ariaLabel}" value="${e=>e.urlInputData.value}" @input=${e=>e.handleUrlChange()} data-t="${e=>e.urlInputTelemetryTag}" ${(0,T.i)("urlInputControl")}>${e=>e.urlInputData.name}</fluent-text-field>${(0,C.g)((e=>e.newOrUpdatedUrlError?.isInvalid&&e.newOrUpdatedUrlError?.errorMessage),S.dy`<div class="input-container--error">${e=>e.newOrUpdatedUrlError?.errorMessage||""}</div>`)}</div>`)}</div><div class="footer-actions" part="main-container-footer-actions"><fluent-button appearance="neutral" tabindex="0" title=${e=>e.saveText} id=${e=>e.saveText} aria-label=${e=>e.saveText} role="button" ?disabled=${e=>e.validator&&e.isEmptyUrl} @click=${e=>e.saveTopSite&&e.saveTopSite()} @keypress=${e=>e.saveTopSite&&e.saveTopSite()} data-t="${e=>e.saveButtonTelemetryTag}">${e=>e.saveText}</fluent-button><fluent-button appearance="neutral" tabindex="0" title=${e=>e.cancelText} id=${e=>e.cancelText} aria-label=${e=>e.cancelText} role="button" @click=${e=>e.closeTopSiteDialog&&e.closeTopSiteDialog()} @keypress=${e=>e.closeTopSiteDialog&&e.closeTopSiteDialog()} data-t="${e=>e.cancelButtonTelemetryTag}">${e=>e.cancelText}</fluent-button></div><slot name="subSection" ${(0,T.i)("subSection")}></slot><fluent-button id="closeButton" appearance="stealth" class="close-button" title=${e=>e.closeText} aria-label=${e=>e.closeText} role="button" @click=${e=>e.closeTopSiteDialog&&e.closeTopSiteDialog()} @keydown=${(e,t)=>e.closeButtonHandler(t.event)} data-t="${e=>e.closeButtonTelemetryTag}"><svg viewBox="0 0 16 16" width="16px" height="16px" xmlns="http://www.w3.org/2000/svg"><path d="M14.1016 1.60156L8.20312 7.5L14.1016 13.3984L13.3984 14.1016L7.5 8.20312L1.60156 14.1016L0.898438 13.3984L6.79688 7.5L0.898438 1.60156L1.60156 0.898438L7.5 6.79688L13.3984 0.898438L14.1016 1.60156Z"></path></svg></fluent-button></div></fluent-dialog>`;let A=class extends h{};A=(0,n.gn)([(0,c.M)({name:"msn-top-site-dialog",template:k,styles:_,shadowOptions:{delegatesFocus:!0}})],A),i.D.define(a.H.registry),r.D.define(a.H.registry),s.D.define(a.H.registry)},11359:function(e,t,o){"use strict";o.d(t,{v:function(){return u}});var n=o(33940),i=o(45900),a=o(63070);var r=o(28904);class s extends r.H{}var c=o(35680),d=o(78923),l=o(27186);const p=d.i` ${(0,l.j)("inline-block")} :host{box-sizing:border-box;border-radius:4px;position:relative;justify-content:center;height:80px;width:104px;height:var(--top-site-height,80px);width:var(--top-site-width,104px)}.container{width:100%;height:100%}:host(:hover){background:var(--top-site-fill-hover,${c.Xi})}.icon-action{align-items:center;display:flex;height:100%;width:100%}::slotted([slot="icon-action"]){width:100%;height:100%}`;const h=o(49218).dy`<div class="container" id="topSitePlaceHolder"><span part="icon-action" class="icon-action"><slot name="icon-action"></slot></span></div>`;let u=class extends s{};u=(0,n.gn)([(0,r.M)({name:"msft-top-site-placeholder",template:h,styles:p})],u),i.D.define(a.H.registry)},97650:function(e,t,o){"use strict";o.d(t,{B2:function(){return l},dL:function(){return d},f1:function(){return p}});var n=o(42689),i=o(16549),a=o(91290),r=o(78923),s=o(90042),c=o(41314);const d=r.i` .ad-slug,.${(0,s.lj)("ad-slug")}{align-items:center;color:${n.C};display:flex;flex-direction:row;line-height:${i.v};max-width:calc(${a.$f} * 1px - 100px);opacity:0.8;overflow:hidden}.ad-slug.ad-slug-extended,.${(0,s.lj)("ad-slug")}.ad-slug-extended{max-width:calc(${a.$f} * 1px - 30px)}.bi-ad-slug1,.bi-ad-slug2,.bi-ad-slug3,.bi-ad-slug4{align-items:center;border:0.5px solid rgba(26,26,26,.3);color:#999999;display:flex;flex-direction:row;line-height:${i.v};max-width:calc(${a.$f} * 1px - 100px);overflow:hidden;font-size:10px;line-height:15px;padding:0px 5px 1px 5px;border-radius:4px}.blend-in-ad-slug .bi-ad-slug1.ad-label-hidden,.blend-in-ad-slug .bi-ad-slug2.ad-label-hidden,.blend-in-ad-slug .bi-ad-slug3.ad-label-hidden,.blend-in-ad-slug .bi-ad-slug4.ad-label-hidden{max-width:0px;min-width:0px;visibility:hidden;width:0;padding:0;margin:0;border:0}.ad-slug .ad-label,.${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-label")}{display:flex;align-items:center;border:0.5px solid rgba(26,26,26,.3);border-radius:4px;box-sizing:border-box;font-size:11px;height:16px;margin-inline-end:6px;min-width:22px;padding:1px 4px;overflow:hidden}.ad-slug .ad-label-text:focus-visible{outline:2px solid ${n.C};outline-offset:-2px}.ad-slug .ad-label.sponsored-text{border:unset !important;padding:unset !important}.ad-slug .ad-label.ad-label-hidden,.${(0,s.lj)("ad-slug")} .ad-label.ad-label-hidden{max-width:0px;min-width:0px;visibility:hidden;width:0;padding:0;margin:0;border:0}.ad-slug .ad-label-text,.${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-label-text")}{color:inherit;pointer-events:auto;position:relative;text-decoration:none;z-index:1}.ad-slug .ad-provider,.${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-provider")}{flex:4;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ad-slug .ad-provider-text,.${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-provider-text")}{color:inherit;font-size:${i.s};text-decoration:none;text-overflow:ellipsis;vertical-align:bottom}.ad-slug .ad-provider-text:focus-visible,.${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-provider-text")}:focus-visible{text-decoration:underline;outline-offset:-.79px;outline:-webkit-focus-ring-color auto 1px}[size="_2x_2y"] .ad-slug,[size="_2x_2y"] .${(0,s.lj)("ad-slug")}{max-width:calc(${a.$f} * 2px - 100px)}:host(.infopane-slide) .ad-slug .ad-label,[size="_2x_2y"] .ad-slug .ad-label,:host(.infopane-slide) .${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-label")},[size="_2x_2y"] .${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-label")}{border:0.5px solid rgba( 255,255,255,.3)}:host(.infopane-slide) .ad-slug .ad-label.ad-label-hidden,[size="_2x_2y"] .ad-slug .ad-label.ad-label-hidden,:host(.infopane-slide) .${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-label")}.ad-label-hidden,[size="_2x_2y"] .${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-label")}.ad-label-hidden{border:0}.bi-ad-slug1 .ad-label-text,.bi-ad-slug2 .ad-label-text,.bi-ad-slug3 .ad-label-text,.bi-ad-slug4 .ad-label-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.blend-in-ad-slug{line-height:15px}`,l=r.i` .ad-slug .ad-label,.${(0,s.lj)("ad-slug")} .${(0,s.lj)("ad-label")}{border:0.5px solid rgba(255,255,255,.3);opacity:0.9}.bi-ad-slug1,.bi-ad-slug2,.bi-ad-slug3,.bi-ad-slug4{border:0.5px solid rgba(255,255,255,.3)}`,p=r.i` ${d}
`.withBehaviors(new c.Y(null,l))},45914:function(e,t,o){"use strict";o.d(t,{_:function(){return u}});var n=o(67830),i=o(78923),a=o(29717),r=o(81239),s=o(35680),c=o(42689);const d=i.i` :host{display:flex;justify-content:center;--header-offset:0px;--header-top-margin:32px;--header-height:46px;--header-inline-margin:24px}.header-core{left:0px;width:100%;height:var(--header-height);box-sizing:border-box;padding:0 var(--header-inline-margin);display:grid;align-items:center;justify-content:center;grid-template:auto / repeat(3,1fr)}.header-search,::slotted([slot="search"]){height:var(--header-height);grid-row:1;grid-column:1 / 4;justify-self:center}::slotted([slot="start"]),.header-start{position:fixed;top:calc(var(--header-offset) + var(--header-top-margin));width:fit-content;height:var(--header-height);grid-area:1 / 1 / auto / auto;justify-self:start;align-items:center;display:flex;gap:0px}::slotted([slot="end"]),.header-end{position:fixed;top:calc(var(--header-offset) + var(--header-top-margin));width:fit-content;height:var(--header-height);grid-area:1 / 3 / auto / auto;justify-self:end;justify-content:flex-end;align-items:center;display:flex;gap:0px}`,l=i.i` :host{--subtle-background-hover:rgba(255,255,255,6.05%)}:host([showFullPageBlur="true"]){--subtle-background-hover:rgba(0,0,0,0.3)}`,p=i.i`
#marketLanguageToggle{position:absolute;top:calc(var(--header-height) - 3px);margin-inline-end:-6px;border-radius:2px;transition-property:background,border,color;transition-duration:100ms;transition-timing-function:cubic-bezier(0.33,0,0.67,1)}#marketLanguageToggle:hover{background:var(--subtle-background-hover,${s.wF})}#marketLanguageToggle.image-enable:hover{background:rgba(255,255,255,6.05%)}.languageButton{background:transparent;color:white;font-size:small;border-radius:2px}.languageButton.dark::part(content){color:${c.C}}.languageButton.dark-theme{color:black}`,h=i.i`
#signInButton{height:38px;position:relative;padding-inline-start:8px}.sign-in-button.short-text{width:70px;min-width:unset}.sign-in-button{height:36px;min-width:80px;margin-top:0px;transition-property:background,border,color;transition-duration:100ms;transition-timing-function:cubic-bezier(0.33,0,0.67,1);background-color:rgba(255,255,255,0.04);border:1px solid rgba(255,255,255,0.28);border-radius:4px}.sign-in-button:hover{background:rgba(255,255,255,0.14);border:1px solid rgba(255,255,255,0.46)}.sign-in-button:active{background:rgba(255,255,255,0);border:1px solid rgba(255,255,255,0.42)}.text{font-size:12px;color:rgba(255,255,255,0.84);-webkit-text-fill-color:rgba(255,255,255,0.84);font-size:14px}.sign-in-button.translucent{background:rgba(255,255,255,0.7);border:1px solid rgba(0,0,0,0.18)}.sign-in-button.translucent:hover{background:rgba(0,0,0,0.04);border:1px solid rgba(0,0,0,0.22)}.sign-in-button.translucent:active{background:rgba(0,0,0,0.12);border:1px solid rgba(0,0,0,0.30)}.sign-in-button.translucent:hover .text,.sign-in-button.translucent:active .text{color:rgba(0,0,0,0.86);-webkit-text-fill-color:rgba(0,0,0,0.86)}.sign-in-button:hover .text{color:#FFF;-webkit-text-fill-color:#FFF}.sign-in-button.translucent .text{color:rgba(0,0,0,0.74);-webkit-text-fill-color:rgba(0,0,0,0.74)}@media screen and (-ms-high-contrast:active){.text,text.translucent{color:unset !important}}`,u=i.i` ${d} :host{width:100%;--header-height:46px;--header-top-margin:32px;--subtle-background-hover:rgba(0,0,0,3.92%)}:host([showFullPageBlur="true"]){--subtle-background-hover:rgba(229,229,229,0.3)}:host(.search-sticky) .header-core{position:fixed;top:calc(var(--header-offset) + var(--header-top-margin));z-index:1}:host(.search-sticky) .header-start,:host(.search-sticky) .header-end{position:unset;top:unset}.header-start{inset-inline-start:var(--header-inline-margin)}.header-end{inset-inline-end:var(--header-inline-margin)}.header-end welcome-greeting-light::part(wea-popup-area){inset-inline-start:initial;inset-inline-end:0}enterprise-company-logo{padding-inline:8px}.overflow{visibility:hidden;position:absolute}sign-in-control,#signInButton{transition:0.1s ease-in-out}sign-in-control.overflow,#signInButton.overflow{transition-delay:0.1s;position:relative;margin-inline-end:calc(0px - var(--settings-shift,80px))}.header-icon-placeholder{display:flex;fill:white;cursor:pointer;width:fit-content;height:40px;justify-content:center;min-width:40px}.header-icon-placeholder svg{position:absolute;height:40px}.header-icon-placeholder.overflow{visibility:visible;position:initial}ruby-pivot-manager.overflow{visibility:visible;position:absolute;top:0;left:0}${(0,n.vW)(null,n.K$.c1)}{.header-core{grid-template:repeat(2,auto) / repeat(3,1fr)}:host(.search-sticky) .header-core{position:absolute;top:var(--header-offset)}.header-search,::slotted([slot="search"]){height:var(--header-height);justify-self:center;grid-area:2 / 1 / auto / 4}}`.withBehaviors((0,a.Uu)(l),new r.w("isInlineMLTButtonEnabled",!0,p),new r.w("isSICFlightEnabled",!0,h))},34833:function(){"undefined"!=typeof window&&window.performance&&window.performance.mark("ClientInit")},10510:function(e,t,o){"use strict";o(34833);var n=o(12184),i=o(40143),a=o(7379);const{registerExperience:r}=a.n;r(n.sM,(()=>Promise.resolve().then(o.bind(o,87747)))),r(n.Nx,(()=>Promise.resolve().then(o.bind(o,18871)))),r(n.RL,(()=>Promise.resolve().then(o.bind(o,63425)))),r(n.xE,(()=>Promise.resolve().then(o.bind(o,57351)))),r(n.Ur,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("shared-links")]).then(o.bind(o,46600)))),r(n.N9,(()=>o.e("msccCookieBanner").then(o.bind(o,4152)))),r(n.Qm,(()=>o.e("oneTrustCookieConsentData").then(o.bind(o,90567)))),r(n.x0,(()=>Promise.all([o.e("libs_topics-shared-state_dist_TopicData_connector_js-libs_topics-shared-state_dist_TopicData_-62f9da"),o.e("topicData")]).then(o.bind(o,98293)))),r(n.Py,(()=>o.e("category-data-connector").then(o.bind(o,54744)))),r(n.Z3,(()=>o.e("channel-data-connector").then(o.bind(o,81217)))),r(n.rz,(()=>o.e("layoutPreferenceData").then(o.bind(o,87588)))),r(n.S$,(()=>Promise.all([o.e("common-feed-libs"),o.e("libs_location-service_dist_AutoSuggestService_index_js-libs_location-service_dist_profiles_We-d085cf"),o.e("weather-card-data-connector")]).then(o.bind(o,16782)))),r(n.X,(()=>o.e("RewardsData").then(o.bind(o,7024)))),r(n.HS,(()=>o.e("SignInData").then(o.bind(o,65245)))),r(n.TA,(()=>Promise.all([o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("social-data-connector")]).then(o.bind(o,98492)))),r(n.Ou,(()=>o.e("manga-carousel").then(o.bind(o,337)))),r(i._lg,(()=>Promise.resolve().then(o.bind(o,66272)))),r(i.zK8,(()=>Promise.resolve().then(o.bind(o,32531)))),r(i.gLP,(()=>Promise.resolve().then(o.bind(o,90134)))),r(i.q31,(()=>Promise.resolve().then(o.bind(o,71612)))),r(n._S,(()=>o.e("admin-portal-data").then(o.bind(o,97685)))),r(n.tJ,(()=>o.e("office-shared-data").then(o.bind(o,49835)))),r(n.GL,(()=>o.e("shell-new-tab-data").then(o.bind(o,59194)))),r(i.lMJ,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("industry-news-card")]).then(o.bind(o,85158)))),r(i.QE3,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("summary-card-wc")]).then(o.bind(o,32940)))),r(i.lCU,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("office-feed")]).then(o.bind(o,56031)))),r(i.ZyT,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("office-feed")]).then(o.bind(o,19417)))),r(i.o4h,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("office-feed")]).then(o.bind(o,52542)))),r(i.ZjC,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("office-feed")]).then(o.bind(o,7478)))),r(i.D7S,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("office-feed")]).then(o.bind(o,13696)))),r(i.cdX,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("office-feed")]).then(o.bind(o,82464)))),r(i.yLt,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("office-feed")]).then(o.bind(o,82768)))),r(i.gOp,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_experiences-telemetry_dist_object-mappings_ContentMapping_js-libs_feed-layout_dist_card--de782c"),o.e("articleRelatedStories")]).then(o.bind(o,95443)))),r(i._9V,(()=>Promise.all([o.e("common-feed-libs"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("articleTopComment")]).then(o.bind(o,15145)))),r(i.y2o,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("boost-ad-card")]).then(o.bind(o,76616)))),r(i.OTW,(()=>o.e("background-gallery").then(o.bind(o,86769)))),r(i.jXL,(()=>o.e("bingHealthCard").then(o.bind(o,604)))),r(i.YtU,(()=>o.e("healthFitnessCarousel").then(o.bind(o,36755)))),r(i.X$j,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("card-actions-wc")]).then(o.bind(o,58515)))),r(i.N6,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("esports-library"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("node_modules_video_js_dist_video_es_js"),o.e("casual-games-card")]).then(o.bind(o,88375)))),r(i.WJ2,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("esports-library"),o.e("casual-games-carousel-card")]).then(o.bind(o,66671)))),r(i.utF,(()=>Promise.all([o.e("common-others"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("channel-store")]).then(o.bind(o,7827)))),r(i.J4S,(()=>Promise.all([o.e("common-cscore"),o.e("common-infopane-carouselcard"),o.e("cold-start-wc")]).then(o.bind(o,35055)))),r(i.g3x,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-settings-edgenext")]).then(o.bind(o,58395)))),r(i.iUi,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("community-card")]).then(o.bind(o,51158)))),r(i.XIX,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("content-group-card")]).then(o.bind(o,50143)))),r(i.j9W,(()=>Promise.all([o.e("common-feed-libs"),o.e("copilot-ruby-card")]).then(o.bind(o,56767)))),r(i.Mj4,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("nextdoor-card")]).then(o.bind(o,41546)))),r(i.AY3,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("company-news-card")]).then(o.bind(o,99988)))),r(i.KPU,(()=>o.e("SignInData").then(o.bind(o,45043)))),r(i.Up2,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_responsive-card_dist_index_js-libs_feedback-service_dist_FeedbackAuth_js-libs-c2d949"),o.e("dense-card")]).then(o.bind(o,28780)))),r(i.l1V,(()=>Promise.all([o.e("common-cscore"),o.e("dev-tools")]).then(o.bind(o,1005)))),r(i.Tu3,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("digest-card")]).then(o.bind(o,10073)))),r(i.OF7,(()=>Promise.all([o.e("common-feed-libs"),o.e("linked-in-card")]).then(o.bind(o,94018)))),r(i.ySJ,(()=>Promise.all([o.e("common-others"),o.e("common-cscore"),o.e("feedback")]).then(o.bind(o,75201)))),r(i.us7,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("health-tip-wc")]).then(o.bind(o,92188)))),r(i.QTZ,(()=>o.e("oneTrustCookieConsentData").then(o.bind(o,82794)))),r(i.aJp,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_topics-shared-state_dist_TopicData_connector_js-libs_topics-shared-state_dist_TopicData_-62f9da"),o.e("following-fre-full-width")]).then(o.bind(o,88801)))),r(i.KfJ,(()=>o.e("general-elections").then(o.bind(o,81925)))),r(i.LHB,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("election-card")]).then(o.bind(o,13149)))),r(i.wge,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("grid-view-feed")]).then(o.bind(o,23714)))),r(i.UE,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("horoscope-answer-card-wc")]).then(o.bind(o,80198)))),r(i.wwX,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("interest-fre-card")]).then(o.bind(o,63481)))),r(i.eHH,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("libs_location-service_dist_AutoSuggestService_index_js-libs_traffic_dist_index_js"),o.e("interests-wc")]).then(o.bind(o,36157)))),r(i.OiM,(()=>Promise.all([o.e("common-windows-widget-shared"),o.e("common-cscore"),o.e("interest-management-card")]).then(o.bind(o,27474)))),r(n.hZ,(()=>Promise.all([o.e("common-feed-libs"),o.e("libs_weather-common-utils_dist_data_FeedWeatherDataUtils_js-libs_weather-shared-wc_dist_utili-eedc04"),o.e("location-data")]).then(o.bind(o,85766)))),r(i.Q16,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("marketplace-card-wc")]).then(o.bind(o,51554)))),r(i.Czh,(()=>o.e("market-language-toggle-wc").then(o.bind(o,84128)))),r(i.r_z,(()=>o.e("mobile-popup-window").then(o.bind(o,31174)))),r(i.guo,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_data-transformer-shared_dist_index_js-libs_money-info-loader_dist_index_js-libs_money-da-adb667"),o.e("money-info")]).then(o.bind(o,26427)))),r(i.T5A,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_responsive-card_dist_index_js-libs_feedback-service_dist_FeedbackAuth_js-libs-c2d949"),o.e("libs_sports-data-service_dist_SportsDataMapper_js"),o.e("cs-core-desktop_gem-shared_dist_mappers_BaseGemMapper_js-libs_oneservice-card-provider_dist_O-1bf78a"),o.e("ruby-page")]).then(o.bind(o,86477)))),r(i.E6M,(()=>o.e("navigation-page-experience").then(o.bind(o,53629)))),r(i.IhH,(()=>Promise.all([o.e("common-others"),o.e("common-cscore"),o.e("notification-mini-card")]).then(o.bind(o,8593)))),r(i.PcW,(()=>o.e("nurturing-banner").then(o.bind(o,62789)))),r(i.isq,(()=>o.e("nurturing-coach-mark").then(o.bind(o,73055)))),r(n.X1,(()=>o.e("nurturing-placement-manager").then(o.bind(o,92669)))),r(i.vFd,(()=>o.e("office-coachmark").then(o.bind(o,2352)))),r(i.RGQ,(()=>Promise.all([o.e("common-cscore"),o.e("on-this-day-card-wc")]).then(o.bind(o,21176)))),r(i.J1W,(()=>o.e("personalize-nav-button").then(o.bind(o,25751)))),r(i.Fj8,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("polls-card-wc")]).then(o.bind(o,13556)))),r(i.mLM,(()=>o.e("poweredby-legend-wc").then(o.bind(o,94244)))),r(i.vPc,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("prism-sd-card")]).then(o.bind(o,77464)))),r(i.yHC,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-infopane-carouselcard"),o.e("qna-card")]).then(o.bind(o,89112)))),r(i.yuv,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("quiz-card")]).then(o.bind(o,19426)))),r(i.hn8,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("recipes-sd-card")]).then(o.bind(o,33258)))),r(i.WOb,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("recommendedSites-wc")]).then(o.bind(o,14302)))),r(i.IF2,(()=>o.e("rewards-card-wc").then(o.bind(o,3694)))),r(i.EeD,(()=>o.e("rewards-flyout").then(o.bind(o,55425)))),r(i.LoE,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_responsive-card_dist_index_js-libs_feedback-service_dist_FeedbackAuth_js-libs-c2d949"),o.e("libs_sports-data-service_dist_SportsDataMapper_js"),o.e("cs-core-desktop_gem-shared_dist_mappers_BaseGemMapper_js-libs_oneservice-card-provider_dist_O-1bf78a"),o.e("ruby-page")]).then(o.bind(o,53022)))),r(i.Mb7,(()=>o.e("common-segments").then(o.bind(o,21964)))),r(i.x6e,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("select-carousel")]).then(o.bind(o,82837)))),r(i.Es2,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("signal-overlay")]).then(o.bind(o,65762)))),r(i.Vrn,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("shared-links")]).then(o.bind(o,80252)))),r(i.X8y,(()=>Promise.all([o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("top-sites-bookmarks")]).then(o.bind(o,81357)))),r(i.PP6,(()=>Promise.all([o.e("common-feed-libs"),o.e("sharepoint-news-card")]).then(o.bind(o,92045)))),r(i.CRe,(()=>o.e("shopping-card-actions").then(o.bind(o,84794)))),r(i.Tk6,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_shopping-utils_dist_ntpCarouselItem_index_js-web-components_shopping-super-carousel_dist-e7e62a"),o.e("experiences_shopping-entry-base-experience_dist_ShoppingEntryBaseExperience_js"),o.e("experiences_shopping-entry-base-experience_dist_utils_formatter_utils_js-libs_channel-page-ut-adec5b"),o.e("shopping-card-wce")]).then(o.bind(o,21632)))),r(i.gRh,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("shopping-gem-hero-card")]).then(o.bind(o,98361)))),r(i.lIM,(()=>o.e("social-avatar").then(o.bind(o,70012)))),r(i.nUs,(()=>Promise.all([o.e("common-feed-libs"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("social-bar-wc")]).then(o.bind(o,81321)))),r(i.vb7,(()=>Promise.all([o.e("common-cscore"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("social-report-menu")]).then(o.bind(o,16675)))),r(i.Dzg,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("premium-profile-promo-card")]).then(o.bind(o,68965)))),r(i.Vhw,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-sports-lib"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("sports-info")]).then(o.bind(o,89909)))),r(i.GzM,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("spotlight-card-wc")]).then(o.bind(o,78513)))),r(i.c8c,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("super-nav")]).then(o.bind(o,63370)))),r(i.qZt,(()=>o.e("toast-wc").then(o.bind(o,77927)))),r(i.JiM,(()=>Promise.all([o.e("common-cscore"),o.e("pill-wc")]).then(o.bind(o,18808)))),r(i.wr4,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("publisher-subscribe-follow-button")]).then(o.bind(o,52098)))),r(i._jE,(()=>o.e("sign-in-control-wc").then(o.bind(o,68)))),r(i.ItY,(()=>o.e("sign-in-flyout-wc").then(o.bind(o,4077)))),r(i.yn7,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_location-service_dist_AutoSuggestService_index_js-libs_traffic_dist_index_js"),o.e("traffic-card-wc")]).then(o.bind(o,77694)))),r(i.OX5,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_location-service_dist_AutoSuggestService_index_js-libs_traffic_dist_index_js"),o.e("traffic-hero")]).then(o.bind(o,50026)))),r(i.opx,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-infopane-carouselcard"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_travel_dist_index_js"),o.e("travel-destination")]).then(o.bind(o,38122)))),r(i.q7$,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_travel_dist_index_js"),o.e("travel-carousel")]).then(o.bind(o,83599)))),r(i.PkV,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_travel_dist_index_js"),o.e("travel-hotel-carousel")]).then(o.bind(o,31595)))),r(i.$h8,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_travel_dist_index_js"),o.e("travel-flight-carousel")]).then(o.bind(o,22644)))),r(i.EYD,(()=>Promise.all([o.e("common-cscore"),o.e("waffle-wc")]).then(o.bind(o,63467)))),r(i.F11,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("waterfall-view-feed")]).then(o.bind(o,35730)))),r(i.$38,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("d3-library"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_weather-common-utils_dist_data_FeedWeatherDataUtils_js-libs_weather-shared-wc_dist_utili-eedc04"),o.e("libs_weather-shared-wc_dist_utilities_entry_cardSelector_CardSelectorProd_js-libs_weather-sha-e923d9"),o.e("libs_weather-shared-wc_dist_weather-card_index_js-libs_weather-skycode-mapping-svgr_dist_SkyC-851a44"),o.e("weather-card-wc")]).then(o.bind(o,96441)))),r(i.gTT,(()=>Promise.all([o.e("common-feed-libs"),o.e("d3-library"),o.e("libs_weather-common-utils_dist_data_FeedWeatherDataUtils_js-libs_weather-shared-wc_dist_utili-eedc04"),o.e("weather-top-section-card")]).then(o.bind(o,21229)))),r(i.VnC,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("d3-library"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("weather-hero-experience")]).then(o.bind(o,75636)))),r(i.vTu,(()=>Promise.all([o.e("common-feed-libs"),o.e("weather-minimap-wc")]).then(o.bind(o,65955)))),r(i.sJx,(()=>o.e("weather-video-entry-wc").then(o.bind(o,14702)))),r(i.ihw,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("weather-one-liner")]).then(o.bind(o,25964)))),r(i.YD1,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("d3-library"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_weather-shared-wc_dist_weather-card_index_js-libs_weather-skycode-mapping-svgr_dist_SkyC-851a44"),o.e("welcomeGreetingLight")]).then(o.bind(o,84382)))),r(i.UtR,(()=>Promise.all([o.e("common-feed-libs"),o.e("widgets-region")]).then(o.bind(o,25763)))),r(i.rc4,(()=>o.e("xfeed").then(o.bind(o,5941)))),r(i.bN3,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("prism-carousel-card")]).then(o.bind(o,21459)))),r(i.veX,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("trending-search-card")]).then(o.bind(o,42152)))),r(i.kvh,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("real-estate-card")]).then(o.bind(o,80522)))),r(i.Xz8,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_shopping-utils_dist_ntpCarouselItem_index_js-web-components_shopping-super-carousel_dist-e7e62a"),o.e("shopping-events-deal-card")]).then(o.bind(o,32584)))),r(i.BKx,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_shopping-utils_dist_ntpCarouselItem_index_js-web-components_shopping-super-carousel_dist-e7e62a"),o.e("experiences_shopping-entry-base-experience_dist_ShoppingEntryBaseExperience_js"),o.e("shopping-sd-card")]).then(o.bind(o,22069)))),r(i.rnA,(()=>o.e("superBreakingNews").then(o.bind(o,67735)))),r(i.vG7,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("manga-card")]).then(o.bind(o,60262)))),r(i.u4d,(()=>o.e("manga-carousel").then(o.bind(o,67193)))),r(i.Esr,(()=>o.e("na-trending").then(o.bind(o,26020)))),r(i.u8s,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-infopane-carouselcard"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_travel_dist_index_js"),o.e("travel-sd-themes-card")]).then(o.bind(o,68481)))),r(i.gOk,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_travel_dist_index_js"),o.e("travel-sd-destinations-card")]).then(o.bind(o,68596)))),r(i.R5X,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_travel_dist_index_js"),o.e("travel-sd-hotels-card")]).then(o.bind(o,29806)))),r(i.kM8,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_travel_dist_index_js"),o.e("travel-sd-flights-card")]).then(o.bind(o,75892)))),r(i.WfD,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_travel_dist_index_js"),o.e("travel-sd-flights-card")]).then(o.bind(o,26971)))),r(i.ISs,(()=>Promise.all([o.e("common-windows-widget-shared"),o.e("feed-level-feedback-wc")]).then(o.bind(o,41164)))),r(i.wRm,(()=>Promise.all([o.e("common-cscore"),o.e("contextual-feedback-wc")]).then(o.bind(o,71406)))),r(i.iOK,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("hot-list-card")]).then(o.bind(o,74646)))),r(i.pru,(()=>Promise.all([o.e("common-cscore"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("channel-filter-card")]).then(o.bind(o,85361)))),r(i.jK4,(()=>o.e("clarity").then(o.bind(o,20032)))),r(i.T4c,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("libs_shopping-utils_dist_ntpCarouselItem_index_js-web-components_shopping-super-carousel_dist-e7e62a"),o.e("experiences_shopping-entry-base-experience_dist_ShoppingEntryBaseExperience_js"),o.e("experiences_shopping-entry-base-experience_dist_utils_formatter_utils_js-libs_channel-page-ut-adec5b"),o.e("shopping-grouped-carousel")]).then(o.bind(o,20318)))),r(i.jjB,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-fast-msft-web-components"),o.e("cs-core-desktop_responsive-card_dist_index_js-libs_feedback-service_dist_FeedbackAuth_js-libs-c2d949"),o.e("nas-highlight-v1"),o.e("views-native-mon")]).then(o.bind(o,46393)))),r(i.J$A,(()=>Promise.all([o.e("libs_data-transformer-shared_dist_index_js-libs_money-info-loader_dist_index_js-libs_money-da-adb667"),o.e("money-augment-card")]).then(o.bind(o,64030)))),r(i.CSR,(()=>Promise.all([o.e("common-feed-libs"),o.e("common-sports-lib"),o.e("libs_sports-data-service_dist_SportsDataMapper_js"),o.e("sports-augment-card")]).then(o.bind(o,58068)))),r(i.hRd,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("entertainment-premier-card")]).then(o.bind(o,28479)))),r(i.fyI,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("donation-npo-card")]).then(o.bind(o,96469)))),r(i.p_9,(()=>o.e("common-auto-suggest").then(o.bind(o,50318)))),r(i.ZwZ,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("rich-calendar-card")]).then(o.bind(o,97530)))),r(i.jZ0,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_shopping-utils_dist_ntpCarouselItem_index_js-web-components_shopping-super-carousel_dist-e7e62a"),o.e("shopping-augment-card")]).then(o.bind(o,11729)))),r(i.mo8,(()=>o.e("float-button-group-wc").then(o.bind(o,34835)))),r(i._hl,(()=>o.e("hero-container-wc").then(o.bind(o,12501)))),r(i.Kfg,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("shared-hero-news-card")]).then(o.bind(o,49215)))),r(i.wiB,(()=>Promise.all([o.e("common-cscore"),o.e("issue-reporting-wc")]).then(o.bind(o,70455)))),r(i.nZh,(()=>o.e("streaks-badge-wc").then(o.bind(o,75889)))),r(i.ckV,(()=>o.e("health-augment-card").then(o.bind(o,62763)))),r(i.sPK,(()=>Promise.all([o.e("common-others"),o.e("pulse-edgenext")]).then(o.bind(o,3810)))),r(i.lvU,(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("experiences_shopping-entry-base-experience_dist_ShoppingEntryBaseExperience_js"),o.e("shopping-river-card")]).then(o.bind(o,35052)))),r(i.wRF,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("moment-in-time-hero")]).then(o.bind(o,73634)))),r(i.lJY,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("daily-brief-card")]).then(o.bind(o,18373)))),r(i.RmL,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("daily-wonder-copilot-card")]).then(o.bind(o,7115)))),r(i.tCy,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-windows-widget-shared"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("cs-core-desktop_responsive-card_dist_index_js-libs_feedback-service_dist_FeedbackAuth_js-libs-c2d949"),o.e("daily-moments-card")]).then(o.bind(o,57343)))),r(i.SNA,(()=>Promise.all([o.e("common-feed-libs"),o.e("daily-wonder-card")]).then(o.bind(o,79513)))),r(i.Lfv,(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7"),o.e("libs_social-data-service_dist_service_SocialService_js"),o.e("top-comments-card")]).then(o.bind(o,14850)))),r(i.m6X,(()=>o.e("companion-widget").then(o.bind(o,23450))));o(27868),o(67463);var s=JSON.parse('{"pivots":[{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":true},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"coreJSCache":"partial"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumFeedSetting":"off"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumFeedSetting":"off"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"inspirational"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":true,"edgeChromiumLayoutMode":"inspirational"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"focused"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"custom"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":true,"edgeChromiumLayoutMode":"informational"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr","network":"4g","cpuGroup":"1-4","coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr","network":"4g","cpuGroup":"1-4","coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr","network":"4g","cpuGroup":"6-8","coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr","network":"4g","cpuGroup":"6-8","coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr","network":"4g","cpuGroup":"1-4","coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr","network":"4g","cpuGroup":"1-4","coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr","network":"4g","cpuGroup":"6-8","coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr","network":"4g","cpuGroup":"6-8","coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"fallback-csr"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":true},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"coreJSCache":"partial"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"inspirational"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"focused"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"custom"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr","network":"4g","cpuGroup":"1-4","coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr","network":"4g","cpuGroup":"1-4","coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr","network":"4g","cpuGroup":"6-8","coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"ssr","network":"4g","cpuGroup":"6-8","coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr","network":"4g","cpuGroup":"1-4","coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr","network":"4g","cpuGroup":"1-4","coreJSCache":"warm"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr","network":"4g","cpuGroup":"6-8","coreJSCache":"cold"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"edgeChromiumLayoutMode":"informational","renderArch":"csr","network":"4g","cpuGroup":"6-8","coreJSCache":"warm"},{"pageProduct":"entnews","pageType":"dhp","pageName":"default","preRender":true},{"pageProduct":"entnews","pageType":"dhp","pageName":"default","preRender":false},{"pageProduct":"entnews","pageType":"ntp","pageName":"default","preRender":true},{"pageProduct":"entnews","pageType":"ntp","pageName":"default","preRender":false},{"pageProduct":"entnews","pageType":"ntp","pageName":"entnewshp","preRender":true},{"pageProduct":"entnews","pageType":"ntp","pageName":"entnewshp","preRender":false},{"pageProduct":"entnews","pageType":"dhp","pageName":"entnewshp","preRender":true},{"pageProduct":"entnews","pageType":"dhp","pageName":"entnewshp","preRender":false},{"pageProduct":"anaheim","pageType":"verthp","pageName":"gaming","preRender":true},{"pageProduct":"anaheim","pageType":"verthp","pageName":"gaming","preRender":false},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-callpcsfrakm"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-callpcsfrafd"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-callpcsfdshp"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-callpcsfrakm"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-callpcsfrafd"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-callpcsfdshp"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-ctrlnoflw"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-pubflw2"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-waist-bct"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-waist-bdg"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-waist-btn"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-ctrlnoflw"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-pubflw2"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-waist-bct"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-waist-bdg"},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-waist-btn"},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":true,"experimentId":"prg-ntpcompdict","_sampleRate":50},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-ntpcompdict","_sampleRate":50},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":true,"experimentId":"prg-ntpcompdict","_sampleRate":50},{"pageProduct":"anaheim","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-ntpcompdict","_sampleRate":50},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-telemetry-trace","_sampleRate":15},{"pageProduct":"anaheim","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-telemetry-tracec","_sampleRate":15},{"pageProduct":"entnews","pageType":"ntp","pageName":"default","preRender":true,"experimentId":"prg-ntpcompdict","_sampleRate":100},{"pageProduct":"entnews","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"prg-ntpcompdict","_sampleRate":100},{"pageProduct":"entnews","pageType":"dhp","pageName":"default","preRender":true,"experimentId":"prg-ntpcompdict","_sampleRate":100},{"pageProduct":"entnews","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"prg-ntpcompdict","_sampleRate":100},{"pageProduct":"entnews","pageType":"ntp","pageName":"default","preRender":true,"experimentId":"ntpcomp-ctrl","_sampleRate":100},{"pageProduct":"entnews","pageType":"ntp","pageName":"default","preRender":false,"experimentId":"ntpcomp-ctrl","_sampleRate":100},{"pageProduct":"entnews","pageType":"dhp","pageName":"default","preRender":true,"experimentId":"ntpcomp-ctrl","_sampleRate":100},{"pageProduct":"entnews","pageType":"dhp","pageName":"default","preRender":false,"experimentId":"ntpcomp-ctrl","_sampleRate":100}]}'),c=o.t(s,2),d=o(37426),l=o(33394),p=o(90633),h=o(28079),u=o(8797),g=o(51718),m=o(85070),f=o(29985),b=o(94723),y=o(52383),w=o(77886),x=o(49988),v=o(50725),_=o(4511),S=o(63646),C=o(43522),T=o(53241),k=o(44698),A=o(96814),$=o(18883),E=o(14198),P=o(86701),I=o(89181),B=o(17975),F=o(71992),L=o(20291),D=o(56175),M=o(91127),R=o(63275),O=o(53819),j=o(9726),U=o(21602),H=o(24090),N=o(25199),z=o(92209),W=o(34223),G=o(97390),q=o(94718),V=o(81858),K=o(13798),Y=o(92133),Q=o(24310),J=o(76696),X=o(18892),Z=o(1519),ee=o(10059),te=o(11534),oe=o(35109),ne=o(7048),ie=o(54366),ae=o(36109),re=o(32173),se=o(75939),ce=o(59037),de=o(69951),le=o(65425),pe=o(91731),he=o(98575),ue=o(44671),ge=o(77542),me=o(99998),fe=o(91939),be=o(83643),ye=o(47456),we=o(44585),xe=o(22398),ve=o(45997),_e=o(67795),Se=o(97886),Ce=o(89956),Te=o(31253),ke=o(53863),Ae=o(51280),$e=o(4355),Ee=o(75431),Pe=o(59624),Ie=o(74415),Be=o(78746),Fe=o(72612),Le=o(82229),De=o(48353),Me=o(44057),Re=o(72657),Oe=o(27197),je=o(62010),Ue=o(15231),He=o(69361),Ne=o(67830),ze=o(93566);var We=o(90343),Ge=o(15861),qe=o(92939),Ve=o(90755),Ke=o(7738),Ye=o(93393),Qe=o(65410),Je=o(8677),Xe=o(52704),Ze=o(86823),et=o(20968),tt=o(99690),ot=o(4890),nt=o(4575),it=o(67748);new Set([{instanceId:null,configRef:{experienceType:"EdgeChromiumPageWC",instanceSrc:"default"}},{instanceId:"BackgroundImageWC",configRef:{experienceType:"BackgroundImageWC",instanceSrc:"default"}},{instanceId:"CasualGamesCard",configRef:{experienceType:"CasualGamesCard",instanceSrc:"default"}},{instanceId:"CommonSearchBoxEdgeNext",configRef:{experienceType:"CommonSearchBoxEdgeNext",instanceSrc:"default"}},{instanceId:"CommonSettingsEdgeNext",configRef:{experienceType:"CommonSettingsEdgeNext",instanceSrc:"default"}},{instanceId:"CommunityCard",configRef:{experienceType:"CommunityCard",instanceSrc:"default"}},{instanceId:"ContentGroupCard",configRef:{experienceType:"ContentGroupCard",instanceSrc:"default"}},{instanceId:"DailyMomentsCard",configRef:{experienceType:"DailyMomentsCard",instanceSrc:"default"}},{instanceId:"DenseCard",configRef:{experienceType:"DenseCard",instanceSrc:"default"}},{instanceId:"DigestCard",configRef:{experienceType:"DigestCard",instanceSrc:"default"}},{instanceId:"DonationNpoCard",configRef:{experienceType:"DonationNpoCard",instanceSrc:"default"}},{instanceId:"FloatButtonGroupWC",configRef:{experienceType:"FloatButtonGroupWC",instanceSrc:"default"}},{instanceId:"GridViewFeed",configRef:{experienceType:"GridViewFeed",instanceSrc:"default"}},{instanceId:"HoroscopeAnswerCardWC",configRef:{experienceType:"HoroscopeAnswerCardWC",instanceSrc:"default"}},{instanceId:"HotListCard",configRef:{experienceType:"HotListCard",instanceSrc:"default"}},{instanceId:"MarketLanguageToggleWC",configRef:{experienceType:"MarketLanguageToggleWC",instanceSrc:"default"}},{instanceId:"MoneyInfo",configRef:{experienceType:"MoneyInfo",instanceSrc:"default"}},{instanceId:"NextdoorCard",configRef:{experienceType:"NextdoorCard",instanceSrc:"default"}},{instanceId:"RealEstateCard",configRef:{experienceType:"RealEstateCard",instanceSrc:"default"}},{instanceId:"EntertainmentPremierCard",configRef:{experienceType:"EntertainmentPremierCard",instanceSrc:"default"}},{instanceId:"RecommendedSitesWC",configRef:{experienceType:"RecommendedSitesWC",instanceSrc:"default"}},{instanceId:"SharedHeroNewsCard",configRef:{experienceType:"SharedHeroNewsCard",instanceSrc:"default"}},{instanceId:"ShoppingCardWC",configRef:{experienceType:"ShoppingCardWC",instanceSrc:"default"}},{instanceId:"ShoppingRiverCard",configRef:{experienceType:"ShoppingRiverCard",instanceSrc:"default"}},{instanceId:"ShoppingSdCard",configRef:{experienceType:"ShoppingSdCard",instanceSrc:"default"}},{instanceId:"SuperNav",configRef:{experienceType:"SuperNav",instanceSrc:"default"}},{instanceId:"TopSitesEdgeNextWC",configRef:{experienceType:"TopSitesEdgeNextWC",instanceSrc:"default"}},{instanceId:"TrafficCardWC",configRef:{experienceType:"TrafficCardWC",instanceSrc:"default"}},{instanceId:"TrafficHero",configRef:{experienceType:"TrafficHero",instanceSrc:"default"}},{instanceId:"MomentInTimeHero",configRef:{experienceType:"MomentInTimeHero",instanceSrc:"default"}},{instanceId:"TravelDestination",configRef:{experienceType:"TravelDestination",instanceSrc:"default"}},{instanceId:"TravelHotelCarousel",configRef:{experienceType:"TravelHotelCarousel",instanceSrc:"default"}},{instanceId:"TravelSdDestinationsCard",configRef:{experienceType:"TravelSdDestinationsCard",instanceSrc:"default"}},{instanceId:"TravelSdThemesCard",configRef:{experienceType:"TravelSdThemesCard",instanceSrc:"default"}},{instanceId:"TravelFlightCarousel",configRef:{experienceType:"TravelFlightCarousel",instanceSrc:"default"}},{instanceId:"TravelSdFlightsCard",configRef:{experienceType:"TravelSdFlightsCard",instanceSrc:"default"}},{instanceId:"TravelSdCarrentalsCard",configRef:{experienceType:"TravelSdCarrentalsCard",instanceSrc:"default"}},{instanceId:"TravelSdHotelsCard",configRef:{experienceType:"TravelSdHotelsCard",instanceSrc:"default"}},{instanceId:"TrendingSearchCard",configRef:{experienceType:"TrendingSearchCard",instanceSrc:"default"}},{instanceId:"WaterfallViewFeed",configRef:{experienceType:"WaterfallViewFeed",instanceSrc:"default"}},{instanceId:"WeatherCardWC",configRef:{experienceType:"WeatherCardWC",instanceSrc:"default"}},{instanceId:"WeatherHeroExperience",configRef:{experienceType:"WeatherHeroExperience",instanceSrc:"default"}},{instanceId:"WeatherMinimapWC",configRef:{experienceType:"WeatherMinimapWC",instanceSrc:"default"}},{instanceId:"WeatherMinimapWC",configRef:{experienceType:"WeatherMinimapWC",instanceSrc:"ruby",sharedNs:"msn-ns"}},{instanceId:"WeatherVideoEntryWC",configRef:{experienceType:"WeatherVideoEntryWC",instanceSrc:"default"}},{instanceId:"WelcomeGreetingLight",configRef:{experienceType:"WelcomeGreetingLight",instanceSrc:"default"}},{instanceId:"WidgetsRegion",configRef:{experienceType:"WidgetsRegion",instanceSrc:"default"}},{instanceId:"TopCommentsCard",configRef:{experienceType:"TopCommentsCard",instanceSrc:"default"}},{instanceId:"SportsInfo",configRef:{experienceType:"SportsInfo",instanceSrc:"default"}},{instanceId:"SpotlightCardWC",configRef:{experienceType:"SpotlightCardWC",instanceSrc:"default"}},{instanceId:"HealthTipWC",configRef:{experienceType:"HealthTipWC",instanceSrc:"default"}},{instanceId:"HealthAugmentCard",configRef:{experienceType:"HealthAugmentCard",instanceSrc:"default"}},{instanceId:"MarketplaceCardWC",configRef:{experienceType:"MarketplaceCardWC",instanceSrc:"default"}},{instanceId:"BoostAdCard",configRef:{experienceType:"BoostAdCard",instanceSrc:"default"}},{instanceId:"CodexBingChat",configRef:{experienceType:"CodexBingChat",instanceSrc:"default"}},{instanceId:"SignInControlWC",configRef:{experienceType:"SignInControlWC",instanceSrc:"default"}},{instanceId:"QuizCard",configRef:{experienceType:"QuizCard",instanceSrc:"default"}},{instanceId:"ElectionCard",configRef:{experienceType:"ElectionCard",instanceSrc:"default"}},{instanceId:"MoneyAugmentCard",configRef:{experienceType:"MoneyAugmentCard",instanceSrc:"default"}},{instanceId:"SportsAugmentCard",configRef:{experienceType:"SportsAugmentCard",instanceSrc:"default"}},{instanceId:"ShoppingAugmentCard",configRef:{experienceType:"ShoppingAugmentCard",instanceSrc:"default"}},{instanceId:"RichCalendarCard",configRef:{experienceType:"RichCalendarCard",instanceSrc:"default"}},{instanceId:"WaffleWC",configRef:{experienceType:"WaffleWC",instanceSrc:"default"}},{instanceId:"FollowingFreFullWidth",configRef:{experienceType:"FollowingFreFullWidth",instanceSrc:"default"}},{instanceId:"SelectCarousel",configRef:{experienceType:"SelectCarousel",instanceSrc:"default"}},{instanceId:"PrismSdCard",configRef:{experienceType:"PrismSdCard",instanceSrc:"default"}},{instanceId:"HeroContainerWC",configRef:{experienceType:"HeroContainerWC",instanceSrc:"default"}},{instanceId:"DailyWonderCard",configRef:{experienceType:"DailyWonderCard",instanceSrc:"default"}},{instanceId:"DailyWonderCopilotCard",configRef:{experienceType:"DailyWonderCopilotCard",instanceSrc:"default"}},{instanceId:"DailyBriefCard",configRef:{experienceType:"DailyBriefCard",instanceSrc:"default"}},{instanceId:"WeatherTopSectionCard",configRef:{experienceType:"WeatherTopSectionCard",instanceSrc:"default",sharedNs:"msn-ns"}},{instanceId:"CopilotRubyCard",configRef:{experienceType:"CopilotRubyCard",instanceSrc:"default"}}].map((e=>(0,it.ITY)(e.configRef.experienceType))));const at=[i._lg,i.c8c,i.wge,i.Up2,i.Tu3,i.guo,i.Vhw,i.Tk6,i.BKx,i.lvU,i.yn7,i.OX5,i.opx,i.gOk,i.u8s,i.us7,i.$38,i.Q16,i.F11,i.VnC,i.UtR,i.Kfg,i.T5A].map(it.ITY),rt=["waterfall-view-feed","grid-view-feed","msn-feed","cs-responsive-infopane"];n._S,n.Py,n.Z3,n.x4,n.l2,n.rz,n.N9,n.X1,n.tJ,n.Qm,n.$2,n.X,n.Ou,n.GL,n.HS,i.gOp,i._9V,i.OTW,i.jXL,i.IFz,i.yLt,i.X$j,i.WJ2,i.wUc,i.ieX,i.pru,i.ya$,i.utF,i.p_9,i.AY3,i.wRm,i.J4S,i.l1V,i.WGZ,i.NuV,i.ySJ,i.ISs,i.mo8,i.QTZ,i.cdX,i.KfJ,i.YtU,i.lMJ,i.OiM,i.wwX,i.eHH,i.OF7,i.vG7,i.tJU,i.E6M,i.lCU,i.PcW,i.isq,i.IhH,i.ZyT,i.vFd,i.o4h,i.RGQ,i.J1W,i.JiM,i.Fj8,i.mLM,i.Dzg,i.bN3,i.D7S,i.wr4,i.yHC,i.hn8,i.IF2,i.Esr,i.Mb7,i.eMK,i.Vrn,i.PP6,i.Xz8,i.ItY,i.lIM,i.nUs,i.vb7,i.bru,i.udo,i.z82,i.nZh,i.QE3,i.rnA,i.qZt,i.ZjC,i.q7$,i.lTG,i.KPU,i.rc4,i.ihw,i.sPK,i.EeD;var st=o(43708),ct=o(98245),dt=o(36492),lt=o(53315),pt=o(54352),ht=o(66622),ut=o(84560),gt=o(42805),mt=o(91728),ft=o(41213),bt=o(87819),yt=o(53518),wt=o(8272),xt=o(6964),vt=o(98283);const _t={[i.guo]:i.tJU,[i.Vhw]:i.bru,[i.PkV]:"TravelDestinationCarousel",[i.$h8]:"TravelDestinationCarousel",[i.gOk]:i.opx,[i.WfD]:i.opx,[i.R5X]:i.opx,[i.u8s]:i.opx};function St(){const e=(0,T.Ou)(),t=(0,ve.S0)(),o={"cs-flipper":{mode:de.a5.Never},"cs-super-nav-horizontal-item":{mode:de.a5.Never},"fluent-button":{mode:de.a5.Never},"cs-article-card":{mode:[de.a5.IdleAfterTtvr,de.a5.OnHover,de.a5.OnPageResize],markTtvr:e=>e.data?.visualReadinessCallback?.(xt.uf.VP,vt.w.provider)},"cs-article-card-28":{mode:[de.a5.IdleAfterTtvr,de.a5.OnHover,de.a5.OnPageResize],markTtvr:e=>e.data?.visualReadinessCallback?.(xt.uf.VP,vt.w.provider)},"cs-responsive-card":{mode:[de.a5.IdleAfterTtvr,de.a5.OnHover,de.a5.OnPageResize],markTtvr:e=>e.visualReadinessCallback?.()},"cs-attribution":{mode:[de.a5.OnHover]},[(0,it.ITY)(i._lg)]:{mode:de.a5.Immediate},[(0,it.ITY)(i.wge)]:{mode:de.a5.Immediate},[(0,it.ITY)(i.F11)]:{mode:de.a5.Immediate},[(0,it.ITY)(i.T5A)]:{mode:de.a5.Immediate},[(0,it.ITY)(i.Up2)]:{mode:de.a5.Immediate},[(0,it.ITY)(i.GHS)]:{mode:de.a5.Immediate},[(0,it.ITY)(i.rER)]:{mode:[de.a5.OnHover]},[(0,it.ITY)(i.EYD)]:{mode:[de.a5.OnHover]},[(0,it.ITY)(i.nUs)]:{mode:[de.a5.OnHover]},[(0,it.ITY)(i.zK8)]:{mode:[de.a5.IdleAfterTtvr,de.a5.OnHover],markTtvr:()=>{(0,D.c)((()=>{const o=e[L.nz+wt.p.searchBox];t&&_e.g.updateVisuallyReadyTiming.getActionSender(t).send({experienceType:"CommonSearchBoxEdgeNext",experienceInstance:"CommonSearchBoxEdgeNext",startTime:void 0,endTime:o}),(0,L.o_)(i.zK8,!o,o),(0,L.o_)("SearchBox",!o,o),(0,L.BW)("SearchBox",!o,o)}))}}};return window.ssrLoadedExperience?.subscribe((n=>{o[(0,it.ITY)(n.configRef.experienceType)]?.mode!==de.a5.Immediate&&(o[(0,it.ITY)(n.configRef.experienceType)]??={mode:[de.a5.IdleAfterTtvr,de.a5.OnHover]},o[(0,it.ITY)(n.configRef.experienceType)].markTtvr??=()=>{(0,D.c)((()=>{const o=e[L.nz+n.configRef.experienceType]||e[L.nz+wt.p.complete];t&&_e.g.updateVisuallyReadyTiming.getActionSender(t).send({experienceType:n.configRef.experienceType,experienceInstance:n.instanceId,startTime:void 0,endTime:o}),(0,L.o_)(n.configRef.experienceType,!o,o),_t[n.configRef.experienceType]&&(0,L.o_)(_t[n.configRef.experienceType],!o,o)}))})})),o}var Ct=o(61244),Tt=o(1539),kt=o(30449),At=o(16614);class $t{constructor(e,t,o){this.pageSettingsConnector=e,this.isMSASignedin=t,this.currentLayoutState=o,this.fireTelemetryBeacon=!0,this.edgeChromiumPgltCookieName="pglt",this.cookieExpireInDays=182,this.firstLaunchParam=new URLSearchParams(window.location.search).get("firstlaunch");const n=tt.M0.getPageMetadata();if(n&&n.contract&&n.contract.page){const e=(0,Ct.yc)(o,this.isMSASignedin);tt.M0.updatePageConfiguration(e),this.setPgltCookie(String(e)),this.stampTmpl(this.currentLayoutState)}document.dispatchEvent(new Event("trackvaluesupdated",{bubbles:!0,cancelable:!0})),this.registerObservers()}pageSettingTelemetry(e){if("visible"===document.visibilityState){const t=(0,Ct.yc)(e,this.isMSASignedin);tt.M0.updatePageConfiguration(t),this.setPgltCookie(String(t)),this.stampTmpl(e)}}registerObservers(){kt.J.updateLayout.registerObserver((e=>{const t=e&&e.params;!t||t.length<1||this.pageSettingTelemetry(t[0])})),Tt.I.layoutAnimationComplete.registerObserver((()=>{this.pageSettingsConnector.getLayoutStateAsync().then((e=>{if("1"===this.firstLaunchParam){if(!this.fireTelemetryBeacon)return;this.fireTelemetryBeacon=!1}this.currentLayoutState.backgroundImageTypePolicy===e.backgroundImageTypePolicy&&this.currentLayoutState.currentBackgroundImageType===e.currentBackgroundImageType&&this.currentLayoutState.currentLayout===e.currentLayout&&(0,At.Z)(this.currentLayoutState.customBackgroundImageInfo,e.customBackgroundImageInfo)&&this.currentLayoutState.greetingEnabled===e.greetingEnabled&&this.currentLayoutState.imageOfTheDayEnabled===e.imageOfTheDayEnabled&&this.currentLayoutState.market===e.market&&this.currentLayoutState.selectedFeedDisplaySetting===e.selectedFeedDisplaySetting||(this.currentLayoutState=e,(0,Qe.TR)()||(0,Ye.eC)().then((()=>{tt.M0.sendPageView(null,!1)})))}))}))}setPgltCookie(e){let t=this.edgeChromiumPgltCookieName;const{apptype:o,pagetype:n}=W.Al.ClientSettings?W.Al.ClientSettings:{apptype:void 0,pagetype:void 0};o&&n&&(t+="-"+W.Al.ClientSettings.apptype+"-"+W.Al.ClientSettings.pagetype),(0,fe.sq)(t,e,this.cookieExpireInDays,G.jG.HostPage.topDomain)}stampTmpl(e){this.stampHardware();const t=(0,Ne.GQ)();if(!t)return;const o=window.innerWidth,n=window.outerWidth;if(!o||!n)return;let i=0;const a=!window.screenTop&&!window.screenY?0:14;e.verticalTabsOpened&&(i=e.verticalTabsCollapsed?48:256);const r=Math.round(1e4*(n-a-i)/innerWidth)/1e4;if(!r)return;const s=Math.round((n-a)/r);if(s&&(tt.M0.addOrUpdateTmplProperty("edge.zoom",r+""),tt.M0.addOrUpdateTmplProperty("edge_ntp.actualWidth",o+""),tt.M0.addOrUpdateTmplProperty("edge_ntp.expectedWidth",s+""),e.verticalTabsOpened)){if(tt.M0.addOrUpdateTmplProperty("vertical_tabs.opened","1"),tt.M0.addOrUpdateTmplProperty("vertical_tabs.collapsed",e.verticalTabsCollapsed?"1":"0"),s<=o)return;let n=1,i=1;for(let e=t.length-1;e>1;e--){const a=t[e].minViewportWidthPx;if(!a||1!=n&&1!=i)break;1==n&&s>a&&(n=e),1==i&&o>a&&(i=e)}n>i&&(tt.M0.addOrUpdateTmplProperty("vertical_tabs.eligiblecol",n+1+""),tt.M0.addOrUpdateTmplProperty("vertical_tabs.showncol",i+1+""))}}stampHardware(){const e=window.navigator;e.deviceMemory&&tt.M0.addOrUpdateTmplProperty("edge.mem",e.deviceMemory+""),e.hardwareConcurrency&&tt.M0.addOrUpdateTmplProperty("edge.concurrency",e.hardwareConcurrency+""),e.connection&&e.connection.effectiveType&&tt.M0.addOrUpdateTmplProperty("edge.connection",e.connection.effectiveType+"");const t=window.performance;t&&t.memory&&(t.memory.jsHeapSizeLimit&&tt.M0.addOrUpdateTmplProperty("edge.jsHeapSizeLimit",t.memory.jsHeapSizeLimit+""),t.memory.totalJSHeapSize&&tt.M0.addOrUpdateTmplProperty("edge.totalJSHeapSize",t.memory.totalJSHeapSize+""),t.memory.usedJSHeapSize&&tt.M0.addOrUpdateTmplProperty("edge.usedJSHeapSize",t.memory.usedJSHeapSize+""))}}var Et=o(11942),Pt=o(59997),It=o(42877),Bt=o(30871);function Ft(){return location.search.includes("widgetsMode=true")}const Lt=performance.getEntriesByName("ClientInit")[0]?.startTime,{end:Dt}=(0,C.m)(Lt),Mt=(0,T.Ou)();Mt.clientInitStart=Lt,Mt.pageStart=performance.now(),window.isSSREnabled&&(Mt.timeOrigin=performance.timeOrigin),window._ssrHeaderTime&&(Mt.TimeToSSRHeader=window._ssrHeaderTime);const Rt=!0===window.isSSREnabled&&!W.Al.CurrentFlightSet.has("prg-ssrdeferscript"),Ot=!0===window.isSSREnabled&&W.Al.CurrentFlightSet.has("prg-ssrdeferscript");Rt&&(0,qe.BF)();const jt=(0,Be._)(G.jG.AppType,G.jG.CurrentRequestTargetScope);if(!0===window.isSSREnabled){const e=(0,q.$o)().getObject(jt)?.treatments;if(e){const t=e.filter((({type:e})=>e!==Fe._h.layoutPromotion));(0,q.$o)().setObject(jt,{treatments:t})}}const Ut=Se.FI.isRuby,Ht=new V.fH({spec:(0,De.k)(jt,Ut?[]:void 0)}),Nt=(0,V.Nd)(Ht.data.locationHref,"locationHref");let zt,Wt=!1,Gt=!1;(0,K.HB)();let[qt,Vt]=[!1,!1];if(!Ft()){(0,k.E)(new A.M(Nt)),(0,We.z)(Nt,$.Im,W.Al,G.jG.CurrentRequestTargetScope,c);const e=(0,E.oA)("entryPoint","entryPoint");zt=e&&e.startMeasure("init").endMeasure;if(W.Al.CurrentFlightSet.has("ntp-deferww")){const e='{"os-sku":"192"}'===window?.external?.getHostEnvironmentValue?.("os-sku")||location.search.includes("overrideos");qt=e,Vt=!e}}const Kt=async()=>{try{if(Ft()){try{!async function(){(0,Oe.P)(),Xe.L.registerDefaultStyleTarget(),"auto"==document.dir&&(document.dir=Pt.N.ltr),Xe.L.create("direction",document.dir),(0,ot.Zv)(),window.__widgetRenderFromAppType=!1;const{setupKnownConnectors:e,setupWidgetPerfTracking:t}=await Promise.all([o.e("libs_widget-loader_dist_WidgetLoader_js"),o.e("manga-carousel")]).then(o.bind(o,65002)),{widgetLoader:i}=await Promise.all([o.e("libs_widget-loader_dist_WidgetLoader_js"),o.e("VpReadyHelper")]).then(o.bind(o,89565)),{requestContext:a,locationHref:r,initEndMeasure:s,app:{store:c,rootReducer:d}}=i,l=i.getWidgetElements();if(0==l.length)return;const p=i.parseWidgetDataAttribute();(0,Re.RA)(r,a),(0,J.ou)(),G.jG.TrackInfo={sitePage:{page_product:G.jG.AppType,page_type:W.Al.ClientSettings.pagetype}},G.jG.WidgetAttributes=p,i.stampTmplForExperimentation(),(0,me.Q)(p&&p.cc),e({sharedStateConnector:!0});const h=new It.g(n.$K,"",d,new Bt.P,c,{},a);tt.M0.sendPageView(null,!0),await i.renderWidgets(l,{preRenderCallback:async function(e){t({experienceTracker:h,widgetBindInfo:e}),h.registerMarkersIfNecessary(),h.registerVisuallyReadyObserver()}}),s&&s()}()}catch(e){(0,P.OO)(e,I.oWU,"Error during edgenext app widget page initialization."),Dt(!0)}finally{tt.M0&&((0,Y.D)(tt.M0.sendAppErrorEvent),(0,Q.g)(tt.M0.sendAppErrorEvent))}return}Mt.startPageStart=Math.round(performance.now()),(0,J.ou)(2e3),Xe.L.registerDefaultStyleTarget(),(0,ot.Zv)();const a=W.Al&&W.Al.ClientSettings||{};qt&&(0,Ce._)(a,[...W.Al?.CurrentFlightSet]),(0,Me.S)(a,!0);const r=X.cX.getQueryParameterByName("title",Nt);r&&(document.title=r),(0,Te.Wt)((()=>{Wt=!0})),(0,Te.FC)((()=>{Gt=!0}));const s=function(){const e=window&&window.chrome&&window.chrome.ntpSettingsPrivate?1:0;if(0===e)return 0;const t=window.chrome.ntpSettingsPrivate.getConfigData?2:0,o=window.chrome.ntpSettingsPrivate.getPref?4:0,n=window.chrome.ntpSettingsPrivate.getAllPrefs?8:0;return e|t|o|n}(),{queryparams:c,aid:C,requestMuid:k}=a,A={isAAD:(0,Re.ii)()};(0,f.Z5)(A);const $=new ce.o;ht.Gq.set(R.U.HydrationPromise,$),window.dispatchEvent(new CustomEvent("HydrationPromiseCreated"));let E="none";W.Al.CurrentFlightSet.has("prg-ntp-rfmbpv")&&(E="psl"),W.Al.CurrentFlightSet.has("prg-ntp-lcrfmbpv")&&(E="local");"none"!==E&&await(0,re.AI)(E),(0,Re.RA)(Nt,Ht,window.isSSREnabled?$.getResultAsync():null),(0,Oe.P)(),(0,N.zY)(),function(){const e=()=>{let e={},t=!1;if(!Ge.O.primaryLoadTimeEventSent){t=!0,Ge.O.primaryLoadTimeEventSent=!0;const o=(0,L.bP)(),n=(0,L.bO)(L.oN.Primary);e=o.reduce(((e,t)=>(e[t[0]]=t[1],e)),e),e.incompleteMarkers=n.join(","),e.unloadBeforeTTVR=1}if(!Ge.O.secondaryLoadTimeEventSent){t=!0,Ge.O.secondaryLoadTimeEventSent=!0;const o=(0,L.gh)(),n=(0,L.bO)(L.oN.Secondary);e=o.reduce(((e,t)=>(e[t[0]]=t[1],e)),e),e.secondaryIncompleteMarkers=n.join(","),e.unloadBeforeSecondaryPerfMarkers=1}t&&tt.M0.sendLoadTimeEvent({markers:e})};window.addEventListener("beforeunload",e,{once:!0})}(),function(){const e=()=>{(0,ae.j)()&&window?._longAnimationFrames&&tt.M0.sendLoadTimeEvent({markers:{longAnimationFrames:window._longAnimationFrames}})};window.addEventListener("beforeunload",e,{once:!0})}(),function(){const e=()=>{(0,ae.j)()&&window?.sweeperInfo&&tt.M0.sendLoadTimeEvent({markers:{sweeperInfo:window.sweeperInfo}})};window.addEventListener("beforeunload",e,{once:!0})}(),Ut&&!window.isSSRCompleted&&function(){const e=`${G.jG.StaticsUrl}latest/fonts/segoesans/Segoe-sans.woff2`;if(!document.querySelector(`link[href="${e}"]`)){const t={rel:"preload",href:`${e}`,as:"font",type:"font/woff2",crossorigin:""},o=document.createElement("link");for(const e of Object.keys(t))o.setAttribute(e,t[e]);document.head.appendChild(o)}const t=document.head.querySelector("style"),o=`\n        @font-face {\n            font-family: Segoe Sans;\n            font-style: normal;\n            src: url("${G.jG.StaticsUrl}latest/fonts/segoesans/Segoe-sans.woff2") format('woff2');\n            font-display: swap;\n        }\n        body {\n            margin: 0;\n            font-family: Segoe Sans;\n            overscroll-behavior-x: none;\n            overscroll-behavior-y: none;\n            --body-font: Segoe Sans, Segoe UI, Segoe UI Midlevel, Segoe WP, Arial, sans-serif;\n        }`;if(t)t.innerText=o;else{const e=document.createElement("style");e.setAttribute("type","text/css"),e.innerText=o}}();const q=new URLSearchParams(c);if("true"===q.get("ssrenable")&&"vp"===q.get("reqsrc")&&!window.isSSREnabled){const e=document.createElement("div");return e.innerHTML=`SSR VP test detected but did not receive SSR response. Skip page rendering.<br/>Request Id: ${C}`,e.style.cssText="background-color: #fff; color: #000; font-size: 20px; font-weight: bold;",void document.body.appendChild(e)}const K=(0,Z.m)();tt.M0.addOrUpdateCustomProperty("server-debug-info",K),tt.M0.addOrUpdateTmplProperty("edge-apis",s.toString()),(0,ut.N)()&&async function(){const e=await $e.U.getPreferenceSetting("nurturing_campaigns",!1);if(!e?.value?.placementsInteractions)return"";const t=e.value.placementsInteractions,o=[];for(const e in t)(e.indexOf("marketLanguage")>-1||e.indexOf("chinaLanguageMismatch")>-1)&&o.push({CoachmarkID:e,CoachmarkInformation:t[e]});t&&t.layoutDemotion&&(0,je.U)("layoutDemotion",t.layoutDemotion);t&&t.layoutPromotion&&(0,je.U)("layoutPromotion",t.layoutPromotion);try{return JSON.stringify({MismatchPromptTitle:"CoachmarkMismatch",MismatchPromptDetails:o})}catch(e){return""}}().then((e=>{tt.M0.addOrUpdateCustomProperty("marketmismatch",e)})),tt.M0.addOrUpdateTmplProperty("isswcached",(0,mt.xI)()?"1":"0"),(0,Te.jL)(),function(e){let t=0;const o=(0,S.Gy)();if(o){t=Date.now()-Math.round(window.performance.now())-o}e.refreshDelay=t}(Mt),Rt||Ot?((0,qe.I3)(),Ot&&(0,qe.Qt)(),(0,de.NU)(at),(0,de.po)(rt)):(0,B.Pj)("renderArch","csr"),window&&window.NONCE_ID&&(o.nc=window.NONCE_ID),(0,ee.rh)(tt.M0.sendAppErrorEvent.bind(tt.M0)),(0,Je.A)(),W.Al.IsPrerender||(0,Ye.hR)().then((()=>{Mt.pagevisible=(0,F.v0)(W.Al.IsPrerender)}));const Be=et._.getInstance(),Fe=Be.rootReducer,De=Be.store,We=(0,V.Nd)(Ht.data.wpoData,"wpoData",!1),Ke=(0,je.s)(We);Ke&&Ke.dddTmplHeaderValue&&tt.M0.addOrUpdateTmplString(Ke.dddTmplHeaderValue),le.l.setHandlers({invalidatePageCacheHandler:async()=>{await Promise.all([Ee.U.purgeCacheAsync(Ee.R.base),Ee.U.purgeCacheAsync(Ee.R.followedTopics),Ee.U.purgeCacheAsync(Ee.R.subscribedPublishers),Ee.U.purgeCacheAsync(Ee.R.publishersProfile)])}}),te.U.setTargetScopeAndConfigServiceParams(),te.U.initialize(tt.M0.sendAppErrorEvent.bind(tt.M0)),(0,me.Q)();const nt={experienceType:Ut?i.LoE:"EdgeChromiumPageWC",instanceSrc:"default"},it=await te.U.getConfig(nt);Mt.CrsConfigReady=Math.round(performance.now());const wt=it&&it.properties;await(0,Le.Ki)({instanceId:null,configRef:{experienceType:"ChromiumPageSettings",instanceSrc:"default"}});const xt=await(0,Le.oU)(n.RL),vt=await xt.getLayoutStateAsync();(0,B.Pj)("edgeChromiumLayoutMode",vt.currentLayout),(0,B.Pj)("edgeChromiumFeedSetting",vt.selectedFeedDisplaySetting),Ut||!window.isSSREnabled||W.Al.IsPrerender||wt.auctionRidOverride||(0,Et.j)(te.U,vt,!wt.enableGridViewFeed),Ut?(0,Me.v)():async function(e){const t=e.bufferSizeOverride??32;(0,He.jU)(12),e.enableResponsiveFeed&&(e.responsiveCardWidths&&(0,He.tr)(e.responsiveCardWidths),(0,He.My)(12),(0,He.nk)(12,e.enableFixedImageGutter?t:80));let o=[He.xs+t,He._3+t,He.Xn+t,He.xX+t,He.sc+t];e.enableFixedImageGutter&&(o=[He.qt+2*t+40,He.SP+2*t+40,He.Iw+2*t+40,He.uL+2*t+40,He.ev+2*t+40]);const n=new Map([[Ne.K$.c1,o[0]],[Ne.K$.c2,o[1]],[Ne.K$.c3,o[2]],[Ne.K$.c4,o[3]],[Ne.K$.c5,o[4]]]);(0,pe.Bn)().useRequestIsolatedTracker=!0,(0,pe.Bn)().breakpoints=o,(0,Ne.q7)(n)}(wt),tt.M0.removeTmplProperty("pageResizeBeforeReady");const _t=(0,pe.Bn)().currentColumnArrangement,Ct=e=>{tt.M0.addOrUpdateTmplProperty("pageResizeBeforeReady",`${_t}>${e}`),(0,pe.Bn)().unsubscribe(Ct)};if((0,pe.Bn)().subscribe(Ct),window.isSSREnabled&&"headingsonly"!==vt.selectedFeedDisplaySetting&&"off"!==vt.selectedFeedDisplaySetting){let e;Ut&&(e={configRef:{experienceType:"SuperComponentData",instanceSrc:"ruby"}}),await gt.L.initConfig(e)}let Tt;(Se.FI.isInRubyDogfoodFlight||Se.FI.isExternalOptInCohort||Se.FI.isInRubyTreatment)&&(Tt=Ut?{configRef:{experienceType:i.LoE,instanceSrc:"default"},instanceId:null}:{configRef:{experienceType:"EdgeChromiumPageWC",instanceSrc:"default"},instanceId:null},(0,dt.Fg)(Tt)),Rt&&(Tt||te.U.getEntryPointConfigRef().then((e=>{Tt=e,(0,dt.Fg)(e)})),window.ssrLoadedBundles?.subscribe((e=>(0,Ve.li)(e))));const kt=(0,st.n)(Fe,De,Ht),At=new we.m(n.Dw,"",Fe,new xe.c,De,void 0,Ht);if(new Ie.L(n.hz,wt),new Pe.m(n.HS,void 0),new U.z(n.xE,"",Be.rootReducer,new H.L,Be.store,void 0,Ht),(0,pt.jt)(wt.topSitesCollapsibleControlConfig),Rt&&(Mt.DependencyLoadStart=Math.round(performance.now()),(0,Le.Ki)({instanceId:"",configRef:{experienceType:"ExperienceTrackerEdgeNextData",instanceSrc:"default"}}).then((()=>{!async function(e){const t=W.Al.CurrentFlightSet.has("ntp-srasvr-all"),o=W.Al.CurrentFlightSet.has("ntp-srasvr-nofd");if(!t&&!o)return;if(await(0,qe.LQ)(),!window.isSSRCompleted)return;await window.waitForTtsrComplete();const i=["headingsonly","off"];if(o&&!i.includes(window.ssrLayoutState?.selectedFeedDisplaySetting))return;const a=await(0,Le.oU)(n.Nx);await a.waitForInitialize(),window.ssrLoadedExperience?.subscribe((t=>{const{instanceId:o,configRef:{experienceType:n}}=t,i=Mt[`TTSR.${n}`]||Mt["TTSR.Complete"],a=Mt[`TTVR.${n}`];void 0!==i&&void 0===a&&((0,L.o_)(n,!1,i,!1),_e.g.updateVisuallyReadyTiming.getActionSender(e)?.send({experienceType:n,experienceInstance:o,startTime:void 0,endTime:i}))}))}(At),Mt.DependencyLoadEnd=Math.round(performance.now()),window.ssrLoadedExperience?.subscribe((e=>{Mt[`${e.configRef.experienceType}EagerLoadStart`]=Math.round(performance.now());const t=window.ssrLayoutState;("WidgetsRegion"!==e.instanceId||"off"!==t?.selectedFeedDisplaySetting&&"headingsonly"!==t?.selectedFeedDisplaySetting)&&requestAnimationFrame((()=>setTimeout((()=>{(0,dt.Fg)(e)}))))}))}))),(0,Re.ii)()){const e=async()=>{const e=await(G.jG?.AccountInfoPromise);return{client:{id:oe.I4,friendlyName:"Enterprise NTP App"},account:e}};let t=new d.a(new l.W(e));wt.authManager?.useRetryDecorator&&(t=(0,p.w)(t));const o=wt.authManager?.useInMemoryCache?new h.y:new u.o;(0,g.BV)(new g.U(t,o,m.oc))}Promise.all([(0,Qe.lr)(),(0,ve.F_)()]).then((()=>(0,pe.Bn)().unsubscribe(Ct))),Mt.AppStartStart=Math.round(performance.now()),Be.start(),Mt.AppStartEnd=Math.round(performance.now());const Lt=(0,ne.XS)(),jt=kt.getCurrentState().signedIn,Kt=jt===O.Hy.SignedIn||Lt?.accountType===O.Qm.MSA,Qt=(0,Re.iG)()||Lt?.accountType===O.Qm.AAD;(0,ke.H)(Ae.g);const Jt=(0,Re.ii)()?{myFeed:he.iz.MicrosoftNews,productivityHub:he.iz.Office365,following:he.iz.Following}:void 0,Xt=await async function(e){const t=await te.U.getConfig({experienceType:"ExperienceTrackerEdgeNextData",instanceSrc:"default"}),o=t?.properties,{feedContentSourceMap:n}=o||{},i=e??n;return i&&(0,ze.f)(i)}(Jt);G.jG.IsHeroNTP||(0,Re.fr)(Xt);const Zt="1"===X.cX.getQueryParameterByName("startpage",Nt);W.Al.IsPrerender&&((0,Re.O$)(Wt),(0,Re.xs)(Gt)),(0,Re.mJ)(),tt.M0.updatePageMetadata({page:{viewType:(0,pe.vi)()}},!0),Ut&&tt.M0.updatePageMetadata({page:{name:"ruby",ocid:(0,Re.F_)((0,Re.ii)(),Zt)}},!0),A.isMsa=Kt&&!Qt,(0,b.MX)(A).then((e=>{tt.M0.updatePageMetadata({user:e},!0)})),zt&&zt(),await(0,y.FA)(Yt,jt);const eo=await xt.getPrimaryAccountInfoAsync();if(window.chrome&&window.chrome.authPrivate?(window.chrome.authPrivate.onSignInStateChanged.addListener((async()=>{const e=await xt.getPrimaryAccountInfoAsync(!1),t=(0,fe.ej)(ne.g8);if(e.accountType===z.l.MSA&&e.accountId!==eo.accountId&&!t)return tt.M0.sendAppErrorEvent({...I.G0e,message:"Sign In Refresh Required. New browser account found."}),sessionStorage.setItem(w.gQ,"1"),x.U.addAuthRecord(v.f.ntpNewAccount,"SUCCESS"),void(0,le.Z)();(0,_.BG)(e,null,!0)})),await(0,_.BG)(eo)):(0,P.H)(I.c$P,"AuthPrivate property is not available"),eo.accountType===z.l.AAD||(0,ie.Yn)()===O.Qm.AAD){const e=5e3;Promise.race([(0,L.b1)(),(0,se.Kb)(e)]).then((async()=>{let e=(0,ie.Rn)()?.access_token;if(x.U.addAuthRecord(v.f.ntpGetEdgeLoginToken,e?"SUCCESS":"FAIL"),!e){const t={id:oe.I4,friendlyName:oe.Ak},o=await((0,g.Kf)()?.getAuthData(t));e=o?.authToken,x.U.addAuthRecord(v.f.ntpAuthManagerToken,e?"SUCCESS":"FAIL")}e&&(0,f.ID)(e),x.U.addAuthRecord(v.f.ntpWorkIdCookie,e?"SUCCESS":"FAIL")}))}if(e=xt,t=A.isMsa,new $t(e,t,vt),tt.M0){if(W.Al.IsPrerender){tt.M0.getPageMetadata().updateContract()}(0,Ye.hR)().then((()=>{(0,Re.HG)(Xt),(0,Te.Rk)();let e=!W.Al.newRidNeeded;if(window.swFeatTmplContents){const{newRid:t}=window.swFeatTmplContents;t&&"X"!==t&&(e=!0)}(0,ft.K)(null,e,null,void 0,void 0,Ut),(0,bt.V1)()&&tt.M0.fireCTagPixelBeacon()})),(0,ve.F_)().then((async()=>{(await o.e("ocvFeedback").then(o.bind(o,5402))).setupOcvTelemetryData()}))}(0,Qe.lr)().then((()=>{if(vt.selectedQuickLinksDisplaySetting!==he.Z3.off)try{(0,Te.w$)("TOP_SITES_VISIBLE")}catch(e){(0,P.OO)(e,I.Qv4,"Error invoking activateTrigger function")}if(window&&window.chrome?.ntpSettingsPrivate?.logSsrState&&window.chrome?.ntpSettingsPrivate?.SsrState){const e=Zt?window.chrome?.ntpSettingsPrivate?.PageType?.DHP:window.chrome?.ntpSettingsPrivate?.PageType?.NTP;window.isSSREnabled||window.chrome.ntpSettingsPrivate.logSsrState(window.chrome.ntpSettingsPrivate.SsrState.SSR_NOT_ATTEMPTED,e)}})),(0,S.cu)(),Promise.all([(0,Ye.eC)(),(0,ve.F_)()]).then((()=>{const e=!!document.getElementById("onetrust-consent-sdk");(0,be.hI)()&&!e&&tt.M0.sendAppErrorEvent({...I.bid,pb:{...I.bid.pb,customMessage:`oneTrustBannerScript: ${document.getElementById(ue.e)?.getAttribute("src")}`}})})),(0,L.pT)(!W.Al.IsPrerender);const to=W.Al.CurrentFlightSet.has("ntp-delay-hydration");if(Rt&&(Mt.WaitForCanRenderStart=Math.round(performance.now()),await(0,qe.LQ)(),Mt.WaitForCanRenderEnd=Math.round(performance.now()),(0,qe.Qt)()),Vt&&(0,Ce._)(a,[...W.Al?.CurrentFlightSet]),!Ut&&(0,Ze.HK)()&&!0!==wt.disableFeed)if(window.isSSREnabled)(0,Ze.gi)().then((e=>{(0,Ze.gG)(e,wt)}));else{const e=await(0,Ze.gi)();(0,Ze.gG)(e,wt),await new Promise((e=>window.setTimeout(e,0)))}const oo=performance.now(),no="RenderRoot";Mt[`${no}Start`]=Math.round(oo),window.ssrAdData?.length&&((0,D.c)((()=>{Mt[j.qG.TimeToServeAd]=Mt["TTSR.Complete"],Mt[j.qG.RenderReady]=Mt["TTSR.Complete"],Mt[j.qG.AdRendered]=1,Mt[j.qG.TimeToRenderAd]=Mt["TTSR.Complete"]})),window.ssrAdData?.forEach((e=>yt.k.addDedupeIds(e.crids)))),Tt||(Tt=await te.U.getEntryPointConfigRef());(0,lt.o)(Tt,"fluent-design-system-provider",{controlCornerRadius:2},{hydrationModes:to?St():void 0}).finally((()=>{$.set(),delete window.ssrLayoutState,window.isHydrationCompleted=!0,(Rt||Ot)&&(0,de.$L)(rt)})).then((async()=>{const e=(await(0,L.b1)()).find((e=>"TTVR"===e[0]))?.[1];let t=e;if(window.isSSRCompleted){const o=performance.getEntriesByName("waterfall-view-feed:hydration.start")[0]?performance.getEntriesByName("waterfall-view-feed:hydration.start")[0]:performance.getEntriesByName("grid-view-feed:hydration.start")[0],n=performance.getEntriesByName("waterfall-view-feed:hydration.topDownHydrateComplete")[0]?performance.getEntriesByName("waterfall-view-feed:hydration.topDownHydrateComplete")[0]:performance.getEntriesByName("grid-view-feed:hydration.topDownHydrateComplete")[0];(0,L.su)("FeedHydrationStart",o?.startTime),(0,L.su)("FeedHydrationEnd",n?.startTime),(0,L.su)("PageHydrationStart",performance.getEntriesByName("WholePageHydration.start")[0]?.startTime);const i=performance.getEntriesByName("WholePageHydration.topDownHydrateComplete")[0]?.startTime;(0,L.su)("PageHydrationEnd",i),i>e&&(t=i)}if((0,L.su)("TTF",t),window&&window.chrome?.ntpSettingsPrivate?.logSsrState&&window.chrome?.ntpSettingsPrivate?.SsrState){const e=Zt?window.chrome?.ntpSettingsPrivate?.PageType?.DHP:window.chrome?.ntpSettingsPrivate?.PageType?.NTP;window.isSSREnabled&&window.isCSRFallback?window.chrome.ntpSettingsPrivate.logSsrState(window.chrome.ntpSettingsPrivate.SsrState.SSR_FAILED,e):window.isSSREnabled&&window.isSSRCompleted&&window.chrome.ntpSettingsPrivate.logSsrState(window.chrome.ntpSettingsPrivate.SsrState.SSR_SUCCEEDED,e)}}));const io=performance.now();if(!to){const e=document.body.querySelector("fluent-design-system-provider");(0,Ue.o)(e,$)}Mt[`${no}End`]=Math.round(io),Mt[no]=Math.round(io-oo),(0,M.QP)({name:no,startTime:oo,endTime:io});const ao=ye.A.get(ye.U.CookieConsentStatus);if(W.Al?.CurrentFlightSet?.has("prg-wpo-clarity")){const e=ao!==ge._.NotRequired;Kt||Qt||e||(0,ve.F_)().then((async()=>{const e=await o.e("clarity").then(o.bind(o,36301));e.setupClarity(),G.jG.ClarityEnabled=!0,e.writeBasicTags()}))}(0,ve.F_)().then((()=>{(0,ct.b)((0,T.Ou)(),(0,T.WP)(),W.Al.IsPrerender)})),function(){const e=performance.getEntriesByName("ssrExt").find((e=>"measure"===e.entryType));e?.startTime&&(Mt.ssrExtStart=Math.round(e.startTime)),e?.duration&&(Mt.ssrExtDuration=Math.round(e.duration))}(),"visible"===document.visibilityState&&(0,Te.fL)(),function(){let e=!1;Promise.all([(0,Qe.lr)(),(0,L.b1)()]).then((()=>{e=!0}));const t=()=>{if(!(0,ae.j)())return;const t={};tt.M0.isContentViewBeaconSent()||(t.cv={queued:tt.M0.isEventInQueue("ContentView")?1:0,ttvr:e?1:0}),(0,bt.V1)()||tt.M0.isPageViewBeaconSent()||(t.pv={queued:tt.M0.isEventInQueue("PageView")?1:0,ttvr:e?1:0}),Object.keys(t).length&&tt.M0.sendLoadTimeEvent({markers:t})};window.addEventListener("beforeunload",t,{once:!0})}(),Dt()}catch(e){await async function(e,t){const o="isNetworkFailure"in e&&e.isNetworkFailure,n="Encountered a critical app error.",i=o?I.u6c:I.lWE;if((0,P.OO)(e,i,n),!t){const t=o?I.aF1:I.u4m,i=o?I.Wzw:I.Jtn;nt.F.tryReloadNtpPage(t,i,n,e)}}(e),Dt(!0)}finally{(0,Y.D)((()=>tt.M0.sendAppErrorEvent)),(0,Q.g)((()=>tt.M0.sendAppErrorEvent)),Mt.startPageEnd=Math.round(performance.now())}var e,t};function Yt(e){tt.M0.sendAppErrorEvent({...I.Ggm,message:"Error while fetching APP_ANON cookie.",pb:{...I.Ggm.pb,customMessage:`Error: ${e}`,stack:e.stack}})}(0,Ke.S)(),window.SSRONLY?window.startPage=Kt:Kt()},43512:function(e,t,o){"use strict";o.d(t,{m4:function(){return n.m}});var n=o(25383);o(9047)},25383:function(e,t,o){"use strict";o.d(t,{m:function(){return i}});function n(e){const t=e;return void 0!==t.clientValue?t.clientValue:Object.keys(e).reduce(((t,o)=>Object.assign(Object.assign({},t),{[o]:n(e[o])})),{})}class i{constructor(e,t){this.spec=e,this.qspPrefix=t||"rd"}get verParam(){return`${this.qspPrefix}.ver`}get data(){return void 0===this.memo&&(this.memo=n(this.spec.spec)),this.memo}loadQueryParams(e){if(void 0!==this.memo)throw"overwriting existing query string parameters.";const t=e.get([this.qspPrefix,"ver"].join("."));if(t!==this.spec.ver)throw`unmatched spec version: expected "${this.spec.ver}" but got ${t}.`;this.memo={},e.forEach(((e,t)=>{if(!t.startsWith(this.qspPrefix))return;if(t===this.verParam)return;const o=t.split(".").slice(1);let n=this.memo;o.forEach(((t,i)=>{let a={};if(i===o.length-1)try{a=()=>JSON.parse(e)}catch(e){throw this.memo=void 0,e}Object.prototype.hasOwnProperty.call(n,t)||(n[t]=a),"object"!=typeof a||(n=n[t])}))}))}getURLSearchParams(){const e=new URLSearchParams,t=(o,n)=>{Object.keys(o).forEach((i=>{const a=o[i],r=[...n,i];"object"!=typeof a?e.append(r.join("."),JSON.stringify(a())):t(a,r)}))};return t(this.data,[this.qspPrefix]),e.sort(),e.append(this.verParam,this.spec.ver),e}getSerializedData(){return this.getURLSearchParams().toString()}}},52696:function(e,t,o){"use strict";var n;!function(e){e.JSON="application/json;charset=UTF-8",e.HTML="text/html;charset=UTF-8"}(n=n||(n={}))},9047:function(e,t,o){"use strict";o(52696)},40359:function(e,t,o){"use strict";o.d(t,{FB:function(){return v},Rn:function(){return _},lq:function(){return T}});var n=o(33940),i=o(59833),a=o(85594),r=o(35602),s=o(18744),c=o(74597),d=o(75509),l=o(97390),p=o(36e3),h=o(92530),u=o(12044),g=o(81200),m=o(69566);const f="ClickedBeaconAttached",b="ViewedBeaconAttached",y="UserActionBeaconAttached",w=new WeakSet,x=["control icon-only","menu","ad-label","ad-choice"],v="WebViewVisible";let _;class S{constructor(e){this.options=e}createHTML(e){return i.Lu.attribute(e(this))}createBehavior(){return new C(this.options,this.targetNodeId)}}class C{constructor(e,t){this.options=e,this.targetNodeId=t,this.beaconService=null,this.nativeAdBeaconsSetAttribute="nab",this.adsFlights="",this.onClick=(e,t)=>o=>{if(o.target===o.currentTarget){const e=o.composedPath();for(let t=0;t<7&&t<e.length;t++){const o=e[t];if(o.className&&x.includes(o.className))return}return this.fireClickedBeacon(),void this.handleClickUserAction(o)}if(!this.isTelemetryWc(o.currentTarget))return;const n=this.getImmediateChildFromTelemetryWc(o.currentTarget);o.target===n&&(o.currentTarget.clickedBeacon&&(this.fireTelemetryWcClickedBeacon(o),this.userActionLog.fireClickLog?.(e,t,o)),o.currentTarget.clickedUserAction&&this.handleClickUserAction(o),o.currentTarget.clickedTelemetry&&this.handleClickTelemetry(o))},this.fireTelemetryWcClickedBeacon=e=>{const t=e.composedPath();let o=!1;for(let n=t.length-1;n>=0;n--)if(e.currentTarget!==t[n]){if(o&&this.isTelemetryWc(t[n]))return}else o=!0;this.fireClickedBeacon()},this.fireClickedBeacon=()=>{this.beaconService.fireClickTrackingCall(this.clickBeacons,this.beaconsJson),this.options.isClarityEnabled&&u.V.writeCustomTag("NativeAdHasBeenClicked",!0)},this.getImmediateChildFromTelemetryWc=e=>{if(this.isTelemetryWc(e)&&e.children.length>0)return e.children[0]},this.getUserActionBeacons=e=>{const t=JSON.parse(e),o=t&&t.trb;if(this.userActionBeacons=new Array,o)for(let e=0;e<o.length;e++)if(o[e].includes("srtb.msn.com")){const t=new URL(o[e]);t.pathname=t.pathname.replace("served","useraction"),this.userActionBeacons.push(t)}},this.onUserActionEvt=e=>{e.stopPropagation();const{eventType:t,dur:o,message:n,element:i}=e.detail;this.onUserActionCore(t,o,n,i)},this.onUserActionCore=(e,t,o,n)=>{if(this.userActionBeacons)for(let i=0;i<this.userActionBeacons.length;i++){for(const e of Object.values(r.Cw))this.userActionBeacons[i].searchParams.delete(e);if(e){this.userActionBeacons[i].searchParams.set(r.Cw.EventType,e),this.userActionBeacons[i].searchParams.set(r.Cw.Timestamp,(this.start?(new Date).getTime()-this.start:0).toString()),this.userActionBeacons[i].searchParams.set(r.Cw.Duration,t||0);const a={m:o,f:this.adsFlights};this.userActionBeacons[i].searchParams.set(r.Cw.Message,JSON.stringify(a)),this.userActionBeacons[i].searchParams.set(r.Cw.Element,n||"")}this.beaconService.fireUserActionBeacon(this.userActionBeacons[i].href)}},this.isTelemetryWc=e=>e.tagName&&"msn-native-ad-telemetry"===e.tagName.toLowerCase(),this.handleClickUserAction=e=>{const t=this.getClickTarget(e),o=[];t?.tagName?.toLowerCase&&o.push(t?.tagName.toLowerCase()),t?.className?.toLowerCase&&o.push(t?.className.toLowerCase()),t?.id?.toLowerCase&&o.push(t?.id.toLowerCase())},this.handleClickTelemetry=e=>{const t=this.getClickTarget(e);e.currentTarget.dataset.t&&(t.dataset.t=e.currentTarget.dataset.t,delete e.currentTarget.dataset.t,c.u.addElement(t))},this.getBindingSource=e=>e instanceof HTMLElement?["native-ad-wc","msn-native-ad-title-mask"].includes(e?.tagName?.toLocaleLowerCase())?e.adData:["boost-ad-card"].includes(e?.tagName?.toLocaleLowerCase())?e.boostAdData:["cashback-template-halfu-card"].includes(e?.tagName?.toLocaleLowerCase())?e.productData:["win-app-wc"].includes(e?.tagName?.toLocaleLowerCase())?e.winAppData:["promoted-ad-wc"].includes(e?.tagName?.toLocaleLowerCase())?e.promotedAdData:e.data:e}bind(e){const{context:t,source:o,targets:n}=e;Array.isArray(l.jG?.CurrentRequestTargetScope?.pageExperiments)&&(this.adsFlights=(0,h.Z)(d.N,l.jG?.CurrentRequestTargetScope?.pageExperiments).join(";")),this.target=n[this.targetNodeId];const{beaconsJson:i,adBeaconServiceConfig:a,clickBeacons:r,geminiViewabilityDataJson:c,bingTrackerUrls:p,items:u,videoViewabilityData:x,isClarityEnabled:S,isProng2Promotion:C,isProng1Promotion:T,enableInfopaneDVBeacon:k,id:A}=this.getBindingSource(o);if(i&&r&&!window.adsListened?.includes(o.id)){this.start=(new Date).getTime(),this.options.isClarityEnabled=S,this.beaconsJson=i,this.clickBeacons=r,this.geminiViewabilityDataJson=c,this.beaconService=new s.A(a);const e={eventTrackers:u&&u[0]?u[0].eventtrackers:void 0,enableInfopaneDVBeacon:k,nativeAdId:A};if(C&&g.gL.subscribe(2,(async e=>{e&&this.options.shouldCallVisibilityBeacon&&(this.beaconService.elementVisibilityObserver(this.target,i,c,u&&u[0]?u[0].uniqueId:void 0,p,x,o,t),this.target[b]=!0)})),T&&document.addEventListener(v,_=()=>{this.options.shouldCallVisibilityBeacon&&(this.beaconService.elementVisibilityObserver(this.target,i,c,u&&u[0]?u[0].uniqueId:void 0,p,x,o,t),this.target[b]=!0)}),!(0,m.Un)()&&T&&this.options.shouldCallVisibilityBeacon&&(this.beaconService.elementVisibilityObserver(this.target,i,c,u&&u[0]?u[0].uniqueId:void 0,p,x,o,t),this.target[b]=!0),this.options.shouldCallClickBeacon&&(window._pageTimings["TTNativeAd.ClickListened"]||(window._pageTimings["TTNativeAd.ClickListened"]=performance.now()),this.target.addEventListener("click",this.onClick(o,t)),this.target[f]=!0),this.options.shouldWatchCustomEvent&&(this.getUserActionBeacons(i),this.target.addEventListener("NativeAdUserAction",this.onUserActionEvt),this.target[y]=!0),this.options.shouldCallVisibilityBeacon&&!C&&!T){if(x){if(w.has(this.target))return;w.add(this.target)}window._pageTimings["TTNativeAd.ViewListened"]||(window._pageTimings["TTNativeAd.ViewListened"]=performance.now()),this.beaconService.elementVisibilityObserver(this.target,i,c,u&&u[0]?u[0].uniqueId:void 0,p,x,o,t,e),this.target[b]=!0}}}unbind(){}getClickTarget(e){if("adChoice"===e.currentTarget.telemetryMetadata&&e.composedPath&&"function"==typeof e.composedPath){const t=e.composedPath();for(let e=0;e<t.length;++e){const o=t[e];if(o.tagName&&"a"===o.tagName.toLowerCase()&&"native-ad-ad-choice"===o.className.toLowerCase())return o}}if(!e.composedPath||"function"!=typeof e.composedPath)return e.target;{const t=e.composedPath();if(t.length>0)return t[0]}}}function T(e=!0,t=!0,o=!1){return new S({shouldCallClickBeacon:e,shouldCallVisibilityBeacon:t,shouldWatchCustomEvent:o})}(0,n.gn)([(0,p.f3)(r.Km)],C.prototype,"userActionLog",void 0),a.m0.define(S)},12044:function(e,t,o){"use strict";o.d(t,{V:function(){return i}});const n={adCarousel:"NativeAdCarouselEnabled",allowedAssets:"NativeAdAllowedAssets",animatedImage:"NativeAdAnimatedImageType",landscapeOverlay:"NativeAdLandscapeOverlayEnabled",progressiveDisplay:"NativeAdProgressiveDisplayEnabled",templateType:"NativeAdTemplateType"},i=new class{constructor(){this.loggedTags={}}writeCustomTag(e,t){if(window.clarity&&null!=t){const o=t.toString();this.loggedTags[e]&&this.loggedTags[e][o]||(window.clarity("set",e,o),this.loggedTags[e]=this.loggedTags[e]||{},this.loggedTags[e][o]=!0)}}logAdTemplateConfig(e){e&&Object.keys(n).forEach((t=>{this.writeCustomTag(n[t],e[t])}))}}},73973:function(e,t,o){"use strict";o.d(t,{n:function(){return D}});var n=o(90755);const i=(0,n.AF)("MsftFeedModule",(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-infopane-carouselcard"),o.e("web-components_info-pane_dist_info-pane-panel_index_js-web-components_info-pane_dist_info-pan-692423")]).then(o.bind(o,14979)))),a=(0,n.AF)("CsFeedInfopane24Module",(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-50bdb3")]).then(o.bind(o,3424)))),r=(0,n.AF)("CsFeedInfopane28Module",(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-8a4274")]).then(o.bind(o,92644)))),s=(0,n.AF)("CsFeedInfopane28Slim1Module",(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-6a2665")]).then(o.bind(o,11575)))),c=(0,n.AF)("CsFeedInfopaneImmersiveModule",(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-4186ff")]).then(o.bind(o,774)))),d=(0,n.AF)("CsFeedInfopaneModule",(()=>Promise.all([o.e("common-others"),o.e("common-segments"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-08b982")]).then(o.bind(o,77831)))),l=(0,n.AF)("MsftInfopaneModule",(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("common-infopane-carouselcard"),o.e("web-components_info-pane_dist_info-pane-panel_index_js-web-components_info-pane_dist_info-pan-692423")]).then(o.bind(o,22346)))),p=(0,n.AF)("CsFeedNoHoverModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-220d7b")]).then(o.bind(o,91599)))),h=(0,n.AF)("CsFeedNoHoverWithTopAdModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_core_dist_interaction-tracke-116c7b")]).then(o.bind(o,6469)))),u=(0,n.AF)("CsFeedLightNoHoverModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-15054c")]).then(o.bind(o,95149)))),g=(0,n.AF)("CsFeedNoHoverChannelModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-218b4f")]).then(o.bind(o,24794)))),m=(0,n.AF)("CsFeedRIS1Module",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-f6626c")]).then(o.bind(o,78355)))),f=(0,n.AF)("CsFeedAdsLoggingModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-550ef2")]).then(o.bind(o,30539)))),b=(0,n.AF)("CsFeedAdsCTAModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-4ead84")]).then(o.bind(o,88104)))),y=(0,n.AF)("CsFeedSmallerFontModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-e03665")]).then(o.bind(o,86074)))),w=(0,n.AF)("CsFeedT1FontModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-49b8f0")]).then(o.bind(o,94724)))),x=(0,n.AF)("CsFeedT2FontModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-8478b9")]).then(o.bind(o,86865)))),v=(0,n.AF)("CsFeedVideoCardModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-3b5251")]).then(o.bind(o,29129)))),_=(0,n.AF)("CsFeedDescriptionModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-ea1d75")]).then(o.bind(o,72071)))),S=(0,n.AF)("CsFeedSafeAdsModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-19cdc0")]).then(o.bind(o,89507)))),C=(0,n.AF)("CsFeedEntNewsModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-79910a")]).then(o.bind(o,6218)))),T=(0,n.AF)("CsFeedImmersiveModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("experiences_spotlight-card-wc_dist_Mappers_PublisherSpotlight_PublisherSpotlightCardMapper_js-890523")]).then(o.bind(o,68382)))),k=(0,n.AF)("CsFeedT1FullModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_channel-page-utils_dist_UrlUtilities_js-libs_core_dist_interaction-tracker_MouseTracker_-ea1654")]).then(o.bind(o,86842)))),A=(0,n.AF)("CsFeedGeometricAdsModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-8fbff4")]).then(o.bind(o,41766)))),$=(0,n.AF)("CsFeedModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-2d0301")]).then(o.bind(o,53413)))),E=(0,n.AF)("CsWaterfallFeedModule",(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_responsive-card_dist_index_js-libs_feedback-service_dist_FeedbackAuth_js-libs-c2d949"),o.e("ruby-page")]).then(o.bind(o,78030)))),P=(0,n.AF)("CsResponsiveInfopaneModule",(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_responsive-card_dist_index_js-libs_feedback-service_dist_FeedbackAuth_js-libs-c2d949"),o.e("dense-card")]).then(o.bind(o,45326)))),I=(0,n.AF)("CsNativeAdWithoutFooterModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-a699a7")]).then(o.bind(o,84278)))),B=(0,n.AF)("CsVAPhase2Module",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_core_dist_interaction-tracke-2b99bd")]).then(o.bind(o,38695)))),F=(0,n.AF)("CsGemModule",(()=>Promise.all([o.e("common-feed-libs"),o.e("common-cscore"),o.e("cs-core-desktop_gem-greeting_dist_index_js")]).then(o.bind(o,42012)))),L=(0,n.AF)("CsFeedAdsDefaultModule",(()=>Promise.all([o.e("common-others"),o.e("common-feed-libs"),o.e("common-cscore"),o.e("libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_core_dist_interaction-tracke-b8b2d2")]).then(o.bind(o,21591))));async function D(e){if(!e||!e.enableSuperCardsBundles)return void await i();let t=!1;const o=[];e.enableResponsiveIPBundles&&o.push(P()),e.enableSuperInfopaneCard24Bundles?(o.push(a()),t=!0):e.enabSupInfopane28Bundles?(e.enabSip28Slim1Bundles?o.push(s()):o.push(r()),t=!0):e.enableSuperInfopaneCardImmersiveBundles?(o.push(c()),t=!0):e.enableSuperInfopaneCardBundles?(o.push(d()),t=!0):e.enableSuperCardsMLiteBundles||e.disableInfoPaneBundle||o.push(l()),e.enableTopAdCardBundles&&(o.push(h()),t=!0),e.enableSuperCardsMobileBundles&&(o.push(p()),t=!0),e.enableSuperCardsMLiteBundles&&(o.push(u()),t=!0),e.enableSuperCardsChannel&&(o.push(g()),t=!0),e.enableSuperCardsRIS1Bundle&&(o.push(m()),t=!0),(e.enableSuperCardsAdsLoggingBundles||e.enableSuperCardsAdsImprLog)&&(o.push(f()),t=!0),e.enableSuperCardsAdsCTABundles&&(o.push(b()),t=!0),e.enableSuperCardsSmallerFontBundles&&(o.push(y()),t=!0),e.enableSuperCardsT1FontBundles&&(o.push(w()),t=!0),e.enableSuperCardsT2FontBundles&&(o.push(x()),t=!0),e.enableSuperVideoCardBundles&&(o.push(v()),t=!0),e.enableSuperCardsDescriptionBundles&&(o.push(_()),t=!0),e.enableSuperCardsSafeBundles&&(o.push(S()),t=!0),e.enableEntNewsBundles&&(o.push(C()),t=!0),e.enableSuperCardsImmersiveBundles&&(o.push(T()),t=!0),e.enableSuperCardsT1FullBundles&&(o.push(k()),t=!0),e.enableSuperCardsGeometricBundles&&(o.push(A()),t=!0),(e.enableFeed3Cards||e.enableResponsiveCards)&&o.push(E()),e.enableVAPhase2WithShimmer&&o.push(L()),!e.enableVAPhase2WithShimmer&&e.enableVAPhase2&&o.push(B()),4===e.enableBlendInAdSlugStyle&&(o.push(I()),t=!0),t||o.push($()),e.enableGarnetGemCards&&o.push(F()),await Promise.all(o)}},7209:function(e,t,o){"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}o.d(t,{Z:function(){return n}})},55090:function(e,t,o){"use strict";function n(e){return e.split("-")[0]}function i(e){return e.split("-")[1]}function a(e){return["top","bottom"].includes(n(e))?"x":"y"}function r(e){return"y"===e?"height":"width"}function s(e,t,o){let{reference:s,floating:c}=e;const d=s.x+s.width/2-c.width/2,l=s.y+s.height/2-c.height/2,p=a(t),h=r(p),u=s[h]/2-c[h]/2,g="x"===p;let m;switch(n(t)){case"top":m={x:d,y:s.y-c.height};break;case"bottom":m={x:d,y:s.y+s.height};break;case"right":m={x:s.x+s.width,y:l};break;case"left":m={x:s.x-c.width,y:l};break;default:m={x:s.x,y:s.y}}switch(i(t)){case"start":m[p]-=u*(o&&g?-1:1);break;case"end":m[p]+=u*(o&&g?-1:1)}return m}o.d(t,{Me:function(){return re},oo:function(){return pe},RR:function(){return ce},Cp:function(){return le},uY:function(){return se},dp:function(){return de}});function c(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function d(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}async function l(e,t){var o;void 0===t&&(t={});const{x:n,y:i,platform:a,rects:r,elements:s,strategy:l}=e,{boundary:p="clippingAncestors",rootBoundary:h="viewport",elementContext:u="floating",altBoundary:g=!1,padding:m=0}=t,f=c(m),b=s[g?"floating"===u?"reference":"floating":u],y=d(await a.getClippingRect({element:null==(o=await(null==a.isElement?void 0:a.isElement(b)))||o?b:b.contextElement||await(null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating)),boundary:p,rootBoundary:h,strategy:l})),w=d(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({rect:"floating"===u?{...r.floating,x:n,y:i}:r.reference,offsetParent:await(null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating)),strategy:l}):r[u]);return{top:y.top-w.top+f.top,bottom:w.bottom-y.bottom+f.bottom,left:y.left-w.left+f.left,right:w.right-y.right+f.right}}const p=Math.min,h=Math.max;function u(e,t,o){return h(e,p(t,o))}const g={left:"right",right:"left",bottom:"top",top:"bottom"};function m(e){return e.replace(/left|right|bottom|top/g,(e=>g[e]))}function f(e,t,o){void 0===o&&(o=!1);const n=i(e),s=a(e),c=r(s);let d="x"===s?n===(o?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[c]>t.floating[c]&&(d=m(d)),{main:d,cross:m(d)}}const b={start:"end",end:"start"};function y(e){return e.replace(/start|end/g,(e=>b[e]))}const w=["top","right","bottom","left"];w.reduce(((e,t)=>e.concat(t,t+"-start",t+"-end")),[]);function x(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function v(e){return w.some((t=>e[t]>=0))}function _(e){return"x"===e?"y":"x"}const S=Math.min,C=Math.max,T=Math.round,k=Math.floor,A=e=>({x:e,y:e});function $(e){return I(e)?(e.nodeName||"").toLowerCase():"#document"}function E(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function P(e){var t;return null==(t=(I(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function I(e){return e instanceof Node||e instanceof E(e).Node}function B(e){return e instanceof Element||e instanceof E(e).Element}function F(e){return e instanceof HTMLElement||e instanceof E(e).HTMLElement}function L(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof E(e).ShadowRoot)}function D(e){const{overflow:t,overflowX:o,overflowY:n,display:i}=U(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+o)&&!["inline","contents"].includes(i)}function M(e){return["table","td","th"].includes($(e))}function R(e){const t=O(),o=U(e);return"none"!==o.transform||"none"!==o.perspective||!!o.containerType&&"normal"!==o.containerType||!t&&!!o.backdropFilter&&"none"!==o.backdropFilter||!t&&!!o.filter&&"none"!==o.filter||["transform","perspective","filter"].some((e=>(o.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(o.contain||"").includes(e)))}function O(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function j(e){return["html","body","#document"].includes($(e))}function U(e){return E(e).getComputedStyle(e)}function H(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function N(e){if("html"===$(e))return e;const t=e.assignedSlot||e.parentNode||L(e)&&e.host||P(e);return L(t)?t.host:t}function z(e){const t=N(e);return j(t)?e.ownerDocument?e.ownerDocument.body:e.body:F(t)&&D(t)?t:z(t)}function W(e,t,o){var n;void 0===t&&(t=[]),void 0===o&&(o=!0);const i=z(e),a=i===(null==(n=e.ownerDocument)?void 0:n.body),r=E(i);return a?t.concat(r,r.visualViewport||[],D(i)?i:[],r.frameElement&&o?W(r.frameElement):[]):t.concat(i,W(i,[],o))}function G(e){const t=U(e);let o=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const i=F(e),a=i?e.offsetWidth:o,r=i?e.offsetHeight:n,s=T(o)!==a||T(n)!==r;return s&&(o=a,n=r),{width:o,height:n,$:s}}function q(e){return B(e)?e:e.contextElement}function V(e){const t=q(e);if(!F(t))return A(1);const o=t.getBoundingClientRect(),{width:n,height:i,$:a}=G(t);let r=(a?T(o.width):o.width)/n,s=(a?T(o.height):o.height)/i;return r&&Number.isFinite(r)||(r=1),s&&Number.isFinite(s)||(s=1),{x:r,y:s}}const K=A(0);function Y(e){const t=E(e);return O()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:K}function Q(e,t,o,n){void 0===t&&(t=!1),void 0===o&&(o=!1);const i=e.getBoundingClientRect(),a=q(e);let r=A(1);t&&(n?B(n)&&(r=V(n)):r=V(e));const s=function(e,t,o){return void 0===t&&(t=!1),!(!o||t&&o!==E(e))&&t}(a,o,n)?Y(a):A(0);let c=(i.left+s.x)/r.x,l=(i.top+s.y)/r.y,p=i.width/r.x,h=i.height/r.y;if(a){const e=E(a),t=n&&B(n)?E(n):n;let o=e,i=o.frameElement;for(;i&&n&&t!==o;){const e=V(i),t=i.getBoundingClientRect(),n=U(i),a=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,r=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,l*=e.y,p*=e.x,h*=e.y,c+=a,l+=r,o=E(i),i=o.frameElement}}return d({width:p,height:h,x:c,y:l})}const J=[":popover-open",":modal"];function X(e){return J.some((t=>{try{return e.matches(t)}catch(e){return!1}}))}function Z(e){return Q(P(e)).left+H(e).scrollLeft}function ee(e,t,o){let n;if("viewport"===t)n=function(e,t){const o=E(e),n=P(e),i=o.visualViewport;let a=n.clientWidth,r=n.clientHeight,s=0,c=0;if(i){a=i.width,r=i.height;const e=O();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,c=i.offsetTop)}return{width:a,height:r,x:s,y:c}}(e,o);else if("document"===t)n=function(e){const t=P(e),o=H(e),n=e.ownerDocument.body,i=C(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=C(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let r=-o.scrollLeft+Z(e);const s=-o.scrollTop;return"rtl"===U(n).direction&&(r+=C(t.clientWidth,n.clientWidth)-i),{width:i,height:a,x:r,y:s}}(P(e));else if(B(t))n=function(e,t){const o=Q(e,!0,"fixed"===t),n=o.top+e.clientTop,i=o.left+e.clientLeft,a=F(e)?V(e):A(1);return{width:e.clientWidth*a.x,height:e.clientHeight*a.y,x:i*a.x,y:n*a.y}}(t,o);else{const o=Y(e);n={...t,x:t.x-o.x,y:t.y-o.y}}return d(n)}function te(e,t){const o=N(e);return!(o===t||!B(o)||j(o))&&("fixed"===U(o).position||te(o,t))}function oe(e,t,o){const n=F(t),i=P(t),a="fixed"===o,r=Q(e,!0,a,t);let s={scrollLeft:0,scrollTop:0};const c=A(0);if(n||!n&&!a)if(("body"!==$(t)||D(i))&&(s=H(t)),n){const e=Q(t,!0,a,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else i&&(c.x=Z(i));return{x:r.left+s.scrollLeft-c.x,y:r.top+s.scrollTop-c.y,width:r.width,height:r.height}}function ne(e,t){return F(e)&&"fixed"!==U(e).position?t?t(e):e.offsetParent:null}function ie(e,t){const o=E(e);if(!F(e)||X(e))return o;let n=ne(e,t);for(;n&&M(n)&&"static"===U(n).position;)n=ne(n,t);return n&&("html"===$(n)||"body"===$(n)&&"static"===U(n).position&&!R(n))?o:n||function(e){let t=N(e);for(;F(t)&&!j(t);){if(R(t))return t;t=N(t)}return null}(e)||o}const ae={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:o,offsetParent:n,strategy:i}=e;const a="fixed"===i,r=P(n),s=!!t&&X(t.floating);if(n===r||s&&a)return o;let c={scrollLeft:0,scrollTop:0},d=A(1);const l=A(0),p=F(n);if((p||!p&&!a)&&(("body"!==$(n)||D(r))&&(c=H(n)),F(n))){const e=Q(n);d=V(n),l.x=e.x+n.clientLeft,l.y=e.y+n.clientTop}return{width:o.width*d.x,height:o.height*d.y,x:o.x*d.x-c.scrollLeft*d.x+l.x,y:o.y*d.y-c.scrollTop*d.y+l.y}},getDocumentElement:P,getClippingRect:function(e){let{element:t,boundary:o,rootBoundary:n,strategy:i}=e;const a=[..."clippingAncestors"===o?function(e,t){const o=t.get(e);if(o)return o;let n=W(e,[],!1).filter((e=>B(e)&&"body"!==$(e))),i=null;const a="fixed"===U(e).position;let r=a?N(e):e;for(;B(r)&&!j(r);){const t=U(r),o=R(r);o||"fixed"!==t.position||(i=null),(a?!o&&!i:!o&&"static"===t.position&&i&&["absolute","fixed"].includes(i.position)||D(r)&&!o&&te(e,r))?n=n.filter((e=>e!==r)):i=t,r=N(r)}return t.set(e,n),n}(t,this._c):[].concat(o),n],r=a[0],s=a.reduce(((e,o)=>{const n=ee(t,o,i);return e.top=C(n.top,e.top),e.right=S(n.right,e.right),e.bottom=S(n.bottom,e.bottom),e.left=C(n.left,e.left),e}),ee(t,r,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ie,getElementRects:async function(e){const t=this.getOffsetParent||ie,o=this.getDimensions;return{reference:oe(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,...await o(e.floating)}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:o}=G(e);return{width:t,height:o}},getScale:V,isElement:B,isRTL:function(e){return"rtl"===U(e).direction}};function re(e,t,o,n){void 0===n&&(n={});const{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:r="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,d=q(e),l=i||a?[...d?W(d):[],...W(t)]:[];l.forEach((e=>{i&&e.addEventListener("scroll",o,{passive:!0}),a&&e.addEventListener("resize",o)}));const p=d&&s?function(e,t){let o,n=null;const i=P(e);function a(){var e;clearTimeout(o),null==(e=n)||e.disconnect(),n=null}return function r(s,c){void 0===s&&(s=!1),void 0===c&&(c=1),a();const{left:d,top:l,width:p,height:h}=e.getBoundingClientRect();if(s||t(),!p||!h)return;const u={rootMargin:-k(l)+"px "+-k(i.clientWidth-(d+p))+"px "+-k(i.clientHeight-(l+h))+"px "+-k(d)+"px",threshold:C(0,S(1,c))||1};let g=!0;function m(e){const t=e[0].intersectionRatio;if(t!==c){if(!g)return r();t?r(!1,t):o=setTimeout((()=>{r(!1,1e-7)}),100)}g=!1}try{n=new IntersectionObserver(m,{...u,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(m,u)}n.observe(e)}(!0),a}(d,o):null;let h,u=-1,g=null;r&&(g=new ResizeObserver((e=>{let[n]=e;n&&n.target===d&&g&&(g.unobserve(t),cancelAnimationFrame(u),u=requestAnimationFrame((()=>{var e;null==(e=g)||e.observe(t)}))),o()})),d&&!c&&g.observe(d),g.observe(t));let m=c?Q(e):null;return c&&function t(){const n=Q(e);!m||n.x===m.x&&n.y===m.y&&n.width===m.width&&n.height===m.height||o();m=n,h=requestAnimationFrame(t)}(),o(),()=>{var e;l.forEach((e=>{i&&e.removeEventListener("scroll",o),a&&e.removeEventListener("resize",o)})),null==p||p(),null==(e=g)||e.disconnect(),g=null,c&&cancelAnimationFrame(h)}}const se=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:o,y:i,placement:r}=t,{mainAxis:s=!0,crossAxis:c=!1,limiter:d={fn:e=>{let{x:t,y:o}=e;return{x:t,y:o}}},...p}=e,h={x:o,y:i},g=await l(t,p),m=a(n(r)),f=_(m);let b=h[m],y=h[f];if(s){const e="y"===m?"bottom":"right";b=u(b+g["y"===m?"top":"left"],b,b-g[e])}if(c){const e="y"===f?"bottom":"right";y=u(y+g["y"===f?"top":"left"],y,y-g[e])}const w=d.fn({...t,[m]:b,[f]:y});return{...w,data:{x:w.x-o,y:w.y-i}}}}},ce=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var o;const{placement:i,middlewareData:a,rects:r,initialPlacement:s,platform:c,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:u,fallbackStrategy:g="bestFit",flipAlignment:b=!0,...w}=e,x=n(i),v=u||(x!==s&&b?function(e){const t=m(e);return[y(e),t,y(t)]}(s):[m(s)]),_=[s,...v],S=await l(t,w),C=[];let T=(null==(o=a.flip)?void 0:o.overflows)||[];if(p&&C.push(S[x]),h){const{main:e,cross:t}=f(i,r,await(null==c.isRTL?void 0:c.isRTL(d.floating)));C.push(S[e],S[t])}if(T=[...T,{placement:i,overflows:C}],!C.every((e=>e<=0))){var k,A;const e=(null!=(k=null==(A=a.flip)?void 0:A.index)?k:0)+1,t=_[e];if(t)return{data:{index:e,overflows:T},reset:{placement:t}};let o="bottom";switch(g){case"bestFit":{var $;const e=null==($=T.map((e=>[e,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:$[0].placement;e&&(o=e);break}case"initialPlacement":o=s}if(i!==o)return{reset:{placement:o}}}return{}}}},de=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){const{placement:o,rects:a,platform:r,elements:s}=t,{apply:c=(()=>{}),...d}=e,p=await l(t,d),u=n(o),g=i(o);let m,f;"top"===u||"bottom"===u?(m=u,f=g===(await(null==r.isRTL?void 0:r.isRTL(s.floating))?"start":"end")?"left":"right"):(f=u,m="end"===g?"top":"bottom");const b=h(p.left,0),y=h(p.right,0),w=h(p.top,0),x=h(p.bottom,0),v={availableHeight:a.floating.height-(["left","right"].includes(o)?2*(0!==w||0!==x?w+x:h(p.top,p.bottom)):p[m]),availableWidth:a.floating.width-(["top","bottom"].includes(o)?2*(0!==b||0!==y?b+y:h(p.left,p.right)):p[f])};await c({...t,...v});const _=await r.getDimensions(s.floating);return a.floating.width!==_.width||a.floating.height!==_.height?{reset:{rects:!0}}:{}}}},le=function(e){let{strategy:t="referenceHidden",...o}=void 0===e?{}:e;return{name:"hide",async fn(e){const{rects:n}=e;switch(t){case"referenceHidden":{const t=x(await l(e,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:v(t)}}}case"escaped":{const t=x(await l(e,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:v(t)}}}default:return{}}}}},pe=(e,t,o)=>{const n=new Map,i={platform:ae,...o},a={...i.platform,_c:n};return(async(e,t,o)=>{const{placement:n="bottom",strategy:i="absolute",middleware:a=[],platform:r}=o,c=a.filter(Boolean),d=await(null==r.isRTL?void 0:r.isRTL(t));let l=await r.getElementRects({reference:e,floating:t,strategy:i}),{x:p,y:h}=s(l,n,d),u=n,g={},m=0;for(let o=0;o<c.length;o++){const{name:a,fn:f}=c[o],{x:b,y,data:w,reset:x}=await f({x:p,y:h,initialPlacement:n,placement:u,strategy:i,middlewareData:g,rects:l,platform:r,elements:{reference:e,floating:t}});p=null!=b?b:p,h=null!=y?y:h,g={...g,[a]:{...g[a],...w}},x&&m<=50&&(m++,"object"==typeof x&&(x.placement&&(u=x.placement),x.rects&&(l=!0===x.rects?await r.getElementRects({reference:e,floating:t,strategy:i}):x.rects),({x:p,y:h}=s(l,u,d))),o=-1)}return{x:p,y:h,placement:u,strategy:i,middlewareData:g}})(e,t,{...i,platform:a})}}},s={};function c(e){var t=s[e];if(void 0!==t)return t.exports;var o=s[e]={id:e,loaded:!1,exports:{}};return r[e].call(o.exports,o,o.exports,c),o.loaded=!0,o.exports}c.m=r,e=[],c.O=function(t,o,n,i){if(!o){var a=1/0;for(l=0;l<e.length;l++){o=e[l][0],n=e[l][1],i=e[l][2];for(var r=!0,s=0;s<o.length;s++)(!1&i||a>=i)&&Object.keys(c.O).every((function(e){return c.O[e](o[s])}))?o.splice(s--,1):(r=!1,i<a&&(a=i));if(r){e.splice(l--,1);var d=n();void 0!==d&&(t=d)}}return t}i=i||0;for(var l=e.length;l>0&&e[l-1][2]>i;l--)e[l]=e[l-1];e[l]=[o,n,i]},c.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return c.d(t,{a:t}),t},o=Object.getPrototypeOf?function(e){return Object.getPrototypeOf(e)}:function(e){return e.__proto__},c.t=function(e,n){if(1&n&&(e=this(e)),8&n)return e;if("object"==typeof e&&e){if(4&n&&e.__esModule)return e;if(16&n&&"function"==typeof e.then)return e}var i=Object.create(null);c.r(i);var a={};t=t||[null,o({}),o([]),o(o)];for(var r=2&n&&e;"object"==typeof r&&!~t.indexOf(r);r=o(r))Object.getOwnPropertyNames(r).forEach((function(t){a[t]=function(){return e[t]}}));return a.default=function(){return e},c.d(i,a),i},c.d=function(e,t){for(var o in t)c.o(t,o)&&!c.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},c.f={},c.e=function(e){return Promise.all(Object.keys(c.f).reduce((function(t,o){return c.f[o](e,t),t}),[]))},c.u=function(e){return e+"."+{ocvFeedback:"b689481cdef6a9f98e3a",clarity:"c46e14caff3684ea1765","common-feed-libs":"4424998c3ed7cab9737d","common-cscore":"37c932bee12b15aaec2e","shared-links":"c64b0370613aa1cf8230",msccCookieBanner:"9ed785761f4f09d34a4d",oneTrustCookieConsentData:"3e80f9af62e1fce3eb8e","libs_topics-shared-state_dist_TopicData_connector_js-libs_topics-shared-state_dist_TopicData_-62f9da":"effae3c8a8f2edafb300",topicData:"5c96549b576f222c6441","category-data-connector":"e72221e360c942813e6a","channel-data-connector":"851c356fe4d267bf1c4d",layoutPreferenceData:"8de18608c355badd06ef","libs_location-service_dist_AutoSuggestService_index_js-libs_location-service_dist_profiles_We-d085cf":"16059992a36b6e069fc4","weather-card-data-connector":"6084e7e23bb9d83c14be",RewardsData:"0e05fcb15354eba5ba54",SignInData:"bdf986e66c766a5ffb48","libs_social-data-service_dist_service_SocialService_js":"7fd851a5e58face85a30","social-data-connector":"81e2f972433e6cbfa932","manga-carousel":"ba3704450154c14f7cfe","admin-portal-data":"44ada5af1ee3a59fd7c5","office-shared-data":"e7ac40a92bba23564013","shell-new-tab-data":"97af3d0f0e82bc014e22","common-others":"ab209fb50bdc5c217b71","common-segments":"b2c6c5c3741ef50736e3","cs-core-desktop_card-components_dist_card-banner_index_js-cs-core-desktop_card-components_dis-389dd7":"5e7f187021f9823bdbc4","industry-news-card":"5a15741e2e545b5e0b58","summary-card-wc":"48f668f41c7d85350d09","office-feed":"82738ca8a7480d5c044f","libs_experiences-telemetry_dist_object-mappings_ContentMapping_js-libs_feed-layout_dist_card--de782c":"6d8ba75dcb7a5cfd0ada",articleRelatedStories:"6976edc058883d5c0c83",articleTopComment:"bc105292295d1ea78963","boost-ad-card":"d6e6c68ca53393d8c843","background-gallery":"c86b3ace99e3ea4404ae",bingHealthCard:"c0aec41e834d776ef1e8",healthFitnessCarousel:"dc93f3cf8e2bc57d2795","common-fast-msft-web-components":"9838dae5a50740d6d3c2","card-actions-wc":"d3c52273fe75a94aeb34","common-windows-widget-shared":"3a3d020af422f21f0634","esports-library":"138ffce8122d45af0cb2",node_modules_video_js_dist_video_es_js:"f024bc1c61c6ca9e5d94","casual-games-card":"c99a74281adb2d4e4b67","casual-games-carousel-card":"baccbc736bed4e6e0494","channel-store":"c63b9a3c68a15d0c1726","common-infopane-carouselcard":"93c175aff25611e3c895","cold-start-wc":"17321d52c7001b2db898","common-settings-edgenext":"b7d0bdcaf5e628a52dff","community-card":"f88da5b617f45c8efdca","content-group-card":"7862054746316f75d85c","copilot-ruby-card":"2b6d44585fb9d7f07389","nextdoor-card":"e72f384c9bb45ce7954b","company-news-card":"a5d0a01d1438b92733cb","cs-core-desktop_responsive-card_dist_index_js-libs_feedback-service_dist_FeedbackAuth_js-libs-c2d949":"fea0cd561151c0e17968","dense-card":"56029102fd633ad4e034","dev-tools":"a8226c87ddcba547567e","digest-card":"96d9f57de58ea240f000","linked-in-card":"e8e23e3187a1631637ad",feedback:"78058f1d6ea89420273e","health-tip-wc":"c94b7bd7136d2acf5f37","following-fre-full-width":"326721e6f36dbdb20fb5","general-elections":"d74ed40534239e8d694c","election-card":"7ac501dabb6c72693580","grid-view-feed":"312b291256a5e9a2c28d","horoscope-answer-card-wc":"ca584fd4250a28ae6710","interest-fre-card":"a3b80e0946f276465fe8","libs_location-service_dist_AutoSuggestService_index_js-libs_traffic_dist_index_js":"28575b441bdba3f2d3a7","interests-wc":"9996be0fcfe7f0e3211f","interest-management-card":"7e6a9f893c550f4cbc99","libs_weather-common-utils_dist_data_FeedWeatherDataUtils_js-libs_weather-shared-wc_dist_utili-eedc04":"74f3b78ce5dd55087ce0","location-data":"b051fbfe1f8bbaabc51e","marketplace-card-wc":"7a4bbce54d59555925ff","market-language-toggle-wc":"1026f5de21781cb92779","mobile-popup-window":"be4c5ab367e249a77511","libs_data-transformer-shared_dist_index_js-libs_money-info-loader_dist_index_js-libs_money-da-adb667":"38e6a590a1c2855e1464","money-info":"1d020884df7ed1247034","libs_sports-data-service_dist_SportsDataMapper_js":"2abb4a01a883d48e104d","cs-core-desktop_gem-shared_dist_mappers_BaseGemMapper_js-libs_oneservice-card-provider_dist_O-1bf78a":"218ea2007ea2cdb4f566","ruby-page":"496f829ba2018f3af02c","navigation-page-experience":"ba5ace5cc27a05339c85","notification-mini-card":"31dca63e4b00b3d56309","nurturing-banner":"aa2f66a5a224a8336858","nurturing-coach-mark":"bd76d97894d430a63e22","nurturing-placement-manager":"4bc58aaff6cf5f57fc69","office-coachmark":"85d988d6f4d25d3e37a1","on-this-day-card-wc":"bf5520c3700fd9385f14","personalize-nav-button":"3cfe12b369f1fcd1a671","polls-card-wc":"93c3d47577c5760822e8","poweredby-legend-wc":"8c2e38da1e173639c1ec","prism-sd-card":"ba55bcea77761ffca245","qna-card":"546d60343e886fb0ce87","quiz-card":"36ae78a83038a3d4d0e8","recipes-sd-card":"101d71c4f9e062a4438c","recommendedSites-wc":"b63f9168b4f83b0b8f0b","rewards-card-wc":"62a92b3573fc3204a007","rewards-flyout":"de30d4b30c408d0abf5c","select-carousel":"bd013f60a05f119a7c6b","signal-overlay":"96863603b3f227106f6f","top-sites-bookmarks":"2f1a664b98ee0ec3ade3","sharepoint-news-card":"62a3d032b6228b461310","shopping-card-actions":"262f5c8c1e898ca8f77d","libs_shopping-utils_dist_ntpCarouselItem_index_js-web-components_shopping-super-carousel_dist-e7e62a":"a16a00c6ee3a4f77e7ee","experiences_shopping-entry-base-experience_dist_ShoppingEntryBaseExperience_js":"401aca5f08a0136679ec","experiences_shopping-entry-base-experience_dist_utils_formatter_utils_js-libs_channel-page-ut-adec5b":"4b69883589414e69f624","shopping-card-wce":"62cc0d7ec909c7904500","shopping-gem-hero-card":"da5a70a3565ecc53491e","social-avatar":"dba03867cb84cbc1e659","social-bar-wc":"9b902f5a39464b8895ca","social-report-menu":"b104bb46297182aab9bd","premium-profile-promo-card":"eaccbc9056752f0af8be","common-sports-lib":"eb9434895730f2234571","sports-info":"6d39b121677612adb52d","spotlight-card-wc":"50e32daa4ee858f2ef94","super-nav":"d62c2a4c337a53b532df","toast-wc":"0564094d8dfb294063ea","pill-wc":"18e794f3097a3a88260c","publisher-subscribe-follow-button":"108d06f71cc117afd6c5","sign-in-control-wc":"b5bbfd9384093150c20d","sign-in-flyout-wc":"751e33e2a4296e085c19","traffic-card-wc":"30d760c7ec3965216c73","traffic-hero":"93c5408678fa70d5675e",libs_travel_dist_index_js:"a4873434e447b82f7dc6","travel-destination":"a7bdcc8218ad9ed8e40b","travel-carousel":"df71a0566c4b695a7d7e","travel-hotel-carousel":"7cb4fffcebf7880b37b9","travel-flight-carousel":"372805e8a1d6a72c89f8","waffle-wc":"7af3b43ea48b87177de7","waterfall-view-feed":"024f0b448f6d82134cc1","d3-library":"a64547ff18388d387d59","libs_weather-shared-wc_dist_utilities_entry_cardSelector_CardSelectorProd_js-libs_weather-sha-e923d9":"efa7f0a74c0e46ff17ae","libs_weather-shared-wc_dist_weather-card_index_js-libs_weather-skycode-mapping-svgr_dist_SkyC-851a44":"9296f22068d024c895f3","weather-card-wc":"9d4b59fa5fa59eae41b7","weather-top-section-card":"33a5c499675073fc7246","weather-hero-experience":"b2e804fd9a39c36a28d3","weather-minimap-wc":"be08b7bbcd4cbcecd3d6","weather-video-entry-wc":"d189d373b4b7dab02f4b","weather-one-liner":"badb67f2395680eb6b40",welcomeGreetingLight:"ad15738557c711b56fb1","widgets-region":"e25b0b9b058f1f11ea95",xfeed:"cef64e723541ca0d0a8a","prism-carousel-card":"9f753e9e37b06acf949e","trending-search-card":"86161f9ff182f4644fb4","real-estate-card":"f503ceb25a3d833ccede","shopping-events-deal-card":"e8308088e4a91173517d","shopping-sd-card":"cc121861d1e3a92ed285",superBreakingNews:"33cdbcb3e7636aa45808","manga-card":"b327e0a72b201ec5d49d","na-trending":"3f8b5af2b180b54fb723","travel-sd-themes-card":"f06b65a32126fce75c07","travel-sd-destinations-card":"da43d4c7c8e74cdda813","travel-sd-hotels-card":"c81fb1e6b840bf4f22c1","travel-sd-flights-card":"27a132bcd3b9765d5a0e","feed-level-feedback-wc":"88dd8fa35fc43560c9a7","contextual-feedback-wc":"b14fb1fe1b5eec0016f0","hot-list-card":"1149dda5365690683771","channel-filter-card":"9b775d4757d466626d21","shopping-grouped-carousel":"c73d1dcc66ae3af4a4e0","nas-highlight-v1":"fd2a17dd2e823278c3f9","views-native-mon":"8186be21f2b63af7a3af","money-augment-card":"e0b5793747a191fe65cf","sports-augment-card":"25c2d8cfdc7ce7f1f6c0","entertainment-premier-card":"9a8b58893baa932d3a9c","donation-npo-card":"b4b8daf2365313db486b","common-auto-suggest":"aae0a59510b8d6ea904e","rich-calendar-card":"1925adbaa6d5d558937b","shopping-augment-card":"880ef52a0177e3160d49","float-button-group-wc":"f30b05a337e363101304","hero-container-wc":"4cb579b9bf18660e0d26","shared-hero-news-card":"e340d822f106c92367e5","issue-reporting-wc":"6496180a334f12d78d21","streaks-badge-wc":"2bd0ec80580defabc8d9","health-augment-card":"176c6b64fe85b33cb7f4","pulse-edgenext":"b07a55f4016fd0ba95f2","shopping-river-card":"30890cc43bd71d33531b","moment-in-time-hero":"e0f6668297a36916c97a","daily-brief-card":"4dc8d94e0b4493100bd8","daily-wonder-copilot-card":"a66ab83c965c5f4533f6","daily-moments-card":"c764f95998fbf3841575","daily-wonder-card":"2112a249a12709db9935","top-comments-card":"b86313fd7946fc710eaa","companion-widget":"799c1591d3b798a6980b","web-components_ruby-pivot-manager_dist_index_js":"59f2c833d4325c8896bf","libs_feed-layout_dist_card-templates_boost-ad-card_BoostAdCard_js-libs_feed-layout_dist_card--f795ce":"18a8a524b14bb40d90bc","libs_super-feed_dist_feed-manager_FeedManagerWithClientAd_js":"93a6ed47aefeb639bb1f","experiences_edge-chromium-page-wc_dist_lazy-loadings_feedManager_js":"9420d83f7cad69f6df2f",feedDependencies:"0ed5b20abb3ec6aec67c","experiences_edge-chromium-page-wc_dist_lazy-loadings_widgetLayoutTemplateMaps_js":"0694a3e5fc99830e9bc6","experiences_edge-chromium-page-wc_dist_feedDependenciesGaming_js":"12b60fd1a652dcbd8ee4","experiences_edge-chromium-page-wc_dist_lazy-loadings_waterfallCardMappers_js":"92073d61767303a74c36",VpReadyHelper:"75be537ea166a86fbd08","na-like-button":"2d3d92cf74be5badaf2c","na-call-to-action":"e2c7df09f21f99162ea1","nas-highlight-v3v4":"aee484fc9915e85601b0","na-decoration-combo":"204a806da71eacad1fe3",nativeadstemplates:"65eb01f47c0984833a33","node_modules_intersection-observer_intersection-observer_js":"79034a59754981b3c3a1","web-components_info-pane_dist_info-pane-panel_index_js-web-components_info-pane_dist_info-pan-692423":"3a4c9e4908275d5eed91","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-50bdb3":"ec5e4c96d882cf996adb","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-8a4274":"14d756c60df315ffd3d8","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-6a2665":"9bf3bcbaddddc77297de","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-4186ff":"a9b37f90ba1f08c3980b","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-08b982":"25249589c49b882d6b20","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-220d7b":"b83d264320a71565c06c","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_core_dist_interaction-tracke-116c7b":"678c218b8cfa3a39202c","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-15054c":"e9c3d83e5c0fda681f46","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-218b4f":"b44d9113344aa0c7b597","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-f6626c":"ba5e9c4ec58751c2e955","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-550ef2":"7bc895954864a9cb4498","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-4ead84":"ecd34f0be9ee8bb0a70d","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-e03665":"d79d6fd8fca4e2790ed4","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-49b8f0":"2c7ea9845a3103c75cbc","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-8478b9":"ec4972ddcf17bddce055","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-3b5251":"1fe486aff216b86ba97a","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-ea1d75":"09b461bef044c6f77348","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-19cdc0":"4e201b641fc745b0b4b8","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-79910a":"c36d2e68de6541845d88","experiences_spotlight-card-wc_dist_Mappers_PublisherSpotlight_PublisherSpotlightCardMapper_js-890523":"1241962f543bd7344733","libs_channel-page-utils_dist_UrlUtilities_js-libs_core_dist_interaction-tracker_MouseTracker_-ea1654":"1494a11d3b55aab78834","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-8fbff4":"c4bce3af94af608ee89f","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-2d0301":"6c4323246a6c4e7c3ef8","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_channel-page-utils_dist_UrlU-a699a7":"cfc268cd554b7b8116c7","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_core_dist_interaction-tracke-2b99bd":"4dd5cbb6e4003f69b82c","cs-core-desktop_gem-greeting_dist_index_js":"403ccdc30828a2138a25","libs_ads-utils_dist_logging_sendAdImageSizeMismatchError_js-libs_core_dist_interaction-tracke-b8b2d2":"a26d771927e4e97ec9ed","experiences_top-sites-edgenext-wc_dist_TopSitesEdgeNextWC_actionMenu_js-experiences_spotlight-76d58f":"68d28ee53b4e0cb78773","experiences_top-sites-edgenext-wc_dist_TopSitesEdgeNextWC_addDialog_js-experiences_top-sites--c06b42":"a0509a3af8761440a566",node_modules_sortablejs_modular_sortable_esm_js:"98cd32e4ed2776436d71","libs_widget-loader_dist_WidgetLoader_js":"50778e73eaef659ead24",scrollPerfMetricTrackers:"f463039ace401b2384c1","telemetry-validation":"3af99b3aea2a65745bcc","diagnostic-web-vitals":"ebe7adc48b02d3419552","web-components_render-mode-indicator_dist_index_js":"3fe0a6f56cf112ac6b89","libs_topics-shared-state_dist_CustomChannelIds_js-experiences_spotlight-card-wc_dist_ContentT-ff51e1":"ca39607a1e5986311306",umfChannelDataProvider:"c460cb60fe5d41b49fb1",serviceBasedChannelDataProvider:"98a19cf7df9a9c4ebdba","card-action-service":"6f16824a1ae33e0c7d43","libs_feed-layout_dist_MobileFeedLayoutCardTypes_js-libs_feed-layout_dist_card-templates_infop-8307de":"4f8afbe88aed7ba6f4c4",cardTypeToCardInterfaceMapForUmf:"57d8df325ad055d52003",waterfallCardTypeToCardInterfaceMapForUmf:"34cd9853741e717fa957","web-components_share-view_dist_index_js":"13206d50a7d5e401408c","experiences_views-native-ad_dist_components_AdFeedbackHideStoryCard_index_js-libs_video-manag-e6722d":"b6c4bcfe786c737aad8b","cold-start-gif":"df181787a0213b261720",MarketMismatchCoachMark:"5b23b6e8684895f7d3d8",CnexEndBanner:"2a3836c45c07f5f5af29","community-active-discussions":"b24516040fea6104e4a2","community-creation-ideas":"ec1471efdc281da5b806","polls-service":"2be2adfa0c4ab2365d89","experiences_content-group-card_dist_carousel-utils_CarouselInit_js-libs_views-helpers_dist_da-22407d":"6b3a22e595d69ae127e8",EsportsDataService:"cc9a1f77562f66cb877f","weather-card-backup-mapper":"4bd5747e10779f9d0052","exploration-banner":"030521f3c2251f1f6825","feeds-notification-toast":"a6698c17c80ebb6143d3","money-info-service":"fb2b5ae5835a485d1f59","money-watchlist-summary":"10eb5bfc6c087812134d","money-quote-vertical-quote-list":"05771c8a8a458513a029","money-earnings-quote-list":"b6cc0b152a4c6020ebb4","money-info-spotlight-earnings":"c4890c86d3f0854c928c","money-info-spotlight-pre-earnings":"ae14ced21db28475098d","money-info-spotlight-currency":"97b208f3e0a7c571a99a","money-info-event-brief":"78c0220134f5ae6ee63e","money-info-price-spotlight":"c80d1d7bdfeeb0ec14a3","money-info-price-spotlight-v2":"e1523a83dbed40c7a7a2","money-info-watchlist-ideas":"2f6653e0dd22a9a685af","money-info-market-movers":"d7084578fa9f7112ccb8","money-info-market-summary":"3657a00370d0d10f7ef8","money-info-radar-graph":"bdf3b790b9e5ea03217b","money-quote-vertical-watchlist-v2":"30591f57726831bf144d","money-quote-vertical-watchlist":"9c627992d970c1b3c4ab","money-info-horizontal-quote-list":"ecf9f6e2d5a0718d6c1c","web-components_native-ad-video_dist_index_js":"480fcd14d61faa70ce9c","sports-match-list":"7c017a5dcefb0ebb7c22","sports-cricket":"bb455667be441d1a02d4","sports-tennis":"412c9d703decfb255fc6","sports-spotlight":"117a30c91d00de5397c2","experiences_recipes-sd-card_src_index_ts":"9b2f2a5ec061f8377dd4","sports-info-utils":"44e67a75e89e3a28ccae","sports-info-span":"657a4e2962479eb18b30","sports-fre":"d52f8f270b39f54406c2","sports-leaderboard":"3d34c5bcee8807487d87","sports-marchmadness":"3efa79406c17658785f2","sports-countdown":"9f18a345060f1f2ae1cd","sports-team-exploration":"4ab0beb196ddb025342d","sports-event-results":"d98043f55efc4afd52f1","sports-event-country":"325decd5e5747e351f62","sports-event-schedule":"ffe38ccd1b9eeab8652d","sports-cricket-exploration":"fdf4ce7f381e6b8646c0","sports-tennis-exploration":"b385c7609e260729035c","sports-info-event-brief":"d8d108cd0926d1bca95e","sports-info-spotlight-race":"831df08a5939c116713b","sports-info-spotlight-tennis":"a83f0ce69c2060cb15ec","sports-info-spotlight-golf":"ef870fe46fca189c4117","sports-info-standings-rankings":"b95b2eec5ee326d546a1","sports-info-medal":"5bf145201d7040845c55","sports-recent-medal":"858eda11e569c2b85829","sports-info-spotlight-cricket":"c78b16ea9805e62b0739","sports-info-spotlight-2c":"6f63425428eb5489bd2b","sports-tournaments-list":"6f1575dd28ea6cbde324","experiences_spotlight-card-wc_dist_Mappers_RealEstate_RealEstateCardMapper_js-libs_video-mana-bca52e":"8a56b5269500c8470097","experiences_spotlight-card-wc_dist_ContentTemplates_CopilotCard_CopilotCard_template_js-exper-6860ac":"3d6f21bd00be8c56b94a","experiences_spotlight-card-wc_dist_ContentTemplates_LocalEvents_LocalEventsCard_template_js-e-7c995f":"a3df82f436fdec7b3e49","experiences_waterfall-view-feed_dist_Utils_ContentPrefetcher_js-web-components_super-cards_di-9167b8":"7f797eece21a8a310c95","super-nav-icons":"a076f250ce8096e12e73","web-components_traffic-card_dist_index_js":"f804ca42ba6124b5825a","web-components_traffic-card-settings_dist_index_js-_379e1":"d56f00e88f9e1d8491a9","libs_shopping-utils_dist_Common_index_js-web-components_fast-msft-web-components_dist_index_js":"e52cfc922b605f3f7702","web-components_traffic-location-settings_dist_index_js":"826b434c959517990670","web-components_traffic-card_dist_traffic-card_js":"bd7f27c685afd59954f8","experiences_traffic-hero_dist_components_transit_index_js":"e8165acb4b9376542101","web-components_traffic-card-settings_dist_index_js-_379e0":"2d30099710133b455599","weather-shared-wc-deferred-dev":"d9c782247e633764e5de","weather-shared-wc-deferred-content":"fe396f83150b27c0ae30","weather-shared-wc-deferred-trend-chart":"5ee8c791d9be33e34d0f","weather-shared-wc-deferred-tide-chart":"0bdf3aaf5f2a4653682c","weather-shared-wc-deferred-dailysummary-chart":"bcd69bbfe0cb70b066c8","weather-svg-chart":"47523a9518ca9ac159fd","weather-shared-wc-deferred-svgchart-content":"614e6c02ba8dd347a89c","weather-card-wc-init-account-type":"5dc2c9867c243c9c70e6","weather-card-wc-deferred":"6933c7a64e07d2ce2010","weather-card-wc-ssr-preload":"1502b5c71852479c593f","weather-legacy-card":"54155595b256fd12cbfb",node_modules_xmlbuilder2_lib_xmlbuilder2_min_js:"558800cef01e9c426563","weather-topsection-card-deferred":"9a6a1b34a8e4d79392c8","weather-lazy-utils":"f6a18b6e0fae1c393243","components_weather-popup_dist_index_js":"ecd6057733e6c6dfbe8e","libs_weather-shared-wc_dist_index_js":"2762a3478882b31af483","web-components_native-ad-report-ad_dist_index_js":"9c5971c1c2826007342d","web-components_super-cards_dist_cards_super-native-ad-card_native-ad-card-intra-article-full--86e116":"21140895e0ab14ee0497","rich-calendar-component-spotlight":"537c946afe401e7186ae","rich-calendar-component":"502e6c8837d9504e4079","web-components_super-cards_dist_cards_super-infopane_CardActionTemplates_js":"f10369de8864addc3860","sports-data-service":"d8d4615aea4133763847","weather-legacy-card-deferred":"2750c5eb79a02267329e"}[e]+".js"},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.hmd=function(e){return(e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:function(){throw new Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e},c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n={},i="@msnews/msnews-experiences:",c.l=function(e,t,o,a){if(n[e])n[e].push(t);else{var r,s;if(void 0!==o)for(var d=document.getElementsByTagName("script"),l=0;l<d.length;l++){var p=d[l];if(p.getAttribute("src")==e||p.getAttribute("data-webpack")==i+o){r=p;break}}r||(s=!0,(r=document.createElement("script")).charset="utf-8",r.timeout=120,c.nc&&r.setAttribute("nonce",c.nc),r.setAttribute("data-webpack",i+o),r.src=c.tu(e),0!==r.src.indexOf(window.location.origin+"/")&&(r.crossOrigin="anonymous")),n[e]=[t];var h=function(t,o){r.onerror=r.onload=null,clearTimeout(u);var i=n[e];if(delete n[e],r.parentNode&&r.parentNode.removeChild(r),i&&i.forEach((function(e){return e(o)})),t)return t(o)},u=setTimeout(h.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=h.bind(null,r.onerror),r.onload=h.bind(null,r.onload),s&&document.head.appendChild(r)}},c.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e},c.tt=function(){return void 0===a&&(a={createScriptURL:function(e){return e}},"undefined"!=typeof trustedTypes&&trustedTypes.createPolicy&&(a=trustedTypes.createPolicy("webpackTrustedTypesPolicy",a))),a},c.tu=function(e){return c.tt().createScriptURL(e)},c.p="/bundles/v1/edgeChromium/buildNumber/",function(){if(void 0!==c){const e=c.e,t={};c.e=function(o){return e(o).catch((function(e){const n=t.hasOwnProperty(o)?t[o]:2;if(1===n&&(c.p=c.p.replace("/assets.","/assets2.")),n<1)throw e;return new Promise((function(e){setTimeout((function(){t[o]=n-1,e(c.e(o))}),100)}))}))}}}(),function(){if(void 0!==c&&void 0!==c.tu){const e=c.tu;c.tu=function(t){return e(function(e){if("string"!=typeof e||"undefined"==typeof document)return e;const t=document.head.getAttribute("data-info");if(!(t.indexOf("ntp-afdh3t")>=0||t.indexOf("ntp-afdh3c")>=0))return e;const o="afdprotocol=";var n=document.cookie.split("; ").find((e=>0===e.indexOf(o)));if(n){const t=n.split("=");if(t[1])return e+(e.indexOf("?")<0?"?":"&")+o+t[1]}return e}(t))}}}(),function(){var e={experience:0};c.f.j=function(t,o){var n=c.o(e,t)?e[t]:void 0;if(0!==n)if(n)o.push(n[2]);else{var i=new Promise((function(o,i){n=e[t]=[o,i]}));o.push(n[2]=i);var a=c.p+c.u(t),r=new Error;c.l(a,(function(o){if(c.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var i=o&&("load"===o.type?"missing":o.type),a=o&&o.target&&o.target.src;r.message="Loading chunk "+t+" failed.\n("+i+": "+a+")",r.name="ChunkLoadError",r.type=i,r.request=a,n[1](r)}}),"chunk-"+t,t)}},c.O.j=function(t){return 0===e[t]};var t=function(t,o){var n,i,a=o[0],r=o[1],s=o[2],d=0;if(a.some((function(t){return 0!==e[t]}))){for(n in r)c.o(r,n)&&(c.m[n]=r[n]);if(s)var l=s(c)}for(t&&t(o);d<a.length;d++)i=a[d],c.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return c.O(l)},o=self.edgeChromiumWebpackChunks=self.edgeChromiumWebpackChunks||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))}(),c.nc=void 0;var d=c.O(void 0,["common","microsoft","vendors"],(function(){return c(10510)}));d=c.O(d)}();