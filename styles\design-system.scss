// 设计系统 - 统一的样式规范
// ================================

// 1. 间距系统 (基于8px网格系统)
// ================================
$spacing-base: 8px;
$spacing-xs: $spacing-base * 0.5;    // 4px
$spacing-sm: $spacing-base * 1;      // 8px
$spacing-md: $spacing-base * 2;      // 16px
$spacing-lg: $spacing-base * 3;      // 24px
$spacing-xl: $spacing-base * 4;      // 32px
$spacing-xxl: $spacing-base * 6;     // 48px

// 2. 圆角系统
// ================================
$radius-xs: 4px;
$radius-sm: 8px;
$radius-md: 12px;
$radius-lg: 16px;
$radius-xl: 20px;
$radius-round: 50%;

// 3. 字体系统
// ================================
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 4. 颜色系统
// ================================
$color-primary: #4382FF;
$color-primary-light: #6B9FFF;
$color-primary-dark: #2A5FCC;

$color-text-primary: #333333;
$color-text-secondary: #666666;
$color-text-tertiary: #999999;
$color-text-disabled: #CCCCCC;

$color-bg-primary: #FFFFFF;
$color-bg-secondary: #F8F9FA;
$color-bg-tertiary: #EDEDED;

$color-border: #E5E5E5;
$color-border-light: #F0F0F0;

// 5. 阴影系统
// ================================
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
$shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);

// 6. 布局系统
// ================================
$container-padding: $spacing-md;
$container-margin: $spacing-md;
$container-width: 90%;

// 7. 组件尺寸
// ================================
$button-height-sm: 32px;
$button-height-md: 40px;
$button-height-lg: 48px;

$icon-size-sm: 16px;
$icon-size-md: 24px;
$icon-size-lg: 32px;
$icon-size-xl: 40px;

// 8. 通用类
// ================================
.container {
  width: $container-width;
  margin: 0 auto;
  padding: 0 $container-padding;
}

.card {
  background-color: $color-bg-primary;
  border-radius: $radius-md;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  box-shadow: $shadow-sm;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

// 9. 间距工具类
// ================================
@each $name, $value in (
  'xs': $spacing-xs,
  'sm': $spacing-sm,
  'md': $spacing-md,
  'lg': $spacing-lg,
  'xl': $spacing-xl,
  'xxl': $spacing-xxl
) {
  .m-#{$name} { margin: $value; }
  .mt-#{$name} { margin-top: $value; }
  .mr-#{$name} { margin-right: $value; }
  .mb-#{$name} { margin-bottom: $value; }
  .ml-#{$name} { margin-left: $value; }
  .mx-#{$name} { margin-left: $value; margin-right: $value; }
  .my-#{$name} { margin-top: $value; margin-bottom: $value; }
  
  .p-#{$name} { padding: $value; }
  .pt-#{$name} { padding-top: $value; }
  .pr-#{$name} { padding-right: $value; }
  .pb-#{$name} { padding-bottom: $value; }
  .pl-#{$name} { padding-left: $value; }
  .px-#{$name} { padding-left: $value; padding-right: $value; }
  .py-#{$name} { padding-top: $value; padding-bottom: $value; }
}

// 10. 响应式断点
// ================================
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

@mixin respond-to($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) { @content; }
  }
  @if $breakpoint == md {
    @media (min-width: $breakpoint-md) { @content; }
  }
  @if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) { @content; }
  }
  @if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) { @content; }
  }
}

// 11. 动画系统
// ================================
$transition-fast: 0.15s ease-in-out;
$transition-normal: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;

.transition-all {
  transition: all $transition-normal;
}

.transition-transform {
  transition: transform $transition-normal;
}

.transition-opacity {
  transition: opacity $transition-normal;
}
