<template>
  <view class="container">
    <!-- 顶部信息栏 -->
    <view class="stats-header">
      <view class="stats-item">
        <view class="stats-label">代理个数</view>
        <view class="stats-value">{{ performanceObj.myProxy.length }}</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-label">个人业绩</view>
        <view class="stats-value">{{ performanceObj.self.money }} ￥</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-label">总体业绩</view>
        <view class="stats-value">{{ sum }} ￥</view>
      </view>
    </view>

    <!-- 内容区 -->
    <view class="content">
      <view v-if="showList.length === 0" class="empty-message">
        {{ msg }}
      </view>

      <view
        class="proxy-card"
        v-for="(data, index) in showList"
        :key="index"
        @click="toProxyDetail(data)"
      >
        <view class="proxy-header">
          <view class="avatar">
            <image :src="data.headPic" class="avatar-image"></image>
          </view>
          <view class="proxy-info">
            <view class="proxy-name">{{ data.userName }}</view>
            <view class="proxy-date">{{ formatTiem(data.createTime) }}</view>
          </view>
          <view class="arrow">›</view>
        </view>

        <view class="divider"></view>

        <view class="proxy-details">
          <view class="detail-item">
            <view class="detail-label">手机尾号</view>
            <view class="detail-value">{{ getPhoneTail(data.phone) }}</view>
          </view>

          <view class="detail-item">
            <view class="detail-label">营业额</view>
            <view class="detail-value amount"
              >{{ data.money === null ? "0" : data.money }} ￥</view
            >
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  showPerformance,
  showProxyPerformance,
} from "@/api/proxyPerformance.js";
import { getDateByDay } from "@/utils/dateUtil.js";
import { isLogin, isProxyLogin } from "@/utils/auth.js";

const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      checkNav: 0,
      performanceObj: {
        ohtherProxy: [],
        self: {
          money: 0,
        },
        myProxy: [],
      },
      showList: [],
      msg: "加载中",
      sum: 0,
      dateRange: [],
    };
  },
  methods: {
    clickNav(index) {
      this.checkNav = index;
      if (index !== 3) {
        let start;
        let end = toLocaleDateString(getDateByDay(0));
        if (index === 0) {
          start = end;
        } else if (index === 1) {
          start = toLocaleDateString(getDateByDay(6));
        } else if (index === 2) {
          start = toLocaleDateString(getDateByDay(30));
        }
        this.dateRange = [start, end];
      }
    },
    showPerformance() {
      showProxyPerformance().then((res) => {
        if (res.data.code !== 200) {
          return;
        }
        this.performanceObj = res.data.data;
        this.index = 0;
        this.showList = this.performanceObj.myProxy;
        let sum = 0;
        this.performanceObj.myProxy.forEach((i) => {
          if (i.money != null) {
            sum += i.money;
          }
        });
        sum += this.performanceObj.self.money;
        this.sum = sum.toFixed(2);
        this.msg = "暂无数据";
      });
    },
    formatTiem(timer = "") {
      return timer.split(" ")[0].replaceAll("/", "-");
    },
    getPhoneTail(phone = "") {
      return phone.substring(phone.length - 4);
    },
    toProxyDetail(proxy) {
      const data = {
        proxy,
        range: this.dateRange.map((o = "") => o.replaceAll("-", "/")),
        checkNav: this.checkNav,
      };
      uni.navigateTo({
        url:
          "/pages/proxyProxyPerformance/proxyPerformance?data=" +
          JSON.stringify(data),
      });
    },
  },
  onLoad() {
    // 检查代理端登录状态
    if (!isLogin() || !isProxyLogin()) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }
    this.showPerformance();
    const start = toLocaleDateString(getDateByDay(0));
    this.dateRange = [start, start];
  },
};
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 20px;
}

.stats-header {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px 15px;
  background-color: white;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.stats-divider {
  width: 1px;
  height: 40px;
  background-color: #e0e0e0;
  margin: 0 15px;
}

.content {
  padding: 0 15px;
}

.empty-message {
  text-align: center;
  color: #999;
  font-size: 16px;
  margin-top: 100px;
}

.proxy-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.proxy-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.avatar {
  margin-right: 15px;
}

.avatar-image {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  border: 2px solid #f0f0f0;
}

.proxy-info {
  flex: 1;
}

.proxy-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.proxy-date {
  font-size: 14px;
  color: #999;
}

.arrow {
  font-size: 20px;
  color: #ccc;
}

.divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 15px 0;
}

.proxy-details {
  display: flex;
  justify-content: space-between;
}

.detail-item {
  text-align: center;
  flex: 1;
}

.detail-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.detail-value.amount {
  color: #ff6b35;
  font-weight: bold;
}
</style>
