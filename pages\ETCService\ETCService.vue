<template>
	<view>
		
	</view>
</template>

<script>
	import { ifGivePage } from '@/api/user.js'
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			
		},
		onShow() {
			
			uni.showModal({
				title: '提示',
				content: '是否跳转微信小程序',
				success: (res) => {
					if (res.confirm) {
						console.log('跳转微信小程序')
						uni.navigateToMiniProgram({
						  appId: 'gh_c3d5b24a4da3',
						  path: 'pages/index/index',
						  extraData: {
						    'data1': 'test'
						  },
						  success(res) {
						    // 打开成功
						  }
						})
						// weixinShare.launchMiniProgram({
						// 	id: 'gh_c3d5b24a4da3',
						// 	type: 0,
						// 	path: 'pages/index/index'
						// })
						
					} else if (res.cancel) {
						
					}
				}
			})
			
			return
			// 验证是否有权限
			uni.showLoading({
				title: '加载中'
			})
			ifGivePage().then(res => {
				uni.hideLoading()
				res = res.data
				if(res.code !== 200) {
					uni.showToast({
						icon: 'error',
						title: '暂无开通权益',
						duration: 1500
					})
					return Promise.reject()
				}
				return Promise.resolve()
			}, () => {uni.hideLoading()}).then(_ => {
				console.log("#")
				// 跳转小程序
				uni.showModal({
					title: '提示',
					content: '是否跳转微信小程序',
					success: (res) => {
						if (res.confirm) {
							if(weixinShare) {
								console.log('跳转微信小程序')
								weixinShare.launchMiniProgram({
									id: 'gh_c3d5b24a4da3',
									type: 0,
									path: 'pages/index/index'
								})
							} else {
								console.log('未设置微信相关操作')
							}
						} else if (res.cancel) {
							
						}
					}
				})
			})
			
			return
			
			uni.switchTab({
				url: '/pages/my/my',
				complete: () => {
					// 获取微信分享对象
					let weixinShare = null
					plus.share.getServices(services => {
						for(let i in services) {
							if(services[i].id === 'weixin') {
								weixinShare = services[i]
								break
							}
						}
						if(weixinShare) {
							// 验证是否有权限
							uni.showLoading({
								title: '加载中'
							})
							ifGivePage().then(res => {
								uni.hideLoading()
								res = res.data
								if(res.code !== 200) {
									uni.showToast({
										icon: 'error',
										title: '暂无开通权益',
										duration: 1500
									})
									return Promise.reject()
								}
								return Promise.resolve()
							}, () => {uni.hideLoading()}).then(_ => {
								console.log("#")
								// 跳转小程序
								uni.showModal({
									title: '提示',
									content: '是否跳转微信小程序',
									success: (res) => {
										if (res.confirm) {
											if(weixinShare) {
												console.log('跳转微信小程序')
												weixinShare.launchMiniProgram({
													id: 'gh_c3d5b24a4da3',
													type: 0,
													path: 'pages/index/index'
												})
											} else {
												console.log('未设置微信相关操作')
											}
										} else if (res.cancel) {
											
										}
									}
								})
							})
							
							// weixinShare.launchMiniProgram({
							// 	id: 'gh_c3d5b24a4da3',
							// 	type: 0,
							// 	path: 'pages/index/index'
							// })
						} else {
							console.log('未设置微信相关操作')
						}
					})
				}
			})
		}
	}
</script>

<style>

</style>
