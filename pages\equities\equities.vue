<template>
  <view class="bkcolor">
    <view class="top-back">
      <image
        src="@/static/equities_image/vip_kq_banner.png"
        style="width: 100%"
        mode="widthFix"
      ></image>
    </view>
    <view class="eq-shop-list">
      <view v-if="isSend"> 正在加载中 </view>
      <view
        class="shop-item-wrapper"
        v-for="(data, index) in shopList"
        :key="index"
        v-else
        @click="toGoodInfo(data.product_id, data.image2)"
      >
        <view class="eq-shop-item">
          <image :src="data.image1" mode="widthFix"></image>
          <view class="eq-shop-item-info">
            <view>
              {{ data.product_name }}
            </view>
            <!-- <view>
							价格: {{data.purchase_price}}￥
						</view> -->
          </view>
          <view class="eq-buy">点击购买</view>
        </view>
        <view class="eq-line" v-if="index !== shopList.length - 1"></view>
      </view>
    </view>
    <!-- 订单按钮 -->
    <view class="fixed-order-btn" @click="toflOrder">
      <view>
        <uni-icons type="list" color="#FA5756"></uni-icons>
      </view>
      <view class="order-text"> 订单 </view>
    </view>
  </view>
</template>

<script>
import { getGoodsList } from "@/api/fuLu.js";
import { isLogin } from "@/utils/auth.js";
export default {
  data() {
    return {
      isSend: false,
      shopList: [],
    };
  },
  methods: {
    getGoodsList() {
      this.isSend = true;
      getGoodsList()
        .then((res) => {
          console.log(res);
          res = res.data;
          if (res.code !== 200) {
            return;
          }
          this.shopList = res.data;
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    toGoodInfo(id, image2) {
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }
      uni.navigateTo({
        url: "/pages/goodInfo/goodInfo?product_id=" + id + "&image2=" + image2,
      });
    },
    // 去权益订单页面
    toflOrder() {
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }
      uni.navigateTo({
        url: "/pages/flOrder/flOrder",
      });
    },
  },
  onLoad() {
    this.getGoodsList();
  },
};
</script>

<style lang="scss">
@import "@/styles/design-system.scss";

// 页面背景
.bkcolor {
  background-color: $color-bg-secondary;
  min-height: 100vh;
}

// 顶部横幅
.top-back {
  margin-bottom: $spacing-md; // 16px

  image {
    width: 100%;
    display: block;
  }
}

// 商品列表容器
.eq-shop-list {
  width: $container-width;
  margin: 0 auto;
  background-color: $color-bg-primary;
  padding: $spacing-lg; // 24px
  border-radius: $radius-md; // 12px
  min-height: 50vh;
  margin-bottom: $spacing-md; // 16px
  box-shadow: $shadow-sm;
  display: flex;
  flex-direction: column;

  // 加载状态
  > view:first-child {
    color: $color-text-tertiary;
    font-size: $font-size-sm; // 14px
  }
}

// 商品项包装器
.shop-item-wrapper {
  display: block;
  width: 100%;
}

// 商品项
.eq-shop-item {
  min-height: 100px;
  @extend .flex-between;
  font-size: $font-size-sm; // 14px
  transition: all $transition-normal;

  &:active {
    transform: scale(0.98);
    background-color: $color-bg-secondary;
    border-radius: $radius-sm; // 8px
    margin: 0 (-$spacing-sm); // -8px
    padding: $spacing-lg $spacing-sm; // 24px 8px
  }

  image {
    width: 60px;
    height: 60px;
    border-radius: $radius-sm; // 8px
    margin-right: $spacing-lg; // 24px
    flex-shrink: 0;
  }
}

// 分割线
.eq-line {
  width: 100%;
  height: 1px;
  background-color: $color-border-light;
  margin: $spacing-md 0; // 16px 0
}

// 商品信息
.eq-shop-item-info {
  flex: 1;
  margin-right: $spacing-md; // 16px

  > view:first-child {
    font-size: $font-size-md; // 16px
    font-weight: $font-weight-medium;
    color: $color-text-primary;
    margin-bottom: $spacing-sm; // 8px
    line-height: 1.4;
  }

  > view:last-child {
    font-size: $font-size-sm; // 14px
    color: $color-text-secondary;
    line-height: 1.3;
  }
}

// 购买按钮
.eq-buy {
  padding: $spacing-sm $spacing-md; // 8px 16px
  font-size: $font-size-xs; // 12px
  border-radius: $radius-sm; // 8px
  color: $color-bg-primary;
  background-color: $color-primary;
  text-align: center;
  font-weight: $font-weight-medium;
  min-width: 60px;
  transition: all $transition-normal;

  &:active {
    background-color: $color-primary-dark;
    transform: scale(0.95);
  }
}

// 固定订单按钮
.fixed-order-btn {
  position: fixed;
  bottom: 70px;
  right: $spacing-lg; // 24px
  @extend .flex-column;
  align-items: center;
  background-color: $color-bg-primary;
  border: 1px solid $color-border-light;
  width: 56px;
  height: 56px;
  border-radius: $radius-round;
  justify-content: center;
  box-shadow: $shadow-md;
  transition: all $transition-normal;

  &:active {
    transform: scale(0.9);
    box-shadow: $shadow-lg;
  }

  .order-text {
    color: #fa5756;
    font-size: $font-size-xs; // 12px
    font-weight: $font-weight-bold;
    margin-top: $spacing-xs; // 4px
  }
}
</style>
