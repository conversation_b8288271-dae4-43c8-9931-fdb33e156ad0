<template>
	<view class="container">
		<view v-for="(item,index) in boxData" :key="index" class="box-container">
			<view class="box-title">
				<!-- <uni-icons type="arrow-down" size="20" class="title-icon"></uni-icons> -->
				<view class="title-icon"></view>
				<text>{{item.title}}</text>
			</view>
			<!-- <img :src="item.img" alt="" class="box-img"/> -->
			<image show-menu-by-longpress="true" :src="item.img" mode="widthFix" v-if="item.type === '0'"></image>
			<view class="box-card" v-else @click="toUrl(item)">
				<view class="card-imgbox">
					<image :src="item.img" mode="widthFix" class="card-img"></image>
				</view>
				<view class="card-title">
					<view style="font-size: 45rpx;font-weight: 700;margin-bottom: 10rpx;">{{item.title}}</view>
					<view style="font-size: 30rpx;color: #8a8a8a;">一卡畅通</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data(){
		return{
			boxData:[ // type： 0二维码图片 1跳转链接
				{
					title:"交通9901",
					img:"https://elcxt.online:8199/files/javaForARResource/img/交通9901.jpg",
					type:"0"
				},
				{
					title:"冀通卡",
					img:"https://elcxt.online:8199/files/javaForARResource/img/冀通卡.jpg",
					type:"0"
				},
				{
					title:"世纪恒通卡",
					img:"https://elcxt.online:8199/files/javaForARResource/img/ETC办理.png",
					type:"1",
				},
				{
					title:"蒙通畅行卡",
					img:"https://elcxt.online:8199/files/javaForARResource/img/蒙通畅行卡.png",
					type:"0"
				},
				{
					title:"速通卡", 
					img:"https://elcxt.online:8199/files/javaForARResource/img/速通卡.jpg",
					type:"0"
				},
			],
			carId:''
			
		}
	},
	methods:{
		toUrl(item){
			if(item.title === '世纪恒通卡'){
				let toPath =
					'pages/empty_hair/new_module/select_etc_handle_type/select_etc_handle_type?isNewTrucks=0&shopId=1303036541465796608&vehPlates=' +
					this.carId + '&extend={"merchantCode":"PPVMZIYXEXGDRCZOG"}'
				uni.navigateToMiniProgram({
					// appid  写你要跳转的小程序的 appid
					appId: 'wxddb3eb32425e4a96',
					// 路径写  src下的路径,假如你跳转的是pages下的页面,就可以写pages/index
					path: toPath,
					extraData: {
						'type': 'out'
					},
					// 这个不写的话会显示开发环境,不能正常跳转,写上就能正常跳转了
					envVersion: 'release',
					success(res) {
						// 打开成功
						uni.showToast({
							title: '跳转成功'
						})
					},
					fail(err) {
						// 打开失败/取消
						// uni.showToast({
						// 	title: '取消跳转',
						// 	icon: 'error'
						// })
					}
				})
			}
		}
	},
	onLoad: function (option) {
		this.carId = option.carId
		console.log(this.carId);
	}
}
</script>

<style scoped>
	
	.container{
		background-color: #f5f5f5;
		height: auto;
		min-height: 100vh;
	}
	
	.box-container{
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40rpx;
		padding: 20rpx 10% 0 10%;
	}
	
	.box-container:last-child {
		padding-bottom: 200rpx;
	}
	
	.box-title{
		/* font-size: 40rpx; */
		font-weight: 700;
		margin-top: 10rpx;
		margin-bottom: 30rpx;
		display: flex;
	}
	
	.title-icon{
		width: 0;
		height: 0;
		border-left: 15rpx solid transparent;
		border-right: 15rpx solid transparent;
		border-top: 25rpx solid #e64f38;
		transform: translateY(10rpx);
		margin-right: 10rpx;
	}
	
	.box-card{
		width: 100%;
		height: 220rpx;
		border-radius: 20rpx;
		background-color: #fff;
		display: flex;
	}
	
	.card-imgbox{
		height: 100%;
		width: 40%;
		display: flex;
		justify-content: center;
		align-items: center;
		transform: translateX(20rpx);
	}
	
	.card-img{
		width: 80%;
	}
	
	.card-title{
		height: 100%;
		width: 60%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		transform: translateX(-20rpx);
	}
	
</style>