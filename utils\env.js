// 环境工具函数
import config, { getCurrentEnv, isDevelopment, isProduction } from '@/config/index.js'

/**
 * 获取当前环境配置
 */
export function getEnvConfig() {
  return config
}

/**
 * 获取当前环境名称
 */
export function getEnv() {
  return getCurrentEnv()
}

/**
 * 判断是否为开发环境
 */
export function isDev() {
  return isDevelopment
}

/**
 * 判断是否为生产环境
 */
export function isProd() {
  return isProduction
}

/**
 * 根据环境执行不同的函数
 * @param {Object} handlers - 处理函数对象
 * @param {Function} handlers.development - 开发环境处理函数
 * @param {Function} handlers.production - 生产环境处理函数
 * @param {Function} handlers.default - 默认处理函数
 */
export function runByEnv(handlers = {}) {
  const env = getCurrentEnv()
  const handler = handlers[env] || handlers.default
  
  if (typeof handler === 'function') {
    return handler()
  }
  
  return null
}

/**
 * 环境相关的日志输出
 * 只在开发环境或启用调试时输出
 */
export function envLog(...args) {
  if (config.DEBUG) {
    console.log('[ENV]', ...args)
  }
}

/**
 * 环境相关的警告输出
 */
export function envWarn(...args) {
  if (config.DEBUG || isProduction) {
    console.warn('[ENV]', ...args)
  }
}

/**
 * 环境相关的错误输出
 */
export function envError(...args) {
  console.error('[ENV]', ...args)
}
