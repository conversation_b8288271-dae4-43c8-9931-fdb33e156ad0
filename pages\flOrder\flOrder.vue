<template>
	<view class="bkcolor" style="background-color: #F8F8F8;">
		<!-- 订单外容器 -->
		<view class="fl-card wd">
			<view v-for="(item,index) in orederList" :key="index">
				<view class="order-info">
					<view>
						{{item.orderName}}
					</view>
					<view>
						订单号: {{item.orderno}}
					</view>
					<view>
						充值账号: {{item.chargeAccount}}
					</view>
					<view>
						创建时间:{{dateString(item.completeDate)}}
					</view>
					<view style="display: flex; align-items: center;">
						订单状态: <view style="margin: 0 5px; font-size: 14px; padding: 2px 10px; border-radius: 5px;" :class="item.status">{{orderStatuDirct[item.status]}}</view>
					</view>
				</view>
				<view class="fl-line" v-if="orederList.length - 1 != index">
					
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { getAllUsOrders } from '@/api/fuLu.js'
	import { mapGetters } from 'vuex'
	export default {
		data() {
			return {
				orederList: [],
				orderStatuDirct: {
					waitPay: '待支付',
					success: '成功',
					processing: '处理中',
					failed: '失败',
					untreated: '未处理'
				}
			}
		},
		methods: {
			getAllUsOrders() {
				const data = {
					phone: this.userInfo.phone
				}
				getAllUsOrders(data).then(res => {
					res = res.data
					console.log(res)
					if(res.code !== 200) {
						return
					}
					const data = res.data
					data.sort((a, b) => {
						return b.id - a.id
					})
					this.orederList = data
				})
			},
			dateString(str) {
				let res = ''
				if(!str) {
					return '未知'
				}
				const arr1 = str.split('/')
				const arr2 = arr1[2].split(' ')
				return arr1[0] + '年' + arr1[1] + '月' + arr2[0] + '日 ' + arr2[1]
			}
		},
		computed: {
			...mapGetters(['userInfo'])
		},
		onLoad() {
			this.getAllUsOrders()
		}
	}
</script>

<style>
.fl-card {
	background-color: white;
	padding: 20px 10px;
	border-radius: 15px;
}
.fl-line {
	height: 1px;
	width: 90%;
	background-color: rgba(0, 0, 0, 0.1);
	margin: 15px auto;
}
.order-info > view {
	margin: 5px 0;
}
.waitPay {
	background-color: #ECF5FF;
	color: #4F9FFF;
	border: 1px #4F9FFF solid;
}
.processing {
	background-color: #FDF6EC;
	color: #E7A849;
	border: 1px solid #E7A849;
}
.success {
	background-color: #F0F9EB;
	color: #70C44C;
	border: 1px solid #70C44C;
}
.failed {
	background-color: #FEF0F0;
	color: #F79191;
	border: 1px solid #F79191;
}
.untreated {
	background-color: #F4F4F5;
	color: #96999E;
	border: 1px solid #96999E;
}
</style>
