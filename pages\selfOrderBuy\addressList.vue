<template>
	<view class="address-page">

		<!-- 地址列表 -->
		<scroll-view v-if="addressList.length > 0" class="address-list" scroll-y>
			<!-- <view v-for="(item, index) in addressList" :key="index" class="address-item"
				@click="selectAddress(item.id)">
				<view class="address-content">
					<view class="radio-container">
						<radio :value="item.id" :checked="selectedAddress === item.id" />
					</view>
					<view class="address-info">
						<view class="user-info">
							<text class="name">{{item.name}}</text>
							<text class="phone">{{item.phone}}</text>
						</view>
						<text class="address">{{item.address}}</text>
						<view class="operation">
							<uni-icons type="compose" size="20" color="#666666" @click="handleEdit(item)" />
						</view>
					</view>
				</view> -->
			<!-- 左滑删除区域 -->
			<uni-swipe-action>
				<view v-for="(item, index) in addressList" :key="index" class="address-item"
					@click="selectAddress(item.addressId)">

					<uni-swipe-action-item :right-options="swipeOptions" @click="handleDelete(item)">
						<view class="address-content">
							<view class="radio-container">
								<radio :value="item.addressId" :checked="selectedAddress === item.addressId" />
							</view>
							<view class="address-info">
								<view class="user-info">
									<text class="name">{{item.recipientName}}</text>
									<text class="phone">{{item.phoneNumber}}</text>
								</view>
								<text class="address">{{item.provinceCityDistrict}}{{item.addressDetail}}</text>
								<view class="operation">
									<uni-icons type="compose" size="20" color="#666666" @click="handleEdit(item)" />
								</view>
							</view>
						</view>
					</uni-swipe-action-item>
				</view>

			</uni-swipe-action>

		</scroll-view>

		<!-- 空状态 -->
		<view v-else class="empty-state">
			<!-- <image class="empty-icon" :src="emptyIconUrl" mode="aspectFit" /> -->
			<text class="empty-text">暂无收件地址</text>
		</view>

		<!-- 底部按钮 -->
		<view class="footer">
			<button class="add-btn" @click="handleAdd">新增收件地址</button>
			<button v-if="addressList.length > 0" class="confirm-btn" @click="handleConfirm">确认选择</button>
		</view>
	</view>
</template>

<script>
	import {
		queryAllAddr,
		updAddr
	} from "@/api/selfGoods.js"
	export default {
		data() {
			return {
				emptyIconUrl: 'https://ai-public.mastergo.com/ai/img_res/7ef021dc91eace78c4d67fd4db2c6934.jpg',
				addressList: [],
				swipeOptions: [{
					text: '删除',
					style: {
						backgroundColor: '#FF3B30'
					}
				}],
				selectedAddress: null
			}
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			selectAddress(id) {
				console.log("点击")
				this.selectedAddress = id;
			},
			handleEdit(item) {
				uni.navigateTo({
					url: '/pages/selfOrderBuy/editAddress?type=upd&item=' + JSON.stringify(item)
				})
			},
			handleDelete(item) {
				console.log(item)
				uni.showModal({
					title: '提示',
					content: '确定要删除该地址吗？',
					success: (res) => {
						if (res.confirm) {
							uni.showLoading({
								title: "删除中"
							})
							let data = {
								addressId: item.addressId,
								logicalDel: 1
							}
							updAddr(data).then(res => {
								if (res.data == "updSuccess") {
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									});
									this.getAdrInfo()
								} else {
									uni.showToast({
										title: '删除失败',
										icon: 'error'
									});
								}
							}).catch(err => {
								uni.showToast({
									title: '错误',
									icon: 'error'
								});
							})
						}
					}
				});
			},
			handleAdd() {
				uni.navigateTo({
					url: '/pages/selfOrderBuy/editAddress'
				})
			},
			handleConfirm() {
				if (!this.selectedAddress) {
					uni.showToast({
						title: '请选择地址',
						icon: 'none'
					});
					return;
				}
				const selected = this.addressList.find(item => item.addressId === this.selectedAddress);
				// 返回上级页面并携带数据
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 2]; // 上一页实例
				console.log(prevPage)
				// 调用上一页的方法设置地址
				prevPage.$vm.setSelectedAddress(selected);

				uni.navigateBack();


				// 这里可以添加其他确认后的逻辑，比如返回上一页并带回选择结果
			},
			//查询所有收货人信息
			getAdrInfo() {
				uni.showLoading({
					title: "加载中"
				})
				let data = {
					userId: uni.getStorageSync("phone"),
					logicalDel: 0
				}
				queryAllAddr(data).then(res => {
					uni.hideLoading()
					this.addressList = res.data
					this.selectedAddress = res.data[0].addressId
				})
			}
		},

		onShow() {
			this.getAdrInfo()
		}
	}
</script>

<style>
	page {
		height: 100%;
	}

	.address-page {
		height: 100%;
		display: flex;
		flex-direction: column;
		background-color: #F5F5F5;
	}

	.header {
		display: flex;
		align-items: center;
		height: 88rpx;
		padding: 0 32rpx;
		background-color: #FFFFFF;
		flex-shrink: 0;
	}

	.title {
		flex: 1;
		text-align: center;
		font-size: 16px;
		font-weight: 500;
		color: #333333;
		margin-right: 44rpx;
	}

	.address-list {
		flex: 1;
		overflow: auto;
		padding: 24rpx;
		box-sizing: border-box;
	}

	.address-item {
		/* margin-bottom: 24rpx; */
		border-radius: 12rpx;
		/* background-color: #FFFFFF; */
		overflow: hidden;
	}

	.address-content {
		padding: 32rpx;
		position: relative;
		background-color: #fff;
		display: flex;
		align-items: center;
	}

	.radio-container {
		margin-right: 20rpx;
	}

	.address-info {
		flex: 1;
	}

	.user-info {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.name {
		font-size: 16px;
		color: #333333;
		margin-right: 24rpx;
	}

	.phone {
		font-size: 14px;
		color: #666666;
	}

	.address {
		font-size: 14px;
		color: #666666;
		line-height: 1.4;
	}

	.operation {
		position: absolute;
		right: 32rpx;
		top: 32rpx;
	}

	.empty-state {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 32rpx;
	}

	.empty-text {
		font-size: 14px;
		color: #999999;
	}

	.footer {
		padding: 32rpx;
		background-color: #FFFFFF;
		flex-shrink: 0;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.add-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		background-color: #FFFFFF;
		color: #FF4D4F;
		font-size: 16px;
		border-radius: 44rpx;
		border: 1px solid #FF4D4F;
	}

	.confirm-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		background-color: #FF4D4F;
		color: #FFFFFF;
		font-size: 16px;
		border-radius: 44rpx;
	}

	.swipe-content {
		width: 100%;
		height: 100%;
		background-color: #FFFFFF;
	}
</style>