<template>
  <view class="container">
    <image class="background-image" src="@/static/wecome/wecome.png"></image>

    <!-- 顶部选择器 -->
    <view class="selector-section">
      <picker @change="bindPickerChange" :value="index" :range="devList">
        <view class="selector-button">
          <text class="selector-text">
            {{ devList[index] === undefined ? "请先登录" : devList[index] }}
          </text>
          <uni-icons type="right" size="20" color="white"></uni-icons>
        </view>
      </picker>
    </view>

    <!-- 二维码卡片 -->
    <view class="qr-card" v-if="url !== ''">
      <view class="qr-header">
        <view class="qr-title">发展成员二维码</view>
        <view class="qr-subtitle">请使用{{ softName }}扫描二维码</view>
      </view>

      <view class="qr-content">
        <view class="qr-wrapper">
          <image :src="url" class="qr-image" mode="widthFix"></image>
        </view>
      </view>

      <view class="qr-info">
        <view class="info-item">
          <view class="info-label">当前级别</view>
          <view class="info-value">{{ devList[index] }}</view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-card" v-if="url === '' && !isSend">
      <view class="loading-text">正在生成二维码...</view>
    </view>
  </view>
</template>

<script>
import { showEwm, getUnlimitedQRCode } from "@/api/proxyDevelopment.js";
import { mapGetters } from "vuex";
import { getUserId, isLogin, isProxyLogin } from "@/utils/auth.js";

const devDirct = ["普通用户", "1级代理", "2级代理"];
const devLabelAndValue = {
  普通用户: 0,
  "1级代理": 1,
  "2级代理": 2,
};

export default {
  data() {
    return {
      devList: [],
      index: 0,
      url: "",
      isSend: false,
    };
  },
  methods: {
    bindPickerChange(e) {
      console.log(e);
      const index = parseInt(e.detail.value);
      this.index = index;
    },
    showEwm() {
      if (this.isSend) {
        return;
      }
      this.isSend = true;
      this.url = "";
      uni.showLoading({
        mask: true,
        title: "加载二维码中",
      });
      const checkIndex = this.index;
      const adminRabk = devLabelAndValue[this.devList[checkIndex]];
      const data = {
        adminRabk,
        id: getUserId(),
      };
      showEwm(data)
        .then((res) => {
          console.log(res);
          if (res.data.code !== 200) {
            return;
          }
          this.url = res.data.data;
          console.log(this.url);
        })
        .finally(() => {
          this.isSend = false;
          uni.hideLoading();
        });
    },
    // 获取能发展列表
    updateDevList() {
      this.devList = [];
      const adminRabk =
        this.userInfo.adminRabk === "" ? 0 : parseInt(this.userInfo.adminRabk);
      if (adminRabk === 1) {
        this.devList = ["普通用户", "2级代理"];
      } else if (adminRabk === 2) {
        this.devList = ["普通用户"];
      } else if (adminRabk === 3) {
        this.devList = ["普通用户", "1级代理"];
      }
    },
  },
  computed: {
    ...mapGetters("proxyUser", ["userInfo"]),
    softName() {
      if (this.index === 0) {
        return "App";
      }
      return "小程序";
    },
  },
  onLoad() {
    // 检查代理端登录状态
    if (!isLogin() || !isProxyLogin()) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }
    this.updateDevList();
    this.showEwm();
  },
  watch: {
    userInfo() {
      this.updateDevList();
    },
    index() {
      this.showEwm();
    },
  },
};
</script>

<style>
.container {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 0;
}

.selector-section {
  position: relative;
  z-index: 1;
  padding: 30px 20px 20px;
}

.selector-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 15px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.selector-text {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.qr-card {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 300px;
  max-width: 350px;
}

.qr-header {
  text-align: center;
  margin-bottom: 20px;
}

.qr-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.qr-subtitle {
  font-size: 14px;
  color: #666;
}

.qr-content {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.qr-wrapper {
  background: white;
  padding: 15px;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.qr-image {
  width: 200px;
  display: block;
}

.qr-info {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  font-weight: 600;
  color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  padding: 4px 12px;
  border-radius: 12px;
}

.loading-card {
  position: fixed;
  bottom: 40px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.loading-text {
  font-size: 16px;
  color: #666;
  text-align: center;
}
</style>
