<template>
  <view>
    <view class="container" v-if="product">
      <!-- 商品封面区域 -->
      <swiper
        class="main-image-swiper"
        :indicator-dots="true"
        :autoplay="true"
        :interval="3000"
        :duration="500"
        circular
      >
        <swiper-item v-for="(img, index) in coverImages" :key="index">
          <image class="main-image" :src="img" mode="aspectFill" />
        </swiper-item>
      </swiper>

      <!-- 价格信息区 -->
      <view class="price-section">
        <text class="price">￥{{ product.goodsTotalPrice }}</text>
      </view>

      <!-- 商品标语区 -->
      <view class="title-section">
        <text class="title">
          {{ product.goodsName }}
        </text>
      </view>

      <!-- 服务保障区 -->
      <view class="service-section" @click="showServe()">
        <text class="service-text1">服务</text>
        <text class="service-text single-line-ellipsis">{{
          product.serviceDescription
        }}</text>
      </view>

      <!-- 导航标签栏 -->
      <view class="nav-tabs">
        <view
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab-item"
          :class="{ active: activeTab === index }"
          @click="switchTab(index)"
        >
          {{ tab.title }}
        </view>
      </view>

      <!-- 导航内容栏 -->
      <view class="nav-content">
        <view v-if="activeTab === 0">
          <view v-if="detailImages && detailImages.length > 0">
            <image
              v-for="(img, index) in detailImages"
              :key="index"
              :src="img"
              style="width: 100%"
              mode="widthFix"
            ></image>
          </view>
          <view v-else class="no-content">暂无图文详情</view>
        </view>
        <view
          v-else
          style="
            width: 100%;
            padding: 20rpx;
            box-sizing: border-box;
            display: flex;
          "
        >
          <text style="width: 100%; text-align: left">
            {{ product.goodsParameter }}
          </text>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="footer">
        <!-- <view class="gohome" @click="goToHome">
			<uni-icons type="home" size="30" color="#666666"></uni-icons>
			<text>首页</text>
		</view> -->

        <view class="action-btn buy-btn" @click="buyNow">
          <text class="btn-text">立即购买</text>
        </view>
      </view>
    </view>
    <view v-else class="loading">
      <text>加载中...</text>
    </view>

    <uni-popup
      ref="popup"
      background-color="#fff"
      border-radius="10px"
      @change="change"
    >
      <view class="popup-content">
        <text class="text">{{ product.serviceDescription }}</text>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getGoodsDetail } from "@/api/goods";

export default {
  data() {
    return {
      activeTab: 0,
      tabs: [
        {
          title: "图文详情",
        },
        {
          title: "商品参数",
        },
      ],
      product: null,
      goodsId: 0,
      coverImages: [], // 存储分割后的封面图片
      detailImages: [], // 存储分割后的详情图片
    };
  },
  onLoad(options) {
    // 获取传递的商品ID
    if (options.id) {
      this.goodsId = options.id;
      this.loadGoodsDetail();
    } else {
      uni.showToast({
        title: "商品信息获取失败",
        icon: "none",
      });
    }
  },
  methods: {
    // 加载商品详情
    loadGoodsDetail() {
      uni.showLoading({
        title: "加载中...",
      });

      getGoodsDetail({
        id: this.goodsId,
      })
        .then((res) => {
          uni.hideLoading();
          console.log("商品详情:", res);

          if (res.data) {
            // 处理返回的数组数据，取第一个元素
            if (Array.isArray(res.data) && res.data.length > 0) {
              this.product = res.data[0]; // 从数组中取第一个商品
              // 处理商品封面图片
              this.handleCoverImages();
              // 处理商品详情图片
              this.handleDetailImages();
              console.log("处理后的商品数据:", this.product);
            } else {
              uni.showToast({
                title: "商品不存在",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: "数据加载失败",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          console.error("请求失败:", err);
          uni.hideLoading();
          uni.showToast({
            title: "网络连接失败",
            icon: "none",
          });
        });
    },
    // 处理商品封面图片
    handleCoverImages() {
      if (this.product && this.product.goodsCover) {
        this.coverImages = this.product.goodsCover
          .split(",")
          .filter((img) => img.trim() !== "");
        console.log("处理后的封面图片:", this.coverImages);
      } else {
        this.coverImages = [];
      }
    },
    // 处理商品详情图片
    handleDetailImages() {
      if (this.product && this.product.goodsPic) {
        this.detailImages = this.product.goodsPic
          .split(",")
          .filter((img) => img.trim() !== "");
        console.log("处理后的详情图片:", this.detailImages);
      } else {
        this.detailImages = [];
      }
    },
    switchTab(index) {
      this.activeTab = index;
    },
    buyNow() {
      console.log("立即购买");
      uni.navigateTo({
        url: `/pages/selfOrderBuy/selfOrderBuy?id=${this.goodsId}`,
      });
    },
    // 去首页
    goToHome() {
      uni.navigateTo({
        url: "/pages/my/my",
      });
    },
    showServe() {
      this.$refs.popup.open("bottom");
    },
  },
};
</script>

<style scoped>
.container {
  background: #f1f1f1;
  padding-bottom: 120rpx;
}

.main-image-swiper {
  width: 100%;
  height: 750rpx;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 30rpx;
  color: #999;
}

.no-content {
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.main-image {
  width: 100%;
  height: 750rpx;
}

.price-section {
  padding: 30rpx 30rpx 0 30rpx;
  background: #fff;
}

.price {
  font-size: 48rpx;
  color: #ff4444;
  font-weight: bold;
  display: block;
}

.title-section {
  border-bottom: 1px solid #eee;
  padding: 30rpx;
  background: #fff;
}

.description {
  font-size: 28rpx;
  color: #666;
}

.service-section {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  padding: 40rpx 30rpx;
  background: #fff;
  display: flex;
}

.service-text1 {
  font-size: 28rpx;
  color: #999;
  position: relative;
  padding: 0 20rpx;
  width: 10vw;
}

.service-text {
  font-size: 28rpx;
  width: 40vw;
  position: relative;
  padding-left: 20rpx;
}

.nav-tabs {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #eee;
  background: #fff;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 35rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #ff5490;
  font-weight: bold;
}

.footer {
  padding: 10rpx 20rpx 10rpx 20rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  height: 100rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-btn {
  width: 80%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.buy-btn {
  background: #ff5490;
  color: white;
  margin-left: 20rpx;
  border-radius: 100px;
}

.btn-text {
  margin-left: 10rpx;
}

.gohome {
  display: flex;
  flex-direction: column;
  text-align: center;
}

.gohome text {
  color: #666666;
  font-size: 25rpx;
}

.nav-content {
  background: #fff;
  text-align: center;
  padding: 20rpx;
  min-height: 300rpx;
}

.single-line-ellipsis {
  display: block;
  /* 或 inline-block */
  white-space: nowrap;
  /* 禁止换行 */
  overflow: hidden;
  /* 隐藏溢出内容 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  width: 100%;
  /* 需要指定宽度 */
}

.popup-content {
  height: 40vh;
  padding: 30rpx;
}

text {
  letter-spacing: 1px;
}
</style>
