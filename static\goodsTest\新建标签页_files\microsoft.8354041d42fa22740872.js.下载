/*! For license information please see microsoft.8354041d42fa22740872.js.LICENSE.txt */
(self.edgeChromiumWebpackChunks=self.edgeChromiumWebpackChunks||[]).push([["microsoft"],{63165:function(t,e,n){"use strict";n.d(e,{Z:function(){return R}});var i=n(45362),r=n(23806),o=n(80221),s=n(60851),a=n(26454),c=n(254),l=n(74539),u=n(98500),d=n(69509),h=n(39289),f=500,p="Channel has invalid priority - ";function g(t,e,n){e&&(0,l.kJ)(e)&&e[c.R5]>0&&(e=e.sort((function(t,e){return t[u.yi]-e[u.yi]})),(0,l.tO)(e,(function(t){t[u.yi]<f&&(0,l._y)(p+t[c.pZ])})),t[c.MW]({queue:(0,l.FL)(e),chain:(0,d.jV)(e,n[c.TC],n)}))}var v=n(85282),m=n(5482),b=n(33220),y=n(26932),w=function(t){function e(){var n,i,a=t.call(this)||this;function d(){n=0,i=[]}return a.identifier="TelemetryInitializerPlugin",a.priority=199,d(),(0,r.Z)(e,a,(function(t,e){t.addTelemetryInitializer=function(t){var e={id:n++,fn:t};return i[c.MW](e),{remove:function(){(0,l.tO)(i,(function(t,n){if(t.id===e.id)return i[c.cb](n,1),-1}))}}},t[u.hL]=function(e,n){for(var r=!1,a=i[c.R5],u=0;u<a;++u){var d=i[u];if(d)try{if(!1===d.fn[c.ZV](null,[e])){r=!0;break}}catch(t){(0,o.kP)(n[c.mc](),1,64,"One of telemetry initializers failed, telemetry item will not be sent: "+(0,l.jj)(t),{exception:(0,s.eU)(t)},!0)}}r||t[c.uL](e,n)},t[c.F3]=function(){d()}})),a}return(0,i.ne)(e,t),e.__ieDyn=1,e}(n(27981).i),x=n(9894),C="Plugins must provide initialize method",k="_notificationManager",S="SDK is still unloading...",$={loggingLevelConsole:1};function T(t,e){return new y.Jk(e)}function L(t,e){var n=!1;return(0,l.tO)(e,(function(e){if(e===t)return n=!0,-1})),n}var O=function(){function t(){var e,n,s,p,O,I,E,D,R,A,B,F,H,M,N,P,j,_,z,V,U=0;(0,r.Z)(t,this,(function(t){function r(){n=!1,e=(0,l.mm)(!0,{},$),t[c.TC]=e,t[c.eZ]=new o.AQ(e),t[c.iC]=[],N=new w,s=[],p=null,O=null,I=null,E=null,D=null,A=null,R=[],B=null,F=null,H=null,M=!1,P=null,j=(0,m.J)("AIBaseCore",!0),_=(0,x.Y)(),V=null}function q(){return(0,d.CD)(Z(),e,t)}function W(n){var i=function(t,e,n){var i,r=[],s={};return(0,l.tO)(n,(function(n){((0,l.le)(n)||(0,l.le)(n[c.VL]))&&(0,l._y)(C);var i=n[u.yi],a=n[c.pZ];n&&i&&((0,l.le)(s[i])?s[i]=a:(0,o.jV)(t,"Two extensions have same priority #"+i+" - "+s[i]+", "+a)),(!i||i<e)&&r[c.MW](n)})),(i={all:n})[u.oV]=r,i}(t[c.eZ],f,R);A=i[u.oV],D=null;var r=i.all;if(H=(0,l.FL)(function(t,e,n){var i=[];if(t&&(0,l.tO)(t,(function(t){return g(i,t,n)})),e){var r=[];(0,l.tO)(e,(function(t){t[u.yi]>f&&r[c.MW](t)})),g(i,r,n)}return i}(F,r,t)),B){var s=(0,l.UA)(r,B);-1!==s&&r[c.cb](s,1),-1!==(s=(0,l.UA)(A,B))&&A[c.cb](s,1),B._setQueue(H)}else B=function(t,e){function n(){return(0,d.CD)(null,e[c.TC],e,null)}function i(t,e,n,i){var r=t?t[c.R5]+1:1;function o(){0==--r&&(i&&i(),i=null)}r>0&&(0,l.tO)(t,(function(t){if(t&&t.queue[c.R5]>0){var i=t.chain,s=e[c.zV](i);s[c.Fc](o),n(s)}else r--})),o()}var r=!1,o={identifier:"ChannelControllerPlugin",priority:f,initialize:function(e,n,i,o){r=!0,(0,l.tO)(t,(function(t){t&&t.queue[c.R5]>0&&(0,h.bP)((0,d.CD)(t.chain,e,n),i)}))},isInitialized:function(){return r},processTelemetry:function(e,r){i(t,r||n(),(function(t){t[c.uL](e)}),(function(){r[c.uL](e)}))},update:function(e,n){var r=n||{reason:0};return i(t,e,(function(t){t[c.uL](r)}),(function(){e[c.uL](r)})),!0},pause:function(){i(t,n(),(function(t){t.iterate((function(t){t.pause&&t.pause()}))}),null)},resume:function(){i(t,n(),(function(t){t.iterate((function(t){t.resume&&t.resume()}))}),null)},teardown:function(e,n){var o=n||{reason:0,isAsync:!1};return i(t,e,(function(t){t[c.uL](o)}),(function(){e[c.uL](o),r=!1})),!0},getChannel:function(e){var n=null;return t&&t[c.R5]>0&&(0,l.tO)(t,(function(t){if(t&&t.queue[c.R5]>0&&((0,l.tO)(t.queue,(function(t){if(t[c.pZ]===e)return n=t,-1})),n))return-1})),n},flush:function(e,r,o,s){var a=1,l=!1,u=null;function d(){a--,l&&0===a&&(u&&(clearTimeout(u),u=null),r&&r(l),r=null)}return s=s||5e3,i(t,n(),(function(t){t.iterate((function(t){if(t[c.kL]){a++;var n=!1;t[c.kL](e,(function(){n=!0,d()}),o)||n||(e&&null==u?u=setTimeout((function(){u=null,d()}),s):d())}}))}),(function(){l=!0,d()})),!0},_setQueue:function(e){t=e}};return o}(H,t);r[c.MW](B),A[c.MW](B),t[c.iC]=(0,h.AA)(r),B[c.VL](e,t,r),(0,h.bP)(q(),r),t[c.iC]=(0,l.FL)((0,h.AA)(A||[])).slice(),n&&function(e){var n=(0,d.xy)(Z(),t);t._updateHook&&!0===t._updateHook(n,e)||n[c.uL](e)}(n)}function K(e){var n,i=null,r=null;return(0,l.tO)(t[c.iC],(function(t){if(t[c.pZ]===e&&t!==B&&t!==N)return r=t,-1})),!r&&B&&(r=B.getChannel(e)),r&&((n={plugin:r})[c.$h]=function(t){(0,h.OY)(r)[u.mE]=!t},n.isEnabled=function(){var t=(0,h.OY)(r);return!t[c.fi]&&!t[u.mE]},n.remove=function(t,e){var n;void 0===t&&(t=!0);var i=[r],o=((n={reason:1})[c.d]=t,n);G(i,o,(function(t){t&&W({reason:32,removed:i}),e&&e(t)}))},i=n),i}function Z(){if(!D){var n=(A||[]).slice();-1===(0,l.UA)(n,N)&&n[c.MW](N),D=(0,d.jV)((0,h.AA)(n),e,t)}return D}function G(n,i,r){if(n&&n[c.R5]>0){var o=(0,d.jV)(n,e,t),s=(0,d.Bt)(o,t);s[c.Fc]((function(){var t=!1,e=[];(0,l.tO)(R,(function(i,r){L(i,n)?t=!0:e[c.MW](i)})),R=e;var i=[];F&&((0,l.tO)(F,(function(e,r){var o=[];(0,l.tO)(e,(function(e){L(e,n)?t=!0:o[c.MW](e)})),i[c.MW](o)})),F=i),r&&r(t)})),s[c.uL](i)}else r(!1)}function X(){var n=t[c.eZ]?t[c.eZ].queue:[];n&&((0,l.tO)(n,(function(n){var i,r=((i={})[c.I]=P||"InternalMessageId: "+n[c.Gc],i.iKey=(0,l.v4)(e[c.p$]),i.time=(0,l.Y6)(new Date),i.baseType=o.lQ.dataType,i.baseData={message:n[c.gU]},i);t.track(r)})),n[c.R5]=0)}function J(t,e,n,i){return B?B[c.kL](t,e,n||6,i):(e&&e(!1),!0)}function Y(e){var n=t[c.eZ];n?(0,o.kP)(n,2,73,e):(0,l._y)(e)}r(),t[c.yl]=function(){return n},t[c.VL]=function(r,o,s,a){M&&(0,l._y)(S),t[c.yl]()&&(0,l._y)("Core should not be initialized more than once"),e=r||{},t[c.TC]=e,(0,l.le)(r[c.p$])&&(0,l._y)("Please provide instrumentation key"),p=a,t[k]=a,function(){var t=(0,l.v4)(e.disableDbgExt);!0===t&&z&&(p[c.n7](z),z=null);p&&!z&&!0!==t&&(z=(0,b.p)(e),p[c.g](z))}(),function(){var t=(0,l.v4)(e.enablePerfMgr);!t&&I&&(I=null);t&&(0,l.qK)(e,u.DN,T)}(),(0,l.qK)(e,u.Zh,{}).NotificationManager=p,s&&(t[c.eZ]=s);var d=(0,l.qK)(e,u.js,[]);(R=[])[c.MW].apply(R,(0,i.$h)((0,i.$h)([],o,!1),d,!1)),F=(0,l.qK)(e,u.Z,[]),W(null),H&&0!==H[c.R5]||(0,l._y)("No "+u.Z+" available"),n=!0,t.releaseQueue()},t.getTransmissionControls=function(){var t=[];return H&&(0,l.tO)(H,(function(e){t[c.MW](e.queue)})),(0,l.FL)(t)},t.track=function(n){n.iKey=n.iKey||e[c.p$],n[c.Z_]=n[c.Z_]||(0,l.Y6)(new Date),n.ver=n.ver||"4.0",!M&&t[c.yl]()?q()[c.uL](n):s[c.MW](n)},t[c.SD]=q,t[c.tb]=function(){var e;return p||(p=(0,a.pu)(((e={})[c.g]=function(t){},e[c.n7]=function(t){},e[u.uC]=function(t){},e[u.$F]=function(t,e){},e[u.f_]=function(t,e){},e)),t[k]=p),p},t[c.g]=function(t){p&&p[c.g](t)},t[c.n7]=function(t){p&&p[c.n7](t)},t.getCookieMgr=function(){return E||(E=(0,v.Nz)(e,t[c.eZ])),E},t.setCookieMgr=function(t){E=t},t[u.kl]=function(){if(!O&&!I&&(0,l.v4)(e.enablePerfMgr)){var n=(0,l.v4)(e[u.DN]);(0,l.mf)(n)&&(I=n(t,t[c.tb]()))}return O||I||(0,y.j5)()},t.setPerfMgr=function(t){O=t},t.eventCnt=function(){return s[c.R5]},t.releaseQueue=function(){if(n&&s[c.R5]>0){var t=s;s=[],(0,l.tO)(t,(function(t){q()[c.uL](t)}))}},t.pollInternalLogs=function(t){P=t||null;var n=(0,l.v4)(e.diagnosticLogInterval);return n&&n>0||(n=1e4),U&&clearInterval(U),U=setInterval((function(){X()}),n)},t[c.LS]=function(){U&&(clearInterval(U),U=0,X())},(0,l.Vb)(t,(function(){return N}),["addTelemetryInitializer"]),t.unload=function(e,i,o){var s;void 0===e&&(e=!0),n||(0,l._y)("SDK is not initialized"),M&&(0,l._y)(S);var a=((s={reason:50})[c.d]=e,s.flushComplete=!1,s),u=(0,d.Bt)(Z(),t);function h(e){a.flushComplete=e,M=!0,_.run(u,a),t[c.LS](),u[c.uL](a)}u[c.Fc]((function(){r(),i&&i(a)}),t),J(e,h,6,o)||h(!1)},t[c.TO]=K,t.addPlugin=function(t,e,n,i){if(!t)return i&&i(!1),void Y(C);var r=K(t[c.pZ]);if(r&&!e)return i&&i(!1),void Y("Plugin ["+t[c.pZ]+"] is already loaded!");var o={reason:16};function s(e){R[c.MW](t),o.added=[t],W(o),i&&i(!0)}if(r){var a=[r.plugin];G(a,{reason:2,isAsync:!!n},(function(t){t?(o.removed=a,o.reason|=32,s()):i&&i(!1)}))}else s()},t.evtNamespace=function(){return j},t[c.kL]=J,t.getTraceCtx=function(t){return V||(V=(0,h.Yn)()),V},t.setTraceCtx=function(t){V=t||null},(0,l.Oi)(t,"addUnloadCb",(function(){return _}),"add")}))}return t.__ieDyn=1,t}(),I=n(18366),E=n(91254),D=function(t){function e(){var n=t.call(this)||this;return(0,r.Z)(e,n,(function(t,e){t.initialize=function(n,i,r,a){n&&!n.endpointUrl&&(n.endpointUrl=I.YT),t.getWParam=function(){return E.x9||n.enableWParam?0:-1};try{e.initialize(n,i,r,a)}catch(e){(0,o.kP)(t.logger,1,514,"Initialization Failed: "+(0,s.eU)(e)+"\n - Note: Channels must be provided through config.channels only")}},t.track=function(t){var n=t;if(n){var i=n.ext=n.ext||{};i.sdk=i.sdk||{},i.sdk.ver=E.vs}e.track(n)}})),n}return(0,i.ne)(e,t),e.__ieDyn=1,e}(O),R=D},65004:function(t,e,n){"use strict";n.d(e,{zU:function(){return k}});var i,r=n(45362),o=n(71178),s=n(74539),a="Failed",c=a+"MonitorAjax",l="Track",u="Start",d="Stop",h="Event",f="AuthContext",p="Exception",g="Local",v="Session",m="Storage",b="Browser",y="Cannot",w="Buffer",x="InstrumentationKey",C=((0,o.By)({CRITICAL:1,WARNING:2}),(0,o.By)(((i={})[b+"DoesNotSupport"+g+m]=0,i[b+y+"Read"+g+m]=1,i[b+y+"Read"+v+m]=2,i[b+y+"Write"+g+m]=3,i[b+y+"Write"+v+m]=4,i[b+a+"RemovalFrom"+g+m]=5,i[b+a+"RemovalFrom"+v+m]=6,i[y+"SendEmptyTelemetry"]=7,i.ClientPerformanceMathError=8,i["ErrorParsingAI"+v+"Cookie"]=9,i.ErrorPVCalc=10,i[p+"WhileLoggingError"]=11,i[a+"AddingTelemetryTo"+w]=12,i[c+"Abort"]=13,i[c+"Dur"]=14,i[c+"Open"]=15,i[c+"RSC"]=16,i[c+"Send"]=17,i[c+"GetCorrelationHeader"]=18,i[a+"ToAddHandlerForOnBeforeUnload"]=19,i[a+"ToSendQueuedTelemetry"]=20,i[a+"ToReportDataLoss"]=21,i["Flush"+a]=22,i.MessageLimitPerPVExceeded=23,i.MissingRequiredFieldSpecification=24,i.NavigationTimingNotSupported=25,i.OnError=26,i[v+"RenewalDateIsZero"]=27,i.SenderNotInitialized=28,i[u+l+h+a]=29,i[d+l+h+a]=30,i[u+l+a]=31,i[d+l+a]=32,i.TelemetrySampledAndNotSent=33,i[l+h+a]=34,i[l+p+a]=35,i[l+"Metric"+a]=36,i[l+"PV"+a]=37,i[l+"PV"+a+"Calc"]=38,i[l+"Trace"+a]=39,i["Transmission"+a]=40,i[a+"ToSet"+m+w]=41,i[a+"ToRestore"+m+w]=42,i.InvalidBackendResponse=43,i[a+"ToFixDepricatedValues"]=44,i.InvalidDurationValue=45,i.TelemetryEnvelopeInvalid=46,i.CreateEnvelopeError=47,i[y+"SerializeObject"]=48,i[y+"SerializeObjectNonSerializable"]=49,i.CircularReferenceDetected=50,i["Clear"+f+a]=51,i[p+"Truncated"]=52,i.IllegalCharsInName=53,i.ItemNotInArray=54,i.MaxAjaxPerPVExceeded=55,i.MessageTruncated=56,i.NameTooLong=57,i.SampleRateOutOfRange=58,i["Set"+f+a]=59,i["Set"+f+a+"AccountName"]=60,i.StringValueTooLong=61,i.StartCalledMoreThanOnce=62,i.StopCalledWithoutStart=63,i["TelemetryInitializer"+a]=64,i.TrackArgumentsNotSpecified=65,i.UrlTooLong=66,i[v+m+w+"Full"]=67,i[y+"AccessCookie"]=68,i.IdTooLong=69,i.InvalidEvent=70,i[c+"SetRequestHeader"]=71,i["Send"+b+"InfoOnUserInit"]=72,i["Plugin"+p]=73,i["Notification"+p]=74,i.SnippetScriptLoadFailure=99,i["Invalid"+x]=100,i[y+"ParseAiBlobValue"]=101,i.InvalidContentBlob=102,i[l+"PageAction"+h+a]=103,i[a+"AddingCustomDefinedRequestContext"]=104,i["InMemory"+m+w+"Full"]=105,i[x+"Deprecation"]=106,i))),k=((0,o.By)({NotSet:0,Pii_DistinguishedName:1,Pii_GenericData:2,Pii_IPV4Address:3,Pii_IPv6Address:4,Pii_MailSubject:5,Pii_PhoneNumber:6,Pii_QueryString:7,Pii_SipAddress:8,Pii_SmtpAddress:9,Pii_Identity:10,Pii_Uri:11,Pii_Fqdn:12,Pii_IPV4AddressLegacy:13,CustomerContent_GenericContent:32}),(0,o.By)({Normal:1,CostDeferred:2,RealTime:3,Immediate:4}));(0,o.By)({Unspecified:0,String:1,Int32:2,UInt32:3,Int64:4,UInt64:5,Double:6,Bool:7,Guid:8,DateTime:9}),(0,o.By)({Normal:1,Critical:2}),(0,o.By)({NONE:0,ERROR:1,WARNING:2,INFORMATION:3}),(0,s.FL)((0,r.uc)((0,r.uc)({},C),(0,o.By)({AuthHandShakeError:501,AuthRedirectFail:502,BrowserCannotReadLocalStorage:503,BrowserCannotWriteLocalStorage:504,BrowserDoesNotSupportLocalStorage:505,CannotParseBiBlobValue:506,CannotParseDataAttribute:507,CVPluginNotAvailable:508,DroppedEvent:509,ErrorParsingAISessionCookie:510,ErrorProvidedChannels:511,FailedToGetCookies:512,FailedToInitializeCorrelationVector:513,FailedToInitializeSDK:514,InvalidContentBlob:515,InvalidCorrelationValue:516,SessionRenewalDateIsZero:517,SendPostOnCompleteFailure:518,PostResponseHandler:519,SDKNotInitialized:520})))},18366:function(t,e,n){"use strict";n.d(e,{YT:function(){return r},qS:function(){return i}});var i="",r="https://browser.events.data.microsoft.com/OneCollector/1.0/"},91254:function(t,e,n){"use strict";n.d(e,{Do:function(){return S},Sn:function(){return w},Vv:function(){return k},cm:function(){return $},hK:function(){return L},if:function(){return E},jM:function(){return x},l7:function(){return T},mJ:function(){return R},ot:function(){return A},vs:function(){return p},x9:function(){return y},yj:function(){return C}});var i,r=n(60851),o=n(74539),s=n(85282),a=n(39860),c=n(21908),l=n(10607),u=n(42774),d=n(26454),h=n(18366),f="3.2.8",p="1DS-Web-JS-"+f,g="Microsoft_ApplicationInsights_BypassAjaxInstrumentation",v="withCredentials",m="timeout",b=((i={})[0]=0,i[2]=6,i[1]=1,i[3]=7,i[4098]=6,i[4097]=1,i[4099]=7,i),y=Boolean((0,r.Me)());Boolean((0,r.Jj)());function w(t){return!(t===h.qS||(0,o.le)(t))}function x(t){if(t){var e=t.indexOf("-");if(e>-1)return t.substring(0,e)}return h.qS}function C(t,e,n){if(!e&&!w(e)||"string"!=typeof t)return null;var i=typeof e;if("string"===i||"number"===i||"boolean"===i||(0,o.kJ)(e))e={value:e};else if("object"!==i||c.CY.call(e,"value")){if((0,o.le)(e.value)||e.value===h.qS||!(0,o.HD)(e.value)&&!(0,o.hj)(e.value)&&!(0,o.jn)(e.value)&&!(0,o.kJ)(e.value))return null}else e={value:n?JSON.stringify(e):e};if((0,o.kJ)(e.value)&&!I(e.value))return null;if(!(0,o.le)(e.kind)){if((0,o.kJ)(e.value)||!O(e.kind))return null;e.value=e.value.toString()}return e}function k(t,e,n){var i=-1;if(!(0,o.o8)(t))if(e>0&&(32===e?i=8192:e<=13&&(i=e<<5)),function(t){if(t>=0&&t<=9)return!0;return!1}(n))-1===i&&(i=0),i|=n;else{var r=b[D(t)]||-1;-1!==i&&-1!==r?i|=r:6===r&&(i=r)}return i}function S(t,e,n){var i;return void 0===n&&(n=!0),t&&(i=t.get(e),n&&i&&decodeURIComponent&&(i=decodeURIComponent(i))),i||h.qS}function $(t){void 0===t&&(t="D");var e=(0,a.GW)();return"B"===t?e="{"+e+"}":"P"===t?e="("+e+")":"N"===t&&(e=e.replace(/-/g,h.qS)),e}function T(t,e,n,i,r){var s={},a=!1,l=0,u=arguments.length,d=arguments;for("[object Boolean]"===Object[c.hB].toString.call(d[0])&&(a=d[0],l++);l<u;l++){t=d[l];(0,o.rW)(t,(function(t,e){a&&e&&(0,o.Kn)(e)?(0,o.kJ)(e)?(s[t]=s[t]||[],(0,o.tO)(e,(function(e,n){e&&(0,o.Kn)(e)?s[t][n]=T(!0,s[t][n],e):s[t][n]=e}))):s[t]=T(!0,s[t],e):s[t]=e}))}return s}var L=a.Jj;function O(t){return 0===t||t>0&&t<=13||32===t}function I(t){return t.length>0}function E(t,e){var n=t;n.timings=n.timings||{},n.timings.processTelemetryStart=n.timings.processTelemetryStart||{},n.timings.processTelemetryStart[e]=L()}function D(t){var e=0;if(null!=t){var n=typeof t;"string"===n?e=1:"number"===n?e=2:"boolean"===n?e=3:n===c.fK&&(e=4,(0,o.kJ)(t)?(e=4096,t.length>0&&(e|=D(t[0]))):c.CY.call(t,"value")&&(e=8192|D(t.value)))}return e}c.jA,c.fK,c.jA,o.tO,o.UA,o.Mr,o.Xz,o.FY,o.Y6,r.b$,o.HD,o.hj,o.jn,o.mf,o.kJ,o.Kn,o.nd,r.MF,o.Y6,r.cp,s.p7,s.UY,o.l_,l.c9,l.Ib,o.Id,o.rW,o.Ym,o.o8,o.le,o.nr,o.mf,o.Kn,o.J_,o.kJ,o.VZ,o.HD,o.hj,o.jn,o.Y6,o.tO,o.UA,o.Mr,o.Xz,o.nd,d.pu,o.FY,o.l_,l.Ib,o.m6,r.w1,a.GW,a.Jj,u.pZ,u.az,u._l,u.CN,u.F6,a.DO;function R(){return!!(0,r.a8)("chrome")}function A(t,e,n,i,r,o){function s(t,e,n){try{t[e]=n}catch(t){}}void 0===i&&(i=!1),void 0===r&&(r=!1);var a=new XMLHttpRequest;return i&&s(a,g,i),n&&s(a,v,n),a.open(t,e,!r),n&&s(a,v,n),!r&&o&&s(a,m,o),a}},71178:function(t,e,n){"use strict";n.d(e,{By:function(){return r}});var i=n(74539);function r(t){var e={};return(0,i.rW)(t,(function(t,n){e[t]=n,e[n]=t})),(0,i._A)(e)}},27981:function(t,e,n){"use strict";n.d(e,{i:function(){return u}});var i=n(23806),r=n(254),o=n(74539),s=n(98500),a=n(69509),c=n(9894),l="getPlugin",u=function(){function t(){var e,n,u,d,h,f=this;function p(t){void 0===t&&(t=null);var e=t;if(!e){var i=n||(0,a.CD)(null,{},f[s.oV]);e=u&&u[l]?i[r.zV](null,u[l]):i[r.zV](null,u)}return e}function g(t,e,i){t&&(0,o.sO)(t,s.Zh,[],null,o.le),!i&&e&&(i=e[r.SD]()[r.W2]());var c=u;u&&u[l]&&(c=u[l]()),f[s.oV]=e,n=(0,a.CD)(i,t,e,c)}function v(){e=!1,f[s.oV]=null,n=null,u=null,h=[],d=(0,c.Y)()}v(),(0,i.Z)(t,f,(function(t){t[r.VL]=function(t,n,i,r){g(t,n,r),e=!0},t[r.fi]=function(e,n){var i,c=t[s.oV];if(c&&(!e||c===e[s.oV]())){var f,p=!1,g=e||(0,a.Bt)(null,c,u&&u[l]?u[l]():u),m=n||((i={reason:0})[r.d]=!1,i);return t[r.F3]&&!0===t[r.F3](g,m,b)?f=!0:b(),f}function b(){if(!p){p=!0,d.run(g,n);var t=h;h=[],(0,o.tO)(t,(function(t){t.rm()})),!0===f&&g[r.uL](m),v()}}},t[r.Tu]=function(e,n){var i=t[s.oV];if(i&&(!e||i===e[s.oV]())){var o,c=!1,d=e||(0,a.xy)(null,i,u&&u[l]?u[l]():u),h=n||{reason:0};return t._doUpdate&&!0===t._doUpdate(d,h,f)?o=!0:f(),o}function f(){c||(c=!0,g(d.getCfg(),d.core(),d[r.W2]()))}},t._addHook=function(t){t&&((0,o.kJ)(t)?h=h.concat(t):h[r.MW](t))},(0,o.Oi)(t,"_addUnloadCb",(function(){return d}),"add")})),f[r.mc]=function(t){return p(t)[r.mc]()},f[r.yl]=function(){return e},f.setInitialized=function(t){e=t},f[r.Jd]=function(t){u=t},f[r.uL]=function(t,e){e?e[r.uL](t):u&&(0,o.mf)(u[s.hL])&&u[s.hL](t,null)},f._getTelCtx=p}return t.__ieDyn=1,t}()},85282:function(t,e,n){"use strict";n.d(e,{JP:function(){return $},Nz:function(){return T},UY:function(){return A},kj:function(){return C},p7:function(){return L}});var i=n(254),r=n(80221),o=n(60851),s=n(74539),a=n(98500),c="toGMTString",l="toUTCString",u="cookie",d="expires",h="enabled",f="isCookieUseDisabled",p="disableCookiesUsage",g="_ckMgr",v=null,m=null,b=null,y=(0,o.Me)(),w={},x={};function C(t,e){var n=T[g]||x[g];return n||(n=T[g]=T(t,e),x[g]=n),n}function k(t){return!t||t.isEnabled()}function S(t,e){return!!(e&&t&&(0,s.kJ)(t.ignoreCookies))&&-1!==t.ignoreCookies[i.ou](e)}function $(t,e){var n;if(t)n=t.getCookieMgr();else if(e){var r=e[i.Lk];n=r[g]?r[g]:T(e)}return n||(n=C(e,(t||{})[i.eZ])),n}function T(t,e){var n,r=function(t){var e=t[i.Lk]=t[i.Lk]||{};if((0,s.sO)(e,"domain",t.cookieDomain,s.BX,s.le),(0,s.sO)(e,"path",t.cookiePath||"/",null,s.le),(0,s.le)(e[h])){var n=void 0;(0,s.o8)(t[f])||(n=!t[f]),(0,s.o8)(t[p])||(n=!t[p]),e[h]=n}return e}(t||x),u=r.path||"/",v=r.domain,b=!1!==r[h],y=((n={isEnabled:function(){var t=b&&L(e),n=x[g];return t&&n&&y!==n&&(t=k(n)),t}})[i.$h]=function(t){b=!1!==t},n.set=function(t,e,n,h,f){var p=!1;if(k(y)&&!function(t,e){return!!(e&&t&&(0,s.kJ)(t.blockedCookies)&&-1!==t.blockedCookies[i.ou](e))||S(t,e)}(r,t)){var g={},b=(0,s.nd)(e||a.qS),w=b[i.ou](";");if(-1!==w&&(b=(0,s.nd)(e[i.zc](0,w)),g=O(e[i.zc](w+1))),(0,s.sO)(g,"domain",h||v,s.fQ,s.o8),!(0,s.le)(n)){var x=(0,o.w1)();if((0,s.o8)(g[d])){var C=(0,s.m6)()+1e3*n;if(C>0){var $=new Date;$.setTime(C),(0,s.sO)(g,d,I($,x?c:l)||I($,x?c:l)||a.qS,s.fQ)}}x||(0,s.sO)(g,"max-age",a.qS+n,null,s.o8)}var T=(0,o.k$)();T&&"https:"===T.protocol&&((0,s.sO)(g,"secure",null,null,s.o8),null===m&&(m=!A(((0,o.jW)()||{})[i.qV])),m&&(0,s.sO)(g,"SameSite","None",null,s.o8)),(0,s.sO)(g,"path",f||u,null,s.o8),(r.setCookie||R)(t,E(b,g)),p=!0}return p},n.get=function(t){var e=a.qS;return k(y)&&!S(r,t)&&(e=(r.getCookie||D)(t)),e},n.del=function(t,e){var n=!1;return k(y)&&(n=y.purge(t,e)),n},n.purge=function(t,n){var i,s=!1;if(L(e)){var c=((i={}).path=n||"/",i[d]="Thu, 01 Jan 1970 00:00:01 GMT",i);(0,o.w1)()||(c["max-age"]="0"),(r.delCookie||R)(t,E(a.qS,c)),s=!0}return s},n);return y[g]=y,y}function L(t){if(null===v){v=!1;try{v=void 0!==(y||{})[u]}catch(e){(0,r.kP)(t,2,68,"Cannot access document.cookie - "+(0,s.jj)(e),{exception:(0,o.eU)(e)})}}return v}function O(t){var e={};if(t&&t[i.R5]){var n=(0,s.nd)(t)[i.w6](";");(0,s.tO)(n,(function(t){if(t=(0,s.nd)(t||a.qS)){var n=t[i.ou]("=");-1===n?e[t]=null:e[(0,s.nd)(t[i.zc](0,n))]=(0,s.nd)(t[i.zc](n+1))}}))}return e}function I(t,e){return(0,s.mf)(t[e])?t[e]():null}function E(t,e){var n=t||a.qS;return(0,s.rW)(e,(function(t,e){n+="; "+t+((0,s.le)(e)?a.qS:"="+e)})),n}function D(t){var e=a.qS;if(y){var n=y[u]||a.qS;b!==n&&(w=O(n),b=n),e=(0,s.nd)(w[t]||a.qS)}return e}function R(t,e){y&&(y[u]=t+"="+e)}function A(t){return!!(0,s.HD)(t)&&(!(!(0,s._Q)(t,"CPU iPhone OS 12")&&!(0,s._Q)(t,"iPad; CPU OS 12"))||(!!((0,s._Q)(t,"Macintosh; Intel Mac OS X 10_14")&&(0,s._Q)(t,"Version/")&&(0,s._Q)(t,"Safari"))||(!(!(0,s._Q)(t,"Macintosh; Intel Mac OS X 10_14")||!(0,s.Id)(t,"AppleWebKit/605.1.15 (KHTML, like Gecko)"))||(!(!(0,s._Q)(t,"Chrome/5")&&!(0,s._Q)(t,"Chrome/6"))||(!(!(0,s._Q)(t,"UnrealEngine")||(0,s._Q)(t,"Chrome"))||!(!(0,s._Q)(t,"UCBrowser/12")&&!(0,s._Q)(t,"UCBrowser/11")))))))}},39860:function(t,e,n){"use strict";n.d(e,{DO:function(){return g},GW:function(){return f},Jj:function(){return p}});var i,r=n(26454),o=n(254),s=n(85282),a=n(60851),c=n(10607),l=n(74539),u=n(98500),d=n(42774),h=null;function f(){var t=g();return t[o.zc](0,8)+"-"+t[o.zc](8,12)+"-"+t[o.zc](12,16)+"-"+t[o.zc](16,20)+"-"+t[o.zc](20)}function p(){var t=(0,a.r)();return t&&t.now?t.now():(0,l.m6)()}function g(){for(var t,e=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],n=u.qS,i=0;i<4;i++)n+=e[15&(t=(0,d._l)())]+e[t>>4&15]+e[t>>8&15]+e[t>>12&15]+e[t>>16&15]+e[t>>20&15]+e[t>>24&15]+e[t>>28&15];var r=e[8+(3&(0,d._l)())|0];return n[o.Jn](0,8)+n[o.Jn](9,4)+"4"+n[o.Jn](13,3)+r+n[o.Jn](16,3)+n[o.Jn](19,12)}var v={_canUseCookies:void 0,isTypeof:l.Ym,isUndefined:l.o8,isNullOrUndefined:l.le,hasOwnProperty:l.nr,isFunction:l.mf,isObject:l.Kn,isDate:l.J_,isArray:l.kJ,isError:l.VZ,isString:l.HD,isNumber:l.hj,isBoolean:l.jn,toISOString:l.Y6,arrForEach:l.tO,arrIndexOf:l.UA,arrMap:l.Mr,arrReduce:l.Xz,strTrim:l.nd,objCreate:r.pu,objKeys:l.FY,objDefineAccessors:l.l_,addEventHandler:c.Ib,dateNow:l.m6,isIE:a.w1,disableCookies:function(){m()[o.$h](!1)},newGuid:f,perfNow:p,newId:d.pZ,randomValue:d.az,random32:d._l,mwcRandomSeed:d.CN,mwcRandom32:d.F6,generateW3CId:g};c.pZ,c.pZ,c.pD,c.pD;function m(t,e){var n=(0,s.kj)(t,e),r=v._canUseCookies;return null===h&&(h=[],i=r,(0,l.l_)(v,"_canUseCookies",(function(){return i}),(function(t){i=t,(0,l.tO)(h,(function(e){e[o.$h](t)}))}))),-1===(0,l.UA)(h,n)&&h[o.MW](n),(0,l.jn)(r)&&n[o.$h](r),(0,l.jn)(i)&&n[o.$h](i),n}},5482:function(t,e,n){"use strict";n.d(e,{J:function(){return p},K:function(){return g}});var i=n(21908),r=n(254),o=n(74539),s=n(98500),a=n(42774),c=i.RJ,l="2.8.9",u="."+(0,a.pZ)(6),d=0;function h(t){return 1===t[r.xv]||9===t[r.xv]||!+t[r.xv]}function f(t,e){var n=e[t.id];if(!n){n={};try{h(e)&&(function(t,e,n){if(c)try{return c(t,e,{value:n,enumerable:!1,configurable:!0}),!0}catch(t){}return!1}(e,t.id,n)||(e[t.id]=n))}catch(t){}}return n}function p(t,e){return void 0===e&&(e=!1),(0,o.Gf)(t+d+++(e?"."+l:s.qS)+u)}function g(t){var e={id:p("_aiData-"+(t||s.qS)+"."+l),accept:function(t){return h(t)},get:function(t,n,i,r){var s=t[e.id];return s?s[(0,o.Gf)(n)]:(r&&((s=f(e,t))[(0,o.Gf)(n)]=i),i)},kill:function(t,e){if(t&&t[e])try{delete t[e]}catch(t){}}};return e}},33220:function(t,e,n){"use strict";n.d(e,{j:function(){return l},p:function(){return u}});var i,r=n(254),o=n(60851),s=["eventsSent","eventsDiscarded","eventsSendRequest","perfEvent"],a=null;function c(t,e){return function(){var n=arguments,i=l(e);if(i){var o=i.listener;o&&o[t]&&o[t][r.ZV](o,n)}}}function l(t){var e,n=a;return n||!0===t.disableDbgExt||(n=a||((e=(0,o.a8)("Microsoft"))&&(a=e.ApplicationInsights),a)),n?n.ChromeDbgExt:null}function u(t){if(!i){i={};for(var e=0;e<s[r.R5];e++)i[s[e]]=c(s[e],t)}return i}},80221:function(t,e,n){"use strict";n.d(e,{AQ:function(){return f},jV:function(){return v},kP:function(){return g},lQ:function(){return d},vH:function(){return h}});var i=n(23806),r=n(254),o=n(33220),s=n(60851),a=n(74539),c=n(98500);function l(t){return t?'"'+t[r.i7](/\"/g,c.qS)+'"':c.qS}function u(t,e){var n=(0,s.dr)();if(n){var i="log";n[t]&&(i=t),(0,a.mf)(n[i])&&n[i](e)}}var d=function(){function t(t,e,n,i){void 0===n&&(n=!1);var o=this;o[r.Gc]=t,o[r.gU]=(n?"AI: ":"AI (Internal): ")+t;var a=c.qS;(0,s.nS)()&&(a=(0,s.xA)().stringify(i));var u=(e?" message:"+l(e):c.qS)+(i?" props:"+l(a):c.qS);o[r.gU]+=u}return t.dataType="MessageData",t}();function h(t,e){return(t||{})[r.eZ]||new f(e)}var f=function(){function t(e){this.identifier="DiagnosticLogger",this.queue=[];var n,l,h,f,p=0,g={};(0,i.Z)(t,this,(function(t){function i(e,n){if(!(p>=h)){var i=!0,o="AITR_"+n[r.Gc];if(g[o]?i=!1:g[o]=!0,i&&(e<=l&&(t.queue[r.MW](n),p++,v(1===e?"error":"warn",n)),p===h)){var s="Internal events throttle limit per PageView reached for this app.",a=new d(23,s,!1);t.queue[r.MW](a),1===e?t[c.ys](s):t[c.cL](s)}}}function v(t,n){var i=(0,o.j)(e||{});i&&i[r.mc]&&i[r.mc](t,n)}!function(t){n=(0,a.v4)(t.loggingLevelConsole,0),l=(0,a.v4)(t.loggingLevelTelemetry,1),h=(0,a.v4)(t.maxMessageLimit,25),f=(0,a.v4)(t[r.Fr],!1)}(e||{}),t.consoleLoggingLevel=function(){return n},t.telemetryLoggingLevel=function(){return l},t.maxInternalMessageLimit=function(){return h},t[r.Fr]=function(){return f},t.throwInternal=function(e,o,l,u,h){void 0===h&&(h=!1);var p=new d(o,l,h,u);if(f)throw(0,s.eU)(p);var m=1===e?c.ys:c.cL;if((0,a.o8)(p[r.gU]))v("throw"+(1===e?"Critical":"Warning"),p);else{if(h){var b=+p[r.Gc];!g[b]&&n>=e&&(t[m](p[r.gU]),g[b]=!0)}else n>=e&&t[m](p[r.gU]);i(e,p)}},t[c.cL]=function(t){u("warn",t),v("warning",t)},t[c.ys]=function(t){u("error",t),v("error",t)},t.resetInternalMessageCount=function(){p=0,g={}},t[r.jk]=i}))}return t.__ieDyn=1,t}();function p(t){return t||new f}function g(t,e,n,i,r,o){void 0===o&&(o=!1),p(t).throwInternal(e,n,i,r,o)}function v(t,e){p(t)[c.cL](e)}},60851:function(t,e,n){"use strict";n.d(e,{JO:function(){return q},Jj:function(){return I},MF:function(){return U},MX:function(){return P},Me:function(){return D},Nv:function(){return E},Z3:function(){return K},a8:function(){return L},b$:function(){return _},cp:function(){return W},dr:function(){return F},eU:function(){return V},gz:function(){return j},jW:function(){return A},k$:function(){return B},nS:function(){return M},r:function(){return H},w1:function(){return z},xA:function(){return N}});var i=n(21908),r=n(26454),o=n(254),s=n(74539),a=n(98500),c="window",l="document",u="navigator",d="location",h="console",f="performance",p="JSON",g="crypto",v="msCrypto",m="ReactNative",b="msie",y="trident/",w="XMLHttpRequest",x=null,C=null,k=!1,S=null,$=null;function T(t,e){var n=!1;if(t){try{if(!(n=e in t)){var r=t[i.hB];r&&(n=e in r)}}catch(t){}if(!n)try{var o=new t;n=!(0,s.o8)(o[e])}catch(t){}}return n}function L(t){var e=(0,r.Rd)();return e&&e[t]?e[t]:t===c&&O()?window:null}function O(){return Boolean(typeof window===i.fK&&window)}function I(){return O()?window:L(c)}function E(){return Boolean(typeof document===i.fK&&document)}function D(){return E()?document:L(l)}function R(){return Boolean(typeof navigator===i.fK&&navigator)}function A(){return R()?navigator:L(u)}function B(t){if(t&&k){var e=L("__mockLocation");if(e)return e}return typeof location===i.fK&&location?location:L(d)}function F(){return typeof console!==i.jA?console:L(h)}function H(){return L(f)}function M(){return Boolean(typeof JSON===i.fK&&JSON||null!==L(p))}function N(){return M()?JSON||L(p):null}function P(){return L(g)}function j(){return L(v)}function _(){var t=A();return!(!t||!t.product)&&t.product===m}function z(){var t=A();if(t&&(t[o.qV]!==C||null===x)){var e=((C=t[o.qV])||a.qS)[o.T1]();x=(0,s._Q)(e,b)||(0,s._Q)(e,y)}return x}function V(t){var e=Object[i.hB].toString[o._S](t),n=a.qS;return"[object Error]"===e?n="{ stack: '"+t.stack+"', message: '"+t.message+"', name: '"+t[o.I]+"'":M()&&(n=N().stringify(t)),e+n}function U(){return null===$&&($=R()&&Boolean(A().sendBeacon)),$}function q(t){var e=!1;try{e=!!L("fetch");var n=L("Request");e&&t&&n&&(e=T(n,"keepalive"))}catch(t){}return e}function W(){return null===S&&(S=typeof XDomainRequest!==i.jA)&&K()&&(S=S&&!T(L(w),"withCredentials")),S}function K(){var t=!1;try{t=!!L(w)}catch(t){}return t}},10607:function(t,e,n){"use strict";n.d(e,{C9:function(){return z},Ib:function(){return H},JA:function(){return j},TJ:function(){return _},Yl:function(){return U},c9:function(){return P},jU:function(){return D},nD:function(){return V},pD:function(){return F},pZ:function(){return B}});var i=n(254),r=n(5482),o=n(60851),s=n(74539),a=n(98500),c="on",l="attachEvent",u="addEventListener",d="detachEvent",h="removeEventListener",f="events",p="visibilitychange",g="pagehide",v="pageshow",m="unload",b="beforeunload",y=(0,r.J)("aiEvtPageHide"),w=(0,r.J)("aiEvtPageShow"),x=/\.[\.]+/g,C=/[\.]+$/,k=1,S=(0,r.K)("events"),$=/^([^.]*)(?:\.(.+)|)/;function T(t){return t&&t[i.i7]?t[i.i7](/^[\s\.]+|(?=[\s\.])[\.\s]+$/g,a.qS):t}function L(t,e){var n;if(e){var r=a.qS;(0,s.kJ)(e)?(r=a.qS,(0,s.tO)(e,(function(t){(t=T(t))&&("."!==t[0]&&(t="."+t),r+=t)}))):r=T(e),r&&("."!==r[0]&&(r="."+r),t=(t||a.qS)+r)}var o=$.exec(t||a.qS)||[];return(n={})[i.P6]=o[1],n.ns=(o[2]||a.qS).replace(x,".").replace(C,a.qS)[i.w6](".").sort().join("."),n}function O(t,e,n){void 0===n&&(n=!0);var i=S.get(t,f,{},n),r=i[e];return r||(r=i[e]=[]),r}function I(t,e,n,r){t&&e&&e[i.P6]&&(t[h]?t[h](e[i.P6],n,r):t[d]&&t[d](c+e[i.P6],n))}function E(t,e,n,r){for(var o=e[i.R5];o--;){var s=e[o];s&&(n.ns&&n.ns!==s.evtName.ns||r&&!r(s)||(I(t,s.evtName,s[i.Kn],s.capture),e[i.cb](o,1)))}}function D(t,e){return e?L("xx",(0,s.kJ)(e)?[t].concat(e):[t,e]).ns[i.w6]("."):t}function R(t,e,n,r,o){var s;void 0===o&&(o=!1);var a=!1;if(t)try{var d=L(e,r);if(a=function(t,e,n,r){var o=!1;return t&&e&&e[i.P6]&&n&&(t[u]?(t[u](e[i.P6],n,r),o=!0):t[l]&&(t[l](c+e[i.P6],n),o=!0)),o}(t,d,n,o),a&&S.accept(t)){var h=((s={guid:k++,evtName:d})[i.Kn]=n,s.capture=o,s);O(t,d.type)[i.MW](h)}}catch(t){}return a}function A(t,e,n,r,o){if(void 0===o&&(o=!1),t)try{var a=L(e,r),c=!1;!function(t,e,n){if(e[i.P6])E(t,O(t,e[i.P6]),e,n);else{var r=S.get(t,f,{});(0,s.rW)(r,(function(i,r){E(t,r,e,n)})),0===(0,s.FY)(r)[i.R5]&&S.kill(t,f)}}(t,a,(function(t){return!((!a.ns||n)&&t[i.Kn]!==n)&&(c=!0,!0)})),c||I(t,a,n,o)}catch(t){}}function B(t,e,n,i){return void 0===i&&(i=!1),R(t,e,n,null,i)}function F(t,e,n,i){void 0===i&&(i=!1),A(t,e,n,null,i)}function H(t,e,n){var i=!1,r=(0,o.Jj)();r&&(i=R(r,t,e,n),i=R(r.body,t,e,n)||i);var s=(0,o.Me)();return s&&(i=R(s,t,e,n)||i),i}function M(t,e,n,r){var o=!1;return e&&t&&t[i.R5]>0&&(0,s.tO)(t,(function(t){t&&(n&&-1!==(0,s.UA)(n,t)||(o=H(t,e,r)||o))})),o}function N(t,e,n){t&&(0,s.kJ)(t)&&(0,s.tO)(t,(function(t){t&&function(t,e,n){var i=(0,o.Jj)();i&&(A(i,t,e,n),A(i.body,t,e,n));var r=(0,o.Me)();r&&A(r,t,e,n)}(t,e,n)}))}function P(t,e,n){return function(t,e,n,r){var o=!1;return e&&t&&(0,s.kJ)(t)&&!(o=M(t,e,n,r))&&n&&n[i.R5]>0&&(o=M(t,e,null,r)),o}([b,m,g],t,e,n)}function j(t,e){N([b,m,g],t,e)}function _(t,e,n){var i=D(y,n),r=M([g],t,e,i);return e&&-1!==(0,s.UA)(e,p)||(r=M([p],(function(e){var n=(0,o.Me)();t&&n&&"hidden"===n.visibilityState&&t(e)}),e,i)||r),!r&&e&&(r=_(t,null,n)),r}function z(t,e){var n=D(y,e);N([g],t,n),N([p],null,n)}function V(t,e,n){var i=D(w,n),r=M([v],t,e,i);return!(r=M([p],(function(e){var n=(0,o.Me)();t&&n&&"visible"===n.visibilityState&&t(e)}),e,i)||r)&&e&&(r=V(t,null,n)),r}function U(t,e){var n=D(w,e);N([v],t,n),N([p],null,n)}},74539:function(t,e,n){"use strict";n.d(e,{Ax:function(){return xt},BX:function(){return H},FL:function(){return ut},FY:function(){return st},Gf:function(){return j},HD:function(){return Z},Id:function(){return z},J_:function(){return q},Kn:function(){return N},Mr:function(){return et},Oi:function(){return yt},UA:function(){return tt},VZ:function(){return K},Vb:function(){return wt},Xz:function(){return nt},Y6:function(){return Y},Ym:function(){return A},_A:function(){return lt},_Q:function(){return U},_y:function(){return mt},fQ:function(){return vt},hj:function(){return G},jj:function(){return ht},jn:function(){return X},kJ:function(){return W},l_:function(){return at},le:function(){return F},m6:function(){return dt},mf:function(){return P},mm:function(){return Ct},nd:function(){return it},nr:function(){return M},o8:function(){return B},qK:function(){return pt},rW:function(){return _},sO:function(){return ft},tO:function(){return Q},v4:function(){return gt},xe:function(){return V}});var i=n(21908),r=n(26454),o=n(254),s=n(98500),a="toISOString",c="endsWith",l="startsWith",u="indexOf",d="map",h="reduce",f="trim",p="toString",g="__proto__",v="constructor",m=i.RJ,b=i.Pw.freeze,y=(i.Pw.seal,i.Pw.keys),w=String[i.hB],x=w[f],C=w[c],k=w[l],S=Date[i.hB][a],$=Array.isArray,T=i.V4[p],L=i.CY[p],O=L[o._S](i.Pw),I=/-([a-z])/g,E=/([^\w\d_$])/g,D=/^(\d+[\w\d_$])/,R=Object.getPrototypeOf;function A(t,e){return typeof t===e}function B(t){return void 0===t||typeof t===i.jA}function F(t){return null===t||B(t)}function H(t){return!F(t)}function M(t,e){return!(!t||!i.CY[o._S](t,e))}function N(t){return!(!t||typeof t!==i.fK)}function P(t){return!(!t||typeof t!==i.cb)}function j(t){var e=t;return e&&Z(e)&&(e=(e=(e=e[o.i7](I,(function(t,e){return e.toUpperCase()})))[o.i7](E,"_"))[o.i7](D,(function(t,e){return"_"+e}))),e}function _(t,e){if(t)for(var n in t)i.CY[o._S](t,n)&&e[o._S](t,n,t[n])}function z(t,e){var n=!1;return t&&e&&!(n=t===e)&&(n=C?t[c](e):function(t,e){var n=!1,i=e?e[o.R5]:0,r=t?t[o.R5]:0;if(i&&r&&r>=i&&!(n=t===e)){for(var s=r-1,a=i-1;a>=0;a--){if(t[s]!=e[a])return!1;s--}n=!0}return n}(t,e)),n}function V(t,e){var n=!1;return t&&e&&!(n=t===e)&&(n=k?t[l](e):function(t,e){var n=!1,i=e?e[o.R5]:0;if(t&&i&&t[o.R5]>=i&&!(n=t===e)){for(var r=0;r<i;r++)if(t[r]!==e[r])return!1;n=!0}return n}(t,e)),n}function U(t,e){return!(!t||!e)&&-1!==t[o.ou](e)}function q(t){return!(!t||"[object Date]"!==T[o._S](t))}var W=$||function(t){return!(!t||"[object Array]"!==T[o._S](t))};function K(t){return!(!t||"[object Error]"!==T[o._S](t))}function Z(t){return"string"==typeof t}function G(t){return"number"==typeof t}function X(t){return"boolean"==typeof t}function J(t){var e=!1;if(t&&"object"==typeof t){var n=R?R(t):function(t){if(t){if(R)return R(t);var e=t[g]||t[i.hB]||t[v];if(e)return e}return null}(t);n?(n[v]&&i.CY[o._S](n,v)&&(n=n[v]),e=typeof n===i.cb&&L[o._S](n)===O):e=!0}return e}function Y(t){if(t)return S?t[a]():function(t){if(t&&t.getUTCFullYear){var e=function(t){var e=String(t);return 1===e[o.R5]&&(e="0"+e),e};return t.getUTCFullYear()+"-"+e(t.getUTCMonth()+1)+"-"+e(t.getUTCDate())+"T"+e(t.getUTCHours())+":"+e(t.getUTCMinutes())+":"+e(t.getUTCSeconds())+"."+String((t.getUTCMilliseconds()/1e3).toFixed(3)).slice(2,5)+"Z"}}(t)}function Q(t,e,n){var i=t[o.R5];try{for(var r=0;r<i&&(!(r in t)||-1!==e[o._S](n||t,t[r],r,t));r++);}catch(t){}}function tt(t,e,n){if(t){if(t[u])return t[u](e,n);var i=t[o.R5],r=n||0;try{for(var s=Math.max(r>=0?r:i-Math.abs(r),0);s<i;s++)if(s in t&&t[s]===e)return s}catch(t){}}return-1}function et(t,e,n){var i;if(t){if(t[d])return t[d](e,n);var r=t[o.R5],s=n||t;i=new Array(r);try{for(var a=0;a<r;a++)a in t&&(i[a]=e[o._S](s,t[a],t))}catch(t){}}return i}function nt(t,e,n){var i;if(t){if(t[h])return t[h](e,n);var r=t[o.R5],s=0;if(arguments[o.R5]>=3)i=n;else{for(;s<r&&!(s in t);)s++;i=t[s++]}for(;s<r;)s in t&&(i=e(i,t[s],s,t)),s++}return i}function it(t){return t&&(t=x&&t[f]?t[f]():t[o.i7]?t[o.i7](/^\s+|(?=\s)\s+$/g,s.qS):t),t}var rt=!{toString:null}.propertyIsEnumerable("toString"),ot=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"];function st(t){var e=typeof t;if(e===i.cb||e===i.fK&&null!==t||(0,r.ZU)("objKeys called on non-object"),!rt&&y)return y(t);var n=[];for(var s in t)t&&i.CY[o._S](t,s)&&n[o.MW](s);if(rt)for(var a=ot[o.R5],c=0;c<a;c++)t&&i.CY[o._S](t,ot[c])&&n[o.MW](ot[c]);return n}function at(t,e,n,i){if(m)try{var r={enumerable:!0,configurable:!0};return n&&(r.get=n),i&&(r.set=i),m(t,e,r),!0}catch(t){}return!1}function ct(t){return t}function lt(t){return b&&_(t,(function(t,e){(W(e)||N(e))&&b(e)})),ut(t)}var ut=b||ct;function dt(){var t=Date;return t.now?t.now():(new t).getTime()}function ht(t){return K(t)?t[o.I]:s.qS}function ft(t,e,n,i,r){var o=n;return t&&((o=t[e])===n||r&&!r(o)||i&&!i(n)||(o=n,t[e]=o)),o}function pt(t,e,n){var i;return t?!(i=t[e])&&F(i)&&(i=B(n)?{}:n,t[e]=i):i=B(n)?{}:n,i}function gt(t,e){return F(t)?e:t}function vt(t){return!!t}function mt(t){throw new Error(t)}function bt(t,e){var n=null,i=null;return P(t)?n=t:i=t,function(){var t=arguments;if(n&&(i=n()),i)return i[e][o.ZV](i,t)}}function yt(t,e,n,i,r){t&&e&&n&&(!1!==r||B(t[e]))&&(t[e]=bt(n,i))}function wt(t,e,n,i){return t&&e&&N(t)&&W(n)&&Q(n,(function(n){Z(n)&&yt(t,n,e,n,i)})),t}function xt(t){return t&&i.rl&&(t=(0,i.Pw)((0,i.rl)({},t))),t}function Ct(t,e,n,r,s,a){var c=arguments,l=c[0]||{},u=c[o.R5],d=!1,h=1;for(u>0&&X(l)&&(d=l,l=c[h]||{},h++),N(l)||(l={});h<u;h++){var f=c[h],p=W(f),g=N(f);for(var v in f){if(p&&v in f||g&&i.CY[o._S](f,v)){var m=f[v],b=void 0;if(d&&m&&((b=W(m))||J(m))){var y=l[v];b?W(y)||(y=[]):J(y)||(y={}),m=Ct(d,y,m)}void 0!==m&&(l[v]=m)}}}return l}},98500:function(t,e,n){"use strict";n.d(e,{$F:function(){return f},C$:function(){return g},DN:function(){return s},Z:function(){return r},Zh:function(){return c},cL:function(){return m},f_:function(){return p},hL:function(){return u},js:function(){return l},kl:function(){return b},mE:function(){return a},oV:function(){return o},qS:function(){return i},uC:function(){return h},yi:function(){return d},ys:function(){return v}});var i="",r="channels",o="core",s="createPerfMgr",a="disabled",c="extensionConfig",l="extensions",u="processTelemetry",d="priority",h="eventsSent",f="eventsDiscarded",p="eventsSendRequest",g="perfEvent",v="errorToConsole",m="warnToConsole",b="getPerfMgr"},26932:function(t,e,n){"use strict";n.d(e,{Jk:function(){return h},Lm:function(){return p},j5:function(){return g}});var i=n(23806),r=n(254),o=n(74539),s=n(98500),a="ctx",c="ParentContextKey",l="ChildrenContextKey",u=null,d=function(){function t(e,n,i){var s,u=this,d=!1;(u.start=(0,o.m6)(),u[r.I]=e,u[r.d]=i,u[r.PL]=function(){return!1},(0,o.mf)(n))&&(d=(0,o.l_)(u,"payload",(function(){return!s&&(0,o.mf)(n)&&(s=n(),n=null),s})));u[r.T]=function(e){return e?e===t[c]||e===t[l]?u[e]:(u[a]||{})[e]:null},u[r.wu]=function(e,n){if(e)if(e===t[c])u[e]||(u[r.PL]=function(){return!0}),u[e]=n;else if(e===t[l])u[e]=n;else{(u[a]=u[a]||{})[e]=n}},u[r.Km]=function(){var e=0,i=u[r.T](t[l]);if((0,o.kJ)(i))for(var s=0;s<i[r.R5];s++){var a=i[s];a&&(e+=a[r.Z_])}u[r.Z_]=(0,o.m6)()-u.start,u.exTime=u[r.Z_]-e,u[r.Km]=function(){},!d&&(0,o.mf)(n)&&(u.payload=n())}}return t.ParentContextKey="parent",t.ChildrenContextKey="childEvts",t}(),h=function(){function t(e){this.ctx={},(0,i.Z)(t,this,(function(t){t.create=function(t,e,n){return new d(t,e,n)},t.fire=function(t){t&&(t[r.Km](),e&&(0,o.mf)(e[s.C$])&&e[s.C$](t))},t[r.wu]=function(e,n){e&&((t[a]=t[a]||{})[e]=n)},t[r.T]=function(e){return(t[a]||{})[e]}}))}return t.__ieDyn=1,t}(),f="CoreUtils.doPerf";function p(t,e,n,i,o){if(t){var a=t;if(a[s.kl]&&(a=a[s.kl]()),a){var u=void 0,h=a[r.T](f);try{if(u=a.create(e(),i,o)){if(h&&u[r.wu]&&(u[r.wu](d[c],h),h[r.T]&&h[r.wu])){var p=h[r.T](d[l]);p||(p=[],h[r.wu](d[l],p)),p[r.MW](u)}return a[r.wu](f,u),n(u)}}catch(t){u&&u[r.wu]&&u[r.wu]("exception",t)}finally{u&&a.fire(u),a[r.wu](f,h)}}}return n()}function g(){return u}},69509:function(t,e,n){"use strict";n.d(e,{Bt:function(){return v},CD:function(){return g},jV:function(){return b},xy:function(){return m}});var i=n(254),r=n(80221),o=n(60851),s=n(74539),a=n(98500),c=n(26932),l=n(39289),u="TelemetryPluginChain",d="_hasRun",h="_getTelCtx",f=0;function p(t,e,n,c){var l=null,u=[];null!==c&&(l=c?function(t,e,n){for(;t;){if(t[i.TO]()===n)return t;t=t[i.W2]()}return b([n],e[i.TC]||{},e)}(t,n,c):t);var d={_next:function(){var t=l;if(l=t?t[i.W2]():null,!t){var e=u;e&&e[i.R5]>0&&((0,s.tO)(e,(function(t){try{t.func[i._S](t.self,t.args)}catch(t){(0,r.kP)(n[i.eZ],2,73,"Unexpected Exception during onComplete - "+(0,o.eU)(t))}})),u=[])}return t},ctx:{core:function(){return n},diagLog:function(){return(0,r.vH)(n,e)},getCfg:function(){return e},getExtCfg:h,getConfig:function(t,n,i){void 0===i&&(i=!1);var r,o=h(t,null);o&&!(0,s.le)(o[n])?r=o[n]:e&&!(0,s.le)(e[n])&&(r=e[n]);return(0,s.le)(r)?i:r},hasNext:function(){return!!l},getNext:function(){return l},setNext:function(t){l=t},iterate:function(t){var e;for(;e=d._next();){var n=e[i.TO]();n&&t(n)}},onComplete:function(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];t&&u[i.MW]({func:t,self:(0,s.o8)(e)?d.ctx:e,args:n})}}};function h(t,n,i){var r;if(void 0===n&&(n={}),void 0===i&&(i=0),e){var o=e[a.Zh];o&&t&&(r=o[t])}if(r){if((0,s.Kn)(n)&&0!==i){var c=(0,s.mm)(!0,n,r);e&&2===i&&(0,s.rW)(n,(function(t){if((0,s.le)(c[t])){var n=e[t];(0,s.le)(n)||(c[t]=n)}})),r=c}}else r=n;return r}return d}function g(t,e,n,r){var o=p(t,e,n,r),c=o.ctx;return c[i.uL]=function(t){var e=o._next();return e&&e[a.hL](t,c),!e},c[i.zV]=function(t,r){return void 0===t&&(t=null),(0,s.kJ)(t)&&(t=b(t,e,n,r)),g(t||c[i.W2](),e,n,r)},c}function v(t,e,n){var r=e[i.TC]||{},o=p(t,r,e,n),a=o.ctx;return a[i.uL]=function(t){var e=o._next();return e&&e.unload(a,t),!e},a[i.zV]=function(t,n){return void 0===t&&(t=null),(0,s.kJ)(t)&&(t=b(t,r,e,n)),v(t||a[i.W2](),e,n)},a}function m(t,e,n){var r=e[i.TC]||{},o=p(t,r,e,n).ctx;return o[i.uL]=function(t){return o.iterate((function(e){(0,s.mf)(e[i.Tu])&&e[i.Tu](o,t)}))},o[i.zV]=function(t,n){return void 0===t&&(t=null),(0,s.kJ)(t)&&(t=b(t,r,e,n)),m(t||o[i.W2](),e,n)},o}function b(t,e,n,p){var v=null,m=!p;if((0,s.kJ)(t)&&t[i.R5]>0){var y=null;(0,s.tO)(t,(function(t){if(m||p!==t||(m=!0),m&&t&&(0,s.mf)(t[a.hL])){var b=function(t,e,n){var p,v=null,m=(0,s.mf)(t[a.hL]),b=(0,s.mf)(t[i.Jd]);p=t?t[i.pZ]+"-"+t[a.yi]+"-"+f++:"Unknown-0-"+f++;var y={getPlugin:function(){return t},getNext:function(){return v},processTelemetry:C,unload:k,update:S,_id:p,_setNext:function(t){v=t}};function w(){var i;return t&&(0,s.mf)(t[h])&&(i=t[h]()),i||(i=g(y,e,n)),i}function x(e,n,s,l,h){var f=!1,g=t?t[i.pZ]:u,m=e[d];return m||(m=e[d]={}),e.setNext(v),t&&(0,c.Lm)(e[a.oV](),(function(){return g+":"+s}),(function(){m[p]=!0;try{var t=v?v._id:a.qS;t&&(m[t]=!1),f=n(e)}catch(t){var c=!v||m[v._id];c&&(f=!0),v&&c||(0,r.kP)(e[i.mc](),1,73,"Plugin ["+g+"] failed during "+s+" - "+(0,o.eU)(t)+", run flags: "+(0,o.eU)(m))}}),l,h),f}function C(e,n){function r(n){if(!t||!m)return!1;var r=(0,l.OY)(t);return!r[i.fi]&&!r[a.mE]&&(b&&t[i.Jd](v),t[a.hL](e,n),!0)}x(n=n||w(),r,"processTelemetry",(function(){return{item:e}}),!e.sync)||n[i.uL](e)}function k(e,n){function r(){var r=!1;if(t){var o=(0,l.OY)(t),s=t[a.oV]||o[a.oV];!t||s&&s!==e.core()||o[i.fi]||(o[a.oV]=null,o[i.fi]=!0,o[i.yl]=!1,t[i.fi]&&!0===t[i.fi](e,n)&&(r=!0))}return r}x(e,r,"unload",(function(){}),n[i.d])||e[i.uL](n)}function S(e,n){function r(){var r=!1;if(t){var o=(0,l.OY)(t),s=t[a.oV]||o[a.oV];!t||s&&s!==e.core()||o[i.fi]||t[i.Tu]&&!0===t[i.Tu](e,n)&&(r=!0)}return r}x(e,r,"update",(function(){}),!1)||e[i.uL](n)}return(0,s.FL)(y)}(t,e,n);v||(v=b),y&&y._setNext(b),y=b}}))}return p&&!v?b([p],e,n):v}},42774:function(t,e,n){"use strict";n.d(e,{CN:function(){return v},F6:function(){return m},_l:function(){return g},az:function(){return p},pZ:function(){return b}});var i=n(254),r=n(60851),o=n(74539),s=n(98500),a=4294967296,c=4294967295,l=!1,u=123456789,d=987654321;function h(t){t<0&&(t>>>=0),u=123456789+t&c,d=987654321-t&c,l=!0}function f(){try{var t=2147483647&(0,o.m6)();h((Math.random()*a^t)+t)}catch(t){}}function p(t){return t>0?Math.floor(g()/c*(t+1))>>>0:0}function g(t){var e=0,n=(0,r.MX)()||(0,r.gz)();return n&&n.getRandomValues&&(e=n.getRandomValues(new Uint32Array(1))[0]&c),0===e&&(0,r.w1)()&&(l||f(),e=m()&c),0===e&&(e=Math.floor(a*Math.random()|0)),t||(e>>>=0),e}function v(t){t?h(t):f()}function m(t){var e=((d=36969*(65535&d)+(d>>16)&c)<<16)+(65535&(u=18e3*(65535&u)+(u>>16)&c))>>>0&c|0;return t||(e>>>=0),e}function b(t){void 0===t&&(t=22);for(var e=g()>>>0,n=0,r=s.qS;r[i.R5]<t;)n++,r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(63&e),e>>>=6,5===n&&(e=(g()<<2&4294967295|3&e)>>>0,n=0);return r}},39289:function(t,e,n){"use strict";n.d(e,{AA:function(){return d},OY:function(){return l},Yn:function(){return h},bP:function(){return u}});var i=n(254),r=n(5482),o=n(74539),s=n(98500),a=n(54480),c=(0,r.K)("plugin");function l(t){return c.get(t,"state",{},!0)}function u(t,e){for(var n,r=[],a=null,c=t[i.W2]();c;){var u=c[i.TO]();if(u){a&&(0,o.mf)(a[i.Jd])&&(0,o.mf)(u[s.hL])&&a[i.Jd](u);((0,o.mf)(u[i.yl])?u[i.yl]():(n=l(u))[i.yl])||r[i.MW](u),a=u,c=c[i.W2]()}}(0,o.tO)(r,(function(r){var o=t[s.oV]();r[i.VL](t.getCfg(),o,e,t[i.W2]()),n=l(r),r[s.oV]||n[s.oV]||(n[s.oV]=o),n[i.yl]=!0,delete n[i.fi]}))}function d(t){return t.sort((function(t,e){var n=0;if(e){var i=(0,o.mf)(e[s.hL]);(0,o.mf)(t[s.hL])?n=i?t[s.yi]-e[s.yi]:1:i&&(n=-1)}else n=t?1:-1;return n}))}function h(t){var e={};return{getName:function(){return e[i.I]},setName:function(n){t&&t.setName(n),e[i.I]=n},getTraceId:function(){return e[i.nY]},setTraceId:function(n){t&&t.setTraceId(n),(0,a.jN)(n)&&(e[i.nY]=n)},getSpanId:function(){return e[i._d]},setSpanId:function(n){t&&t.setSpanId(n),(0,a.Lc)(n)&&(e[i._d]=n)},getTraceFlags:function(){return e[i.T0]},setTraceFlags:function(n){t&&t.setTraceFlags(n),e[i.T0]=n}}}},9894:function(t,e,n){"use strict";n.d(e,{Y:function(){return a}});var i=n(254),r=n(80221),o=n(60851),s=n(74539);function a(){var t=[];return{add:function(e){e&&t[i.MW](e)},run:function(e,n){(0,s.tO)(t,(function(t){try{t(e,n)}catch(t){(0,r.kP)(e[i.mc](),2,73,"Unexpected error calling unload handler - "+(0,o.eU)(t))}})),t=[]}}}},54480:function(t,e,n){"use strict";n.d(e,{Lc:function(){return c},jN:function(){return a}});var i=n(254),r="00000000000000000000000000000000",o="0000000000000000";function s(t,e,n){return!(!t||t[i.R5]!==e||t===n)&&!!t.match(/^[\da-f]*$/)}function a(t){return s(t,32,r)}function c(t){return s(t,16,o)}},254:function(t,e,n){"use strict";n.d(e,{$h:function(){return P},F3:function(){return O},Fc:function(){return y},Fr:function(){return U},Gc:function(){return $},I:function(){return r},Jd:function(){return R},Jn:function(){return j},Km:function(){return Q},Kn:function(){return G},LS:function(){return b},Lk:function(){return B},MW:function(){return a},P6:function(){return Z},PL:function(){return X},R5:function(){return h},SD:function(){return g},T:function(){return J},T0:function(){return nt},T1:function(){return W},TC:function(){return l},TO:function(){return w},Tu:function(){return I},VL:function(){return i},W2:function(){return E},ZV:function(){return z},Z_:function(){return f},_S:function(){return K},_d:function(){return et},cb:function(){return k},d:function(){return L},eZ:function(){return d},fi:function(){return S},g:function(){return v},gU:function(){return T},i7:function(){return V},iC:function(){return C},jk:function(){return q},kL:function(){return x},mc:function(){return D},n7:function(){return m},nY:function(){return tt},ou:function(){return F},p$:function(){return u},pZ:function(){return s},qV:function(){return M},tb:function(){return o},uL:function(){return p},w6:function(){return N},wu:function(){return Y},xv:function(){return _},yl:function(){return c},zV:function(){return A},zc:function(){return H}});var i="initialize",r="name",o="getNotifyMgr",s="identifier",a="push",c="isInitialized",l="config",u="instrumentationKey",d="logger",h="length",f="time",p="processNext",g="getProcessTelContext",v="addNotificationListener",m="removeNotificationListener",b="stopPollingInternalLogs",y="onComplete",w="getPlugin",x="flush",C="_extensions",k="splice",S="teardown",$="messageId",T="message",L="isAsync",O="_doTeardown",I="update",E="getNext",D="diagLog",R="setNextPlugin",A="createNew",B="cookieCfg",F="indexOf",H="substring",M="userAgent",N="split",P="setEnabled",j="substr",_="nodeType",z="apply",V="replace",U="enableDebugExceptions",q="logInternalMessage",W="toLowerCase",K="call",Z="type",G="handler",X="isChildEvt",J="getCtx",Y="setCtx",Q="complete",tt="traceId",et="spanId",nt="traceFlags"},3232:function(t,e,n){"use strict";n.d(e,{Z:function(){return mt}});var i=n(45362),r=n(23806),o=n(26932),s=n(10607),a=n(5482),c=n(91254),l=n(74539),u=n(60851),d=(0,n(71178).By)({Unknown:0,NonRetryableStatus:1,InvalidEvent:2,SizeLimitExceeded:3,KillSwitch:4,QueueFull:5}),h=n(80221),f=n(27981),p="REAL_TIME",g="",v="POST",m="drop",b="send",y="requeue",w="rspFail",x="application/x-json-stream",C="cache-control",k="content-type",S="kill-tokens",$="kill-duration",T="time-delta-millis",L="client-version",O="client-id",I="time-delta-to-apply-millis",E="upload-time",D="apikey",R="AuthMsaDeviceTicket",A="AuthXToken",B="msfpc",F="trace",H="user";function M(t){var e=(t.ext||{}).intweb;return e&&(0,c.Sn)(e[B])?e[B]:null}function N(t){for(var e=null,n=0;null===e&&n<t.length;n++)e=M(t[n]);return e}var P=function(){function t(e,n){var i=n?[].concat(n):[],r=this,o=N(i);r.iKey=function(){return e},r.Msfpc=function(){return o||g},r.count=function(){return i.length},r.events=function(){return i},r.addEvent=function(t){return!!t&&(i.push(t),o||(o=M(t)),!0)},r.split=function(n,r){var s;if(n<i.length){var a=i.length-n;(0,l.le)(r)||(a=r<a?r:a),s=i.splice(n,a),o=N(i)}return new t(e,s)}}return t.create=function(e,n){return new t(e,n)},t}(),j=function(){function t(){var e=!0,n=!0,i=!0,o="use-collector-delta",s=!1;(0,r.Z)(t,this,(function(t){t.allowRequestSending=function(){return e},t.firstRequestSent=function(){i&&(i=!1,s||(e=!1))},t.shouldAddClockSkewHeaders=function(){return n},t.getClockSkewHeaderValue=function(){return o},t.setClockSkew=function(t){s||(t?(o=t,n=!0,s=!0):n=!1,e=!0)}}))}return t.__ieDyn=1,t}(),_=function(){function t(){var e={};(0,r.Z)(t,this,(function(t){t.setKillSwitchTenants=function(t,n){if(t&&n)try{var i=(s=t.split(","),a=[],s&&(0,l.tO)(s,(function(t){a.push((0,l.nd)(t))})),a);if("this-request-only"===n)return i;for(var r=1e3*parseInt(n,10),o=0;o<i.length;++o)e[i[o]]=(0,l.m6)()+r}catch(t){return[]}var s,a;return[]},t.isTenantKilled=function(t){var n=e,i=(0,l.nd)(t);return void 0!==n[i]&&n[i]>(0,l.m6)()||(delete n[i],!1)}}))}return t.__ieDyn=1,t}(),z=n(21908);function V(t){var e,n=Math.floor(1200*Math.random())+2400;return e=Math.pow(2,t)*n,Math.min(e,6e5)}var U,q=2e6,W=Math.min(q,65e3),K="metadata",Z="f",G=/\./,X=function(){function t(e,n,i,s){var a="data",u="baseData",d=!!s,h=n,f={};(0,r.Z)(t,this,(function(t){function n(t,e,r,o,s,a,u){(0,l.rW)(t,(function(t,p){var g=null;if(p||(0,c.Sn)(p)){var v=r,m=t,b=s,y=e;if(d&&!o&&G.test(t)){var w=t.split("."),x=w.length;if(x>1){b&&(b=b.slice());for(var C=0;C<x-1;C++){var k=w[C];y=y[k]=y[k]||{},v+="."+k,b&&b.push(k)}m=w[x-1]}}var S=o&&function(t,e){var n=f[t];return void 0===n&&(t.length>=7&&(n=(0,l.xe)(t,"ext.metadata")||(0,l.xe)(t,"ext.web")),f[t]=n),n}(v);if(g=!S&&h&&h.handleField(v,m)?h.value(v,m,p,i):(0,c.yj)(m,p,i)){var $=g.value;if(y[m]=$,a&&a(b,m,g),u&&"object"==typeof $&&!(0,l.kJ)($)){var T=b;T&&(T=T.slice()).push(m),n(p,$,v+"."+m,o,T,a,u)}}}}))}t.createPayload=function(t,e,n,i,r,o){return{apiKeys:[],payloadBlob:g,overflow:null,sizeExceed:[],failedEvts:[],batches:[],numEvents:0,retryCnt:t,isTeardown:e,isSync:n,isBeacon:i,sendType:o,sendReason:r}},t.appendPayload=function(n,i,r){var s=n&&i&&!n.overflow;return s&&(0,o.Lm)(e,(function(){return"Serializer:appendPayload"}),(function(){for(var e=i.events(),o=n.payloadBlob,s=n.numEvents,a=!1,c=[],u=[],d=n.isBeacon,h=d?65e3:3984588,f=d?W:q,p=0,g=0;p<e.length;){var v=e[p];if(v){if(s>=r){n.overflow=i.split(p);break}var m=t.getEventBlob(v);if(m&&m.length<=f){var b=m.length;if(o.length+b>h){n.overflow=i.split(p);break}o&&(o+="\n"),o+=m,++g>20&&(o.substr(0,1),g=0),a=!0,s++}else m?c.push(v):u.push(v),e.splice(p,1),p--}p++}if(c&&c.length>0&&n.sizeExceed.push(P.create(i.iKey(),c)),u&&u.length>0&&n.failedEvts.push(P.create(i.iKey(),u)),a){n.batches.push(i),n.payloadBlob=o,n.numEvents=s;var y=i.iKey();-1===(0,l.UA)(n.apiKeys,y)&&n.apiKeys.push(y)}}),(function(){return{payload:n,theBatch:{iKey:i.iKey(),evts:i.events()},max:r}})),s},t.getEventBlob=function(t){try{return(0,o.Lm)(e,(function(){return"Serializer.getEventBlob"}),(function(){var e={};e.name=t.name,e.time=t.time,e.ver=t.ver,e.iKey="o:"+(0,c.jM)(t.iKey);var i={},r=t.ext;r&&(e.ext=i,(0,l.rW)(r,(function(t,e){n(e,i[t]={},"ext."+t,!0,null,null,!0)})));var o=e[a]={};o.baseType=t.baseType;var s=o[u]={};return n(t.baseData,s,u,!1,[u],(function(t,e,n){J(i,t,e,n)}),true),n(t.data,o,a,!1,[],(function(t,e,n){J(i,t,e,n)}),true),JSON.stringify(e)}),(function(){return{item:t}}))}catch(t){return null}}}))}return t.__ieDyn=1,t}();function J(t,e,n,i){if(i&&t){var r=(0,c.Vv)(i.value,i.kind,i.propertyType);if(r>-1){var o=t[K];o||(o=t[K]={f:{}});var s=o[Z];if(s||(s=o[Z]={}),e)for(var a=0;a<e.length;a++){var u=e[a];s[u]||(s[u]={f:{}});var d=s[u][Z];d||(d=s[u][Z]={}),s=d}s=s[n]={},(0,l.kJ)(i.value)?s.a={t:r}:s.t=r}}}var Y="sendAttempt",Q="&NoResponseBody=true",tt=((U={})[1]=y,U[100]=y,U[200]="sent",U[8004]=m,U[8003]=m,U),et={},nt={};function it(t,e,n){et[t]=e,!1!==n&&(nt[e]=t)}function rt(t){try{return t.responseText}catch(t){}return g}function ot(t,e){var n=!1;if(t&&e){var i=(0,l.FY)(t);if(i&&i.length>0)for(var r=e.toLowerCase(),o=0;o<i.length;o++){var s=i[o];if(s&&(0,l.nr)(e,s)&&s.toLowerCase()===r){n=!0;break}}}return n}function st(t,e,n,i){e&&n&&n.length>0&&(i&&et[e]?(t.hdrs[et[e]]=n,t.useHdrs=!0):t.url+="&"+e+"="+n)}function at(t,e){return e&&((0,l.hj)(e)?t=[e].concat(t):(0,l.kJ)(e)&&(t=e.concat(t))),t}it(R,R,!1),it(L,L),it(O,"Client-Id"),it(D,D),it(I,I),it(E,E),it(A,A);var ct=function(){function t(e,n,i,s,a){this._responseHandlers=[];var d,f,p,y,R,A,F,H,M,N,P="?cors=true&"+k.toLowerCase()+"="+x,U=new _,q=!1,W=new j,K=!1,Z=0,G=!0,J=[],et={},it=[],ct=null,lt=!1,ut=!1,dt=!1;(0,r.Z)(t,this,(function(t){var r=!0;function j(t,e){for(var n=0,i=null,r=0;null==i&&r<t.length;)1===(n=t[r])?(0,u.cp)()?i=_:(0,u.Z3)()&&(i=ft):2===n&&(0,u.JO)(e)&&(!e||e&&!H)?i=ht:K&&3===n&&(0,u.MF)()&&(i=gt),r++;return i?{_transport:n,_isSync:e,sendPOST:i}:null}function _(t,e,n){var i=new XDomainRequest;i.open(v,t.urlString),t.timeout&&(i.timeout=t.timeout),i.onload=function(){var t=rt(i);pt(e,200,{},t),Lt(t)},i.onerror=function(){pt(e,400,{})},i.ontimeout=function(){pt(e,500,{})},i.onprogress=function(){},n?i.send(t.data):a.set((function(){i.send(t.data)}),0)}function ht(t,e,n){var i,o=t.urlString,s=!1,c=!1,u=((i={body:t.data,method:v}).Microsoft_ApplicationInsights_BypassAjaxInstrumentation=!0,i);n&&(u.keepalive=!0,2===t._sendReason&&(s=!0,N&&(o+=Q))),r&&(u.credentials="include"),t.headers&&(0,l.FY)(t.headers).length>0&&(u.headers=t.headers),fetch(o,u).then((function(t){var n={},i=g,r=t.headers;r&&r.forEach((function(t,e){n[e]=t})),t.body&&t.text().then((function(t){i=t})),c||(c=!0,pt(e,t.status,n,i),Lt(i))})).catch((function(t){c||(c=!0,pt(e,0,{}))})),s&&!c&&(c=!0,pt(e,200,{})),!c&&t.timeout>0&&a.set((function(){c||(c=!0,pt(e,500,{}))}),t.timeout)}function ft(t,e,n){var i=t.urlString;function o(t,e,n){if(!t[n]&&e&&e.getResponseHeader){var i=e.getResponseHeader(n);i&&(t[n]=(0,l.nd)(i))}return t}function s(t){var e={};return t.getAllResponseHeaders?e=function(t){var e={};if((0,l.HD)(t)){var n=(0,l.nd)(t).split(/[\r\n]+/);(0,l.tO)(n,(function(t){if(t){var n=t.indexOf(": ");if(-1!==n){var i=(0,l.nd)(t.substring(0,n)).toLowerCase(),r=(0,l.nd)(t.substring(n+1));e[i]=r}else e[(0,l.nd)(t)]=1}}))}return e}(t.getAllResponseHeaders()):(e=o(e,t,T),e=o(e,t,$),e=o(e,t,"kill-duration-seconds")),e}function a(t,n){pt(e,t.status,s(t),n)}n&&t.disableXhrSync&&(n=!1);var u=(0,c.ot)(v,i,r,!0,n,t.timeout);(0,l.rW)(t.headers,(function(t,e){u.setRequestHeader(t,e)})),u.onload=function(){var t=rt(u);a(u,t),Lt(t)},u.onerror=function(){a(u)},u.ontimeout=function(){a(u)},u.send(t.data)}function pt(t,e,n,i){try{t(e,n,i)}catch(t){(0,h.kP)(f,2,518,(0,u.eU)(t))}}function gt(t,e,n){var i=200,r=t._thePayload,o=t.urlString+(N?Q:g);try{var s=(0,u.jW)();if(!s.sendBeacon(o,t.data))if(r){var a=[];(0,l.tO)(r.batches,(function(t){if(a&&t&&t.count()>0){for(var e=t.events(),n=0;n<e.length;n++)if(!s.sendBeacon(o,ct.getEventBlob(e[n]))){a.push(t.split(n));break}}else a.push(t.split(0))})),Ot(a,8003,r.sendType,!0)}else i=0}catch(t){(0,h.jV)(f,"Failed to send telemetry using sendBeacon API. Ex:"+(0,u.eU)(t)),i=0}finally{pt(e,i,{},g)}}function vt(t){return 2===t||3===t}function mt(t){return ut&&vt(t)&&(t=2),t}function bt(){return!q&&Z<n}function yt(){var t=it;return it=[],t}function wt(t,e,n){var i=!1;return t&&t.length>0&&!q&&p[e]&&ct&&(i=0!==e||bt()&&(n>0||W.allowRequestSending())),i}function xt(t){var e={};return t&&(0,l.tO)(t,(function(t,n){e[n]={iKey:t.iKey(),evts:t.events()}})),e}function Ct(t,n,i,r,s){if(t&&0!==t.length)if(q)Ot(t,1,r);else{r=mt(r);try{var a=t,l=0!==r;(0,o.Lm)(y,(function(){return"HttpManager:_sendBatches"}),(function(o){o&&(t=t.slice(0));for(var a=[],u=null,d=(0,c.hK)(),h=p[r]||(l?p[1]:p[0]),f=h&&h._transport,g=M&&(ut||vt(r)||3===f||h._isSync&&2===f);wt(t,r,n);){var v=t.shift();v&&v.count()>0&&(U.isTenantKilled(v.iKey())?a.push(v):(u=u||ct.createPayload(n,i,l,g,s,r),ct.appendPayload(u,v,e)?null!==u.overflow&&(t=[u.overflow].concat(t),u.overflow=null,$t(u,d,(0,c.hK)(),s),d=(0,c.hK)(),u=null):($t(u,d,(0,c.hK)(),s),d=(0,c.hK)(),t=[v].concat(t),u=null)))}u&&$t(u,d,(0,c.hK)(),s),t.length>0&&(it=t.concat(it)),Ot(a,8004,r)}),(function(){return{batches:xt(a),retryCount:n,isTeardown:i,isSynchronous:l,sendReason:s,useSendBeacon:vt(r),sendType:r}}),!l)}catch(t){(0,h.kP)(f,2,48,"Unexpected Exception sending batch: "+(0,u.eU)(t))}}}function kt(t,e){var n={url:P,hdrs:{},useHdrs:!1};e?(n.hdrs=(0,c.l7)(n.hdrs,et),n.useHdrs=(0,l.FY)(n.hdrs).length>0):(0,l.rW)(et,(function(t,e){nt[t]?st(n,nt[t],e,!1):(n.hdrs[t]=e,n.useHdrs=!0)})),st(n,O,"NO_AUTH",e),st(n,L,c.vs,e);var i=g;(0,l.tO)(t.apiKeys,(function(t){i.length>0&&(i+=","),i+=t})),st(n,D,i,e),st(n,E,(0,l.m6)().toString(),e);var r=function(t){for(var e=0;e<t.batches.length;e++){var n=t.batches[e].Msfpc();if(n)return encodeURIComponent(n)}return g}(t);if((0,c.Sn)(r)&&(n.url+="&ext.intweb.msfpc="+r),W.shouldAddClockSkewHeaders()&&st(n,I,W.getClockSkewHeaderValue(),e),y.getWParam){var o=y.getWParam();o>=0&&(n.url+="&w="+o)}for(var s=0;s<J.length;s++)n.url+="&"+J[s].name+"="+J[s].value;return n}function St(t,e,n){t[e]=t[e]||{},t[e][d.identifier]=n}function $t(e,n,r,s){if(e&&e.payloadBlob&&e.payloadBlob.length>0){var a=!!t.sendHook,g=p[e.sendType];!vt(e.sendType)&&e.isBeacon&&2===e.sendReason&&(g=p[2]||p[3]||g);var v=dt;(e.isBeacon||3===g._transport)&&(v=!1);var m=kt(e,v);v=v||m.useHdrs;var b=(0,c.hK)();(0,o.Lm)(y,(function(){return"HttpManager:_doPayloadSend"}),(function(){for(var p=0;p<e.batches.length;p++)for(var w=e.batches[p].events(),L=0;L<w.length;L++){var O=w[L];if(lt){var I=O.timings=O.timings||{};St(I,"sendEventStart",b),St(I,"serializationStart",n),St(I,"serializationCompleted",r)}O[Y]>0?O[Y]++:O[Y]=1}Ot(e.batches,1e3+(s||0),e.sendType,!0);var E={data:e.payloadBlob,urlString:m.url,headers:m.hdrs,_thePayload:e,_sendReason:s,timeout:A,disableXhrSync:F,disableFetchKeepAlive:H};v&&(ot(E.headers,C)||(E.headers[C]="no-cache, no-store"),ot(E.headers,k)||(E.headers[k]=x));var D=null;g&&(D=function(n){W.firstRequestSent();var r=function(n,r){!function(e,n,r,o){var s=9e3,a=null,u=!1,h=!1;try{var f=!0;if(typeof e!==z.jA){if(n){W.setClockSkew(n[T]);var p=n[$]||n["kill-duration-seconds"];(0,l.tO)(U.setKillSwitchTenants(n[S],p),(function(t){(0,l.tO)(r.batches,(function(e){if(e.iKey()===t){a=a||[];var n=e.split(0);r.numEvents-=n.count(),a.push(n)}}))}))}if(200==e||204==e)return void(s=200);((v=e)>=300&&v<500&&408!=v&&429!=v||501==v||505==v||r.numEvents<=0)&&(f=!1),s=9e3+e%1e3}if(f){s=100;var g=r.retryCnt;0===r.sendType&&(g<i?(u=!0,Tt((function(){0===r.sendType&&Z--,Ct(r.batches,g+1,r.isTeardown,ut?2:r.sendType,5)}),ut,V(g))):(h=!0,ut&&(s=8001)))}}finally{u||(W.setClockSkew(),function(e,n,i,r){try{r&&d._backOffTransmission(),200===n&&(r||e.isSync||d._clearBackOff(),function(t){if(lt){var e=(0,c.hK)();(0,l.tO)(t,(function(t){t&&t.count()>0&&function(t,e){lt&&(0,l.tO)(t,(function(t){St(t.timings=t.timings||{},"sendEventCompleted",e)}))}(t.events(),e)}))}}(e.batches)),Ot(e.batches,n,e.sendType,!0)}finally{0===e.sendType&&(Z--,5!==i&&t.sendQueuedRequests(e.sendType,i))}}(r,s,o,h)),Ot(a,8004,r.sendType)}var v}(n,r,e,s)},o=e.isTeardown||e.isSync;try{g.sendPOST(n,r,o),t.sendListener&&t.sendListener(E,n,o,e.isBeacon)}catch(t){(0,h.jV)(f,"Unexpected exception sending payload. Ex:"+(0,u.eU)(t)),pt(r,0,{})}}),(0,o.Lm)(y,(function(){return"HttpManager:_doPayloadSend.sender"}),(function(){if(D)if(0===e.sendType&&Z++,a&&!e.isBeacon&&3!==g._transport){var n={data:E.data,urlString:E.urlString,headers:(0,c.l7)({},E.headers),timeout:E.timeout,disableXhrSync:E.disableXhrSync,disableFetchKeepAlive:E.disableFetchKeepAlive},i=!1;(0,o.Lm)(y,(function(){return"HttpManager:_doPayloadSend.sendHook"}),(function(){try{t.sendHook(n,(function(t){i=!0,G||t._thePayload||(t._thePayload=t._thePayload||E._thePayload,t._sendReason=t._sendReason||E._sendReason),D(t)}),e.isSync||e.isTeardown)}catch(t){i||D(E)}}))}else D(E)}))}),(function(){return{thePayload:e,serializationStart:n,serializationCompleted:r,sendReason:s}}),e.isSync)}e.sizeExceed&&e.sizeExceed.length>0&&Ot(e.sizeExceed,8003,e.sendType),e.failedEvts&&e.failedEvts.length>0&&Ot(e.failedEvts,8002,e.sendType)}function Tt(t,e,n){e?t():a.set(t,n)}function Lt(e){var n=t._responseHandlers;try{for(var i=0;i<n.length;i++)try{n[i](e)}catch(t){(0,h.kP)(f,1,519,"Response handler failed: "+t)}if(e){var r=JSON.parse(e);(0,c.Sn)(r.webResult)&&(0,c.Sn)(r.webResult[B])&&R.set("MSFPC",r.webResult[B],31536e3)}}catch(t){}}function Ot(t,e,n,i){if(t&&t.length>0&&s){var r=s[function(t){var e=tt[t];(0,c.Sn)(e)||(e="oth",t>=9e3&&t<=9999?e=w:t>=8e3&&t<=8999?e=m:t>=1e3&&t<=1999&&(e=b));return e}(e)];if(r){var a=0!==n;(0,o.Lm)(y,(function(){return"HttpManager:_sendBatchesNotification"}),(function(){Tt((function(){try{r.call(s,t,e,a,n)}catch(t){(0,h.kP)(f,1,74,"send request notification failed: "+t)}}),i||a,0)}),(function(){return{batches:xt(t),reason:e,isSync:a,sendSync:i,sendType:n}}),!a)}}}t.initialize=function(t,e,n,i,o){var s;o||(o={}),P=t+P,dt=!!(0,l.o8)(o.avoidOptions)||!o.avoidOptions,y=e,R=e.getCookieMgr(),lt=!y.config.disableEventTimings;var a=!!y.config.enableCompoundKey;f=(d=n).diagLog();var c=o.valueSanitizer,g=o.stringifyObjects;(0,l.o8)(o.enableCompoundKey)||(a=!!o.enableCompoundKey),A=o.xhrTimeout,F=!!o.disableXhrSync,H=!!o.disableFetchKeepAlive,N=!1!==o.addNoResponse,K=!(0,u.b$)(),ct=new X(y,c,g,a),(0,l.le)(o.useSendBeacon)||(K=!!o.useSendBeacon);var v=i,m=o.alwaysUseXhrOverride?i:null,b=o.alwaysUseXhrOverride?i:null,w=[3,2];if(!i){G=!1;var x=(0,u.k$)();x&&x.protocol&&"file:"===x.protocol.toLowerCase()&&(r=!1);var C=[];(0,u.b$)()?(C=[2,1],w=[2,1,3]):C=[1,2,3],(i=j(C=at(C,o.transports),!1))||(0,h.jV)(f,"No available transport to send events"),v=j(C,!0)}m||(m=j(w=at(w,o.unloadTransports),!0)),M=!G&&(K&&(0,u.MF)()||!H&&(0,u.JO)(!0)),(s={})[0]=i,s[1]=v||j([1,2,3],!0),s[2]=m||v||j([1],!0),s[3]=b||j([2,3],!0)||v||j([1],!0),p=s},t._getDbgPlgTargets=function(){return[p[0],U,ct,p]},t.addQueryStringParameter=function(t,e){for(var n=0;n<J.length;n++)if(J[n].name===t)return void(J[n].value=e);J.push({name:t,value:e})},t.addHeader=function(t,e){et[t]=e},t.canSendRequest=function(){return bt()&&W.allowRequestSending()},t.sendQueuedRequests=function(t,e){(0,l.o8)(t)&&(t=0),ut&&(t=mt(t),e=2),wt(it,t,0)&&Ct(yt(),0,!1,t,e||0)},t.isCompletelyIdle=function(){return!q&&0===Z&&0===it.length},t.setUnloading=function(t){ut=t},t.addBatch=function(t){if(t&&t.count()>0){if(U.isTenantKilled(t.iKey()))return!1;it.push(t)}return!0},t.teardown=function(){it.length>0&&Ct(yt(),0,!0,2,2)},t.pause=function(){q=!0},t.resume=function(){q=!1,t.sendQueuedRequests(0,4)},t.sendSynchronousBatch=function(t,e,n){t&&t.count()>0&&((0,l.le)(e)&&(e=1),ut&&(e=mt(e),n=2),Ct([t],0,!1,e,n||0))}}))}return t.__ieDyn=1,t}();function lt(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return setTimeout(t,e,n)}function ut(t){clearTimeout(t)}function dt(t,e){return{set:t||lt,clear:e||ut}}var ht="eventsDiscarded",ft="overrideInstrumentationKey",pt="maxEventRetryAttempts",gt="maxUnloadEventRetryAttempts",vt=function(t){function e(){var n,i=t.call(this)||this;i.identifier="PostChannel",i.priority=1011,i.version="3.2.8";var f,g,v,m,b,y,w,x=!1,C=[],k=null,S=!1,$=0,T=500,L=0,O=1e4,I={},E=p,D=null,A=null,B=0,M=0,N={},j=-1,_=!0,z=!1,U=6,q=2;return(0,r.Z)(e,i,(function(t,e){function i(t){"beforeunload"!==(t||(0,u.Jj)().event).type&&(z=!0,g.setUnloading(z)),Q(2,2)}function r(t){z=!1,g.setUnloading(z)}function W(t,e){if(t.sendAttempt||(t.sendAttempt=0),t.latency||(t.latency=1),t.ext&&t.ext[F]&&delete t.ext[F],t.ext&&t.ext[H]&&t.ext[H].id&&delete t.ext[H].id,_&&(c.if,t.ext=(0,l.Ax)(t.ext),t.baseData&&(t.baseData=(0,l.Ax)(t.baseData)),t.data&&(t.data=(0,l.Ax)(t.data))),t.sync)if(B||S)t.latency=3,t.sync=!1;else if(g)return _&&(t=(0,l.Ax)(t)),void g.sendSynchronousBatch(P.create(t.iKey,[t]),!0===t.sync?1:t.sync,3);var n=t.latency,i=L,r=O;4===n&&(i=$,r=T);var o=!1;if(i<r)o=!nt(t,e);else{var s=1,a=20;4===n&&(s=4,a=1),o=!0,function(t,e,n,i){for(;n<=e;){var r=tt(t,e,!0);if(r&&r.count()>0){var o=r.split(0,i),s=o.count();if(s>0)return 4===n?$-=s:L-=s,mt(ht,[o],d.QueueFull),!0}n++}return it(),!1}(t.iKey,t.latency,s,a)&&(o=!nt(t,e))}o&&vt(ht,[t],d.QueueFull)}function K(t,e,n){var i=rt(t,e,n);return g.sendQueuedRequests(e,n),i}function Z(){return L>0}function G(){if(j>=0&&rt(j,0,b)&&g.sendQueuedRequests(0,b),$>0&&!A&&!S){var t=I[E][2];t>=0&&(A=J((function(){A=null,K(4,0,1),G()}),t))}var e=I[E][1];!D&&!k&&e>=0&&!S&&(Z()?D=J((function(){D=null,K(0===M?3:1,0,1),M++,M%=2,G()}),e):M=0)}function X(){n=null,x=!1,C=[],k=null,S=!1,$=0,T=500,L=0,O=1e4,I={},E=p,D=null,A=null,B=0,M=0,f=null,N={},v=void 0,m=0,j=-1,b=null,_=!0,z=!1,U=6,q=2,y=null,w=dt(),g=new ct(500,2,1,{requeue:lt,send:bt,sent:yt,drop:wt,rspFail:xt,oth:Ct},w),at(),N[4]={batches:[],iKeyMap:{}},N[3]={batches:[],iKeyMap:{}},N[2]={batches:[],iKeyMap:{}},N[1]={batches:[],iKeyMap:{}},kt()}function J(t,e){0===e&&B&&(e=1);var n=1e3;return B&&(n=V(B-1)),w.set(t,e*n)}function Y(){return null!==D&&(w.clear(D),D=null,M=0,!0)}function Q(t,e){Y(),k&&(w.clear(k),k=null),S||K(1,t,e)}function tt(t,e,n){var i=N[e];i||(i=N[e=1]);var r=i.iKeyMap[t];return!r&&n&&(r=P.create(t),i.batches.push(r),i.iKeyMap[t]=r),r}function et(e,n){g.canSendRequest()&&!B&&(v>0&&L>v&&(n=!0),n&&null==k&&t.flush(e,null,20))}function nt(t,e){_&&(t=(0,l.Ax)(t));var n=t.latency,i=tt(t.iKey,n,!0);return!!i.addEvent(t)&&(4!==n?(L++,e&&0===t.sendAttempt&&et(!t.sync,m>0&&i.count()>=m)):$++,!0)}function it(){for(var t=0,e=0,n=function(n){var i=N[n];i&&i.batches&&(0,l.tO)(i.batches,(function(i){4===n?t+=i.count():e+=i.count()}))},i=1;i<=4;i++)n(i);L=e,$=t}function rt(e,n,i){var r=!1,s=0===n;return!s||g.canSendRequest()?(0,o.Lm)(t.core,(function(){return"PostChannel._queueBatches"}),(function(){for(var t=[],n=4;n>=e;){var i=N[n];i&&i.batches&&i.batches.length>0&&((0,l.tO)(i.batches,(function(e){g.addBatch(e)?r=r||e&&e.count()>0:t=t.concat(e.events()),4===n?$-=e.count():L-=e.count()})),i.batches=[],i.iKeyMap={}),n--}t.length>0&&vt(ht,t,d.KillSwitch),r&&j>=e&&(j=-1,b=0)}),(function(){return{latency:e,sendType:n,sendReason:i}}),!s):(j=j>=0?Math.min(j,e):e,b=Math.max(b,i)),r}function ot(t,e){K(1,0,e),it(),st((function(){t&&t(),C.length>0?k=J((function(){k=null,ot(C.shift(),e)}),0):(k=null,G())}))}function st(t){g.isCompletelyIdle()?t():k=J((function(){k=null,st(t)}),.25)}function at(){(I={})[p]=[2,1,0],I.NEAR_REAL_TIME=[6,3,0],I.BEST_EFFORT=[18,9,0]}function lt(e,n){var i=[],r=U;z&&(r=q),(0,l.tO)(e,(function(e){e&&e.count()>0&&(0,l.tO)(e.events(),(function(e){e&&(e.sync&&(e.latency=4,e.sync=!1),e.sendAttempt<r?((0,c.if)(e,t.identifier),W(e,!1)):i.push(e))}))})),i.length>0&&vt(ht,i,d.NonRetryableStatus),z&&Q(2,2)}function ut(e,n){var i=t._notificationManager||{},r=i[e];if(r)try{r.apply(i,n)}catch(n){(0,h.kP)(t.diagLog(),1,74,e+" notification failed: "+n)}}function vt(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];e&&e.length>0&&ut(t,[e].concat(n))}function mt(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];e&&e.length>0&&(0,l.tO)(e,(function(e){e&&e.count()>0&&ut(t,[e.events()].concat(n))}))}function bt(t,e,n){t&&t.length>0&&ut("eventsSendRequest",[e>=1e3&&e<=1999?e-1e3:0,!0!==n])}function yt(t,e){mt("eventsSent",t,e),G()}function wt(t,e){mt(ht,t,e>=8e3&&e<=8999?e-8e3:d.Unknown)}function xt(t){mt(ht,t,d.NonRetryableStatus),G()}function Ct(t,e){mt(ht,t,d.Unknown),G()}function kt(){m=n&&n.disableAutoBatchFlushLimit?0:Math.max(1500,O/6)}X(),t._getDbgPlgTargets=function(){return[g]},t.initialize=function(u,d,h){(0,o.Lm)(d,(function(){return"PostChannel:initialize"}),(function(){var o=d;e.initialize(u,d,h);try{d.addUnloadCb;y=(0,s.jU)((0,a.J)(t.identifier),d.evtNamespace&&d.evtNamespace());var p=t._getTelCtx();u.extensionConfig[t.identifier]=u.extensionConfig[t.identifier]||{},n=p.getExtCfg(t.identifier),w=dt(n.setTimeoutOverride,n.clearTimeoutOverride),_=!n.disableOptimizeObj&&(0,c.mJ)(),function(t){var e=t.getWParam;t.getWParam=function(){var t=0;return n.ignoreMc1Ms0CookieProcessing&&(t|=2),t|e()}}(o),n.eventsLimitInMem>0&&(O=n.eventsLimitInMem),n.immediateEventLimit>0&&(T=n.immediateEventLimit),n.autoFlushEventsLimit>0&&(v=n.autoFlushEventsLimit),(0,l.hj)(n[pt])&&(U=n[pt]),(0,l.hj)(n[gt])&&(q=n[gt]),kt(),n.httpXHROverride&&n.httpXHROverride.sendPOST&&(f=n.httpXHROverride),(0,c.Sn)(u.anonCookieName)&&g.addQueryStringParameter("anoncknm",u.anonCookieName),g.sendHook=n.payloadPreprocessor,g.sendListener=n.payloadListener;var m=n.overrideEndpointUrl?n.overrideEndpointUrl:u.endpointUrl;t._notificationManager=d.getNotifyMgr(),g.initialize(m,t.core,t,f,n);var b=u.disablePageUnloadEvents||[];(0,s.c9)(i,b,y),(0,s.TJ)(i,b,y),(0,s.nD)(r,u.disablePageShowEvents,y)}catch(e){throw t.setInitialized(!1),e}}),(function(){return{coreConfig:u,core:d,extensions:h}}))},t.processTelemetry=function(e,i){(0,c.if)(e,t.identifier);var r=(i=t._getTelCtx(i)).getExtCfg(t.identifier),o=!!n.disableTelemetry;r&&(o=o||!!r.disableTelemetry);var s=e;o||x||(n[ft]&&(s.iKey=n[ft]),r&&r[ft]&&(s.iKey=r[ft]),W(s,!0),z?Q(2,2):G()),t.processNext(s,i)},t._doTeardown=function(t,e){Q(2,2),x=!0,g.teardown(),(0,s.JA)(null,y),(0,s.C9)(null,y),(0,s.Yl)(null,y),X()},t.setEventQueueLimits=function(t,e){O=t>0?t:1e4,v=e>0?e:0,kt();var n=L>t;if(!n&&m>0)for(var i=1;!n&&i<=3;i++){var r=N[i];r&&r.batches&&(0,l.tO)(r.batches,(function(t){t&&t.count()>=m&&(n=!0)}))}et(!0,n)},t.pause=function(){Y(),S=!0,g.pause()},t.resume=function(){S=!1,g.resume(),G()},t.addResponseHandler=function(t){g._responseHandlers.push(t)},t._loadTransmitProfiles=function(t){Y(),at(),E=p,G(),(0,l.rW)(t,(function(t,e){var n=e.length;if(n>=2){var i=n>2?e[2]:0;if(e.splice(0,n-2),e[1]<0&&(e[0]=-1),e[1]>0&&e[0]>0){var r=e[0]/e[1];e[0]=Math.ceil(r)*e[1]}i>=0&&e[1]>=0&&i>e[1]&&(i=e[1]),e.push(i),I[t]=e}}))},t.flush=function(t,e,n){if(void 0===t&&(t=!0),!S)if(n=n||1,t)null==k?(Y(),rt(1,0,n),k=J((function(){k=null,ot(e,n)}),0)):C.push(e);else{var i=Y();K(1,1,n),null!=e&&e(),i&&G()}},t.setMsaAuthTicket=function(t){g.addHeader(R,t)},t.hasEvents=Z,t._setTransmitProfile=function(t){E!==t&&void 0!==I[t]&&(Y(),E=t,G())},t._backOffTransmission=function(){B<4&&(B++,Y(),G())},t._clearBackOff=function(){B&&(B=0,Y(),G())},(0,l.l_)(t,"_setTimeoutOverride",(function(){return w.set}),(function(t){w=dt(t,w.clear)})),(0,l.l_)(t,"_clearTimeoutOverride",(function(){return w.clear}),(function(t){w=dt(w.set,t)}))})),i}return(0,i.ne)(e,t),e.__ieDyn=1,e}(f.i),mt=vt},14590:function(t,e,n){"use strict";n.d(e,{Z:function(){return Kt}});var i=n(45362),r=n(23806),o=n(91254),s=n(74539),a=n(27981),c=n(21908),l="call",u="toISOString",d="endsWith",h="startsWith",f="trim",p="toString",g=(c.RJ,c.Pw.freeze),v=(c.Pw.seal,c.Pw.keys,String[c.hB]),m=(v[f],v[d],v[h],Date[c.hB][u],Array.isArray),b=c.V4[p],y=c.CY[p];y[l](c.Pw),Object.getPrototypeOf;function w(t){return!(!t||typeof t!==c.fK)}function x(t,e){if(t)for(var n in t)c.CY[l](t,n)&&e[l](t,n,t[n])}var C=m||function(t){return!(!t||"[object Array]"!==b[l](t))};({toString:null}).propertyIsEnumerable("toString");function k(t){return t}var S=g||k;function $(t){var e,n={};return x(t,(function(t,e){n[t]=e[1],n[e[0]]=e[1]})),e=n,g&&x(e,(function(t,e){(C(e)||w(e))&&g(e)})),S(e)}var T,L="locale",O="ver",I="name",E=$({UserExt:[0,"user"],DeviceExt:[1,"device"],TraceExt:[2,"trace"],WebExt:[3,"web"],AppExt:[4,"app"],OSExt:[5,"os"],SdkExt:[6,"sdk"],IntWebExt:[7,"intweb"],UtcExt:[8,"utc"],LocExt:[9,"loc"],CloudExt:[10,"cloud"],DtExt:[11,"dt"]}),D=$({id:[0,"id"],ver:[1,O],appName:[2,I],locale:[3,L],expId:[4,"expId"],env:[5,"env"]}),R=$({domain:[0,"domain"],browser:[1,"browser"],browserVer:[2,"browserVer"],screenRes:[3,"screenRes"],userConsent:[4,"userConsent"],consentDetails:[5,"consentDetails"]}),A=$({locale:[0,L],localId:[1,"localId"],id:[2,"id"]}),B=$({osName:[0,I],ver:[1,O]}),F=$({ver:[0,O],seq:[1,"seq"],installId:[2,"installId"],epoch:[3,"epoch"]}),H=$({msfpc:[0,"msfpc"],anid:[1,"anid"],serviceName:[2,"serviceName"]}),M=$({popSample:[0,"popSample"],eventFlags:[1,"eventFlags"]}),N=$({tz:[0,"tz"]}),P=$({sessionId:[0,"sesId"]}),j=$({localId:[0,"localId"],deviceClass:[1,"deviceClass"],make:[2,"make"],model:[3,"model"]}),_=$({role:[0,"role"],roleInstance:[1,"roleInstance"],roleVer:[2,"roleVer"]}),z=$({traceId:[0,"traceID"],traceName:[1,I],parentId:[2,"parentID"]}),V=$({traceId:[0,"traceId"],spanId:[1,"spanId"],traceFlags:[2,"traceFlags"]}),U=n(54480),q=n(80221),W=n(85282),K=n(42774),Z=n(26454);function G(){return void 0===T&&(T=!!J(0)),T}function X(){return G()?J(0):null}function J(t){var e,n,i=null;try{var r=(0,Z.Rd)();if(!r)return null;n=new Date,(i=0===t?r.localStorage:r.sessionStorage)&&(0,s.mf)(i.setItem)&&(i.setItem(n,n),e=i.getItem(n)!==n,i.removeItem(n),e&&(i=null))}catch(t){i=null}return i}function Y(){return this.getId()}function Q(t){this.setId(t)}var tt=function(){function t(){(0,r.Z)(t,this,(function(t){t.setId=function(e){t.customId=e},t.getId=function(){return(0,s.HD)(t.customId)?t.customId:t.automaticId}}))}return t._staticInit=void(0,s.l_)(t.prototype,"id",Y,Q),t}(),et="ai_session",nt=function(){function t(e,n){var i,a,c=(0,q.vH)(e),l=(0,W.JP)(e);(0,r.Z)(t,this,(function(e){var r,u={sessionRenewalMs:(r=n).sessionRenewalMs&&function(){return r.sessionRenewalMs},sessionExpirationMs:r.sessionExpirationMs&&function(){return r.sessionExpirationMs},cookieDomain:r.cookieDomain&&function(){return r.cookieDomain},namePrefix:r.namePrefix&&function(){return r.namePrefix},sessionAsGuid:function(){return r.sessionAsGuid},idLength:function(){return r.idLength?r.idLength:22}};function d(t){var n=e.automaticSession,i=t.split("|");i.length>0&&n.setId(i[0]);try{if(i.length>1){var r=+i[1];n.acquisitionDate=+new Date(r),n.acquisitionDate=n.acquisitionDate>0?n.acquisitionDate:0}if(i.length>2){var o=+i[2];n.renewalDate=+new Date(o),n.renewalDate=n.renewalDate>0?n.renewalDate:0}}catch(t){(0,q.kP)(c,1,510,"Error parsing ai_session cookie, session will be reset: "+t)}0===n.renewalDate&&(0,q.kP)(c,2,517,"AI session renewal date is 0, session will be reset.")}function h(){var t=e.automaticSession,n=(new Date).getTime(),i=e.config.sessionAsGuid();!(0,s.o8)(i)&&i?(0,s.jn)(i)?t.setId((0,o.cm)()):t.setId((0,o.cm)(i)):t.setId((0,K.pZ)(u&&u.idLength?u.idLength():22)),t.acquisitionDate=n,t.renewalDate=n,f(t.getId(),t.acquisitionDate,t.renewalDate),G()||(0,q.kP)(c,2,505,"Browser does not support local storage. Session durations will be inaccurate.")}function f(t,n,r){var o=n+e.config.sessionExpirationMs(),s=r+e.config.sessionRenewalMs(),c=new Date,u=[t,n,r];o<s?c.setTime(o):c.setTime(s);var d=e.config.cookieDomain?e.config.cookieDomain():null;l.set(a(),u.join("|")+";expires="+c.toUTCString(),null,d),i=(new Date).getTime()}(0,s.mf)(n.sessionExpirationMs)||(u.sessionExpirationMs=function(){return t.acquisitionSpan}),(0,s.mf)(n.sessionRenewalMs)||(u.sessionRenewalMs=function(){return t.renewalSpan}),e.config=u,a=function(){return e.config.namePrefix&&e.config.namePrefix()?et+e.config.namePrefix():et},e.automaticSession=new tt,e.update=function(){e.automaticSession.getId()||function(){var t=l.get(a());if(t&&(0,s.mf)(t.split))d(t);else{var n=function(t,e){var n=X();if(null!==n)try{return n.getItem(e)}catch(e){T=!1,(0,q.kP)(t,1,503,"Browser failed read of local storage. "+e)}return null}(c,a());n&&d(n)}e.automaticSession.getId()||h()}();var n=e.automaticSession,r=e.config,o=(new Date).getTime(),u=o-n.acquisitionDate>r.sessionExpirationMs(),p=o-n.renewalDate>r.sessionRenewalMs();if(u||p)h();else{(!i||o-i>t.cookieUpdateInterval)&&(n.renewalDate=o,f(n.getId(),n.acquisitionDate,n.renewalDate))}},e.backup=function(){var t,n,i,r=e.automaticSession;t=r.getId(),n=r.acquisitionDate,i=r.renewalDate,function(t,e,n){var i=X();if(null!==i)try{return i.setItem(e,n),!0}catch(e){T=!1,(0,q.kP)(t,1,504,"Browser failed write to local storage. "+e)}}(c,a(),[t,n,i].join("|"))}}))}return t.acquisitionSpan=864e5,t.renewalSpan=18e5,t.cookieUpdateInterval=6e4,t}(),it=n(60851),rt=["AX","EX","SF","CS","CF","CT","CU","DC","DF","H5","HL","WS","WP"];function ot(t,e){void 0===e&&(e=rt);var n=null;if(t)for(var i=t.split(","),r=0;r<i.length;r++)st(i[r],e)&&(n?n+=","+i[r]:n=i[r]);return n}function st(t,e){if(void 0===e&&(e=rt),!t||t.length<4)return!1;for(var n=!1,i=t.substring(0,3).toString().toUpperCase(),r=0;r<e.length;r++)if(e[r]+":"===i&&t.length<=256){n=!0;break}return n}function at(){return this.getExpId()}var ct=function(){function t(e,n){var i=null,s=rt.slice(0),a="Treatments",c=(0,W.JP)(n),l=e;(0,r.Z)(t,this,(function(t){if((0,it.Nv)()){var n=(0,it.Me)().documentElement;n&&(t.locale=n.lang)}function r(t){t!==i&&(i=ot(t,s))}t.env=e.env?e.env:function(t){var e,n={},i=(0,it.Me)();if(i){e=i&&i.querySelectorAll("meta");for(var r=0;r<e.length;r++){var o=e[r];if(o.name)if(0===o.name.toLowerCase().indexOf(t))n[o.name.replace(t,"")]=o.content}}return n}("awa-").env,t.getExpId=function(){return l.expId?(r(l.expId),i):(r((0,o.Do)(c,a)),i)}}))}return t.validateAppExpId=ot,t._staticInit=void(0,s.l_)(t.prototype,"expId",at),t}(),lt=function(){},ut=function(){};function dt(){return this.getMsfpc()}function ht(){return this.getAnid()}var ft=function(){function t(e,n){var i=(0,W.JP)(n);(0,r.Z)(t,this,(function(t){e.serviceName&&(t.serviceName=e.serviceName),t.getMsfpc=function(){return(0,o.Do)(i,"MSFPC")},t.getAnid=function(){return(0,o.Do)(i,"ANON").slice(0,34)}}))}var e;return t._staticInit=(e=t.prototype,(0,s.l_)(e,"msfpc",dt),void(0,s.l_)(e,"anid",ht)),t}(),pt=function(){var t=(new Date).getTimezoneOffset(),e=t%60,n=(t-e)/60,i="+";n>0&&(i="-"),n=Math.abs(n),e=Math.abs(e),this.tz=i+(n<10?"0"+n:n.toString())+":"+(e<10?"0"+e:e.toString())},gt={WIN:/(windows|win32)/i,WINRT:/ arm;/i,WINPHONE:/windows\sphone\s\d+\.\d+/i,OSX:/(macintosh|mac os x)/i,IOS:/(ipad|iphone|ipod)(?=.*like mac os x)/i,LINUX:/(linux|joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk)/i,ANDROID:/android/i,CROS:/CrOS/i},vt={5.1:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1","10.0":"10"},mt="([\\d,.]+)",bt="([\\d,_,.]+)",yt="Unknown",wt=[{r:gt.WINPHONE,os:"Windows Phone"},{r:gt.WINRT,os:"Windows RT"},{r:gt.WIN,os:"Windows"},{r:gt.IOS,os:"iOS"},{r:gt.ANDROID,os:"Android"},{r:gt.LINUX,os:"Linux"},{r:gt.CROS,os:"Chrome OS"},{s:"x11",os:"Unix"},{s:"blackberry",os:"BlackBerry"},{s:"symbian",os:"Symbian"},{s:"nokia",os:"Nokia"},{r:gt.OSX,os:"Mac OS X"}];function xt(t,e){return"Windows"===e?Ct(t,"Windows NT"):"Android"===e?Ct(t,e):"Mac OS X"===e?function(t){var e=t.match(new RegExp("Mac OS X "+bt));if(e){var n=e[1].replace(/_/g,".");if(n){var i=kt(n);return i?n.split(i)[0]:n}}return yt}(t):"iOS"===e?function(t){var e=t.match(new RegExp("OS "+bt));if(e){var n=e[1].replace(/_/g,".");if(n){var i=kt(n);return i?n.split(i)[0]:n}}return yt}(t):yt}function Ct(t,e){var n=t.match(new RegExp(e+" "+mt));return n?vt[n[1]]?vt[n[1]]:n[1]:yt}function kt(t){return t.indexOf(".")>-1?".":t.indexOf("_")>-1?"_":null}var St=function(t){if(t.populateOperatingSystemInfo){var e=this,n=(0,it.jW)()||{},i=t.userAgent||n.userAgent||"",r=t.userAgentData||n.userAgentData||{};if(i){var o=function(t){for(var e=0;e<wt.length;e++){var n=wt[e];if(n.r&&t.match(n.r))return n.os;if(n.s&&-1!==t.indexOf(n.s))return n.os}return yt}(i.toLowerCase());e.name=o,e.ver=xt(i,o)}e.name&&e.name!==yt||!(0,s.HD)(r.platform)||(e.name=r.platform)}},$t=n(39860),Tt="MicrosoftApplicationsTelemetryDeviceId";var Lt=function(){function t(e,n){var i=0;(0,r.Z)(t,this,(function(t){var r=e.propertyStorageOverride;t.seq=i,t.epoch=(0,K._l)(!1).toString();var s=(0,W.JP)(n,e);if(s.isEnabled()||r){var a=function(t,e,n){return e?e.getProperty(n)||"":(0,o.Do)(t,n)}(s,r,Tt);a||(a=(0,$t.GW)()),function(t,e,n,i){e?e.setProperty(n,i):t.set(n,i,31536e3)}(s,r,Tt,a),t.installId=a}else s.purge(Tt);t.getSequenceId=function(){return++i}}))}return t.__ieDyn=1,t}(),Ot=function(t,e,n,i){var r=this;if(r.traceId=e||(0,$t.DO)(),t.enableDistributedTracing&&!n&&(n=(0,$t.DO)().substring(0,16)),r.parentId=n,t.enableApplicationInsightsTrace){r.name=i;var o=(0,it.k$)();o&&o.pathname&&(r.name=o.pathname)}},It="setLocalId";function Et(){return this.getLocalId()}function Dt(t){this[It](t)}var Rt=function(){function t(e,n,i){var a,c=n,l=(0,W.JP)(i,e);(0,r.Z)(t,this,(function(n){if(l&&l.isEnabled()&&(p(),c.enableApplicationInsightsUser)){var i=(0,o.Do)(l,t.userCookieName);if(i){var r=i.split(t.cookieSeparator);r.length>0&&(n.id=r[0])}if(!n.id){n.id=(0,K.pZ)(e&&!(0,s.o8)(e.idLength)?e.idLength:22);var u=(0,s.Y6)(new Date);n.accountAcquisitionDate=u;var d=[n.id,u],h=c.cookieDomain?c.cookieDomain:void 0;l.set(t.userCookieName,d.join(t.cookieSeparator),31536e3,h)}}if("undefined"!=typeof navigator){var f=navigator;n.locale=f.userLanguage||f.language}function p(){if(!c.hashIdentifiers&&!c.dropIdentifiers){var t=(0,o.Do)(l,"MUID");t&&(a="t:"+t)}return a}n.getLocalId=function(){return a||p()},n[It]=function(t){a=t}}))}return t.cookieSeparator="|",t.userCookieName="ai_user",t._staticInit=void(0,s.l_)(t.prototype,"localId",Et,Dt),t}(),At=function(t){var e=this;e.popSample=100,e.eventFlags=0,t.hashIdentifiers&&(e.eventFlags=1048576|e.eventFlags),t.dropIdentifiers&&(e.eventFlags=2097152|e.eventFlags)},Bt="([\\d,.]+)",Ft="Unknown",Ht="Edg/",Mt=[{ua:"OPR/",b:"Opera"},{ua:"PhantomJS",b:"PhantomJS"},{ua:"Edge",b:"Edge"},{ua:Ht,b:"Edge"},{ua:"Electron",b:"Electron"},{ua:"Chrome",b:"Chrome"},{ua:"Trident",b:"MSIE"},{ua:"MSIE ",b:"MSIE"},{ua:"Firefox",b:"Firefox"},{ua:"Safari",b:"Safari"},{ua:"SkypeShell",b:"SkypeShell"}],Nt=[{br:"Microsoft Edge",b:"Edge"},{br:"Google Chrome",b:"Chrome"},{br:"Opera",b:"Opera"}];function Pt(t,e){return e.indexOf(t)>-1}function jt(t,e){for(var n=0;n<e.length;n++)if(t==e[n].brand)return e[n].version;return null}function _t(t,e){return"MSIE"===e?function(t){var e=t.match(new RegExp("MSIE "+Bt));if(e)return e[1];var n=t.match(new RegExp("rv:"+Bt));if(n)return n[1]}(t):function(t,e){"Safari"===t?t="Version":"Edge"===t&&Pt(Ht,e)&&(t="Edg");var n=e.match(new RegExp(t+"/"+Bt));if(n)return n[1];if("Opera"===t&&(n=e.match(new RegExp("OPR/"+Bt))))return n[1];return Ft}(e,t)}function zt(){return this.getUserConsent()}var Vt=function(){function t(e,n){var i=(0,W.JP)(n),a=e||{};(0,r.Z)(t,this,(function(t){var e=(0,it.k$)();if(e){var n=e.hostname;n&&(t.domain="file:"===e.protocol?"local":n)}if(a.populateBrowserInfo){var r=a.userAgent,c=(a.userAgentData||{}).brands,l=(0,it.jW)();l&&(r=r||l.userAgent||"",c=c||(l.userAgentData||{}).brands),function(e,n){if((0,s.kJ)(n))try{for(var i=0;i<Nt.length;i++){var r=jt(Nt[i].br,n);if(r)return t.browser=Nt[i].b,void(t.browserVer=r)}}catch(t){}if(e){var o=function(t){if(t)for(var e=0;e<Mt.length;e++)if(Pt(Mt[e].ua,t))return Mt[e].b;return Ft}(e);t.browser=o,t.browserVer=_t(e,o)}}(r,c);var u=function(){var t={h:0,w:0},e=(0,it.Jj)();return e&&e.screen&&(t.h=screen.height,t.w=screen.width),t}();t.screenRes=u.w+"X"+u.h}t.getUserConsent=function(){return a.userConsented||!!(0,o.Do)(i,a.userConsentCookieName||"MSCC")},t.getUserConsentDetails=function(){try{var t=a.callback;if(t&&t.userConsentDetails){var e=t.userConsentDetails();if(e)return JSON.stringify({Required:e.Required||!1,Analytics:e.Analytics||!1,SocialMedia:e.SocialMedia||!1,Advertising:e.Advertising||!1})}}catch(t){}return null},(0,s.l_)(t,"userConsent",t.getUserConsent)}))}return t._staticInit=void(0,s.l_)(t.prototype,"userConsent",zt),t}();function Ut(t,e,n,i,r){var o=e.ext[E[t]];return o&&(0,s.rW)(i,(function(t,e){if((0,s.HD)(e)||(0,s.hj)(e)||(0,s.jn)(e)){var i=o[n[t]];!r&&(i||(0,s.HD)(i)||(0,s.hj)(i)||(0,s.jn)(i))&&(e=i),o[n[t]]=e}})),o}var qt=function(){function t(e,n,i){(0,r.Z)(t,this,(function(t){t.app=new ct(n,i),t.cloud=new lt,t.user=new Rt(e,n,i),t.os=new St(n),t.web=new Vt(n,i);var r=new Lt(e,i),o=new ft(n,i),a=new At(n);t.loc=new pt,t.device=new ut;var c=new nt(i,n);t.session=new tt;var l,u,d,h=(l=new Ot(n),u=g(),d=l||{},{getName:function(){return d.name},setName:function(t){u&&u.setName(t),d.name=t},getTraceId:function(){return d.traceId},setTraceId:function(t){u&&u.setTraceId(t),(0,U.jN)(t)&&(d.traceId=t)},getSpanId:function(){return d.parentId},setSpanId:function(t){u&&u.setSpanId(t),(0,U.Lc)(t)&&(d.parentId=t)},getTraceFlags:function(){return d.traceFlags},setTraceFlags:function(t){u&&u.setTraceFlags(t),d.traceFlags=t}}),f=!(n||{}).eventContainExtFields;function p(){var e=t.session;if(e&&(0,s.HD)(e.customId))return e.customId;c.update();var n=c.automaticSession;if(n){var i=n.getId();i&&(0,s.HD)(i)&&(e.automaticId=i)}return e.automaticId}function g(){var t=h;return i&&i.getTraceCtx&&(t=i.getTraceCtx(!1)||h),t}t.getTraceCtx=function(){return h},t.getSessionId=p,t.applyApplicationContext=function(e){var n,i=t.app;Ut(4,e,D,((n={})[0]=i.id,n[1]=i.ver,n[2]=i.name,n[3]=i.locale,n[4]=i.getExpId(),n[5]=i.env,n),f)},t.applyUserContext=function(e){var n,i=t.user;Ut(0,e,A,((n={})[1]=i.getLocalId(),n[0]=i.locale,n[2]=i.id,n),f)},t.applyWebContext=function(e){var n,i=t.web;Ut(3,e,R,((n={})[0]=i.domain,n[1]=i.browser,n[2]=i.browserVer,n[3]=i.screenRes,n[5]=i.getUserConsentDetails(),n[4]=i.getUserConsent(),n),f)},t.applyOsContext=function(e){var n,i=t.os;Ut(5,e,B,((n={})[0]=i.name,n[1]=i.ver,n),f)},t.applySdkContext=function(t){var e;Ut(6,t,F,((e={})[2]=r.installId,e[1]=r.getSequenceId(),e[3]=r.epoch,e),f)},t.applyIntWebContext=function(t){var e;Ut(7,t,H,((e={})[0]=o.getMsfpc(),e[1]=o.getAnid(),e[2]=o.serviceName,e),f)},t.applyUtcContext=function(t){var e,n=((e={})[0]=a.popSample,e);a.eventFlags>0&&(n[1]=a.eventFlags),Ut(8,t,M,n,f)},t.applyLocContext=function(e){var n;Ut(9,e,N,((n={})[0]=t.loc.tz,n),f)},t.applySessionContext=function(t){var e;Ut(4,t,P,((e={})[0]=p(),e),f)},t.applyDeviceContext=function(e){var n,i=t.device;Ut(1,e,j,((n={})[0]=i.localId,n[2]=i.make,n[3]=i.model,n[1]=i.deviceClass,n),f)},t.applyCloudContext=function(e){var n,i=t.cloud;Ut(10,e,_,((n={})[0]=i.role,n[1]=i.roleInstance,n[2]=i.roleVer,n),f)},t.applyAITraceContext=function(t){var e;if(n.enableApplicationInsightsTrace){var i=g();i&&Ut(2,t,z,((e={})[0]=i.getTraceId(),e[1]=i.getName(),e[2]=i.getSpanId(),e),!1)}},t.applyDistributedTraceContext=function(t){var e,n=g();if(n){var i=((e={})[0]=n.getTraceId(),e[1]=n.getSpanId(),e),r=n.getTraceFlags();(0,s.le)(r)||(i[2]=r),Ut(11,t,V,i,!1)}}}))}return t.__ieDyn=1,t}();var Wt=[E[4],E[0],E[3],E[5],E[6],E[7],E[8],E[9],E[1],E[2],E[11],E[10]],Kt=function(t){function e(){var n,i,a,c=t.call(this)||this;return c.identifier="SystemPropertiesCollector",c.priority=3,c.version="3.2.8",(0,r.Z)(e,c,(function(t,e){function r(){n=null,i={}}r(),t.initialize=function(i,r,o){e.initialize(i,r,o),a=t._getTelCtx().getExtCfg(t.identifier),n=new qt(i,a,r),r&&r.setTraceCtx&&r.setTraceCtx(n.getTraceCtx())},t.processTelemetry=function(e,r){(0,o.if)(e,t.identifier),r=t._getTelCtx(r);var c,l,u=e.ext=e.ext?e.ext:{};e.data=e.data?e.data:{},(0,s.tO)(Wt,(function(t){u[t]=u[t]||{}})),n&&(n.applyApplicationContext(e),n.applyUserContext(e),n.applyWebContext(e),n.applyOsContext(e),n.applySdkContext(e),n.applyIntWebContext(e),n.applyUtcContext(e),n.applyLocContext(e),n.applySessionContext(e),n.applyDeviceContext(e),a.enableApplicationInsightsTrace&&n.applyAITraceContext(e),a.enableDistributedTracing&&n.applyDistributedTraceContext(e),n.applyCloudContext(e)),(0,s.tO)((0,s.FY)(u),(function(t){0===(0,s.FY)(u[t]).length&&delete u[t]})),c=i,l=e.data,c&&(0,s.rW)(c,(function(t,e){l[t]||(l[t]=e)})),t.processNext(e,r)},t.getPropertiesContext=function(){return n},t.setProperty=function(t,e){i[t]=e},t._doTeardown=function(t,e){var i=(t||{}).core();if(i&&i.getTraceCtx&&n){var o=i.getTraceCtx(!1);o&&o===n.getTraceCtx()&&i.setTraceCtx(null)}r()}})),c}return(0,i.ne)(e,t),e.__ieDyn=1,e}(a.i)},21908:function(t,e,n){"use strict";n.d(e,{CY:function(){return f},Pw:function(){return c},RJ:function(){return h},V4:function(){return l},bO:function(){return a},cb:function(){return i},fK:function(){return r},hB:function(){return s},jA:function(){return o},rl:function(){return u},yu:function(){return d}});var i="function",r="object",o="undefined",s="prototype",a="hasOwnProperty",c=Object,l=c[s],u=c.assign,d=c.create,h=c.defineProperty,f=l[a]},26454:function(t,e,n){"use strict";n.d(e,{Rd:function(){return o},ZU:function(){return s},pu:function(){return a}});var i=n(21908),r=null;function o(t){void 0===t&&(t=!0);var e=!1===t?null:r;return e||(typeof globalThis!==i.jA&&(e=globalThis),e||typeof self===i.jA||(e=self),e||typeof window===i.jA||(e=window),e||typeof n.g===i.jA||(e=n.g),r=e),e}function s(t){throw new TypeError(t)}function a(t){var e=i.yu;if(e)return e(t);if(null==t)return{};var n=typeof t;function r(){}return n!==i.fK&&n!==i.cb&&s("Object prototype may only be an Object:"+t),r[i.hB]=t,new r}},45362:function(t,e,n){"use strict";n.d(e,{$h:function(){return c},ne:function(){return a},uc:function(){return o}});var i=n(21908),r=n(26454),o=(((0,r.Rd)()||{}).Symbol,((0,r.Rd)()||{}).Reflect,i.rl||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])i.V4[i.bO].call(e,o)&&(t[o]=e[o]);return t}),s=function(t,e){return s=i.Pw.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e[i.bO](n)&&(t[n]=e[n])},s(t,e)};function a(t,e){function n(){this.constructor=t}typeof e!==i.cb&&null!==e&&(0,r.ZU)("Class extends value "+String(e)+" is not a constructor or null"),s(t,e),t[i.hB]=null===e?(0,r.pu)(e):(n[i.hB]=e[i.hB],new n)}function c(t,e){for(var n=0,i=e.length,r=t.length;n<i;n++,r++)t[r]=e[n];return t}},23806:function(t,e,n){"use strict";var i;n.d(e,{Z:function(){return P}});var r="undefined",o="constructor",s="prototype",a="function",c="_dynInstFuncs",l="_isDynProxy",u="_dynClass",d="_dynCls$",h="_dynInstChk",f=h,p="_dfOpts",g="_unknown_",v="__proto__",m="_dyn"+v,b="__dynProto$Gbl",y="_dynInstProto",w="useBaseInst",x="setInstFuncs",C=Object,k=C.getPrototypeOf,S=C.getOwnPropertyNames;var $,T=(typeof globalThis!==r&&($=globalThis),$||typeof self===r||($=self),$||typeof window===r||($=window),$||typeof n.g===r||($=n.g),$||{}),L=T[b]||(T[b]={o:(i={},i[x]=!0,i[w]=!0,i),n:1e3});function O(t,e){return t&&C[s].hasOwnProperty.call(t,e)}function I(t){return t&&(t===C[s]||t===Array[s])}function E(t){return I(t)||t===Function[s]}function D(t){var e;if(t){if(k)return k(t);var n=t[v]||t[s]||(t[o]?t[o][s]:null);e=t[m]||n,O(t,m)||(delete t[y],e=t[m]=t[y]||t[m],t[y]=n)}return e}function R(t,e){var n=[];if(S)n=S(t);else for(var i in t)"string"==typeof i&&O(t,i)&&n.push(i);if(n&&n.length>0)for(var r=0;r<n.length;r++)e(n[r])}function A(t,e,n){return e!==o&&typeof t[e]===a&&(n||O(t,e))&&e!==v&&e!==s}function B(t){throw new TypeError("DynamicProto: "+t)}function F(){return Object.create?(t=null,(e=Object.create)?e(t):{}):{};var t,e}function H(t,e){for(var n=t.length-1;n>=0;n--)if(t[n]===e)return!0;return!1}function M(t,e,n,i,r){function o(t,e){var n=function(){var i=function(t,e,n,i){var r=null;if(t&&O(n,u)){var o=t[c]||F();if((r=(o[n[u]]||F())[e])||B("Missing ["+e+"] "+a),!r[h]&&!1!==o[f]){for(var s=!O(t,e),l=D(t),d=[];s&&l&&!E(l)&&!H(d,l);){var p=l[e];if(p){s=p===i;break}d.push(l),l=D(l)}try{s&&(t[e]=r),r[h]=1}catch(t){o[f]=!1}}}return r}(this,e,t,n)||function(t,e,n){var i=e[t];return i===n&&(i=D(e)[t]),typeof i!==a&&B("["+t+"] is not a "+a),i}(e,t,n);return i.apply(this,arguments)};return n[l]=1,n}if(!I(t)){var s=n[c]=n[c]||F();if(!I(s)){var d=s[e]=s[e]||F();!1!==s[f]&&(s[f]=!!r),I(d)||R(n,(function(e){A(n,e,!1)&&n[e]!==i[e]&&(d[e]=n[e],delete n[e],(!O(t,e)||t[e]&&!t[e][l])&&(t[e]=o(t,e)))}))}}}function N(t,e){return O(t,s)?t.name||e||g:((t||{})[o]||{}).name||e||g}function P(t,e,n,i){O(t,s)||B("theClass is an invalid class definition.");var r=t[s];(function(t,e){if(k){for(var n=[],i=D(e);i&&!E(i)&&!H(n,i);){if(i===t)return!0;n.push(i),i=D(i)}return!1}return!0})(r,e)||B("["+N(t)+"] not in hierarchy of ["+N(e)+"]");var o=null;O(r,u)?o=r[u]:(o=d+N(t,"_")+"$"+L.n,L.n++,r[u]=o);var a=P[p],h=!!a[w];h&&i&&void 0!==i[w]&&(h=!!i[w]);var g=function(t){var e=F();return R(t,(function(n){!e[n]&&A(t,n,!1)&&(e[n]=t[n])})),e}(e),v=function(t,e,n,i){function r(t,e,n){var r=e[n];if(r[l]&&i){var o=t[c]||{};!1!==o[f]&&(r=(o[e[u]]||{})[n]||r)}return function(){return r.apply(t,arguments)}}var o=F();R(n,(function(t){o[t]=r(e,n,t)}));for(var s=D(t),a=[];s&&!E(s)&&!H(a,s);)R(s,(function(t){!o[t]&&A(s,t,!k)&&(o[t]=r(e,s,t))})),a.push(s),s=D(s);return o}(r,e,g,h);n(e,v);var m=!!k&&!!a[x];m&&i&&(m=!!i[x]),M(r,o,e,g,!1!==m)}P[p]=L.o},92531:function(t,e,n){"use strict";var i;n.d(e,{DO:function(){return c},ZP:function(){return l}}),function(t){t.ltr="ltr",t.rtl="rtl"}(i||(i={}));var r=n(2619),o=n(97186);const s={steps:94,clipLight:0,clipDark:0},a=(Object.assign({},s),Object.assign(Object.assign({},s),{baseColor:(0,o.in)("#0078D4")}),{backgroundColor:"#FFFFFF",contrast:0,density:0,designUnit:4,baseHeightMultiplier:8,baseHorizontalSpacingMultiplier:3,direction:i.ltr,cornerRadius:2,elevatedCornerRadius:4,focusOutlineWidth:2,fontWeight:{light:100,semilight:200,normal:400,semibold:600,bold:700},disabledOpacity:.3,outlineWidth:1,neutralPalette:["#FFFFFF","#FCFCFC","#FAFAFA","#F7F7F7","#F5F5F5","#F2F2F2","#EFEFEF","#EDEDED","#EAEAEA","#E8E8E8","#E5E5E5","#E2E2E2","#E0E0E0","#DDDDDD","#DBDBDB","#D8D8D8","#D6D6D6","#D3D3D3","#D0D0D0","#CECECE","#CBCBCB","#C9C9C9","#C6C6C6","#C3C3C3","#C1C1C1","#BEBEBE","#BCBCBC","#B9B9B9","#B6B6B6","#B4B4B4","#B1B1B1","#AFAFAF","#ACACAC","#A9A9A9","#A7A7A7","#A4A4A4","#A2A2A2","#9F9F9F","#9D9D9D","#9A9A9A","#979797","#959595","#929292","#909090","#8D8D8D","#8A8A8A","#888888","#858585","#838383","#808080","#7D7D7D","#7B7B7B","#787878","#767676","#737373","#717171","#6E6E6E","#6B6B6B","#696969","#666666","#646464","#616161","#5F5F5F","#5C5C5C","#5A5A5A","#575757","#545454","#525252","#4F4F4F","#4D4D4D","#4A4A4A","#484848","#454545","#424242","#404040","#3D3D3D","#3B3B3B","#383838","#363636","#333333","#313131","#2E2E2E","#2B2B2B","#292929","#262626","#242424","#212121","#1E1E1E","#1B1B1B","#181818","#151515","#121212","#101010","#000000"],accentPalette:["#FFFFFF","#FBFDFE","#F6FAFE","#F2F8FD","#EEF6FC","#E9F4FB","#E5F1FB","#E1EFFA","#DCEDF9","#D8EAF8","#D4E8F8","#CFE6F7","#CBE4F6","#C7E1F6","#C2DFF5","#BEDDF4","#BADAF3","#B6D8F3","#B1D6F2","#ADD4F1","#A9D1F0","#A4CFF0","#A0CDEF","#9CCAEE","#97C8EE","#93C6ED","#8FC4EC","#8AC1EB","#86BFEB","#82BDEA","#7DBAE9","#79B8E8","#75B6E8","#70B3E7","#6CB1E6","#68AFE5","#63ADE5","#5FAAE4","#5BA8E3","#56A6E3","#52A3E2","#4EA1E1","#499FE0","#459DE0","#419ADF","#3D98DE","#3896DD","#3493DD","#3091DC","#2B8FDB","#278DDB","#238ADA","#1E88D9","#1A86D8","#1683D8","#1181D7","#0D7FD6","#097DD5","#047AD5","#0078D4","#0075CF","#0072C9","#006FC4","#006CBE","#0069B9","#0066B4","#0063AE","#0060A9","#005CA3","#00599E","#005699","#005393","#00508E","#004D88","#004A83","#00477D","#004478","#004173","#003E6D","#003B68","#003862","#00355D","#003258","#002F52","#002B4D","#002847","#002542","#00223C","#001F36","#001B30","#00182B","#001525","#00121F","#000000"],accentBaseColor:"#0078D4",accentFillRestDelta:0,accentFillHoverDelta:4,accentFillActiveDelta:-5,accentFillFocusDelta:0,accentFillSelectedDelta:12,accentForegroundRestDelta:0,accentForegroundHoverDelta:6,accentForegroundActiveDelta:-4,accentForegroundFocusDelta:0,neutralFillRestDelta:7,neutralFillHoverDelta:10,neutralFillActiveDelta:5,neutralFillFocusDelta:0,neutralFillSelectedDelta:7,neutralFillInputRestDelta:0,neutralFillInputHoverDelta:0,neutralFillInputActiveDelta:0,neutralFillInputFocusDelta:0,neutralFillInputSelectedDelta:0,neutralFillStealthRestDelta:0,neutralFillStealthHoverDelta:5,neutralFillStealthActiveDelta:3,neutralFillStealthFocusDelta:0,neutralFillStealthSelectedDelta:7,neutralFillToggleHoverDelta:8,neutralFillToggleActiveDelta:-5,neutralFillToggleFocusDelta:0,baseLayerLuminance:-1,neutralFillCardDelta:3,neutralForegroundDarkIndex:93,neutralForegroundLightIndex:0,neutralForegroundHoverDelta:0,neutralForegroundActiveDelta:0,neutralForegroundFocusDelta:0,neutralDividerRestDelta:8,neutralOutlineRestDelta:25,neutralOutlineHoverDelta:40,neutralOutlineActiveDelta:16,neutralOutlineFocusDelta:25});function c(t,e){return(0,r.Z)(t)?t(e):t}var l=a},8340:function(t,e,n){"use strict";n.d(e,{$p:function(){return h},ET:function(){return d},K2:function(){return p},Kf:function(){return u},lB:function(){return i},mq:function(){return c},sF:function(){return f},uZ:function(){return g},vm:function(){return a}});var i,r=n(97186),o=n(60279),s=n(9791);function a(t){const e=(0,s.Z)(t);return function(t){return"function"==typeof t?n=>e(Object.assign({},n,{backgroundColor:t(n)})):e(t)}}function c(t,e){const n=(0,s.Z)(e);return e=>"function"==typeof e?i=>n(Object.assign({},i,{backgroundColor:e(i)}))[t]:n(e)[t]}!function(t){t.rest="rest",t.hover="hover",t.active="active",t.focus="focus",t.selected="selected"}(i||(i={}));const l=(0,s.Z)((t=>{let e=(0,r.in)(t);if(null!==e)return e;if(e=(0,r.hg)(t),null!==e)return e;throw new Error(`${t} cannot be converted to a ColorRGBA64. Color strings must be one of the following formats: "#RGB", "#RRGGBB", or "rgb(r, g, b)"`)}));function u(t){return(0,r.pJ)(t)||(0,r.b4)(t)}function d(t,e){return l(t).equalValue(l(e))}const h=(0,s.Z)(((t,e)=>(0,o.wo)(l(t),l(e))),((t,e)=>t+e));function f(t){return(0,o.hM)(l(t))}function p(...t){return e=>Math.max.apply(null,t.map((t=>t(e))))}const g=(t,e,n)=>Math.min(Math.max(t,e),n)},33442:function(t,e,n){"use strict";n.d(e,{$B:function(){return w},A4:function(){return y},l$:function(){return x},s5:function(){return b}});var i,r,o,s=n(74626),a=n(2696),c=n(82917),l=n(33390),u=n(9739),d=n(8340);function h(t,e){return n=>-1===(0,l.q2)(n)?e(n):t(n)}!function(t){t[t.L1=0]="L1",t[t.L1Alt=3]="L1Alt",t[t.L2=10]="L2",t[t.L3=13]="L3",t[t.L4=16]="L4"}(i||(i={})),function(t){t[t.L1=76]="L1",t[t.L1Alt=76]="L1Alt",t[t.L2=79]="L2",t[t.L3=82]="L3",t[t.L4=85]="L4"}(r||(r={})),function(t){t[t.LightMode=1]="LightMode",t[t.DarkMode=.23]="DarkMode"}(o||(o={}));const f=(0,u.hi)(l.yv,(t=>{const e=(0,l.q2)(t);return new a.h(e,e,e,1).toStringHexRGB()})),p=t=>(0,c.uZ)((0,s.$X)(f,l.Dk)(t),0,(0,l.yv)(t).length-1),g=(0,d.K2)(l.MY,l.jW,l.hD),v=(0,d.K2)((0,s.IH)(f,l.Dk),g),m=t=>{const e=.14,n=new a.h(e,e,e,1);return(0,u.hi)(l.yv,n.toStringHexRGB())(t)},b=(0,d.vm)(h((0,u.qZ)((0,s.$X)(p,l.Dk),l.yv),(0,u.Qs)(l.yv)(0,(0,s.$X)(m,(0,s.Jp)(l.Dk,5))))),y=(0,d.vm)(h((0,u.qZ)(p,l.yv),(0,u.Qs)(l.yv)(0,(0,s.$X)(m,(0,s.Jp)(l.Dk,4))))),w=(0,d.vm)(h((0,u.qZ)((0,s.IH)(p,l.Dk),l.yv),(0,u.Qs)(l.yv)(l.Dk,(0,s.$X)(m,(0,s.Jp)(l.Dk,3))))),x=((0,d.vm)(h((0,u.qZ)(f,l.yv),(0,u.Qs)(l.yv)(0,(0,s.$X)(m,(0,s.Jp)(l.Dk,3))))),(0,d.vm)(h((0,u.qZ)(v,l.yv),(0,u.Qs)(l.yv)(g,(0,s.$X)(m,(0,s.Jp)(l.Dk,2))))),(0,d.vm)(h((0,u.qZ)((0,s.IH)(v,l.Dk),l.yv),(0,u.Qs)(l.yv)((0,s.IH)(g,l.Dk),(0,s.$X)(m,l.Dk)))),(0,d.vm)(h((0,u.qZ)((0,s.IH)(v,(0,s.Jp)(l.Dk,2)),l.yv),(0,u.Qs)(l.yv)((0,s.IH)(g,(0,s.Jp)(l.Dk,2)),m))))},9739:function(t,e,n){"use strict";n.d(e,{BD:function(){return g},Ce:function(){return f},Ik:function(){return a},Qs:function(){return d},Tq:function(){return p},Yc:function(){return l},hi:function(){return c},iC:function(){return v},qZ:function(){return u}});var i,r=n(92531),o=n(33390),s=n(8340);function a(t,e){return n=>{if(!(0,s.Kf)(e))return-1;const i=(0,r.DO)(t,n),o=i.indexOf(e);return-1!==o?o:i.findIndex((t=>(0,s.Kf)(t)&&(0,s.ET)(e,t)))}}function c(t,e){return n=>{const i=(0,r.DO)(t,n),o=(0,r.DO)(e,n),c=a(i,o)(n);let l;if(-1!==c)return c;try{l=(0,s.sF)(o)}catch(t){l=-1}return-1===l?0:i.map(((t,e)=>({luminance:(0,s.sF)(t),index:e}))).reduce(((t,e)=>Math.abs(e.luminance-l)<Math.abs(t.luminance-l)?e:t)).index}}function l(t){return(0,s.sF)((0,o.Cz)(t))<=(-.1+Math.sqrt(.21))/2}function u(t,e){return"function"==typeof t?n=>e(n)[(0,s.uZ)(t(n),0,e(n).length-1)]:e[(0,s.uZ)(t,0,e.length-1)]}function d(t){return(e,n)=>i=>u(l(i)?(0,r.DO)(n,i):(0,r.DO)(e,i),t(i))}function h(t,e,n=0,i=t.length-1){if(i===n)return t[n];const r=Math.floor((i-n)/2)+n;return e(t[r])?h(t,e,n,r):h(t,e,r+1,i)}function f(t){return e=>n=>i=>o=>a=>{const c=(0,r.DO)(t,a),l=(0,r.DO)(e,a),u=l.length,d=(0,s.uZ)(n(c,l,a),0,u-1),f=i(d,l,a);const p=[].concat(l),g=u-1;let v=d;return-1===f&&(p.reverse(),v=g-v),h(p,(function(t){return o((0,s.$p)(c,t))}),v,g)}}function p(t,e,n){return c(e,t)(n)}function g(t){return c(o.yv,(0,o.Cz)(t))(t)}function v(t){return e=>e>=t}!function(t){t.neutral="neutral",t.accent="accent"}(i||(i={}))},33390:function(t,e,n){"use strict";n.d(e,{Cz:function(){return o},Db:function(){return c},Dk:function(){return w},IU:function(){return m},MY:function(){return l},Uy:function(){return b},VQ:function(){return h},bu:function(){return a},fq:function(){return g},gs:function(){return v},hD:function(){return d},jW:function(){return u},q2:function(){return y},qD:function(){return p},y$:function(){return f},yv:function(){return s}});var i=n(92531);function r(t){return e=>e&&void 0!==e[t]?e[t]:i.ZP[t]}const o=r("backgroundColor"),s=(r("accentBaseColor"),r("cornerRadius"),r("elevatedCornerRadius"),r("neutralPalette")),a=(r("accentPalette"),r("contrast"),r("designUnit"),r("baseHeightMultiplier"),r("baseHorizontalSpacingMultiplier"),r("direction"),r("outlineWidth")),c=r("focusOutlineWidth"),l=(r("disabledOpacity"),r("accentFillRestDelta"),r("accentFillHoverDelta"),r("accentFillActiveDelta"),r("accentFillFocusDelta"),r("accentFillSelectedDelta"),r("accentForegroundRestDelta"),r("accentForegroundHoverDelta"),r("accentForegroundActiveDelta"),r("accentForegroundFocusDelta"),r("neutralFillRestDelta")),u=r("neutralFillHoverDelta"),d=r("neutralFillActiveDelta"),h=r("neutralFillFocusDelta"),f=r("neutralFillSelectedDelta"),p=(r("neutralFillInputRestDelta"),r("neutralFillInputHoverDelta"),r("neutralFillInputActiveDelta"),r("neutralFillInputFocusDelta"),r("neutralFillInputSelectedDelta"),r("neutralFillStealthRestDelta")),g=r("neutralFillStealthHoverDelta"),v=r("neutralFillStealthActiveDelta"),m=r("neutralFillStealthFocusDelta"),b=r("neutralFillStealthSelectedDelta"),y=(r("neutralFillToggleHoverDelta"),r("neutralFillToggleActiveDelta"),r("neutralFillToggleFocusDelta"),r("baseLayerLuminance")),w=r("neutralFillCardDelta");r("neutralForegroundDarkIndex"),r("neutralForegroundLightIndex"),r("neutralForegroundHoverDelta"),r("neutralForegroundActiveDelta"),r("neutralForegroundFocusDelta"),r("neutralDividerRestDelta"),r("neutralOutlineRestDelta"),r("neutralOutlineHoverDelta"),r("neutralOutlineActiveDelta"),r("fontWeight"),r("neutralOutlineFocusDelta")},69393:function(t,e,n){"use strict";n.d(e,{YP:function(){return h}});var i=n(79022);function r(t,...e){return n=>e.reduce(((t,e,i)=>t.replace(new RegExp(`\\{${i}\\}`,"g"),e(n))),t)}function o(t){return(0,i.Z)(t)?`${t} !important`:e=>o(t(e))}var s=n(42166),a=n(74626),c=n(39619);function l(){return(0,c.N)()&&(window.matchMedia("(forced-colors: none)").matches||window.matchMedia("(forced-colors: active)").matches)}var u=n(33390);const d="@media (-ms-high-contrast:active)";var h;!function(t){t.text="WindowText",t.forcedColorLink="LinkText",t.msLink="-ms-hotlight",t.disabledText="GrayText",t.selectedText="HighlightText",t.selectedBackground="Highlight",t.buttonText="ButtonText",t.buttonBackground="ButtonFace",t.background="Window"}(h||(h={}));const f={"-ms-high-contrast-adjust":"none"};const p=l()?"LinkText !important":"-ms-hotlight !important";Object.assign({background:h.buttonBackground,border:"none",color:h.buttonText,fill:h.buttonText},f),Object.assign({background:h.buttonBackground,"border-color":h.buttonText,color:h.buttonText,fill:h.buttonText},f),Object.assign({background:h.selectedBackground,"border-color":h.selectedBackground,color:h.selectedText,fill:h.selectedText},f),h.background,o(h.buttonBackground),o(h.disabledText),o(h.disabledText),o(h.buttonBackground),o(h.disabledText),o(h.disabledText),o(h.disabledText),o(h.disabledText),o(h.disabledText),o(h.disabledText),h.buttonText,r("0 0 0 {0} inset {1}",(0,s.a)(u.bu),(()=>h.buttonText)),r("0 0 0 2px Background, 0 0 0 {0} {1}",(0,s.a)((0,a.IH)(u.Db,2)),(()=>h.buttonText)),o(h.buttonText),r("0 0 0 {0} inset {1}",(0,s.a)(u.Db),(()=>h.buttonBackground)),h.selectedBackground,h.selectedText,h.selectedText,h.selectedText,h.selectedBackground,h.selectedBackground,h.selectedBackground,o(h.text),o(h.text),o(h.buttonText),o(h.buttonText),o(h.selectedText),o(h.selectedText),o(h.selectedBackground),o(h.selectedBackground),r("{0} solid {1}",(0,s.a)(u.bu),(()=>h.text)),h.buttonText,h.buttonBackground,r("0 0 0 {0} inset {1}",(0,s.a)(u.bu),(()=>p)),h.background,h.buttonText,h.buttonBackground,h.selectedText,h.selectedBackground,h.buttonBackground,h.selectedText,h.selectedBackground,h.selectedText},74626:function(t,e,n){"use strict";function i(t){return(...e)=>n=>{const i=e[0];let r="function"==typeof i?i(n):i;for(let i=1;i<e.length;i++){const o=e[i];r=t(r,"function"==typeof o?o(n):o)}return r}}n.d(e,{$X:function(){return c},IH:function(){return a},Jp:function(){return l}});const r=i(((t,e)=>t+e)),o=i(((t,e)=>t-e)),s=i(((t,e)=>t*e));i(((t,e)=>t/e));function a(...t){return r.apply(this,t)}function c(...t){return o.apply(this,t)}function l(...t){return s.apply(this,t)}},42166:function(t,e,n){"use strict";n.d(e,{a:function(){return o}});var i=n(2619);const r=function(t="px"){return e=>e+t}();function o(t){return(0,i.Z)(t)?e=>r(t(e)):r(t)}},20654:function(t,e,n){"use strict";var i=n(39619),r=n(80329);e.Z=new class{constructor(){this._breakpoints=r.R1,this._defaultBreakpoint=0,this.subscriptions=[],this.update=()=>{const t=(0,i.N)()?(0,r.wk)(window.innerWidth,this._breakpoints):this.defaultBreakpoint;this.breakpoint!==t&&(this.breakpoint=t,this.notifySubscribers(this.breakpoint)),this.openRequestAnimationFrame=!1},this.currentBreakpoint=()=>this.breakpoint,this.requestFrame=()=>{this.openRequestAnimationFrame||(this.openRequestAnimationFrame=!0,window.requestAnimationFrame(this.update))},(0,i.N)()?(this.breakpoint=(0,r.wk)(window.innerWidth,this._breakpoints),window.addEventListener("resize",this.requestFrame)):this.breakpoint=this.defaultBreakpoint}get breakpoints(){return this._breakpoints}set breakpoints(t){this._breakpoints=t,this.update()}get defaultBreakpoint(){return this._defaultBreakpoint}set defaultBreakpoint(t){this._defaultBreakpoint=t,this.update()}subscribe(t){this.subscriptions.includes(t)||this.subscriptions.push(t)}unsubscribe(t){this.subscriptions=this.subscriptions.filter((e=>t!==e))}notifySubscribers(t){this.subscriptions.forEach((e=>{e(t)}))}}},80329:function(t,e,n){"use strict";n.d(e,{R1:function(){return i},wk:function(){return r}});const i=[0,540,768,1084,1400,1779];function r(t,e=i){for(let n=e.length-1;n>=0;n--)if(t>=e[n])return n}},34757:function(){var t;!function(t){var e=function(){function t(){}return t.getGlobal=function(){return"undefined"!=typeof globalThis&&globalThis?globalThis:"undefined"!=typeof self&&self?self:"undefined"!=typeof window&&window?window:null},t.hasWindow=function(){return Boolean("object"==typeof window&&window)},t.getGlobalInst=function(t){if("window"===t&&this.hasWindow())return window;var e=this.getGlobal();return e&&e[t]?e[t]:null},t}();t.getGlobalWindow=e}(t||(t={})),function(t){var e=function(){function t(){}return t.trackerConfig={useTelemetryService:!1,selectorMethod:".",directFunctionCall:!0,impressionIdParamName:"",publisherName:"msnperegrine"},t}();t.Config=e}(t||(t={})),t||(t={}),function(t){var e=function(){this.element=null,this.trackBeacons=[]};t.VideoTrackEvents=e}(t||(t={})),function(t){var e=function(){function t(){}return t.appinsights={stagingConnectionString:"9b1500b8-3f89-4126-8af8-cd9020439a04",prodConnectionString:"922c1827-2168-46e4-b953-34fef5deff39"},t.msn={videoViewIdMacro:"VIDEO_VIEW_ID"},t}();t.Constants=e}(t||(t={})),function(t){t._window=t.getGlobalWindow.getGlobalInst("window");var e=function(){function e(){}return e.isDebug=function(t){return"true"===this.getQueryParam("na-debug",t.location.search)},e.getRid=function(e){if(t.config.publisherName.indexOf("msn")>=0)for(var n=0;n<e.beacons.length;n++){if(0==e.beacons[n].url.indexOf("https://srtb.msn.com"))return this.getQueryParam("rid",e.beacons[n].url)}return e.impid},e.replaceQueryParam=function(t,e,n){var i=this.getQueryParam(t,n),r=t.concat("=").concat(i),o=t.concat("=").concat(e);return n&&n.indexOf(r)>=0?n.replace(r,o):n},e.getQueryParam=function(t,e){var n=this.getQueryParams(new RegExp("^"+this.escapeRegExp(t)+"$"),e);return n&&n.length>0?n[0].value:null},e.getQueryParams=function(t,e){return e&&e.indexOf("?")>=0&&(e=e.substring(e.indexOf("?")+1)),this.getKeyValuePairs(t,e,"&")},e.getKeyValuePairs=function(t,e,n){if(!e||!t||!t.test)return[];for(var i=[],r=e.split(n),o=0;o<r.length;o++){var s=r[o],a=s.indexOf("=");if(-1!==a){var c=s.substring(0,a).trim(),l=s.substring(a+1).trim();t.test(c)&&i.push({name:c,value:l})}}return i},e.escapeRegExp=function(t){return t?t.replace(this.regexSpecialCharsRegex,"\\$1"):t},e.consoleLog=function(t,e){this.isDebug(e)&&console.log(t)},e.JSONstringify=function(t){if("function"==typeof JSON.stringify)return JSON.stringify(t)},e.getMetaContentByName=function(e,n){try{n||(n=t._window);for(var i=n.document.getElementsByTagName("meta"),r=0;r<i.length;r++)if(i[r].getAttribute("name")===e)return i[r].getAttribute("content");return""}catch(t){return""}},e.loadScript=function(t,e){var n=document.getElementsByTagName("head")[0],i=document.createElement("script");i.type="text/javascript",i.src=t,i.onload=e,i.onreadystatechange=function(){"complete"===this.readyState&&e()},n.appendChild(i)},e.fireBeacon=function(e){e&&t._window&&t._window.Image&&((new t._window.Image).src=e)},e.addQueryParam=function(t,e,n){return t+=-1==t.indexOf("?")?"?":"&",t+=e+"="+n},e.isIE=function(t){return t.navigator.userAgent.indexOf("MSIE ")>-1},e.getCurrentScriptTag=function(t){if(!t)return null;if((e=t.document.currentScript)&&e.src)return e.src;var e,n=t.document.getElementsByTagName("script");if(n&&n.length>0&&-1!=(e=n[n.length-1]).src.indexOf("nativeads/msantracker"))return e.src;var i="",r=t.document.querySelector("script[src*='nativeads/msantracker']");return r&&(i=r.src),i},e.isMSN=function(t){return t?-1!=t.location.href.indexOf("msn.com"):null},e.isStagingEnv=function(t){if(!t)return null;var e=this.getCurrentScriptTag(t);return!!e&&-1!=e.indexOf("nativeads/staging/")},e.getPublisherName=function(t){return t?this.getQueryParam("publisherName",t):""},e.inIframe=function(){try{return t._window.self!==t._window.top}catch(t){return!0}},e.getTargetWindow=function(){return this.inIframe()?t._window.parent:t._window},e.isDocumentReady=function(t){return!!t&&(!(!t.navigator.userAgent||-1===t.navigator.userAgent.indexOf("Android")||"loaded"!=t.document.readyState)||"complete"==t.document.readyState)},e.getVideoViewId=function(){return this.getGuid().replace(/-/g,"")},e.getGuid=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}))},e.regexSpecialCharsRegex="/([.*+?^=!:${}()|[]/\\])/g",e}();t.utils=e}(t||(t={})),function(t){var e=function(){function e(){}return e.initialize=function(t){},e.logEvent=function(t,e,n){this.logEventForMSN(t,e,n)},e.logEventForMSN=function(e,n,i){if(n&&i){var r=void 0;this.elementsToTrack&&e&&this.elementsToTrack[e]&&(r=this.elementsToTrack[e]),i.publishername=t.config.publisherName;var o=this.targetWindow.btoa(t.utils.JSONstringify(i)).substring(0,1800),s="https://srtb.msn.com/log?r="+r+"&n="+n+"&d="+encodeURIComponent(o);t.utils.fireBeacon(s),i.rid=r,t.utils.consoleLog("TelemetryEvent: "+n+" "+t.utils.JSONstringify(i),this.targetWindow)}},e.setElementToTrack=function(t,e){t&&e&&(this.elementsToTrack[t]=e)},e.logAdView=function(t,e,n){var i={elementId:e,percentage:n.percentage,duration:n.duration,type:n.type,triggerType:n.triggerType,url:n.url};this.logEvent(t,"mt_view",i)},e.logVideoAdPlay=function(t,e,n){var i={elementId:e,percentage:n.percentage,duration:n.duration,type:n.type,triggerType:n.triggerType,url:n.url};this.logEvent(t,"mt_view",i)},e.logAdClick=function(t,e,n){var i={elementId:e,type:n.type,url:n.url};this.logEvent(t,"mt_click",i)},e.targetWindow=t._window,e.elementsToTrack={},e}();t.Telemetry=e}(t||(t={})),function(t){var e=function(){function e(){}return e.trackElement=function(t,n,i,r,o){var s=e.DEFAULT_PERCENTINVIEW,a=e.DEFAULT_DURATIONINVIEW;return r&&(r.percentage&&(s=r.percentage),r.duration&&(a=r.duration)),e.elementsToTrack.push({selector:t,element:n,targetWindow:i,percentInView:s,milliSecondsInView:a,viewBeacon:r,action:o}),null==e.processing&&(e.processing=setInterval(e.process,e.interval)),this},e.process=function(){try{if(e.elementsToTrack.length>0){for(var n=0;n<e.elementsToTrack.length;n++)e.appearedElements.indexOf(n)<0&&e.elementsWaitingForTimeout.indexOf(n)<0&&e.isElementInView(e.elementsToTrack[n])&&(e.elementsWaitingForTimeout.push(n),setTimeout(e.checkAfterTimeout,e.elementsToTrack[n].milliSecondsInView,e.elementsToTrack[n],n));e.elementsToTrack.length===e.appearedElements.length&&e.stopProcessing()}}catch(n){var i=e.elementsToTrack;i.length>0&&i[0]&&i[0].viewBeacon?t.Telemetry.logEvent(i[0].viewBeacon.guid,"mt_err",{msg:n.message,details:i[0].viewBeacon}):t.Telemetry.logEvent("","mt_err",{msg:n.message})}},e.checkAfterTimeout=function(t,n){e.appearedElements.indexOf(n)<0&&e.isElementInView(t)&&(e.appearedElements.push(n),t.action.call(void 0,t.selector,t.viewBeacon,t.targetWindow)),e.elementsWaitingForTimeout.splice(e.elementsWaitingForTimeout.indexOf(n),1)},e.isElementInView=function(n){var i=n.targetWindow,r=i.document;if(!n.element)return!1;var o,s=n.element,a=r.getElementsByTagName("body");a&&(o=a[0]);var c=s.getBoundingClientRect();if(!c)return!1;var l=-1===c.left?0:c.left,u=-1===c.top?0:c.top,d=s.offsetHeight,h=s.offsetWidth,f=r.elementFromPoint(l+h/2,u+d/2);if("msnperegrine"!=t.config.publisherName&&(!f||f&&s!==f&&!s.contains(f)))return!1;var p=i.innerHeight||r.clientWidth||o.clientWidth,g=i.innerWidth||r.clientHeight||o.clientHeight,v=i.pageYOffset,m=v+p,b=u+i.scrollY,y=b+d,w=i.pageXOffset,x=w+g,C=l+i.scrollX,k={pTop:v,pBottom:m,pLeft:w,pRight:x,eTop:b,eBottom:y,eLeft:C,eRight:C+h,eWidth:h,eHeight:d,percentInView:n.percentInView};return e.validateIsElementInView(k)},e.validateIsElementInView=function(t){if(t.eBottom<=t.pTop||t.eTop>=t.pBottom||t.eRight<=t.pLeft||t.eLeft>=t.pRight)return!1;var e=0,n=0,i=0,r=0;t.pTop>t.eTop&&t.pTop<t.eBottom&&(e=t.pTop-t.eTop),t.pBottom<t.eBottom&&t.pBottom>t.eTop&&(i=t.eBottom-t.pBottom),t.pLeft>t.eLeft&&t.pLeft<t.eRight&&(r=t.pLeft-t.eLeft),t.pRight<t.eRight&&t.pRight>t.eLeft&&(n=t.eRight-t.pRight);var o=r+n;return(e+i)*t.eWidth+o*t.eHeight<=t.eHeight*t.eWidth*(100-t.percentInView)/100},e.stopProcessing=function(){clearInterval(e.processing),e.processing=null},e.DEFAULT_PERCENTINVIEW=50,e.DEFAULT_DURATIONINVIEW=1e3,e.elementsToTrack=[],e.appearedElements=[],e.elementsWaitingForTimeout=[],e.processing=null,e.interval=100,e}();t.AppearUtils=e}(t||(t={})),function(t){var e=function(){function e(){}return e.trackElement=function(n,i,r,o,s,a){for(var c=t.utils.getVideoViewId(),l=[],u=0;u<o.length;u++){var d=o[u];if(e.toTrackUrlCounts=e.toTrackUrlCounts+1,"videoView"!=d.type){var h=t.utils.getQueryParam("rlink",d.url),f=decodeURIComponent(h),p=t.utils.replaceQueryParam("vi",c,f),g=encodeURIComponent(p);d.url=t.utils.replaceQueryParam("rlink",g,d.url)}l.push({selector:n,element:i,videoParenentElement:s,videoBeacon:d,action:a,targetWindow:r,percentInView:d.percentage?d.percentage:50})}return e.elementsToTrack.push({element:i,trackBeacons:l}),null==e.processing&&(e.processing=setInterval(e.process,e.interval)),i.onplay=function(){e.refreshBeaconsWhenStart(i,r)},i.onseeking=function(){e.refreshBeaconsWhenStart(i,r)},this},e.untrackElement=function(t){var n=e.getElementBeaconsIndex(e.elementsToTrack,t);n>=0&&e.elementsToTrack.splice(n,1)},e.getElementBeaconsIndex=function(t,e){if(t&&t.length>0)for(var n=0;n<t.length;n++)if(t[n].element==e)return n;return-1},e.refreshBeaconsWhenStart=function(n,i){if(n&&0==n.currentTime){var r=t.utils.getVideoViewId(),o=e.getElementBeaconsIndex(e.elementsToTrack,n);if(o>=0){for(var s=e.elementsToTrack[o].trackBeacons,a=[],c=0;c<s.length;c++){var l=s[c],u=l.videoBeacon;if("videoView"!=u.type){e.trackedBeacons.indexOf(u.url)>=0&&(e.toTrackUrlCounts=e.toTrackUrlCounts+1);var d=t.utils.getQueryParam("rlink",u.url),h=decodeURIComponent(d),f=t.utils.replaceQueryParam("vi",r,h),p=encodeURIComponent(f);u.url=t.utils.replaceQueryParam("rlink",p,u.url)}a.push({selector:l.selector,element:l.element,videoParenentElement:l.videoParenentElement,videoBeacon:u,action:l.action,targetWindow:i,percentInView:l.percentInView?l.percentInView:50})}e.elementsToTrack[o].trackBeacons=a}null==e.processing&&(e.processing=setInterval(e.process,e.interval))}},e.process=function(){try{if(e.elementsToTrack&&e.elementsToTrack.length>0){for(var n=0;n<e.elementsToTrack.length;n++)for(var i=e.elementsToTrack[n].trackBeacons,r=0;r<i.length;r++){var o=i[r];e.trackedBeacons.indexOf(o.videoBeacon.url)<0&&e.trackPlay(o)}e.toTrackUrlCounts===e.trackedBeacons.length&&e.stopProcessing()}}catch(n){var s=e.elementsToTrack;if(s&&s.length>0&&s[0].trackBeacons&&s[0].trackBeacons[0].videoBeacon){var a=s[0].trackBeacons[0].videoBeacon;t.Telemetry.logEvent(a.guid,"mt_err",{msg:n.message,details:a})}else t.Telemetry.logEvent("","mt_err",{msg:n.message})}},e.trackPlay=function(n){var i=n.element,r=n.videoBeacon.duration,o=1e3*i.currentTime,s=n.videoBeacon.type,a={targetWindow:n.targetWindow,element:n.videoParenentElement?n.videoParenentElement:n.element,percentInView:n.percentInView};t.AppearUtils.isElementInView(a)&&e.trackedBeacons.indexOf(n.videoBeacon.url)<0&&("videoView"==s?this.checkPlayedTime(i)>=r&&(n.action.call(void 0,n.selector,n.videoBeacon),e.trackedBeacons.push(n.videoBeacon.url)):(0==r&&o>r||r>0&&this.checkCurrentTime(o,r))&&(n.action.call(void 0,n.selector,n.videoBeacon),e.trackedBeacons.push(n.videoBeacon.url)))},e.checkCurrentTime=function(t,e){return t>=e-50&&t<=e+50},e.checkPlayedTime=function(t){var e=0;if(t.played&&t.played.length>0)for(var n=0;n<t.played.length;n++)e+=t.played.end(n)-t.played.start(n);return 1e3*e},e.stopProcessing=function(){clearInterval(e.processing),e.processing=null},e.elementsToTrack=[],e.trackedBeacons=[],e.processing=null,e.interval=100,e.toTrackUrlCounts=0,e}();t.PlayUtils=e}(t||(t={})),function(t){var e=function(){this.type="view",this.url="",this.guid="",this.triggerType="view",this.percentage=50,this.duration=1e3};t.ViewBeacon=e}(t||(t={})),function(t){var e=function(){this.type="videoView",this.url="",this.guid="",this.triggerType="play",this.percentage=50,this.duration=0};t.VideoBeacon=e}(t||(t={})),function(t){var e=function(){this.impid="",this.rid="",this.beacons=[]};t.TrackableElement=e}(t||(t={})),function(t){function e(e){var i=new t.TrackableElement,r=new URLSearchParams(t.scriptSrc);if(r){var o="";if(r.has("v")){if(o=r.get("v"),r.has("d")){var s=r.get("d");t.Telemetry.logEvent("","mt_info",{msg:"Version",clientVersion:s})}var a=decodeURI(o),c=new URLSearchParams(a),l=t.config.impressionIdParamName;if(c.has(l)&&(i.impid=c.get(l)),""!=i.impid){c.has("rId")&&(i.rid=c.get("rId"));var u=new t.ViewBeacon;u.url=a,i.beacons=[u],n([i],e,!1)}else t.Telemetry.logEvent("","mt_err",{msg:"view_id is empty or null",url:a})}else t.Telemetry.logEvent("","mt_err",{msg:"viewability beacon not present",url:t.scriptSrc})}else t.Telemetry.logEvent("","mt_err",{msg:"invalid script src url for params",url:t.scriptSrc})}function n(e,n,r){void 0===r&&(r=!0),!e&&e&&e.length>0?t.Telemetry.logEvent("","mt_err",{msg:"elements null or empty"}):e.forEach((function(e){var r=t.utils.getGuid();if(e.rid||(e.rid=t.utils.getRid(e)),t.Telemetry.setElementToTrack(r,e.rid),e.beacons&&e.impid){var o=e.impid;"msn"==t.config.publisherName&&(o="bing-"+e.impid);var s=!0;e.element&&(s=!1);var a=t.config.selectorMethod+""+o;if(s){var c=n.document.querySelector(a);c&&(e.element=c)}if(e.element)i(e,a,n,o,r);else{t.Telemetry.logEvent(r,"mt_err",{msg:"No element with id or element, setting an interval to wait",id:o,selector:a});var l=setInterval((function(){var s=n.document.querySelector(a);s&&(t.Telemetry.logEvent(r,"mt_info",{msg:"Found element after retry",element:a}),clearInterval(l),e.element=s,i(e,a,n,o,r))}),100)}}else t.Telemetry.logEvent(r,"mt_err",{msg:"Beacon or imp id is empty or null"})}))}function i(e,n,i,r,o){var s=e.beacons,a=[];s&&s.length>0&&s.forEach((function(s){t.Telemetry.logEvent(o,"mt_info",{msg:"Beacon Information",id:r,beaconUrl:s.url}),s.guid=o,"play"==s.triggerType?a.push(s):function(e,n,i,r){var o=this;if(e.triggerType&&"view"!==e.triggerType)if("click"===e.type){n.element.addEventListener("click",(function(n){t.Telemetry.logAdClick(e.guid,o.id,e),t.utils.fireBeacon(e.url)}))}else t.Telemetry.logEvent(e.guid,"mt_err",{msg:"Unsupported tracker event",impid:n.impid});else{var s=e;t.AppearUtils.trackElement(i,n.element,r,s,(function(e,n,i){var r=e.split(t.config.selectorMethod)[1];t.Telemetry.logAdView(n.guid,r,n),t.utils.fireBeacon(n.url),console.log("[MSAN view beacon fired] "+n.url)}))}}(s,e,n,i)})),a&&a.length>0&&function(e,n,i,r){var o=e;t.PlayUtils.trackElement(i,n.element,r,o,n.videoParenentElement,(function(e,n){var i=e.split(t.config.selectorMethod)[1];t.Telemetry.logVideoAdPlay(n.guid,i,n),t.utils.fireBeacon(n.url),console.log("[MSAN video beacon fired] "+n.url)}))}(a,e,n,i)}t.config=null,t.isInitialized=!1,t.scriptSrc="",t.Initialize=function(){t.config=t.Config.trackerConfig,t.Telemetry.initialize(t._window),"msnperegrine"!=t.config.publisherName&&t.Telemetry.logEvent("","mt_info",{msg:"MSANTracker JS Inserted"});var n=t.utils.getCurrentScriptTag(t._window);if(n){if(t.scriptSrc=n,!t.isInitialized&&!t.config.directFunctionCall)var i=setInterval((function(){t.utils.isDocumentReady(t._window)&&(t.isInitialized=!0,t.Telemetry.logEvent("","mt_info",{msg:"Document in ready state"}),clearInterval(i),e(t._window))}),100)}else"msnperegrine"!=t.config.publisherName&&t.Telemetry.logEvent("","mt_err",{msg:"Cannot find current script src"})},t.ProcessTrackers=e,t.TrackElements=function(e,i,r){if(void 0===r&&(r=!0),t.Telemetry.logEvent("","mt_info",{msg:"MSANTracker function called",directCall:r}),e||t.Telemetry.logEvent("","mt_err",{msg:"elements null or empty"}),!t.isInitialized&&r)var o=setInterval((function(){t.utils.isDocumentReady(i)&&(t.isInitialized=!0,t.Telemetry.logEvent("","mt_info",{msg:"Document in ready state"}),clearInterval(o),n(e,i,r))}),100);else r&&n(e,i,r)}}(t||(t={})),t._window&&(t.Initialize(),t._window.MSANTracker=t)},14004:function(t){t.exports='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.5 2.75a.75.75 0 00-1.5 0V9H2.75a.75.75 0 000 1.5H9v6.25a.75.75 0 001.5 0V10.5h6.25a.75.75 0 000-1.5H10.5V2.75z"></path></svg>'},53095:function(t){t.exports='<svg width="12" height="12" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg"><path d="M5.7 3.28A1 1 0 004 4v4.02a1 1 0 001.7.7l2.04-2a1 1 0 000-1.42l-2.04-2z"></path></svg>'},36362:function(t){t.exports='<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M2.59 2.72l.06-.07a.5.5 0 01.63-.06l.07.06L8 7.29l4.65-4.64a.5.5 0 01.7.7L8.71 8l4.64 4.65c.18.17.2.44.06.63l-.06.07a.5.5 0 01-.63.06l-.07-.06L8 8.71l-4.65 4.64a.5.5 0 01-.7-.7L7.29 8 2.65 3.35a.5.5 0 01-.06-.63l.06-.07-.06.07z"></path></svg>'},41067:function(t){t.exports='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M4.09 4.22l.06-.07a.5.5 0 01.63-.06l.07.06L10 9.29l5.15-5.14a.5.5 0 01.63-.06l.07.06c.18.17.2.44.06.63l-.06.07L10.71 10l5.14 5.15c.18.17.2.44.06.63l-.06.07a.5.5 0 01-.63.06l-.07-.06L10 10.71l-5.15 5.14a.5.5 0 01-.63.06l-.07-.06a.5.5 0 01-.06-.63l.06-.07L9.29 10 4.15 4.85a.5.5 0 01-.06-.63l.06-.07-.06.07z"></path></svg>'},22091:function(t){t.exports='<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M4.4 4.55l.07-.08a.75.75 0 01.98-.07l.08.07L12 10.94l6.47-6.47a.75.75 0 111.06 1.06L13.06 12l6.47 6.47c.27.27.3.68.07.98l-.07.08a.75.75 0 01-.98.07l-.08-.07L12 13.06l-6.47 6.47a.75.75 0 01-1.06-1.06L10.94 12 4.47 5.53a.75.75 0 01-.07-.98l.07-.08-.07.08z"></path></svg>'},14487:function(t){t.exports='<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 17a2 2 0 110 4 2 2 0 010-4zm7 0a2 2 0 110 4 2 2 0 010-4zM5 17a2 2 0 110 4 2 2 0 010-4zm7-7a2 2 0 110 4 2 2 0 010-4zm7 0a2 2 0 110 4 2 2 0 010-4zM5 10a2 2 0 110 4 2 2 0 010-4zm7-7a2 2 0 110 4 2 2 0 010-4zm7 0a2 2 0 110 4 2 2 0 010-4zM5 3a2 2 0 110 4 2 2 0 010-4z"></path></svg>'},11310:function(t){t.exports='<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M5 8a1 1 0 11-2 0 1 1 0 012 0zm4 0a1 1 0 11-2 0 1 1 0 012 0zm3 1a1 1 0 100-2 1 1 0 000 2z"></path></svg>'},7137:function(t){t.exports='<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M10.06 2.45a1.5 1.5 0 00-2.39.35L5.65 6.59l-2.8.94a.5.5 0 00-.2.82l2.14 2.15-2.64 2.65L2 14l.85-.15 2.65-2.64 2.15 2.14a.5.5 0 00.82-.2l.94-2.8 3.78-2.03a1.5 1.5 0 00.35-2.38l-3.48-3.5z"></path></svg>'},23423:function(t){t.exports='<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M10 2.75a7.25 7.25 0 015.63 11.82l4.9 4.9a.75.75 0 01-.98 1.13l-.08-.07-4.9-4.9A7.25 7.25 0 1110 2.75zm0 1.5a5.75 5.75 0 100 11.5 5.75 5.75 0 000-11.5z"></path></svg>'},77669:function(t){t.exports='<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12.01 2.25c.74 0 1.47.1 2.18.25.32.07.55.33.59.65l.17 1.53a1.38 1.38 0 001.92 1.11l1.4-.61c.3-.13.64-.06.85.17a9.8 9.8 0 012.2 3.8c.1.3 0 .63-.26.82l-1.24.92a1.38 1.38 0 000 2.22l1.24.92c.26.19.36.52.27.82a9.8 9.8 0 01-2.2 3.8.75.75 0 01-.85.17l-1.4-.62a1.38 1.38 0 00-1.93 1.12l-.17 1.52a.75.75 0 01-.58.65 9.52 9.52 0 01-4.4 0 .75.75 0 01-.57-.65l-.17-1.52a1.38 1.38 0 00-1.93-1.11l-1.4.62a.75.75 0 01-.85-.18 9.8 9.8 0 01-2.2-3.8c-.1-.3 0-.63.27-.82l1.24-.92a1.38 1.38 0 000-2.22l-1.24-.92a.75.75 0 01-.28-.82 9.8 9.8 0 012.2-3.8c.23-.23.57-.3.86-.17l1.4.62c.4.17.86.15 1.25-.08.38-.22.63-.6.68-1.04l.17-1.53a.75.75 0 01.58-.65c.72-.16 1.45-.24 2.2-.25zm0 1.5c-.45 0-.9.04-1.35.12l-.11.97a2.89 2.89 0 01-4.02 2.33l-.9-.4A8.3 8.3 0 004.28 9.1l.8.59a2.88 2.88 0 010 4.64l-.8.59a8.3 8.3 0 001.35 2.32l.9-.4a2.88 2.88 0 014.02 2.32l.1.99c.9.15 1.8.15 2.7 0l.1-.99a2.88 2.88 0 014.02-2.32l.9.4a8.3 8.3 0 001.36-2.32l-.8-.59a2.88 2.88 0 010-4.64l.8-.59a8.3 8.3 0 00-1.35-2.32l-.9.4a2.88 2.88 0 01-4.02-2.32l-.11-.98c-.45-.08-.9-.11-1.34-.12zM12 8.25a3.75 3.75 0 110 7.5 3.75 3.75 0 010-7.5zm0 1.5a2.25 2.25 0 100 4.5 2.25 2.25 0 000-4.5z"></path></svg>'},1729:function(t,e,n){"use strict";n.d(e,{Tn:function(){return u},a5:function(){return h},Uk:function(){return d}});var i=n(33940),r=n(42590),o=n(32229),s=n(13261),a=n(54721);const c=n(78923).i` ${a.W} @supports not (contain:content){:host{overflow:hidden}}:host([size="_1x_2y"]){--card-height:304px;--card-width:300px}:host([size="_2x_2y"]){--card-height:304px;--card-width:612px}:host([size="_1x_1y"]){--card-height:146px;--card-width:300px}`;var l=n(87516);class u extends o.u{constructor(){super(),this.cardFillColor="#222",this.size="_1x_2y"}}(0,i.gn)([r.Lj],u.prototype,"size",void 0);const d=u.compose({name:`${l.j.prefix}-article-card`,template:s.X,styles:c,shadowOptions:{delegatesFocus:!0}}),h=c},77338:function(t,e,n){"use strict";n.d(e,{d:function(){return p}});var i=n(33940),r=n(28904),o=n(99452),s=n(42590),a=n(97186),c=n(2696),l=n(46892),u=n(1729),d=n(25529),h=n(24838),f=n(7463);class p extends r.H{constructor(){super(...arguments),this.headingLevel=2,this.title="",this.isArticleCard=()=>this.parent instanceof u.Tn,this.isFlexArticleCard=()=>this.parent instanceof f.W}connectedCallback(){super.connectedCallback(),this.imagePosition||(this.imagePosition="end"),this.parent=(0,l.T)(this),this.parent&&(this.notifier=o.y$.getNotifier(this.parent),this.notifier.subscribe(this,"cardFillColor"),this.notifier.subscribe(this,"size"),this.handleChange(this.parent,"cardFillColor"),this.handleChange(this.parent,"size"),this.addEventListener("mouseover",this.mouseOver),this.addEventListener("mouseout",this.mouseOut),this.addEventListener("focusin",this.focusIn),this.addEventListener("focusout",this.focusOut))}disconnectedCallback(){super.disconnectedCallback(),this.notifier&&(this.notifier.unsubscribe(this,"cardFillColor"),this.notifier.unsubscribe(this,"size"),this.removeEventListener("mouseover",this.mouseOver),this.removeEventListener("mouseout",this.mouseOut),this.removeEventListener("focusin",this.focusIn),this.removeEventListener("focusout",this.focusOut))}handleChange(t,e){switch(this.half="_1x_1y"===t.size,e){case"cardFillColor":if(!t.cardFillColor)break;this.gradientColor=t.cardFillColor,this.gradientVariables=this.generateGradientVariables();break;case"size":this.wide="_2x_2y"===t.size}}handleContentCardLinkClick(t){return this.handleContentCardClickOverride?this.handleContentCardClickOverride(t):(this.$emit("link-invoked",t),!0)}generateGradientVariables(){const{r:t,g:e,b:n}=(0,a.in)(this.gradientColor),i=new c.h(t,e,n,(0,d._)("image",this.childElements)?this.half?.6:.8:.6).toStringWebRGBA(),r=new c.h(t,e,n,0).toStringWebRGBA();return`--gradient-mid-color: ${i}; --gradient-color: ${this.gradientColor}; --radial-gradient-color: ${r}; -webkit-tap-highlight-color: transparent;`}mouseOver(){this.setExpanded(!0),this.hasHover=!0}mouseOut(){this.hasFocus||this.setExpanded(!1),this.hasHover=!1}focusIn(){this.setExpanded(!0),this.hasFocus=!0}focusOut(){this.hasHover||this.setExpanded(!1),this.hasFocus=!1}filteredContentIndicator(){return Array.isArray(this.contentIndicatorNodes)?this.contentIndicatorNodes.filter((t=>t instanceof h.K)):[]}setExpanded(t){this.filteredContentIndicator().forEach((e=>e.expanded=t))}}(0,i.gn)([(0,s.Lj)({attribute:"heading-level",mode:"fromView",converter:s.Id})],p.prototype,"headingLevel",void 0),(0,i.gn)([s.Lj],p.prototype,"href",void 0),(0,i.gn)([s.Lj],p.prototype,"target",void 0),(0,i.gn)([(0,s.Lj)({attribute:"image-position"})],p.prototype,"imagePosition",void 0),(0,i.gn)([(0,s.Lj)({attribute:"immersive-card",mode:"boolean"})],p.prototype,"immersiveCard",void 0),(0,i.gn)([o.LO],p.prototype,"anchorTelemetryTag",void 0),(0,i.gn)([o.LO],p.prototype,"childElements",void 0),(0,i.gn)([o.LO],p.prototype,"wide",void 0),(0,i.gn)([o.LO],p.prototype,"half",void 0),(0,i.gn)([o.LO],p.prototype,"notifier",void 0),(0,i.gn)([o.LO],p.prototype,"parent",void 0),(0,i.gn)([o.LO],p.prototype,"gradientColor",void 0),(0,i.gn)([o.LO],p.prototype,"gradientVariables",void 0),(0,i.gn)([o.LO],p.prototype,"contentIndicatorNodes",void 0),(0,i.gn)([o.LO],p.prototype,"title",void 0),(0,i.gn)([o.LO],p.prototype,"handleContentCardClickOverride",void 0)},27041:function(t,e,n){"use strict";n.d(e,{gK:function(){return y},MJ:function(){return g},x8:function(){return b}});var i=n(78923),r=n(87516),o=n(77338),s=n(41724),a=n(66779),c=n(49218),l=n(99026),u=n(95185),d=n(47548),h=n(25529);const f=t=>e=>e&&(0,h._)(t,e.childElements)?c.dy`<div id="${t}" class="${t}" part="${t}"><slot name="${t}"></slot></div>`:"",p=c.dy`<template role="article" ?immersive-card="${t=>t.isArticleCard()||t.isFlexArticleCard()}" ${(0,l.N)({property:"childElements",filter:(0,u.R)()})}><div part="article" :classList="article${t=>t&&(0,h._)("image",t.childElements)?` image-pos-${t.imagePosition}`:" no-image"}${t=>t.wide?" wide":""} ${t=>t.half?t&&(0,h._)("abstract",t.childElements)?" has-abstract half":"half":""} ${t=>(0,h._)("content-indicator",t.contentIndicatorNodes)?"":" no-content-indicator"} ${t=>t.isFlexArticleCard()?" flex-card":""}" style="${t=>t.gradientVariables}">${t=>"start"===t.imagePosition?f("image")(t):""}<div class="text" part="text"><div class="gradient ${t=>"rtl"===a.o.getValueFor(t)?"gradient-rtl":"gradient-ltr"}" part="gradient"></div>${t=>t&&(0,h._)("start",t.childElements)?c.dy`<slot name="start" part="start"></slot>`:""} ${f("attribution")}<a id="heading" class="heading" part="heading" href="${t=>t.href?t.href:void 0}" target=${t=>t.target?t.target:void 0} @click=${(t,e)=>t.handleContentCardLinkClick(e.event)} data-t="${t=>t.anchorTelemetryTag}" title="${t=>t.title}"><span role="heading" aria-level="${t=>t.headingLevel}"><slot></slot></span></a>${f("abstract")}<div class="actions" part="actions"><slot name="start-action"></slot><slot name="end-action"></slot></div></div>${t=>"end"===t.imagePosition?f("image")(t):""} ${t=>t&&(0,h._)("hide-story",t.childElements)?c.dy`<div class="hide-story-wrapper" part="hide-story-wrapper">${f("hide-story")}</div>`:""}<slot name="content-indicator" ${(0,d.Q)("contentIndicatorNodes")}></slot></div></template>`,g=o.d.compose({name:`${r.j.prefix}-article`,template:p,styles:s.gK,shadowOptions:{delegatesFocus:!0}}),v=i.i`
:host *[part=heading] {
    font-family: Segoe UI Variable Display Semibold, Segoe UI, Segoe UI Midlevel, Arial, Sans-Serif;
}`,m=i.i`
:host *[part=heading] {
    letter-spacing: -0.02em;
}`,b=o.d.compose({name:`${r.j.prefix}-mstf-article`,template:p,styles:i.i`
        ${s.gK}
        ${v}
        ${m}
    `,shadowOptions:{delegatesFocus:!0}}),y=s.gK},24838:function(t,e,n){"use strict";n.d(e,{K:function(){return a}});var i=n(33940),r=n(28904),o=n(42590),s=n(99452);class a extends r.H{constructor(){super(...arguments),this.expanded=!1}defaultItemsChanged(){this.$fastController.isConnected&&(this.shouldanimate=this.defaultItems.length>0)}}(0,i.gn)([(0,o.Lj)({mode:"boolean"})],a.prototype,"expanded",void 0),(0,i.gn)([s.LO],a.prototype,"defaultItems",void 0),(0,i.gn)([(0,o.Lj)({mode:"boolean",attribute:"animate"})],a.prototype,"shouldanimate",void 0)},87516:function(t,e,n){"use strict";n.d(e,{Z:function(){return r},j:function(){return i}});const i=Object.freeze({prefix:"msft",shadowRootMode:"open",registry:customElements}),r=Object.freeze({prefix:"msn",shadowRootMode:"open",registry:customElements})},80620:function(t,e,n){"use strict";function i(t,e){if("undefined"==typeof window||!window.document||window.isRenderServiceEnv)return t;const n=window.trustedTypes;if(!n||!n.createPolicy)return t;const i=window.trustedTypePolicies||(window.trustedTypePolicies={});if(i[e])return i[e].createHTML(t);const r=window.trustedTypes.createPolicy(e,{createHTML:t=>t});return i[e]=r,r.createHTML(t)}n.d(e,{A:function(){return i}})},4890:function(t,e,n){"use strict";n.d(e,{E2:function(){return a},Zv:function(){return c},mX:function(){return s}});var i=n(31699),r=n(31393),o=n(80620);const s=r.K.create({trustedType:{createHTML:t=>t.indexOf("<img")>-1&&-1==t.indexOf(":src")&&(t.indexOf('src="fast-')>-1||t.indexOf("src=fast-")>-1)?t.replaceAll("src=",":src="):t}}),a=r.K.create({trustedType:{createHTML:t=>(0,o.A)(t,"base-html-policy")}});function c(){const t=navigator.userAgent.toLowerCase();t.indexOf("chrome")>-1||t.indexOf("edg")>-1?i.S.setPolicy(a):i.S.setPolicy(s)}},88393:function(t,e,n){"use strict";n.d(e,{h:function(){return o}});var i=n(83245),r=n(31699);const o=n(31393).K.create({trustedType:{createHTML:t=>i.sanitize(t,{RETURN_TRUSTED_TYPE:!0})},guards:{aspects:{[r.O.property]:{innerHTML:(t,e,n,r)=>(t,e,n,...o)=>{const s=i.sanitize(n,{RETURN_TRUSTED_TYPE:!0});r(t,e,s,...o)}}}}})},7463:function(t,e,n){"use strict";n.d(e,{W:function(){return s}});var i=n(33940),r=n(42590),o=n(32229);class s extends o.u{constructor(){super(),this.cardFillColor="#222",this.size="_1x_2y",this.expanded=!1}}(0,i.gn)([r.Lj],s.prototype,"size",void 0),(0,i.gn)([(0,r.Lj)({mode:"boolean"})],s.prototype,"expanded",void 0)},1755:function(t,e,n){"use strict";n.d(e,{hK:function(){return s},tH:function(){return w},lJ:function(){return x}});var i=n(33940),r=n(42590),o=n(55024);class s extends o.b{constructor(){super(...arguments),this.speed=1200,this.headingLevel=3}}(0,i.gn)([r.Lj],s.prototype,"speed",void 0),(0,i.gn)([(0,r.Lj)({attribute:"heading-level",mode:"fromView",converter:r.Id})],s.prototype,"headingLevel",void 0),(0,i.gn)([r.Lj],s.prototype,"heading",void 0);var a=n(49218),c=n(41472),l=n(47548),u=n(95185),d=n(93703),h=n(73873);const f=a.dy`<template>${h.h}<div class="heading-container" part="heading-container"><span class="header"><slot name="icon"></slot><slot name="heading"><span class="heading" role="heading" aria-level=${t=>t.headingLevel}>${t=>t.heading}</span><slot name="details"></slot></slot><slot name="description"></slot></span><slot name="action"></slot></div><div class="scroll-area"><div part="scroll-view" class="scroll-view" @scroll="${t=>t.scrolled()}" ${(0,c.i)("scrollContainer")}><div class="content-container" part="content-container" ${(0,c.i)("content")}><slot ${(0,l.Q)({property:"scrollItems",filter:(0,u.R)()})}></slot></div></div>${(0,d.g)((t=>"mobile"!==t.view),a.dy`<div class="scroll scroll-prev" part="scroll-prev" ${(0,c.i)("previousFlipperContainer")}><div class="scroll-action" @click="${t=>t.scrollToPrevious()}"><slot name="previous-flipper"><fluent-flipper direction="previous"></fluent-flipper></slot></div></div><div class="scroll scroll-next" part="scroll-next" ${(0,c.i)("nextFlipperContainer")}><div class="scroll-action" @click="${t=>t.scrollToNext()}"><slot name="next-flipper"><fluent-flipper></fluent-flipper></slot></div></div>`)}</div>${h.z}</template>`;var p=n(38492),g=n(94585),v=n(16549),m=n(26254);const b=n(78923).i` ${m.W} :host{--scroll-fade-next:${p.I}}.heading-container{display:grid;grid-template-columns:1fr auto;margin:0 0 12px}::slotted([slot="action"]){display:flex;align-items:flex-end}::slotted([slot="icon"]){margin-inline-end:8px}.header,.header ::slotted(h1),.header ::slotted(h2),.header ::slotted(h3),.header ::slotted(h4),.header ::slotted(h5),.header ::slotted(h6){font-size:var(--stripe-heading-font-size,${g.m});line-height:var(--stripe-heading-line-height,${g.I});font-weight:600;margin:0}::slotted([slot="description"]){font-size:${v.s};line-height:${v.v};margin:8px 0 0}`;var y=n(87516);const w=s.compose({name:`${y.j.prefix}-stripe`,template:f,styles:b}),x=b},46892:function(t,e,n){"use strict";function i(t,e){let n=e;for(;null!==n;){if(n===t)return!0;n=r(n)}return!1}function r(t){const e=t.parentElement;if(e)return e;{const e=t.getRootNode();if(e.host instanceof HTMLElement)return e.host}return null}n.d(e,{M:function(){return i},T:function(){return r}})},41314:function(t,e,n){"use strict";n.d(e,{Y:function(){return u},s:function(){return l}});var i=n(38492),r=n(78125),o=n(97186),s=n(60279),a=n(58968);const c=(0,n(9791).Z)((t=>{let e=(0,o.in)(t);if(null!==e)return e;if(e=(0,o.hg)(t),null!==e)return e;throw new Error(`${t} cannot be converted to a ColorRGBA64. Color strings must be one of the following formats: "#RGB", "#RRGGBB", or "rgb(r, g, b)"`)}));function l(t){return(0,s.hM)(c(t))}class u{constructor(t,e){this.cache=new WeakMap,this.light=t,this.dark=e}connectedCallback(t){const e=t.source,n=this.cache.get(e);n?a.H.enqueue((()=>this.attach(e,n))):this.attach(e,new d(this.light,this.dark,e))}disconnectedCallback(t){const e=this.cache.get(t.source);e&&i.I.unsubscribe(e)}attach(t,e){const n=i.I.getValueFor(t);i.I.subscribe(e),e.attach(n),this.cache.set(t,e)}}class d{constructor(t,e,n){this.light=t,this.dark=e,this.source=n,this.attached=null}handleChange(t,e){try{this.attach("default"===e.target?t.default:t.getValueFor(this.source))}catch{}}attach(t){if(window.matchMedia("(forced-colors: active)").matches)return;const e=l(t.toColorString())<=r.h.DarkMode?"dark":"light";window.__theme=e,this.attached!==this[e]&&(null!==this.attached&&this.source.$fastController.removeStyles(this.attached),this.attached=this[e],null!==this.attached&&this.source.$fastController.addStyles(this.attached))}}},25529:function(t,e,n){"use strict";function i(t,e){return e&&e.some((e=>e.slot===t))}n.d(e,{_:function(){return i}})},73873:function(t,e,n){"use strict";n.d(e,{h:function(){return o},z:function(){return r}});var i=n(49218);const r=i.dy`
<span part="end" class="end">
    <slot name="end"></slot>
</span>
`,o=i.dy`
<span part="start" class="start">
    <slot name="start"></slot>
</span>
`},41724:function(t,e,n){"use strict";n.d(e,{gK:function(){return f}});var i=n(42689),r=n(38492),o=n(53131),s=n(27782),a=n(17993),c=n(78923),l=n(27186),u=n(67739),d=n(29717),h=n(22798);c.i` .hide-story-wrapper{right:16px}.hide-story{right:-32px}:host([immersive-card]) .image{right:0}:host([immersive-card]) .image-pos-start .image{right:initial;left:0}:host([immersive-card]) .text{left:0}:host([immersive-card]) .image-pos-start .text{right:0;left:initial}:host([immersive-card]) .gradient{left:0;right:-84px}:host([immersive-card]) .wide.no-image .gradient{right:-100%}:host([immersive-card]) .image-pos-start .gradient{right:0;left:-120px}:host([immersive-card]) .wide .gradient{background:linear-gradient( 270deg,transparent 0%,var(--gradient-mid-color) 102px,var(--gradient-color) 246px )}:host([immersive-card]) .wide.image-pos-start .gradient{background:linear-gradient( 90deg,transparent 0%,var(--gradient-mid-color) 102px,var(--gradient-color) 246px )}:host([immersive-card]) .wide .heading:after{right:-100%;left:0}:host([immersive-card]) .wide.image-pos-start .heading:after{left:-100%;right:0}::slotted([slot="content-indicator"]){left:calc(var(--msft-article-padding) * 1px);pointer-events:none}:host([immersive-card]) .wide.image-pos-start ::slotted([slot="content-indicator"]){left:calc(50% + var(--msft-article-padding) * 1px)}:host([immersive-card]) .half .text{padding:16px 0 16px 16px}:host([immersive-card]) .half .image{padding:16px 16px 6px 8px}:host([immersive-card]) .half .gradient,:host([immersive-card]) .no-image.half .gradient{background:linear-gradient( 76.68deg,var(--gradient-color) 0%,var(--gradient-mid-color) 100% )}:host([immersive-card]) .no-image.half .actions{left:16px}`,c.i` .hide-story-wrapper{left:16px}.hide-story{left:-32px}:host([immersive-card]) .image{left:0}:host([immersive-card]) .image-pos-start .image{left:initial;right:0}:host([immersive-card]) .text{right:0}:host([immersive-card]) .image-pos-start .text{left:0;right:initial}:host([immersive-card]) .gradient{right:0;left:-120px}:host([immersive-card]) .wide.no-image .gradient{left:-100%}:host([immersive-card]) .image-pos-start .gradient{left:0;right:-120px}:host([immersive-card]) .wide .gradient{background:linear-gradient( 90deg,transparent 0%,var(--gradient-mid-color) 102px,var(--gradient-color) 246px )}:host([immersive-card]) .wide.image-pos-start .gradient{background:linear-gradient( 270deg,transparent 0%,var(--gradient-mid-color) 102px,var(--gradient-color) 246px )}::slotted([slot="content-indicator"]){right:calc(var(--msft-article-padding) * 1px)}:host([immersive-card]) .wide.image-pos-start ::slotted([slot="content-indicator"]){right:calc(50% + var(--msft-article-padding) * 1px)}:host([immersive-card]) .half .text{padding:16px 16px 16px 0px}:host([immersive-card]) .half .image{padding:16px 8px 16px 16px}:host([immersive-card]) .half .gradient,:host([immersive-card]) .no-image.half .gradient{background:linear-gradient( 294deg,var(--gradient-color) 0%,var(--gradient-mid-color) 100% )}:host([immersive-card]) .no-image.half .actions{right:16px}`;const f=c.i` ${(0,l.j)("block")} :host{--msft-article-padding:16;--msft-flex-button-padding:40;--msft-card-font-color:${i.C};width:100%;height:100%;outline:none}.article{position:relative;overflow:hidden;display:grid;grid-template-columns:1fr auto;column-gap:calc(var(--msft-article-padding) * 1px)}.heading,.abstract{display:-webkit-box;overflow:hidden;-webkit-box-orient:vertical}.heading{hyphens:auto;font-weight:600;text-decoration:none;color:var(--msft-card-font-color);font-size:var(--msft-article-heading-font-size,16px);line-height:var(--msft-article-heading-line-height,24px);-webkit-line-clamp:var(--heading-max-lines,3);margin:0;outline:0}:host([immersive-card]) .article.half{display:grid;grid-template-columns:1fr auto;grid-gap:0;width:100%;height:100%;overflow:visible;position:relative;background:#FFFFFF}:host([immersive-card]) .half .text{grid-column:1/2;height:100%;position:unset}:host([immersive-card]) .half .image{grid-column:2/3;width:82px;height:82px;position:relative;z-index:1;pointer-events:none}:host([immersive-card]) .half .image ::slotted(img){border-radius:var(--half-card-image-corner-radius,6px)}:host([immersive-card]) .half .actions{width:100%;padding:16px;box-sizing:border-box;position:absolute;bottom:0;left:0;right:0}:host([immersive-card]) .article.no-image.half{display:flex;grid-template-columns:none}:host([immersive-card]) .no-image.half .text{padding:16px}:host([immersive-card]) .no-image.half .actions{padding:0}:host([immersive-card]) .half .gradient,:host([immersive-card]) .no-image.half .gradient{position:absolute;top:0;bottom:0;left:0;right:0}:host([immersive-card]:hover) .half .gradient,:host([immersive-card]:hover) .no-image.half .gradient{background:var(--gradient-color);opacity:0.4;z-index:-1}:host([immersive-card]:hover) .article.half,:host([immersive-card]:hover) .no-image.half{background:transparent}:host([immersive-card]) .half .heading,:host([immersive-card]) .no-image.half .heading{font-size:var(--msft-article-heading-font-size,14px);line-height:var(--msft-article-heading-line-height,20px);-webkit-line-clamp:var(--heading-max-lines,2);margin-bottom:0}:host([immersive-card]) .has-abstract.half .heading{-webkit-line-clamp:var(--heading-max-lines,1)}:host([immersive-card]) .half .abstract,:host([immersive-card]) .no-image.half .abstract{font-size:var(--abstract-font-size,12px);line-height:var(--abstract-line-height,16px);-webkit-line-clamp:var(--heading-max-lines,1);margin-bottom:0}:host([immersive-card]) .article.no-image.half .attribution ::slotted(msft-attribution){max-width:100%}:host([immersive-card]) .half .attribution ::slotted(msft-attribution){margin-bottom:14px;max-width:170px;overflow:hidden}.heading:hover,.heading:${u.b}{text-decoration:underline}.heading:focus{outline:auto}.heading:after{content:"";position:absolute;bottom:0;right:0;left:0;width:var(--card-width);height:var(--card-height);z-index:1}.wide .heading:after,.no-image .heading:after{top:0}.abstract,::slotted(p){-webkit-line-clamp:var(--abstract-max-lines,3);font-weight:400;font-size:var(--abstract-font-size,14px);line-height:var(--abstract-line-height,20px);font-kerning:auto;margin:0;opacity:0.8}:host([immersive-card]){background:${r.I}}:host([immersive-card]) .no-image{background:#FFFFFF}:host([immersive-card]) ::slotted(img){max-width:100%}.image-pos-end{grid-template-columns:auto 1fr}:host([immersive-card]) .article{position:relative;overflow:visible;grid-template-columns:none;height:100%;display:block}:host([immersive-card]) .attribution ::slotted(msft-attribution),:host([immersive-card]) .abstract{color:var(--msft-card-font-color);margin-bottom:12px}:host([immersive-card]) .heading{color:var(--msft-card-font-color);margin-bottom:6px}:host([immersive-card]) .image{position:absolute;display:block;max-width:100%;top:0;transition:opacity 0.1s linear}:host([immersive-card]:${u.b}) .image,:host([immersive-card]:hover) .image{opacity:0.5}:host([immersive-card]) .text{position:absolute;bottom:0;width:100%;z-index:1;padding:24px calc(var(--msft-article-padding) * 1px) 16px calc(var(--msft-article-padding) * 1px);box-sizing:border-box}:host([immersive-card]) .gradient{background:linear-gradient( 180deg,transparent 0%,var(--gradient-mid-color) 62.5%,var(--gradient-color) 100% );position:absolute;top:-21px;bottom:79px;z-index:-1}:host([immersive-card]) .no-image .gradient,:host([immersive-card]) .wide.no-image .gradient{background:linear-gradient( 44.24deg,var(--gradient-color) 0%,var(--gradient-mid-color) 100% )}:host([immersive-card]) .no-image .gradient,:host([immersive-card]) .wide .gradient{bottom:0}:host([immersive-card]) ::slotted([slot="start"]),:host([immersive-card]) ::slotted([slot="end"]){color:var(--msft-card-font-color);position:relative}:host([immersive-card]) .wide .text{height:100%;width:50%}:host([immersive-card]) .text{padding:52px calc(var(--msft-article-padding) * 1px) 16px calc(var(--msft-article-padding) * 1px)}:host([immersive-card]) .no-image .text{height:100%;width:100%;bottom:initial;padding:56px calc(var(--msft-article-padding) * 1px) 16px calc(var(--msft-article-padding) * 1px)}:host([immersive-card]) .actions{display:flex;justify-content:space-between}:host([immersive-card]) .article.no-image .actions,:host([immersive-card]) .article.wide .actions,:host([immersive-card]) .article.wide.image-pos-end.flex-card .actions{position:absolute;bottom:calc(var(--msft-article-padding) * 1px);width:calc(100% - calc(var(--msft-article-padding) * 2px))}:host([immersive-card]) .article.wide.flex-card .actions{width:calc(100% - calc((var(--msft-article-padding) * 2px) + (var(--msft-flex-button-padding) * 1px)))}::slotted([slot="start-action"]),::slotted([slot="end-action"]),::slotted([slot="hide-story"]){z-index:4}:host([immersive-card]) .hide-story-wrapper{position:absolute;display:flex;flex-direction:row;top:16px}:host([immersive-card]) .hide-story{display:flex;justify-content:center;align-items:center;opacity:0;transition:opacity 0.1s linear}:host([immersive-card]:hover) .hide-story,:host([immersive-card]:focus-within) .hide-story{opacity:1}:host([immersive-card]) ::slotted(fluent-button),::slotted(fluent-button){--control-corner-radius:40;height:28px;min-width:28px}::slotted([slot="hide-story"]){font-family:${o.S};display:flex;padding:0;justify-content:center;align-items:center;position:relative;outline:none;font-size:${s.c};line-height:${s.R};height:28px;min-width:28px;background-color:rgba(0,0,0,0.54);color:#FFFFFF;fill:#FFFFFF;cursor:pointer;border-radius:100%;border:2px solid transparent;margin-inline-start:4px}::slotted([slot="hide-story"]:${u.b}){border-color:${a.yG}}::slotted([slot="hide-story"]:${u.b}),::slotted([slot="hide-story"]:hover){background-color:rgba(0,0,0,0.62)}::slotted([slot="hide-story"]:active){background-color:rgba(0,0,0,0.49)}::slotted([slot="content-indicator"]){position:absolute;top:calc(var(--msft-article-padding) * 1px);z-index:1}@supports not (contain:content){:host([immersive-card]){overflow:hidden}}.hide-story-wrapper{inset-inline-end:16px}.hide-story{inset-inline-end:-32px}:host([immersive-card]) .image{inset-inline-end:0}:host([immersive-card]) .image-pos-start .image{inset-inline-end:initial;inset-inline-start:0}:host([immersive-card]) .text{inset-inline-start:0}:host([immersive-card]) .image-pos-start .text{inset-inline-end:0;inset-inline-start:initial}:host([immersive-card]) .gradient{inset-inline-start:0;inset-inline-end:-84px}:host([immersive-card]) .wide.no-image .gradient{inset-inline-end:-100%}:host([immersive-card]) .image-pos-start .gradient{inset-inline-end:0;inset-inline-start:-120px}:host([immersive-card]) .wide .heading:after{inset-inline-end:-100%;inset-inline-start:0}:host([immersive-card]) .wide.image-pos-start .heading:after{inset-inline-start:-100%;inset-inline-end:0}::slotted([slot="content-indicator"]){inset-inline-start:calc(var(--msft-article-padding) * 1px);pointer-events:none}:host([immersive-card]) .wide.image-pos-start ::slotted([slot="content-indicator"]){inset-inline-start:calc(50% + var(--msft-article-padding) * 1px)}:host([immersive-card]) .half .text{padding-block:16px;padding-inline-end:0;padding-inline-start:16px}:host([immersive-card]) .half .image{padding-block-start:16px;padding-inline-end:16px;padding-block-end:6px;padding-inline-start:8px}:host([immersive-card]) .no-image.half .actions{padding-inline-start:16px}:host([immersive-card]) .wide .gradient-ltr{background:linear-gradient( 270deg,transparent 0%,var(--gradient-mid-color) 102px,var(--gradient-color) 246px )}:host([immersive-card]) .wide.image-pos-start .gradient-ltr{background:linear-gradient( 90deg,transparent 0%,var(--gradient-mid-color) 102px,var(--gradient-color) 246px )}:host([immersive-card]) .half .gradient-ltr,:host([immersive-card]) .no-image.half .gradient-ltr{background:linear-gradient( 76.68deg,var(--gradient-color) 0%,var(--gradient-mid-color) 100% )}:host([immersive-card]) .wide .gradient-rtl{background:linear-gradient( 90deg,transparent 0%,var(--gradient-mid-color) 102px,var(--gradient-color) 246px )}:host([immersive-card]) .wide.image-pos-start .gradient-rtl{background:linear-gradient( 270deg,transparent 0%,var(--gradient-mid-color) 102px,var(--gradient-color) 246px )}:host([immersive-card]) .half .gradient-rtl,:host([immersive-card]) .no-image.half .gradient-rtl{background:linear-gradient( 294deg,var(--gradient-color) 0%,var(--gradient-mid-color) 100% )}`.withBehaviors((0,d.vF)(c.i` :host([immersive-card]),:host([immersive-card]) .heading{background:${h.H.ButtonFace};color:${h.H.ButtonText}}:host([immersive-card]) .attribution ::slotted(msft-attribution),:host([immersive-card]) .abstract{background:${h.H.ButtonFace};color:${h.H.CanvasText}}:host([href]) .heading,:host([immersive-card][href]) .heading{color:${h.H.LinkText}}.heading:hover,.heading:${u.b}{text-decoration:underline}:host([immersive-card]) .gradient{background:linear-gradient( 180deg,transparent 0%,${h.H.ButtonFace} 62.5%,${h.H.ButtonFace} 100% )}:host([immersive-card]) .wide .gradient{background:linear-gradient( 270deg,transparent 0%,${h.H.ButtonFace} 102px,${h.H.ButtonFace} 246px )}:host([immersive-card]) .wide.image-pos-start .gradient{background:linear-gradient( 90deg,transparent 0%,${h.H.ButtonFace} 102px,${h.H.ButtonFace} 246px )}:host([immersive-card]) .no-image .gradient,:host([immersive-card]) .wide.no-image .gradient{background:linear-gradient( 65.94deg,${h.H.ButtonFace} 8.23%,${h.H.ButtonFace} 93.81% )}:host([immersive-card]) .hide-story{background:none}.abstract,::slotted(p){opacity:1}::slotted([slot="hide-story"]){background-color:${h.H.ButtonFace};border-color:${h.H.ButtonText};fill:${h.H.ButtonText}}::slotted([slot="hide-story"]:hover),::slotted([slot="hide-story"]:${u.b}){background-color:${h.H.Highlight};border-color:${h.H.ButtonText};fill:${h.H.HighlightText}}:host([immersive-card]) .half .gradient,:host([immersive-card]) .no-image.half .gradient{background:${h.H.ButtonFace}}`))},45751:function(t,e,n){"use strict";n.d(e,{D:function(){return m}});var i=n(33940),r=n(28904),o=n(58968),s=n(42590),a=n(99452),c=n(59997),l=n(85144),u=n(5977);class d extends r.H{constructor(){super(...arguments),this.anchor="",this.viewport="",this.horizontalPositioningMode="uncontrolled",this.horizontalDefaultPosition="unset",this.horizontalViewportLock=!1,this.horizontalInset=!1,this.horizontalScaling="content",this.verticalPositioningMode="uncontrolled",this.verticalDefaultPosition="unset",this.verticalViewportLock=!1,this.verticalInset=!1,this.verticalScaling="content",this.fixedPlacement=!1,this.autoUpdateMode="anchor",this.anchorElement=null,this.viewportElement=null,this.initialLayoutComplete=!1,this.resizeDetector=null,this.baseHorizontalOffset=0,this.baseVerticalOffset=0,this.pendingPositioningUpdate=!1,this.pendingReset=!1,this.currentDirection=c.N.ltr,this.regionVisible=!1,this.forceUpdate=!1,this.updateThreshold=.5,this.update=()=>{this.pendingPositioningUpdate||this.requestPositionUpdates()},this.startObservers=()=>{this.stopObservers(),null!==this.anchorElement&&(this.requestPositionUpdates(),null!==this.resizeDetector&&(this.resizeDetector.observe(this.anchorElement),this.resizeDetector.observe(this)))},this.requestPositionUpdates=()=>{null===this.anchorElement||this.pendingPositioningUpdate||(d.intersectionService.requestPosition(this,this.handleIntersection),d.intersectionService.requestPosition(this.anchorElement,this.handleIntersection),null!==this.viewportElement&&d.intersectionService.requestPosition(this.viewportElement,this.handleIntersection),this.pendingPositioningUpdate=!0)},this.stopObservers=()=>{this.pendingPositioningUpdate&&(this.pendingPositioningUpdate=!1,d.intersectionService.cancelRequestPosition(this,this.handleIntersection),null!==this.anchorElement&&d.intersectionService.cancelRequestPosition(this.anchorElement,this.handleIntersection),null!==this.viewportElement&&d.intersectionService.cancelRequestPosition(this.viewportElement,this.handleIntersection)),null!==this.resizeDetector&&this.resizeDetector.disconnect()},this.getViewport=()=>{if("string"!=typeof this.viewport||""===this.viewport)return document.documentElement;const t=this.getRootNode();return t instanceof ShadowRoot?t.getElementById(this.viewport):document.getElementById(this.viewport)},this.getAnchor=()=>{const t=this.getRootNode();return t instanceof ShadowRoot?t.getElementById(this.anchor):document.getElementById(this.anchor)},this.handleIntersection=t=>{this.pendingPositioningUpdate&&(this.pendingPositioningUpdate=!1,this.applyIntersectionEntries(t)&&this.updateLayout())},this.applyIntersectionEntries=t=>{const e=t.find((t=>t.target===this)),n=t.find((t=>t.target===this.anchorElement)),i=t.find((t=>t.target===this.viewportElement));return void 0!==e&&void 0!==i&&void 0!==n&&(!!(!this.regionVisible||this.forceUpdate||void 0===this.regionRect||void 0===this.anchorRect||void 0===this.viewportRect||this.isRectDifferent(this.anchorRect,n.boundingClientRect)||this.isRectDifferent(this.viewportRect,i.boundingClientRect)||this.isRectDifferent(this.regionRect,e.boundingClientRect))&&(this.regionRect=e.boundingClientRect,this.anchorRect=n.boundingClientRect,this.viewportElement===document.documentElement?this.viewportRect=new DOMRectReadOnly(i.boundingClientRect.x+document.documentElement.scrollLeft,i.boundingClientRect.y+document.documentElement.scrollTop,i.boundingClientRect.width,i.boundingClientRect.height):this.viewportRect=i.boundingClientRect,this.updateRegionOffset(),this.forceUpdate=!1,!0))},this.updateRegionOffset=()=>{this.anchorRect&&this.regionRect&&(this.baseHorizontalOffset=this.baseHorizontalOffset+(this.anchorRect.left-this.regionRect.left)+(this.translateX-this.baseHorizontalOffset),this.baseVerticalOffset=this.baseVerticalOffset+(this.anchorRect.top-this.regionRect.top)+(this.translateY-this.baseVerticalOffset))},this.isRectDifferent=(t,e)=>Math.abs(t.top-e.top)>this.updateThreshold||Math.abs(t.right-e.right)>this.updateThreshold||Math.abs(t.bottom-e.bottom)>this.updateThreshold||Math.abs(t.left-e.left)>this.updateThreshold,this.handleResize=t=>{this.update()},this.reset=()=>{this.pendingReset&&(this.pendingReset=!1,null===this.anchorElement&&(this.anchorElement=this.getAnchor()),null===this.viewportElement&&(this.viewportElement=this.getViewport()),this.currentDirection=(0,u.M)(this),this.startObservers())},this.updateLayout=()=>{let t,e;if("uncontrolled"!==this.horizontalPositioningMode){const t=this.getPositioningOptions(this.horizontalInset);if("center"===this.horizontalDefaultPosition)e="center";else if("unset"!==this.horizontalDefaultPosition){let t=this.horizontalDefaultPosition;if("start"===t||"end"===t){const e=(0,u.M)(this);if(e!==this.currentDirection)return this.currentDirection=e,void this.initialize();t=this.currentDirection===c.N.ltr?"start"===t?"left":"right":"start"===t?"right":"left"}switch(t){case"left":e=this.horizontalInset?"insetStart":"start";break;case"right":e=this.horizontalInset?"insetEnd":"end"}}const n=void 0!==this.horizontalThreshold?this.horizontalThreshold:void 0!==this.regionRect?this.regionRect.width:0,i=void 0!==this.anchorRect?this.anchorRect.left:0,r=void 0!==this.anchorRect?this.anchorRect.right:0,o=void 0!==this.anchorRect?this.anchorRect.width:0,s=void 0!==this.viewportRect?this.viewportRect.left:0,a=void 0!==this.viewportRect?this.viewportRect.right:0;(void 0===e||"locktodefault"!==this.horizontalPositioningMode&&this.getAvailableSpace(e,i,r,o,s,a)<n)&&(e=this.getAvailableSpace(t[0],i,r,o,s,a)>this.getAvailableSpace(t[1],i,r,o,s,a)?t[0]:t[1])}if("uncontrolled"!==this.verticalPositioningMode){const e=this.getPositioningOptions(this.verticalInset);if("center"===this.verticalDefaultPosition)t="center";else if("unset"!==this.verticalDefaultPosition)switch(this.verticalDefaultPosition){case"top":t=this.verticalInset?"insetStart":"start";break;case"bottom":t=this.verticalInset?"insetEnd":"end"}const n=void 0!==this.verticalThreshold?this.verticalThreshold:void 0!==this.regionRect?this.regionRect.height:0,i=void 0!==this.anchorRect?this.anchorRect.top:0,r=void 0!==this.anchorRect?this.anchorRect.bottom:0,o=void 0!==this.anchorRect?this.anchorRect.height:0,s=void 0!==this.viewportRect?this.viewportRect.top:0,a=void 0!==this.viewportRect?this.viewportRect.bottom:0;(void 0===t||"locktodefault"!==this.verticalPositioningMode&&this.getAvailableSpace(t,i,r,o,s,a)<n)&&(t=this.getAvailableSpace(e[0],i,r,o,s,a)>this.getAvailableSpace(e[1],i,r,o,s,a)?e[0]:e[1])}const n=this.getNextRegionDimension(e,t),i=this.horizontalPosition!==e||this.verticalPosition!==t;if(this.setHorizontalPosition(e,n),this.setVerticalPosition(t,n),this.updateRegionStyle(),!this.initialLayoutComplete)return this.initialLayoutComplete=!0,void this.requestPositionUpdates();this.regionVisible||(this.regionVisible=!0,this.style.removeProperty("pointer-events"),this.style.removeProperty("opacity"),this.classList.toggle("loaded",!0),this.$emit("loaded",this,{bubbles:!1})),this.updatePositionClasses(),i&&this.$emit("positionchange",this,{bubbles:!1})},this.updateRegionStyle=()=>{this.style.width=this.regionWidth,this.style.height=this.regionHeight,this.style.transform=`translate(${this.translateX}px, ${this.translateY}px)`},this.updatePositionClasses=()=>{this.classList.toggle("top","start"===this.verticalPosition),this.classList.toggle("bottom","end"===this.verticalPosition),this.classList.toggle("inset-top","insetStart"===this.verticalPosition),this.classList.toggle("inset-bottom","insetEnd"===this.verticalPosition),this.classList.toggle("vertical-center","center"===this.verticalPosition),this.classList.toggle("left","start"===this.horizontalPosition),this.classList.toggle("right","end"===this.horizontalPosition),this.classList.toggle("inset-left","insetStart"===this.horizontalPosition),this.classList.toggle("inset-right","insetEnd"===this.horizontalPosition),this.classList.toggle("horizontal-center","center"===this.horizontalPosition)},this.setHorizontalPosition=(t,e)=>{if(void 0===t||void 0===this.regionRect||void 0===this.anchorRect||void 0===this.viewportRect)return;let n=0;switch(this.horizontalScaling){case"anchor":case"fill":n=this.horizontalViewportLock?this.viewportRect.width:e.width,this.regionWidth=`${n}px`;break;case"content":n=this.regionRect.width,this.regionWidth="unset"}let i=0;switch(t){case"start":this.translateX=this.baseHorizontalOffset-n,this.horizontalViewportLock&&this.anchorRect.left>this.viewportRect.right&&(this.translateX=this.translateX-(this.anchorRect.left-this.viewportRect.right));break;case"insetStart":this.translateX=this.baseHorizontalOffset-n+this.anchorRect.width,this.horizontalViewportLock&&this.anchorRect.right>this.viewportRect.right&&(this.translateX=this.translateX-(this.anchorRect.right-this.viewportRect.right));break;case"insetEnd":this.translateX=this.baseHorizontalOffset,this.horizontalViewportLock&&this.anchorRect.left<this.viewportRect.left&&(this.translateX=this.translateX-(this.anchorRect.left-this.viewportRect.left));break;case"end":this.translateX=this.baseHorizontalOffset+this.anchorRect.width,this.horizontalViewportLock&&this.anchorRect.right<this.viewportRect.left&&(this.translateX=this.translateX-(this.anchorRect.right-this.viewportRect.left));break;case"center":if(i=(this.anchorRect.width-n)/2,this.translateX=this.baseHorizontalOffset+i,this.horizontalViewportLock){const t=this.anchorRect.left+i,e=this.anchorRect.right-i;t<this.viewportRect.left&&!(e>this.viewportRect.right)?this.translateX=this.translateX-(t-this.viewportRect.left):e>this.viewportRect.right&&!(t<this.viewportRect.left)&&(this.translateX=this.translateX-(e-this.viewportRect.right))}}this.horizontalPosition=t},this.setVerticalPosition=(t,e)=>{if(void 0===t||void 0===this.regionRect||void 0===this.anchorRect||void 0===this.viewportRect)return;let n=0;switch(this.verticalScaling){case"anchor":case"fill":n=this.verticalViewportLock?this.viewportRect.height:e.height,this.regionHeight=`${n}px`;break;case"content":n=this.regionRect.height,this.regionHeight="unset"}let i=0;switch(t){case"start":this.translateY=this.baseVerticalOffset-n,this.verticalViewportLock&&this.anchorRect.top>this.viewportRect.bottom&&(this.translateY=this.translateY-(this.anchorRect.top-this.viewportRect.bottom));break;case"insetStart":this.translateY=this.baseVerticalOffset-n+this.anchorRect.height,this.verticalViewportLock&&this.anchorRect.bottom>this.viewportRect.bottom&&(this.translateY=this.translateY-(this.anchorRect.bottom-this.viewportRect.bottom));break;case"insetEnd":this.translateY=this.baseVerticalOffset,this.verticalViewportLock&&this.anchorRect.top<this.viewportRect.top&&(this.translateY=this.translateY-(this.anchorRect.top-this.viewportRect.top));break;case"end":this.translateY=this.baseVerticalOffset+this.anchorRect.height,this.verticalViewportLock&&this.anchorRect.bottom<this.viewportRect.top&&(this.translateY=this.translateY-(this.anchorRect.bottom-this.viewportRect.top));break;case"center":if(i=(this.anchorRect.height-n)/2,this.translateY=this.baseVerticalOffset+i,this.verticalViewportLock){const t=this.anchorRect.top+i,e=this.anchorRect.bottom-i;t<this.viewportRect.top&&!(e>this.viewportRect.bottom)?this.translateY=this.translateY-(t-this.viewportRect.top):e>this.viewportRect.bottom&&!(t<this.viewportRect.top)&&(this.translateY=this.translateY-(e-this.viewportRect.bottom))}}this.verticalPosition=t},this.getPositioningOptions=t=>t?["insetStart","insetEnd"]:["start","end"],this.getAvailableSpace=(t,e,n,i,r,o)=>{const s=e-r,a=o-(e+i);switch(t){case"start":return s;case"insetStart":return s+i;case"insetEnd":return a+i;case"end":return a;case"center":return 2*Math.min(s,a)+i}},this.getNextRegionDimension=(t,e)=>{const n={height:void 0!==this.regionRect?this.regionRect.height:0,width:void 0!==this.regionRect?this.regionRect.width:0};return void 0!==t&&"fill"===this.horizontalScaling?n.width=this.getAvailableSpace(t,void 0!==this.anchorRect?this.anchorRect.left:0,void 0!==this.anchorRect?this.anchorRect.right:0,void 0!==this.anchorRect?this.anchorRect.width:0,void 0!==this.viewportRect?this.viewportRect.left:0,void 0!==this.viewportRect?this.viewportRect.right:0):"anchor"===this.horizontalScaling&&(n.width=void 0!==this.anchorRect?this.anchorRect.width:0),void 0!==e&&"fill"===this.verticalScaling?n.height=this.getAvailableSpace(e,void 0!==this.anchorRect?this.anchorRect.top:0,void 0!==this.anchorRect?this.anchorRect.bottom:0,void 0!==this.anchorRect?this.anchorRect.height:0,void 0!==this.viewportRect?this.viewportRect.top:0,void 0!==this.viewportRect?this.viewportRect.bottom:0):"anchor"===this.verticalScaling&&(n.height=void 0!==this.anchorRect?this.anchorRect.height:0),n},this.startAutoUpdateEventListeners=()=>{window.addEventListener(l.pu,this.update,{passive:!0}),window.addEventListener(l.xG,this.update,{passive:!0,capture:!0}),null!==this.resizeDetector&&null!==this.viewportElement&&this.resizeDetector.observe(this.viewportElement)},this.stopAutoUpdateEventListeners=()=>{window.removeEventListener(l.pu,this.update),window.removeEventListener(l.xG,this.update),null!==this.resizeDetector&&null!==this.viewportElement&&this.resizeDetector.unobserve(this.viewportElement)}}anchorChanged(){this.initialLayoutComplete&&(this.anchorElement=this.getAnchor())}viewportChanged(){this.initialLayoutComplete&&(this.viewportElement=this.getViewport())}horizontalPositioningModeChanged(){this.requestReset()}horizontalDefaultPositionChanged(){this.updateForAttributeChange()}horizontalViewportLockChanged(){this.updateForAttributeChange()}horizontalInsetChanged(){this.updateForAttributeChange()}horizontalThresholdChanged(){this.updateForAttributeChange()}horizontalScalingChanged(){this.updateForAttributeChange()}verticalPositioningModeChanged(){this.requestReset()}verticalDefaultPositionChanged(){this.updateForAttributeChange()}verticalViewportLockChanged(){this.updateForAttributeChange()}verticalInsetChanged(){this.updateForAttributeChange()}verticalThresholdChanged(){this.updateForAttributeChange()}verticalScalingChanged(){this.updateForAttributeChange()}fixedPlacementChanged(){this.$fastController.isConnected&&this.initialLayoutComplete&&this.initialize()}autoUpdateModeChanged(t,e){this.$fastController.isConnected&&this.initialLayoutComplete&&("auto"===t&&this.stopAutoUpdateEventListeners(),"auto"===e&&this.startAutoUpdateEventListeners())}anchorElementChanged(){this.requestReset()}viewportElementChanged(){this.$fastController.isConnected&&this.initialLayoutComplete&&this.initialize()}connectedCallback(){super.connectedCallback(),"auto"===this.autoUpdateMode&&this.startAutoUpdateEventListeners(),this.initialize()}disconnectedCallback(){super.disconnectedCallback(),"auto"===this.autoUpdateMode&&this.stopAutoUpdateEventListeners(),this.stopObservers(),this.disconnectResizeDetector()}adoptedCallback(){this.initialize()}disconnectResizeDetector(){null!==this.resizeDetector&&(this.resizeDetector.disconnect(),this.resizeDetector=null)}initializeResizeDetector(){this.disconnectResizeDetector(),this.resizeDetector=new window.ResizeObserver(this.handleResize)}updateForAttributeChange(){this.$fastController.isConnected&&this.initialLayoutComplete&&(this.forceUpdate=!0,this.update())}initialize(){this.initializeResizeDetector(),null===this.anchorElement&&(this.anchorElement=this.getAnchor()),this.requestReset()}requestReset(){this.$fastController.isConnected&&!1===this.pendingReset&&(this.setInitialState(),o.H.enqueue((()=>this.reset())),this.pendingReset=!0)}setInitialState(){this.initialLayoutComplete=!1,this.regionVisible=!1,this.translateX=0,this.translateY=0,this.baseHorizontalOffset=0,this.baseVerticalOffset=0,this.viewportRect=void 0,this.regionRect=void 0,this.anchorRect=void 0,this.verticalPosition=void 0,this.horizontalPosition=void 0,this.style.opacity="0",this.style.pointerEvents="none",this.forceUpdate=!1,this.style.position=this.fixedPlacement?"fixed":"absolute",this.updatePositionClasses(),this.updateRegionStyle()}}d.intersectionService=new class{constructor(){this.intersectionDetector=null,this.observedElements=new Map,this.requestPosition=(t,e)=>{null!==this.intersectionDetector&&(this.observedElements.has(t)?this.observedElements.get(t)?.push(e):(this.observedElements.set(t,[e]),this.intersectionDetector.observe(t)))},this.cancelRequestPosition=(t,e)=>{const n=this.observedElements.get(t);if(void 0!==n){const t=n.indexOf(e);-1!==t&&n.splice(t,1)}},this.initializeIntersectionDetector=()=>{globalThis.IntersectionObserver&&(this.intersectionDetector=new IntersectionObserver(this.handleIntersection,{root:null,rootMargin:"0px",threshold:[0,1]}))},this.handleIntersection=t=>{if(null===this.intersectionDetector)return;const e=[],n=[];t.forEach((t=>{this.intersectionDetector?.unobserve(t.target);const i=this.observedElements.get(t.target);void 0!==i&&(i.forEach((i=>{let r=e.indexOf(i);-1===r&&(r=e.length,e.push(i),n.push([])),n[r].push(t)})),this.observedElements.delete(t.target))})),e.forEach(((t,e)=>{t(n[e])}))},this.initializeIntersectionDetector()}},(0,i.gn)([s.Lj,(0,i.w6)("design:type",String)],d.prototype,"anchor",void 0),(0,i.gn)([s.Lj,(0,i.w6)("design:type",String)],d.prototype,"viewport",void 0),(0,i.gn)([(0,s.Lj)({attribute:"horizontal-positioning-mode"}),(0,i.w6)("design:type",String)],d.prototype,"horizontalPositioningMode",void 0),(0,i.gn)([(0,s.Lj)({attribute:"horizontal-default-position"}),(0,i.w6)("design:type",String)],d.prototype,"horizontalDefaultPosition",void 0),(0,i.gn)([(0,s.Lj)({attribute:"horizontal-viewport-lock",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],d.prototype,"horizontalViewportLock",void 0),(0,i.gn)([(0,s.Lj)({attribute:"horizontal-inset",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],d.prototype,"horizontalInset",void 0),(0,i.gn)([(0,s.Lj)({attribute:"horizontal-threshold"}),(0,i.w6)("design:type",Number)],d.prototype,"horizontalThreshold",void 0),(0,i.gn)([(0,s.Lj)({attribute:"horizontal-scaling"}),(0,i.w6)("design:type",String)],d.prototype,"horizontalScaling",void 0),(0,i.gn)([(0,s.Lj)({attribute:"vertical-positioning-mode"}),(0,i.w6)("design:type",String)],d.prototype,"verticalPositioningMode",void 0),(0,i.gn)([(0,s.Lj)({attribute:"vertical-default-position"}),(0,i.w6)("design:type",String)],d.prototype,"verticalDefaultPosition",void 0),(0,i.gn)([(0,s.Lj)({attribute:"vertical-viewport-lock",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],d.prototype,"verticalViewportLock",void 0),(0,i.gn)([(0,s.Lj)({attribute:"vertical-inset",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],d.prototype,"verticalInset",void 0),(0,i.gn)([(0,s.Lj)({attribute:"vertical-threshold"}),(0,i.w6)("design:type",Number)],d.prototype,"verticalThreshold",void 0),(0,i.gn)([(0,s.Lj)({attribute:"vertical-scaling"}),(0,i.w6)("design:type",String)],d.prototype,"verticalScaling",void 0),(0,i.gn)([(0,s.Lj)({attribute:"fixed-placement",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],d.prototype,"fixedPlacement",void 0),(0,i.gn)([(0,s.Lj)({attribute:"auto-update-mode"}),(0,i.w6)("design:type",String)],d.prototype,"autoUpdateMode",void 0),(0,i.gn)([a.LO,(0,i.w6)("design:type",Object)],d.prototype,"anchorElement",void 0),(0,i.gn)([a.LO,(0,i.w6)("design:type",Object)],d.prototype,"viewportElement",void 0),(0,i.gn)([a.LO,(0,i.w6)("design:type",Boolean)],d.prototype,"initialLayoutComplete",void 0);var h=n(63070);const f=n(78923).i` :host{contain:layout;display:block}`;var p=n(49218),g=n(93703);const v=p.dy`
        <template data-loaded="${t=>t.initialLayoutComplete?"loaded":""}">
            ${(0,g.g)((t=>t.initialLayoutComplete),p.dy`
                    <slot></slot>
                `)}
        </template>
    `,m=d.compose({name:`${h.H.prefix}-anchored-region`,template:v,styles:f})},45900:function(t,e,n){"use strict";n.d(e,{D:function(){return a}});var i=n(63070),r=n(84355),o=n(51758),s=n(48758);const a=r.X.compose({name:`${i.H.prefix}-button`,template:s.X,styles:o.W,shadowOptions:{delegatesFocus:!0}})},84355:function(t,e,n){"use strict";n.d(e,{X:function(){return m}});var i=n(33940),r=n(42590),o=n(99452),s=n(33714),a=n(71695),c=n(31289),l=n(28904),u=n(23526);class d extends l.H{}class h extends((0,u.Um)(d)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}const f="submit",p="reset";class g extends h{constructor(){super(...arguments),this.handleSubmission=()=>{if(!this.form)return;const t=this.proxy.isConnected;t||this.attachProxy(),"function"==typeof this.form.requestSubmit?this.form.requestSubmit(this.proxy):this.proxy.click(),t||this.detachProxy()},this.handleFormReset=()=>{this.form?.reset()},this.handleUnsupportedDelegatesFocus=()=>{window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&this.$fastController.definition.shadowOptions?.delegatesFocus&&(this.focus=()=>{this.control.focus()})}}formactionChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formAction=this.formaction)}formenctypeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formEnctype=this.formenctype)}formmethodChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formMethod=this.formmethod)}formnovalidateChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formNoValidate=this.formnovalidate)}formtargetChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formTarget=this.formtarget)}typeChanged(t,e){this.proxy instanceof HTMLInputElement&&(this.proxy.type=this.type),e===f&&this.addEventListener("click",this.handleSubmission),t===f&&this.removeEventListener("click",this.handleSubmission),e===p&&this.addEventListener("click",this.handleFormReset),t===p&&this.removeEventListener("click",this.handleFormReset)}validate(){super.validate(this.control)}connectedCallback(){super.connectedCallback(),this.proxy.setAttribute("type",this.type),this.handleUnsupportedDelegatesFocus()}}(0,i.gn)([(0,r.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],g.prototype,"autofocus",void 0),(0,i.gn)([(0,r.Lj)({attribute:"form"}),(0,i.w6)("design:type",String)],g.prototype,"formId",void 0),(0,i.gn)([r.Lj,(0,i.w6)("design:type",String)],g.prototype,"formaction",void 0),(0,i.gn)([r.Lj,(0,i.w6)("design:type",String)],g.prototype,"formenctype",void 0),(0,i.gn)([r.Lj,(0,i.w6)("design:type",String)],g.prototype,"formmethod",void 0),(0,i.gn)([(0,r.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],g.prototype,"formnovalidate",void 0),(0,i.gn)([r.Lj,(0,i.w6)("design:type",String)],g.prototype,"formtarget",void 0),(0,i.gn)([r.Lj,(0,i.w6)("design:type",String)],g.prototype,"type",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Array)],g.prototype,"defaultSlottedContent",void 0);class v{}(0,i.gn)([(0,r.Lj)({attribute:"aria-expanded"}),(0,i.w6)("design:type",Object)],v.prototype,"ariaExpanded",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-pressed"}),(0,i.w6)("design:type",Object)],v.prototype,"ariaPressed",void 0),(0,c.e)(v,s.v),(0,c.e)(g,a.hW,v);class m extends g{constructor(){super(...arguments),this.iconOnly=!1}appearanceChanged(t,e){t!==e&&(this.classList.add(e),this.classList.remove(t))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="neutral")}defaultSlottedContentChanged(){const t=this.defaultSlottedContent.filter((t=>t.nodeType===Node.ELEMENT_NODE));1===t.length&&t[0]instanceof SVGElement?this.control.classList.add("icon-only"):this.control.classList.remove("icon-only")}}(0,i.gn)([r.Lj,(0,i.w6)("design:type",String)],m.prototype,"appearance",void 0),(0,i.gn)([(0,r.Lj)({attribute:"icon-only",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],m.prototype,"iconOnly",void 0)},48758:function(t,e,n){"use strict";n.d(e,{X:function(){return a}});var i=n(49218),r=n(41472),o=n(47548),s=n(71695);const a=function(t={}){return i.dy`
        <button
            class="control"
            part="control"
            ?autofocus="${t=>t.autofocus}"
            ?disabled="${t=>t.disabled}"
            form="${t=>t.formId}"
            formaction="${t=>t.formaction}"
            formenctype="${t=>t.formenctype}"
            formmethod="${t=>t.formmethod}"
            ?formnovalidate="${t=>t.formnovalidate}"
            formtarget="${t=>t.formtarget}"
            name="${t=>t.name}"
            type="${t=>t.type}"
            value="${t=>t.value}"
            aria-atomic="${t=>t.ariaAtomic}"
            aria-busy="${t=>t.ariaBusy}"
            aria-controls="${t=>t.ariaControls}"
            aria-current="${t=>t.ariaCurrent}"
            aria-describedby="${t=>t.ariaDescribedby}"
            aria-details="${t=>t.ariaDetails}"
            aria-disabled="${t=>t.ariaDisabled}"
            aria-errormessage="${t=>t.ariaErrormessage}"
            aria-expanded="${t=>t.ariaExpanded}"
            aria-flowto="${t=>t.ariaFlowto}"
            aria-haspopup="${t=>t.ariaHaspopup}"
            aria-hidden="${t=>t.ariaHidden}"
            aria-invalid="${t=>t.ariaInvalid}"
            aria-keyshortcuts="${t=>t.ariaKeyshortcuts}"
            aria-label="${t=>t.ariaLabel}"
            aria-labelledby="${t=>t.ariaLabelledby}"
            aria-live="${t=>t.ariaLive}"
            aria-owns="${t=>t.ariaOwns}"
            aria-pressed="${t=>t.ariaPressed}"
            aria-relevant="${t=>t.ariaRelevant}"
            aria-roledescription="${t=>t.ariaRoledescription}"
            ${(0,r.i)("control")}
        >
            ${(0,s.m9)(t)}
            <span class="content" part="content">
                <slot ${(0,o.Q)("defaultSlottedContent")}></slot>
            </span>
            ${(0,s.LC)(t)}
        </button>
    `}()},32229:function(t,e,n){"use strict";n.d(e,{u:function(){return p}});var i=n(33940),r=n(35210),o=n(97186),s=n(62795),a=n(99452),c=n(42590),l=n(38492),u=n(56837),d=n(79148),h=n(42355),f=n(89949);class p extends r.v{cardFillColorChanged(t,e){if(e){const t=(0,o.in)(e);null!==t&&(this.neutralPaletteSource=e,l.I.setValueFor(this,h.w.create(t.r,t.g,t.b)))}}neutralPaletteSourceChanged(t,e){if(e){const t=(0,o.in)(e),n=h.w.create(t.r,t.g,t.b);u.y.setValueFor(this,f.v.create(n))}}handleChange(t,e){this.cardFillColor||l.I.setValueFor(this,(t=>t(d.ab).evaluate(t,t(l.I))))}connectedCallback(){super.connectedCallback();const t=(0,s.TC)(this);if(t){const e=a.y$.getNotifier(t);e.subscribe(this,"fillColor"),e.subscribe(this,"neutralPalette"),this.handleChange(t,"fillColor")}}}(0,i.gn)([(0,c.Lj)({attribute:"card-fill-color",mode:"fromView"}),(0,i.w6)("design:type",String)],p.prototype,"cardFillColor",void 0),(0,i.gn)([(0,c.Lj)({attribute:"neutral-palette-source",mode:"fromView"}),(0,i.w6)("design:type",String)],p.prototype,"neutralPaletteSource",void 0)},13261:function(t,e,n){"use strict";n.d(e,{X:function(){return i}});const i=(0,n(35928).O)()},89949:function(t,e,n){"use strict";n.d(e,{v:function(){return L}});var i,r=n(82917),o=n(2696),s=n(60279),a=n(83343);function c(t,e,n=18){const i=(0,s.$2)(t);let r=i.c+e*n;return r<0&&(r=0),(0,s.KW)(new a.t(i.l,r,i.h))}function l(t,e){return t*e}function u(t,e){return new o.h(l(t.r,e.r),l(t.g,e.g),l(t.b,e.b),1)}function d(t,e){return t<.5?(0,r.uZ)(2*e*t,0,1):(0,r.uZ)(1-2*(1-e)*(1-t),0,1)}function h(t,e){return new o.h(d(t.r,e.r),d(t.g,e.g),d(t.b,e.b),1)}!function(t){t[t.Burn=0]="Burn",t[t.Color=1]="Color",t[t.Darken=2]="Darken",t[t.Dodge=3]="Dodge",t[t.Lighten=4]="Lighten",t[t.Multiply=5]="Multiply",t[t.Overlay=6]="Overlay",t[t.Screen=7]="Screen"}(i||(i={}));var f,p=n(9366),g=n(38316),v=n(447),m=n(40272);function b(t,e,n,i){if(isNaN(t)||t<=0)return n;if(t>=1)return i;switch(e){case f.HSL:return(0,s.hP)(function(t,e,n){return isNaN(t)||t<=0?e:t>=1?n:new p.H((0,r.AG)(t,e.h,n.h),(0,r.t7)(t,e.s,n.s),(0,r.t7)(t,e.l,n.l))}(t,(0,s.lw)(n),(0,s.lw)(i)));case f.HSV:return(0,s.iI)(function(t,e,n){return isNaN(t)||t<=0?e:t>=1?n:new g.T((0,r.AG)(t,e.h,n.h),(0,r.t7)(t,e.s,n.s),(0,r.t7)(t,e.v,n.v))}(t,(0,s.T8)(n),(0,s.T8)(i)));case f.XYZ:return(0,s.rD)(function(t,e,n){return isNaN(t)||t<=0?e:t>=1?n:new m.x((0,r.t7)(t,e.x,n.x),(0,r.t7)(t,e.y,n.y),(0,r.t7)(t,e.z,n.z))}(t,(0,s.zP)(n),(0,s.zP)(i)));case f.LAB:return(0,s.DR)(function(t,e,n){return isNaN(t)||t<=0?e:t>=1?n:new v.R((0,r.t7)(t,e.l,n.l),(0,r.t7)(t,e.a,n.a),(0,r.t7)(t,e.b,n.b))}(t,(0,s.v1)(n),(0,s.v1)(i)));case f.LCH:return(0,s.KW)(function(t,e,n){return isNaN(t)||t<=0?e:t>=1?n:new a.t((0,r.t7)(t,e.l,n.l),(0,r.t7)(t,e.c,n.c),(0,r.AG)(t,e.h,n.h))}(t,(0,s.$2)(n),(0,s.$2)(i)));default:return function(t,e,n){return isNaN(t)||t<=0?e:t>=1?n:new o.h((0,r.t7)(t,e.r,n.r),(0,r.t7)(t,e.g,n.g),(0,r.t7)(t,e.b,n.b),(0,r.t7)(t,e.a,n.a))}(t,n,i)}}!function(t){t[t.RGB=0]="RGB",t[t.HSL=1]="HSL",t[t.HSV=2]="HSV",t[t.XYZ=3]="XYZ",t[t.LAB=4]="LAB",t[t.LCH=5]="LCH"}(f||(f={}));class y{constructor(t){if(null==t||0===t.length)throw new Error("The stops argument must be non-empty");this.stops=this.sortColorScaleStops(t)}static createBalancedColorScale(t){if(null==t||0===t.length)throw new Error("The colors argument must be non-empty");const e=new Array(t.length);for(let n=0;n<t.length;n++)0===n?e[n]={color:t[n],position:0}:n===t.length-1?e[n]={color:t[n],position:1}:e[n]={color:t[n],position:n*(1/(t.length-1))};return new y(e)}getColor(t,e=f.RGB){if(1===this.stops.length)return this.stops[0].color;if(t<=0)return this.stops[0].color;if(t>=1)return this.stops[this.stops.length-1].color;let n=0;for(let e=0;e<this.stops.length;e++)this.stops[e].position<=t&&(n=e);let i=n+1;i>=this.stops.length&&(i=this.stops.length-1);return b((t-this.stops[n].position)*(1/(this.stops[i].position-this.stops[n].position)),e,this.stops[n].color,this.stops[i].color)}trim(t,e,n=f.RGB){if(t<0||e>1||e<t)throw new Error("Invalid bounds");if(t===e)return new y([{color:this.getColor(t,n),position:0}]);const i=[];for(let n=0;n<this.stops.length;n++)this.stops[n].position>=t&&this.stops[n].position<=e&&i.push(this.stops[n]);if(0===i.length)return new y([{color:this.getColor(t),position:t},{color:this.getColor(e),position:e}]);i[0].position!==t&&i.unshift({color:this.getColor(t),position:t}),i[i.length-1].position!==e&&i.push({color:this.getColor(e),position:e});const r=e-t,o=new Array(i.length);for(let e=0;e<i.length;e++)o[e]={color:i[e].color,position:(i[e].position-t)/r};return new y(o)}findNextColor(t,e,n=!1,i=f.RGB,r=.005,o=32){isNaN(t)||t<=0?t=0:t>=1&&(t=1);const a=this.getColor(t,i),c=n?0:1,l=this.getColor(c,i);if((0,s.wo)(a,l)<=e)return c;let u=n?0:t,d=n?t:0,h=c,p=0;for(;p<=o;){h=Math.abs(d-u)/2+u;const t=this.getColor(h,i),o=(0,s.wo)(a,t);if(Math.abs(o-e)<=r)return h;o>e?n?u=h:d=h:n?d=h:u=h,p++}return h}clone(){const t=new Array(this.stops.length);for(let e=0;e<t.length;e++)t[e]={color:this.stops[e].color,position:this.stops[e].position};return new y(t)}sortColorScaleStops(t){return t.sort(((t,e)=>{const n=t.position,i=e.position;return n<i?-1:n>i?1:0}))}}var w=n(97186);class x{constructor(t){this.config=Object.assign({},x.defaultPaletteConfig,t),this.palette=[],this.updatePaletteColors()}updatePaletteGenerationValues(t){let e=!1;for(const n in t)this.config[n]&&(this.config[n].equalValue?this.config[n].equalValue(t[n])||(this.config[n]=t[n],e=!0):t[n]!==this.config[n]&&(this.config[n]=t[n],e=!0));return e&&this.updatePaletteColors(),e}updatePaletteColors(){const t=this.generatePaletteColorScale();for(let e=0;e<this.config.steps;e++)this.palette[e]=t.getColor(e/(this.config.steps-1),this.config.interpolationMode)}generatePaletteColorScale(){const t=(0,s.lw)(this.config.baseColor),e=new y([{position:0,color:this.config.scaleColorLight},{position:.5,color:this.config.baseColor},{position:1,color:this.config.scaleColorDark}]).trim(this.config.clipLight,1-this.config.clipDark);let n=e.getColor(0),i=e.getColor(1);if(t.s>=this.config.saturationAdjustmentCutoff&&(n=c(n,this.config.saturationLight),i=c(i,this.config.saturationDark)),0!==this.config.multiplyLight){const t=u(this.config.baseColor,n);n=b(this.config.multiplyLight,this.config.interpolationMode,n,t)}if(0!==this.config.multiplyDark){const t=u(this.config.baseColor,i);i=b(this.config.multiplyDark,this.config.interpolationMode,i,t)}if(0!==this.config.overlayLight){const t=h(this.config.baseColor,n);n=b(this.config.overlayLight,this.config.interpolationMode,n,t)}if(0!==this.config.overlayDark){const t=h(this.config.baseColor,i);i=b(this.config.overlayDark,this.config.interpolationMode,i,t)}return this.config.baseScalePosition?this.config.baseScalePosition<=0?new y([{position:0,color:this.config.baseColor},{position:1,color:i.clamp()}]):this.config.baseScalePosition>=1?new y([{position:0,color:n.clamp()},{position:1,color:this.config.baseColor}]):new y([{position:0,color:n.clamp()},{position:this.config.baseScalePosition,color:this.config.baseColor},{position:1,color:i.clamp()}]):new y([{position:0,color:n.clamp()},{position:.5,color:this.config.baseColor},{position:1,color:i.clamp()}])}}x.defaultPaletteConfig={baseColor:(0,w.in)("#808080"),steps:11,interpolationMode:f.RGB,scaleColorLight:new o.h(1,1,1,1),scaleColorDark:new o.h(0,0,0,1),clipLight:.185,clipDark:.16,saturationAdjustmentCutoff:.05,saturationLight:.35,saturationDark:1.25,overlayLight:0,overlayDark:.25,multiplyLight:0,multiplyDark:0,baseScalePosition:.5},x.greyscalePaletteConfig={baseColor:(0,w.in)("#808080"),steps:11,interpolationMode:f.RGB,scaleColorLight:new o.h(1,1,1,1),scaleColorDark:new o.h(0,0,0,1),clipLight:0,clipDark:0,saturationAdjustmentCutoff:0,saturationLight:0,saturationDark:0,overlayLight:0,overlayDark:0,multiplyLight:0,multiplyDark:0,baseScalePosition:.5};x.defaultPaletteConfig.scaleColorLight,x.defaultPaletteConfig.scaleColorDark;class C{constructor(t){this.palette=[],this.config=Object.assign({},C.defaultPaletteConfig,t),this.regenPalettes()}regenPalettes(){let t=this.config.steps;(isNaN(t)||t<3)&&(t=3);const e=.14,n=new o.h(e,e,e,1),i=new x(Object.assign(Object.assign({},x.greyscalePaletteConfig),{baseColor:n,baseScalePosition:86/94,steps:t})).palette,r=((0,s.rp)(this.config.baseColor)+(0,s.lw)(this.config.baseColor).l)/2,a=this.matchRelativeLuminanceIndex(r,i)/(t-1),c=this.matchRelativeLuminanceIndex(e,i)/(t-1),l=(0,s.lw)(this.config.baseColor),u=(0,s.hP)(p.H.fromObject({h:l.h,s:l.s,l:e})),d=(0,s.hP)(p.H.fromObject({h:l.h,s:l.s,l:.06})),h=new Array(5);h[0]={position:0,color:new o.h(1,1,1,1)},h[1]={position:a,color:this.config.baseColor},h[2]={position:c,color:u},h[3]={position:.99,color:d},h[4]={position:1,color:new o.h(0,0,0,1)};const g=new y(h);this.palette=new Array(t);for(let e=0;e<t;e++){const n=g.getColor(e/(t-1),f.RGB);this.palette[e]=n}}matchRelativeLuminanceIndex(t,e){let n=Number.MAX_VALUE,i=0,r=0;const o=e.length;for(;r<o;r++){const o=Math.abs((0,s.rp)(e[r])-t);o<n&&(n=o,i=r)}return i}}C.defaultPaletteConfig={baseColor:(0,w.in)("#808080"),steps:94};var k=n(42355);function S(t,e,n=0,i=t.length-1){if(i===n)return t[n];const r=Math.floor((i-n)/2)+n;return e(t[r])?S(t,e,n,r):S(t,e,r+1,i)}var $=n(64087),T=n(32470);const L=Object.freeze({create:t=>O.from(t)});class O{constructor(t,e){this.closestIndexCache=new Map,this.source=t,this.swatches=e,this.reversedSwatches=Object.freeze([...this.swatches].reverse()),this.lastIndex=this.swatches.length-1}colorContrast(t,e,n,i){void 0===n&&(n=this.closestIndexOf(t));let r=this.swatches;const o=this.lastIndex;let s=n;void 0===i&&(i=(0,$.a)(t));return-1===i&&(r=this.reversedSwatches,s=o-s),S(r,(n=>(0,T.$)(t,n)>=e),s,o)}get(t){return this.swatches[t]||this.swatches[(0,r.uZ)(t,0,this.lastIndex)]}closestIndexOf(t){if(this.closestIndexCache.has(t.relativeLuminance))return this.closestIndexCache.get(t.relativeLuminance);let e=this.swatches.indexOf(t);if(-1!==e)return this.closestIndexCache.set(t.relativeLuminance,e),e;const n=this.swatches.reduce(((e,n)=>Math.abs(n.relativeLuminance-t.relativeLuminance)<Math.abs(e.relativeLuminance-t.relativeLuminance)?n:e));return e=this.swatches.indexOf(n),this.closestIndexCache.set(t.relativeLuminance,e),e}static from(t){return new O(t,Object.freeze(new C({baseColor:o.h.fromObject(t)}).palette.map((t=>{const e=(0,w.in)(t.toStringHexRGB());return k.w.create(e.r,e.g,e.b)}))))}}},27153:function(t,e,n){"use strict";n.d(e,{a:function(){return o},y:function(){return r}});var i=n(64087);function r(t,e){return t.colorContrast(e,3.5)}function o(t,e,n){return t.colorContrast(n,3.5,t.closestIndexOf(t.source),-1*(0,i.a)(e))}},42355:function(t,e,n){"use strict";n.d(e,{w:function(){return s}});var i=n(2696),r=n(60279),o=n(32470);const s=Object.freeze({create:(t,e,n)=>new a(t,e,n),from:t=>new a(t.r,t.g,t.b)});class a extends i.h{constructor(t,e,n){super(t,e,n,1),this.toColorString=this.toStringHexRGB,this.contrast=o.$.bind(null,this),this.createCSS=this.toColorString,this.relativeLuminance=(0,r.hM)(this)}static fromObject(t){return new a(t.r,t.g,t.b)}}},78125:function(t,e,n){"use strict";n.d(e,{C:function(){return o},h:function(){return i}});var i,r=n(42355);function o(t){return r.w.create(t,t,t)}!function(t){t[t.LightMode=1]="LightMode",t[t.DarkMode=.23]="DarkMode"}(i||(i={}))},56185:function(t,e,n){"use strict";n.d(e,{Pc:function(){return a},Sn:function(){return s},Us:function(){return l},ix:function(){return o}});var i=n(97186),r=n(42355);const o=r.w.create(1,1,1),s=r.w.create(0,0,0),a=r.w.create(.5,.5,.5),c=(0,i.in)("#0078D4"),l=r.w.create(c.r,c.g,c.b)},64087:function(t,e,n){"use strict";n.d(e,{a:function(){return r}});var i=n(3794);function r(t){return(0,i._)(t)?-1:1}},3794:function(t,e,n){"use strict";n.d(e,{_:function(){return r}});const i=(-.1+Math.sqrt(.21))/2;function r(t){return t.relativeLuminance<=i}},32470:function(t,e,n){"use strict";function i(t,e){const n=t.relativeLuminance>e.relativeLuminance?t:e,i=t.relativeLuminance>e.relativeLuminance?e:t;return(n.relativeLuminance+.05)/(i.relativeLuminance+.05)}n.d(e,{$:function(){return i}})},35883:function(t,e,n){"use strict";n.d(e,{D:function(){return Q}});var i=n(33940),r=n(97186),o=n(78923),s=n(29717),a=n(22798),c=n(28904),l=n(99452),u=n(42590),d=n(49218),h=n(27186),f=n(89949),p=n(42355),g=n(38492),v=n(42689),m=n(30363),b=n(56837),y=n(40009),w=n(66779),x=n(55135),C=n(26512),k=n(10970),S=n(3490),$=n(16549),T=n(27782),L=n(32572),O=n(94585),I=n(68530),E=n(12499),D=n(52704);const{create:R}=D.L,A=R("type-ramp-plus-5-font-size","32px"),B=R("type-ramp-plus-5-line-height","40px"),{create:F}=D.L,H=F("type-ramp-plus-6-font-size","40px"),M=F("type-ramp-plus-6-line-height","52px");var N=n(47999),P=n(18681),j=n(20246),_=n(37939),z=n(84659),V=n(12600),U=n(92332),q=n(72582),W=n(14744),K=n(59845),Z=n(63070);const G={toView:t=>null==t?null:t?.toColorString(),fromView(t){if(null==t)return null;const e=(0,r.in)(t);return e?p.w.create(e.r,e.g,e.b):null}},X=o.i`
    :host {
        background-color: ${g.I};
        color: ${v.C};
    }
`.withBehaviors((0,s.vF)(o.i`
            :host {
                background-color: ${a.H.Canvas};
                box-shadow: 0 0 0 1px ${a.H.CanvasText};
                color: ${a.H.CanvasText};
            }
        `));function J(t){return(e,n)=>{e[n+"Changed"]=function(e,n){null!=n?t.setValueFor(this,n):t.deleteValueFor(this)}}}class Y extends c.H{constructor(){super(),this.noPaint=!1,l.y$.getNotifier(this).subscribe({handleChange:this.noPaintChanged.bind(this)},"fillColor")}noPaintChanged(){this.noPaint||void 0===this.fillColor?this.$fastController.removeStyles(X):this.$fastController.addStyles(X)}accentBaseColorChanged(t,e){null!=e?m.a.setValueFor(this,f.v.create(e)):m.a.deleteValueFor(this)}neutralBaseColorChanged(t,e){null!=e?b.y.setValueFor(this,f.v.create(e)):b.y.deleteValueFor(this)}}(0,i.gn)([(0,u.Lj)({attribute:"no-paint",mode:"boolean"}),(0,i.w6)("design:type",Object)],Y.prototype,"noPaint",void 0),(0,i.gn)([(0,u.Lj)({attribute:"fill-color",converter:G}),J(g.I),(0,i.w6)("design:type",Object)],Y.prototype,"fillColor",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-base-color",converter:G,mode:"fromView"}),(0,i.w6)("design:type",Object)],Y.prototype,"accentBaseColor",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-base-color",converter:G,mode:"fromView"}),(0,i.w6)("design:type",Object)],Y.prototype,"neutralBaseColor",void 0),(0,i.gn)([l.LO,J(b.y),(0,i.w6)("design:type",Object)],Y.prototype,"neutralPalette",void 0),(0,i.gn)([l.LO,J(m.a),(0,i.w6)("design:type",Object)],Y.prototype,"accentPalette",void 0),(0,i.gn)([(0,u.Lj)({converter:u.Id}),J(y.hV),(0,i.w6)("design:type",Number)],Y.prototype,"density",void 0),(0,i.gn)([(0,u.Lj)({attribute:"design-unit",converter:u.Id}),J(y._5),(0,i.w6)("design:type",Number)],Y.prototype,"designUnit",void 0),(0,i.gn)([(0,u.Lj)({attribute:"direction"}),J(w.o),(0,i.w6)("design:type",String)],Y.prototype,"direction",void 0),(0,i.gn)([(0,u.Lj)({attribute:"base-height-multiplier",converter:u.Id}),J(y.nf),(0,i.w6)("design:type",Number)],Y.prototype,"baseHeightMultiplier",void 0),(0,i.gn)([(0,u.Lj)({attribute:"base-horizontal-spacing-multiplier",converter:u.Id}),J(y.LQ),(0,i.w6)("design:type",Number)],Y.prototype,"baseHorizontalSpacingMultiplier",void 0),(0,i.gn)([(0,u.Lj)({attribute:"control-corner-radius",converter:u.Id}),J(x.UW),(0,i.w6)("design:type",Number)],Y.prototype,"controlCornerRadius",void 0),(0,i.gn)([(0,u.Lj)({attribute:"stroke-width",converter:u.Id}),J(C.H),(0,i.w6)("design:type",Number)],Y.prototype,"strokeWidth",void 0),(0,i.gn)([(0,u.Lj)({attribute:"focus-stroke-width",converter:u.Id}),J(C.vx),(0,i.w6)("design:type",Number)],Y.prototype,"focusStrokeWidth",void 0),(0,i.gn)([(0,u.Lj)({attribute:"disabled-opacity",converter:u.Id}),J(k.V),(0,i.w6)("design:type",Number)],Y.prototype,"disabledOpacity",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-minus-2-font-size"}),J(S.G),(0,i.w6)("design:type",String)],Y.prototype,"typeRampMinus2FontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-minus-2-line-height"}),J(S.M),(0,i.w6)("design:type",String)],Y.prototype,"typeRampMinus2LineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-minus-1-font-size"}),J($.s),(0,i.w6)("design:type",String)],Y.prototype,"typeRampMinus1FontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-minus-1-line-height"}),J($.v),(0,i.w6)("design:type",String)],Y.prototype,"typeRampMinus1LineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-base-font-size"}),J(T.c),(0,i.w6)("design:type",String)],Y.prototype,"typeRampBaseFontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-base-line-height"}),J(T.R),(0,i.w6)("design:type",String)],Y.prototype,"typeRampBaseLineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-1-font-size"}),J(L.P),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus1FontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-1-line-height"}),J(L.b),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus1LineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-2-font-size"}),J(O.m),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus2FontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-2-line-height"}),J(O.I),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus2LineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-3-font-size"}),J(I.i),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus3FontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-3-line-height"}),J(I.y),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus3LineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-4-font-size"}),J(E.I),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus4FontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-4-line-height"}),J(E.i),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus4LineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-5-font-size"}),J(A),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus5FontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-5-line-height"}),J(B),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus5LineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-6-font-size"}),J(H),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus6FontSize",void 0),(0,i.gn)([(0,u.Lj)({attribute:"type-ramp-plus-6-line-height"}),J(M),(0,i.w6)("design:type",String)],Y.prototype,"typeRampPlus6LineHeight",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-fill-rest-delta",converter:u.Id}),J(N.N7),(0,i.w6)("design:type",Number)],Y.prototype,"accentFillRestDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-fill-hover-delta",converter:u.Id}),J(N.B2),(0,i.w6)("design:type",Number)],Y.prototype,"accentFillHoverDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-fill-active-delta",converter:u.Id}),J(N.Wo),(0,i.w6)("design:type",Number)],Y.prototype,"accentFillActiveDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-fill-focus-delta",converter:u.Id}),J(N.v$),(0,i.w6)("design:type",Number)],Y.prototype,"accentFillFocusDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-foreground-rest-delta",converter:u.Id}),J(P.kp),(0,i.w6)("design:type",Number)],Y.prototype,"accentForegroundRestDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-foreground-hover-delta",converter:u.Id}),J(P.L8),(0,i.w6)("design:type",Number)],Y.prototype,"accentForegroundHoverDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-foreground-active-delta",converter:u.Id}),J(P.kb),(0,i.w6)("design:type",Number)],Y.prototype,"accentForegroundActiveDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"accent-foreground-focus-delta",converter:u.Id}),J(P.nd),(0,i.w6)("design:type",Number)],Y.prototype,"accentForegroundFocusDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-rest-delta",converter:u.Id}),J(j.MY),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillRestDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-hover-delta",converter:u.Id}),J(j.jW),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillHoverDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-active-delta",converter:u.Id}),J(j.hD),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillActiveDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-focus-delta",converter:u.Id}),J(j.VQ),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillFocusDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-input-rest-delta",converter:u.Id}),J(_.ef),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillInputRestDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-input-hover-delta",converter:u.Id}),J(_.EL),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillInputHoverDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-input-active-delta",converter:u.Id}),J(_.q_),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillInputActiveDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-input-focus-delta",converter:u.Id}),J(_.Zb),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillInputFocusDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-layer-rest-delta",converter:u.Id}),J(z.Y),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillLayerRestDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-stealth-rest-delta",converter:u.Id}),J(V.qD),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillStealthRestDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-stealth-hover-delta",converter:u.Id}),J(V.fq),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillStealthHoverDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-stealth-active-delta",converter:u.Id}),J(V.gs),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillStealthActiveDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-stealth-focus-delta",converter:u.Id}),J(V.IU),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillStealthFocusDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-strong-hover-delta",converter:u.Id}),J(U.sc),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillStrongHoverDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-strong-active-delta",converter:u.Id}),J(U.Vt),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillStrongActiveDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-fill-strong-focus-delta",converter:u.Id}),J(U.UC),(0,i.w6)("design:type",Number)],Y.prototype,"neutralFillStrongFocusDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"base-layer-luminance",converter:u.Id}),J(q.q),(0,i.w6)("design:type",Number)],Y.prototype,"baseLayerLuminance",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-stroke-divider-rest-delta",converter:u.Id}),J(W.h),(0,i.w6)("design:type",Number)],Y.prototype,"neutralStrokeDividerRestDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-stroke-rest-delta",converter:u.Id}),J(K.fd),(0,i.w6)("design:type",Number)],Y.prototype,"neutralStrokeRestDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-stroke-hover-delta",converter:u.Id}),J(K.rn),(0,i.w6)("design:type",Number)],Y.prototype,"neutralStrokeHoverDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-stroke-active-delta",converter:u.Id}),J(K.IM),(0,i.w6)("design:type",Number)],Y.prototype,"neutralStrokeActiveDelta",void 0),(0,i.gn)([(0,u.Lj)({attribute:"neutral-stroke-focus-delta",converter:u.Id}),J(K.Bx),(0,i.w6)("design:type",Number)],Y.prototype,"neutralStrokeFocusDelta",void 0);const Q=Y.compose({name:`${Z.H.prefix}-design-system-provider`,template:d.dy`
        <slot></slot>
    `,styles:o.i`
        ${(0,h.j)("block")}
    `})},40894:function(t,e,n){"use strict";n.d(e,{D:function(){return g}});var i=n(36585),r=n(63070),o=n(78923),s=n(62734),a=n(55135),c=n(38492),l=n(26512);const u=o.i` :host([hidden]){display:none}:host{--elevation:14;--dialog-height:480px;--dialog-width:640px;display:block}.overlay{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.3);touch-action:none}.positioning-region{display:flex;justify-content:center;position:fixed;top:0;bottom:0;left:0;right:0;overflow:auto}.control{${s.XC} margin-top:auto;margin-bottom:auto;border-radius:calc(${a.rS} * 1px);width:var(--dialog-width);height:var(--dialog-height);background:${c.I};z-index:1;border:calc(${l.H} * 1px) solid transparent}`;var d=n(49218),h=n(93703),f=n(41472);const p=d.dy`
        <div class="positioning-region" part="positioning-region">
            ${(0,h.g)((t=>t.modal),d.dy`
                    <div
                        class="overlay"
                        part="overlay"
                        role="presentation"
                        @click="${t=>t.dismiss()}"
                    ></div>
                `)}
            <div
                role="dialog"
                tabindex="-1"
                class="control"
                part="control"
                aria-modal="${t=>t.modal?t.modal:void 0}"
                aria-describedby="${t=>t.ariaDescribedby}"
                aria-labelledby="${t=>t.ariaLabelledby}"
                aria-label="${t=>t.ariaLabel}"
                ${(0,f.i)("dialog")}
            >
                <slot></slot>
            </div>
        </div>
    `,g=i.U.compose({name:`${r.H.prefix}-dialog`,template:p,styles:u})},2785:function(t,e,n){"use strict";n.d(e,{V:function(){return m},D:function(){return b}});var i=n(33940),r=n(28904),o=n(42590),s=n(97380);const a="separator";class c extends r.H{constructor(){super(...arguments),this.role=a,this.orientation=s.i.horizontal}}(0,i.gn)([o.Lj,(0,i.w6)("design:type",String)],c.prototype,"role",void 0),(0,i.gn)([o.Lj,(0,i.w6)("design:type",String)],c.prototype,"orientation",void 0);var l=n(63070),u=n(78923),d=n(27186),h=n(40009),f=n(26512),p=n(71942);const g=u.i` ${(0,d.j)("block")} :host{box-sizing:content-box;height:0;margin:calc(${h._5} * 1px) 0;border:none;border-top:calc(${f.H} * 1px) solid ${p.dt}}`;const v=n(49218).dy`
        <template
            role="${t=>t.role}"
            aria-orientation="${t=>t.orientation}"
        ></template>
    `;class m extends c{}const b=m.compose({name:`${l.H.prefix}-divider`,template:v,styles:g})},63070:function(t,e,n){"use strict";n.d(e,{H:function(){return i}});const i=Object.freeze({prefix:"fluent",shadowRootMode:"open",registry:customElements})},55024:function(t,e,n){"use strict";n.d(e,{b:function(){return o}});var i=n(65799),r=n(26254);class o extends i.n{connectedCallback(){super.connectedCallback(),"mobile"!==this.view&&this.$fastController.addStyles(r.P)}}},18449:function(t,e,n){"use strict";n.d(e,{D:function(){return a}});var i=n(3193),r=n(63070),o=n(77670);const s=(0,n(36393).T)(),a=i.Qm.compose({name:`${r.H.prefix}-option`,template:s,styles:o.W})},20158:function(t,e,n){"use strict";n.d(e,{D:function(){return R}});var i=n(63070),r=n(79406),o=n(78923),s=n(27186),a=n(67739),c=n(24484),l=n(29717),u=n(22798),d=n(2658),h=n(53131),f=n(40009),p=n(42689),g=n(27782),v=n(55135),m=n(26512),b=n(17993),y=n(80260),w=n(10970),x=n(74449);const C=o.i` ${(0,s.j)("grid")} :host{contain:layout;overflow:visible;font-family:${h.S};outline:none;box-sizing:border-box;height:calc(${d.i} * 1px);grid-template-columns:minmax(42px,auto) 1fr minmax(42px,auto);grid-template-rows:auto;justify-items:center;align-items:center;padding:0;margin:0 calc(${f._5} * 1px);white-space:nowrap;color:${p.C};fill:currentcolor;cursor:pointer;font-size:${g.c};line-height:${g.R};border-radius:calc(${v.UW} * 1px);border:calc(${m.H} * 1px) solid transparent;position:relative}:host([start-column-count="0"]){grid-template-columns:auto 1fr minmax(42px,auto)}:host([start-column-count="0"]) .content{grid-column:1;grid-row:1;margin-inline-start:10px}:host([start-column-count="0"]) .expand-collapse-glyph-container{grid-column:5;grid-row:1}:host([start-column-count="2"]){grid-template-columns:minmax(42px,auto) minmax(42px,auto) 1fr minmax(42px,auto) minmax(42px,auto)}:host([start-column-count="2"]) .content{grid-column:3;grid-row:1;margin-inline-start:10px}:host([start-column-count="2"]) .expand-collapse-glyph-container{grid-column:5;grid-row:1}:host([start-column-count="2"]) ::slotted([slot="start"]){grid-column:2}:host([start-column-count="2"]) ::slotted([slot="end"]){grid-column:4}:host(:${a.b}){border:calc(${m.H} * 1px) solid ${b.yG};box-shadow:0 0 0 calc((${m.vx} - ${m.H}) * 1px) ${b.yG}}:host(:hover){background:${y.Qp}}:host([aria-checked="true"]),:host(:active),:host([aria-expanded="true"]){background:${y.sG};color:${p.C};z-index:2}:host([disabled]){cursor:${c.H};opacity:${w.V}}:host([disabled]:hover) ::slotted([slot="start"]),:host([disabled]:hover) ::slotted([slot="end"]),:host([disabled]:hover)::slotted(svg){fill:currentcolor}.content{grid-column-start:2;justify-self:start;overflow:hidden;text-overflow:ellipsis}::slotted([slot="start"]),::slotted([slot="end"]){display:flex;justify-content:center}::slotted([slot="end"]){grid-column:3}::slotted(svg){${""} width:16px;height:16px;display:flex}:host(:hover) ::slotted([slot="start"]),:host(:hover) ::slotted([slot="end"]),:host(:hover)::slotted(svg),:host(:active) ::slotted([slot="start"]),:host(:active) ::slotted([slot="end"]),:host(:active)::slotted(svg){fill:${p.C}}:host([start-column-count="0"][aria-haspopup="menu"]){display:grid;grid-template-columns:minmax(42px,auto) auto 1fr minmax(42px,auto) minmax( 42px,auto );align-items:center;min-height:32px}:host([start-column-count="1"][aria-haspopup="menu"]),:host([start-column-count="1"][role="menuitemcheckbox"]),:host([start-column-count="1"][role="menuitemradio"]){display:grid;grid-template-columns:minmax(42px,auto) auto 1fr minmax(42px,auto) minmax( 42px,auto );align-items:center;min-height:32px}:host([start-column-count="2"]:not([aria-haspopup="menu"])) ::slotted([slot="end"]){grid-column:5}:host .input-container,:host .expand-collapse-glyph-container{display:none}:host([aria-haspopup="menu"]) .expand-collapse-glyph-container,:host([role="menuitemcheckbox"]) .input-container,:host([role="menuitemradio"]) .input-container{display:grid;margin-inline-end:10px}:host([aria-haspopup="menu"]) .content,:host([role="menuitemcheckbox"]) .content,:host([role="menuitemradio"]) .content{grid-column-start:3}:host([aria-haspopup="menu"][start-column-count="0"]) .content{grid-column-start:1}:host([aria-haspopup="menu"]) ::slotted([slot="end"]),:host([role="menuitemcheckbox"]) ::slotted([slot="end"]),:host([role="menuitemradio"]) ::slotted([slot="end"]){grid-column-start:4}:host .expand-collapse,:host .checkbox,:host .radio{display:flex;align-items:center;justify-content:center;position:relative;width:20px;height:20px;box-sizing:border-box;outline:none;margin-inline-start:10px}:host .checkbox{border-radius:calc(${v.UW} * 1px)}:host .radio{border-radius:999px}:host .checkbox-indicator,:host .radio-indicator,::slotted([slot="checkbox-indicator"]),::slotted([slot="radio-indicator"]){display:none}::slotted([slot="end"]:not(svg)){margin-inline-end:10px;color:${x.Q}}:host([aria-checked="true"]) .checkbox-indicator,:host([aria-checked="true"]) ::slotted([slot="checkbox-indicator"]){width:100%;height:100%;display:block;fill:${p.C};pointer-events:none}:host([aria-checked="true"]) .radio-indicator{display:block;pointer-events:none}:host([aria-checked="true"]) ::slotted([slot="radio-indicator"]){display:block;pointer-events:none}.submenu-container{position:fixed;top:0;left:0;z-index:1}`.withBehaviors((0,l.vF)(o.i` :host{forced-color-adjust:none;border-color:transparent;color:${u.H.ButtonText};fill:${u.H.ButtonText}}:host(:hover){background:${u.H.Highlight};color:${u.H.HighlightText}}:host(:hover) ::slotted([slot="start"]),:host(:hover) ::slotted([slot="end"]),:host(:hover)::slotted(svg),:host(:active) ::slotted([slot="start"]),:host(:active) ::slotted([slot="end"]),:host(:active)::slotted(svg){fill:${u.H.HighlightText}}:host([aria-expanded="true"]){background:${u.H.Highlight};border-color:${u.H.Highlight};color:${u.H.HighlightText}}:host(:${a.b}){background:${u.H.Highlight};border-color:${u.H.ButtonText};box-shadow:0 0 0 calc(${m.H} * 1px) inset ${u.H.HighlightText};color:${u.H.HighlightText};fill:currentcolor}:host([disabled]),:host([disabled]:hover),:host([disabled]:hover) ::slotted([slot="start"]),:host([disabled]:hover) ::slotted([slot="end"]),:host([disabled]:hover)::slotted(svg){background:${u.H.Canvas};color:${u.H.GrayText};fill:currentcolor;opacity:1}:host .expanded-toggle,:host .checkbox,:host .radio{border-color:${u.H.ButtonText};background:${u.H.HighlightText}}:host([checked="true"]) .checkbox,:host([checked="true"]) .radio{background:${u.H.HighlightText};border-color:${u.H.HighlightText}}:host(:hover) .expanded-toggle,:host(:hover) .checkbox,:host(:hover) .radio,:host(:${a.b}) .expanded-toggle,:host(:${a.b}) .checkbox,:host(:${a.b}) .radio,:host([checked="true"]:hover) .checkbox,:host([checked="true"]:hover) .radio,:host([checked="true"]:${a.b}) .checkbox,:host([checked="true"]:${a.b}) .radio{border-color:${u.H.HighlightText}}:host([aria-checked="true"]){background:${u.H.Highlight};color:${u.H.HighlightText}}:host([aria-checked="true"]) .checkbox-indicator,:host([aria-checked="true"]) ::slotted([slot="checkbox-indicator"]),:host([aria-checked="true"]) ::slotted([slot="radio-indicator"]){fill:${u.H.Highlight}}:host([aria-checked="true"]) .radio-indicator{background:${u.H.Highlight}}`));var k=n(49218),S=n(93703),$=n(41472),T=n(47548),L=n(95185),O=n(71695),I=n(17503),E=n(12695);const D=function(t={}){return k.dy`
    <template
        aria-haspopup="${t=>t.hasSubmenu?"menu":void 0}"
        aria-checked="${t=>t.role!==E.O.menuitem?t.checked:void 0}"
        aria-disabled="${t=>t.disabled}"
        aria-expanded="${t=>t.expanded}"
        @keydown="${(t,e)=>t.handleMenuItemKeyDown(e.event)}"
        @click="${(t,e)=>t.handleMenuItemClick(e.event)}"
        @mouseover="${(t,e)=>t.handleMouseOver(e.event)}"
        @mouseout="${(t,e)=>t.handleMouseOut(e.event)}"
    >
            ${(0,S.g)((t=>t.role===E.O.menuitemcheckbox),k.dy`
                    <div part="input-container" class="input-container">
                        <span part="checkbox" class="checkbox">
                            <slot name="checkbox-indicator">
                                ${(0,I.A)(t.checkboxIndicator)}
                            </slot>
                        </span>
                    </div>
                `)}
            ${(0,S.g)((t=>t.role===E.O.menuitemradio),k.dy`
                    <div part="input-container" class="input-container">
                        <span part="radio" class="radio">
                            <slot name="radio-indicator">
                                ${(0,I.A)(t.radioIndicator)}
                            </slot>
                        </span>
                    </div>
                `)}
        </div>
        ${(0,O.m9)(t)}
        <span class="content" part="content">
            <slot></slot>
        </span>
        ${(0,O.LC)(t)}
        ${(0,S.g)((t=>t.hasSubmenu),k.dy`
                <div
                    part="expand-collapse-glyph-container"
                    class="expand-collapse-glyph-container"
                >
                    <span part="expand-collapse" class="expand-collapse">
                        <slot name="expand-collapse-indicator">
                            ${(0,I.A)(t.expandCollapseGlyph)}
                        </slot>
                    </span>
                </div>
            `)}
        <span
            ?hidden="${t=>!t.expanded}"
            class="submenu-container"
            part="submenu-container"
            ${(0,$.i)("submenuContainer")}
        >
            <slot name="submenu" ${(0,T.Q)({property:"slottedSubmenu",filter:(0,L.R)("[role='menu']")})}></slot>
        </span>
    </template>
    `}({checkboxIndicator:k.dy`
        <svg
            aria-hidden="true"
            part="checkbox-indicator"
            class="checkbox-indicator"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.143 12.6697L15.235 4.5L16.8 5.90363L8.23812 15.7667L3.80005 11.2556L5.27591 9.7555L8.143 12.6697Z"
            />
        </svg>
    `,expandCollapseGlyph:k.dy`
        <svg
            viewBox="0 0 16 16"
            xmlns="http://www.w3.org/2000/svg"
            class="expand-collapse-glyph"
            part="expand-collapse-glyph"
        >
            <path
                d="M5.00001 12.3263C5.00124 12.5147 5.05566 12.699 5.15699 12.8578C5.25831 13.0167 5.40243 13.1437 5.57273 13.2242C5.74304 13.3047 5.9326 13.3354 6.11959 13.3128C6.30659 13.2902 6.4834 13.2152 6.62967 13.0965L10.8988 8.83532C11.0739 8.69473 11.2153 8.51658 11.3124 8.31402C11.4096 8.11146 11.46 7.88966 11.46 7.66499C11.46 7.44033 11.4096 7.21853 11.3124 7.01597C11.2153 6.81341 11.0739 6.63526 10.8988 6.49467L6.62967 2.22347C6.48274 2.10422 6.30501 2.02912 6.11712 2.00691C5.92923 1.9847 5.73889 2.01628 5.56823 2.09799C5.39757 2.17969 5.25358 2.30817 5.153 2.46849C5.05241 2.62882 4.99936 2.8144 5.00001 3.00369V12.3263Z"
            />
        </svg>
    `,radioIndicator:k.dy`
        <svg
            aria-hidden="true"
            part="radio-indicator"
            class="radio-indicator"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M8.143 12.6697L15.235 4.5L16.8 5.90363L8.23812 15.7667L3.80005 11.2556L5.27591 9.7555L8.143 12.6697Z"
            />
        </svg>
    `}),R=r.h.compose({name:`${i.H.prefix}-menu-item`,template:D,styles:C})},79406:function(t,e,n){"use strict";n.d(e,{h:function(){return s}});var i=n(33940),r=n(41034),o=n(42590);class s extends r.WN{connectedCallback(){super.connectedCallback(),this.startColumnCount||(this.startColumnCount=1)}}(0,i.gn)([(0,o.Lj)({attribute:"start-column-count"}),(0,i.w6)("design:type",Object)],s.prototype,"startColumnCount",void 0)},85469:function(t,e,n){"use strict";n.d(e,{D:function(){return O}});var i=n(63070),r=n(33940),o=n(28904),s=n(58968),a=n(99452),c=n(7986),l=n(94537),u=n(12695),d=n(41034);class h extends o.H{constructor(){super(...arguments),this.expandedItem=null,this.focusIndex=-1,this.isNestedMenu=()=>null!==this.parentElement&&(0,c.Re)(this.parentElement)&&"menuitem"===this.parentElement.getAttribute("role"),this.handleFocusOut=t=>{if(!this.contains(t.relatedTarget)&&void 0!==this.menuItems){this.collapseExpandedItem();const t=this.menuItems.findIndex(this.isFocusableElement);this.menuItems[this.focusIndex].setAttribute("tabindex","-1"),this.menuItems[t].setAttribute("tabindex","0"),this.focusIndex=t}},this.handleItemFocus=t=>{const e=t.target;void 0!==this.menuItems&&e!==this.menuItems[this.focusIndex]&&(this.menuItems[this.focusIndex].setAttribute("tabindex","-1"),this.focusIndex=this.menuItems.indexOf(e),e.setAttribute("tabindex","0"))},this.handleExpandedChanged=t=>{if(t.defaultPrevented||null===t.target||void 0===this.menuItems||this.menuItems.indexOf(t.target)<0)return;t.preventDefault();const e=t.target;null===this.expandedItem||e!==this.expandedItem||!1!==e.expanded?e.expanded&&(null!==this.expandedItem&&this.expandedItem!==e&&(this.expandedItem.expanded=!1),this.menuItems[this.focusIndex].setAttribute("tabindex","-1"),this.expandedItem=e,this.focusIndex=this.menuItems.indexOf(e),e.setAttribute("tabindex","0")):this.expandedItem=null},this.changeHandler=t=>{if(void 0===this.menuItems)return;const e=t.target,n=this.menuItems.indexOf(e);if(-1!==n&&"menuitemradio"===e.role&&!0===e.checked){for(let t=n-1;t>=0;--t){const e=this.menuItems[t],n=e.getAttribute("role");if(n===u.O.menuitemradio&&(e.checked=!1),"separator"===n)break}const t=this.menuItems.length-1;for(let e=n+1;e<=t;++e){const t=this.menuItems[e],n=t.getAttribute("role");if(n===u.O.menuitemradio&&(t.checked=!1),"separator"===n)break}}},this.isMenuItemElement=t=>t instanceof d.WN||(0,c.Re)(t)&&t.getAttribute("role")in h.focusableElementRoles,this.isFocusableElement=t=>this.isMenuItemElement(t)}itemsChanged(t,e){this.$fastController.isConnected&&void 0!==this.menuItems&&this.setItems()}connectedCallback(){super.connectedCallback(),s.H.enqueue((()=>{this.setItems()})),this.addEventListener("change",this.changeHandler)}disconnectedCallback(){super.disconnectedCallback(),this.removeItemListeners(),this.menuItems=void 0,this.removeEventListener("change",this.changeHandler)}focus(){this.setFocus(0,1)}collapseExpandedItem(){null!==this.expandedItem&&(this.expandedItem.expanded=!1,this.expandedItem=null)}handleMenuKeyDown(t){if(!t.defaultPrevented&&void 0!==this.menuItems)switch(t.key){case l.iF:return void this.setFocus(this.focusIndex+1,1);case l.SB:return void this.setFocus(this.focusIndex-1,-1);case l.Kh:return void this.setFocus(this.menuItems.length-1,-1);case l.tU:return void this.setFocus(0,1);default:return!0}}removeItemListeners(t=this.items){t.forEach((t=>{t.removeEventListener("focus",this.handleItemFocus),t.removeEventListener("expanded-changed",this.handleExpandedChanged),a.y$.getNotifier(t).unsubscribe(this,"hidden")}))}setItems(){const t=Array.from(this.children);this.removeItemListeners(t),t.forEach((t=>a.y$.getNotifier(t).subscribe(this,"hidden")));const e=t.filter((t=>!t.hasAttribute("hidden")));this.menuItems=e;const n=this.menuItems.filter(this.isMenuItemElement);n.length&&(this.focusIndex=0),n.forEach(((t,e)=>{t.setAttribute("tabindex",0===e?"0":"-1"),t.addEventListener("expanded-change",this.handleExpandedChanged),t.addEventListener("focus",this.handleItemFocus)}))}handleChange(t,e){"hidden"===e&&this.setItems()}setFocus(t,e){if(void 0!==this.menuItems)for(;t>=0&&t<this.menuItems.length;){const n=this.menuItems[t];if(this.isFocusableElement(n)){this.focusIndex>-1&&this.menuItems.length>=this.focusIndex-1&&this.menuItems[this.focusIndex].setAttribute("tabindex","-1"),this.focusIndex=t,n.setAttribute("tabindex","0"),n.focus();break}t+=e}}}h.focusableElementRoles=u.O,(0,r.gn)([a.LO,(0,r.w6)("design:type",Array)],h.prototype,"items",void 0);var f=n(38492),p=n(79406),g=n(27460);class v extends h{setItems(){super.setItems();const t=this.menuItems?.filter(this.isMenuItemElement);t?.forEach(((e,n)=>{const i=t?.reduce(((t,e)=>{const n=v.elementIndent(e);return Math.max(t,n)}),0);e instanceof p.h&&(e.startColumnCount=i)}))}static elementIndent(t){const e=t.getAttribute("role"),n=t.querySelector("[slot=start]");return e&&e!==u.O.menuitem?n?2:1:n?1:0}connectedCallback(){super.connectedCallback(),f.I.setValueFor(this,g.s)}}var m=n(78923),b=n(27186),y=n(62734),w=n(26512),x=n(55135),C=n(40009),k=n(71942);const S=m.i` ${(0,b.j)("block")} :host{--elevation:11;background:${f.I};border:calc(${w.H} * 1px) solid transparent;border-radius:${x.rS};${y.XC} margin:0;border-radius:calc(${x.UW} * 1px);padding:calc(${C._5} * 1px) 0;max-width:368px;min-width:64px}:host([slot="submenu"]){width:max-content;margin:0 calc(${C._5} * 1px)}::slotted(hr){box-sizing:content-box;height:0;margin:0;border:none;border-top:calc(${w.H} * 1px) solid ${k.dt}}`;var $=n(49218),T=n(47548);const L=$.dy`
        <template
            slot="${t=>t.slot?t.slot:t.isNestedMenu()?"submenu":void 0}"
            role="menu"
            @keydown="${(t,e)=>t.handleMenuKeyDown(e.event)}"
            @focusout="${(t,e)=>t.handleFocusOut(e.event)}"
        >
            <slot ${(0,T.Q)("items")}></slot>
        </template>
    `,O=v.compose({name:`${i.H.prefix}-menu`,template:L,styles:S})},79659:function(t,e,n){"use strict";n.d(e,{D:function(){return l}});var i=n(63070),r=n(53839),o=n(74724),s=n(3923),a=n(49218);const c=(0,s.U)({indicator:a.dy`
        <svg
            class="select-indicator"
            part="select-indicator"
            viewBox="0 0 12 7"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M11.85.65c.******* 0 .7L6.4 6.84a.55.55 0 01-.78 0L.14 1.35a.5.5 0 11.71-.7L6 5.8 11.15.65c.2-.2.5-.2.7 0z"
            />
        </svg>
    `}),l=r.x.compose({name:`${i.H.prefix}-select`,template:c,styles:o.W2})},53839:function(t,e,n){"use strict";n.d(e,{x:function(){return y}});var i=n(33940),r=n(55090),o=n(58968),s=n(99452),a=n(42590),c=n(62512),l=n(94537),u=n(98296),d=n(71695),h=n(31289),f=n(13031),p=n(23526);class g extends f.b{}class v extends((0,p.Um)(g)){constructor(){super(...arguments),this.proxy=document.createElement("select")}}class m extends v{constructor(){super(...arguments),this.open=!1,this.shiftListbox="0",this.listboxId=(0,c.EL)("listbox-")}shiftListboxChanged(t,e){void 0!==e&&this.setPositioning()}openChanged(t,e){if(this.collapsible){if(this.open)return this.ariaControls=this.listboxId,this.ariaExpanded="true",o.H.enqueue((()=>this.setPositioning())),this.focusAndScrollOptionIntoView(),this.indexWhenOpened=this.selectedIndex,void o.H.enqueue((()=>this.focus()));this.cleanup?.(),this.ariaControls="",this.ariaExpanded="false"}}get collapsible(){return!(this.multiple||"number"==typeof this.size)}get value(){return s.y$.track(this,"value"),this._value}set value(t){const e=`${this._value}`;if(this._options?.length){const e=this._options.findIndex((e=>e.value===t)),n=this._options[this.selectedIndex]?.value??null,i=this._options[e]?.value??null;-1!==e&&n===i||(t="",this.selectedIndex=e),t=this.firstSelectedOption?.value??t}e!==t&&(this._value=t,super.valueChanged(e,t),s.y$.notify(this,"value"),this.updateDisplayValue())}updateValue(t){this.$fastController.isConnected&&(this.value=this.firstSelectedOption?.value??""),t&&(this.$emit("input"),this.$emit("change",this,{bubbles:!0,composed:void 0}))}selectedIndexChanged(t,e){super.selectedIndexChanged(t,e),this.updateValue()}setPositioning(){this.$fastController.isConnected&&(this.cleanup=(0,r.Me)(this,this.listbox,(async()=>{const t=this.shiftListbox?parseFloat(this.shiftListbox):0,{middlewareData:e,x:n,y:i}=await(0,r.oo)(this.control,this.listbox,{placement:"bottom",strategy:"fixed",middleware:[(0,r.RR)(),(0,r.uY)({padding:t}),(0,r.dp)({apply:({availableHeight:t,rects:e})=>{Object.assign(this.listbox.style,{maxHeight:`${t}px`,minWidth:`${e.reference.width}px`})}}),(0,r.Cp)()]});e.hide?.referenceHidden?this.open=!1:Object.assign(this.listbox.style,{position:"fixed",top:"0",left:"0",transform:`translate(${n}px, ${i}px)`})})))}get displayValue(){return s.y$.track(this,"displayValue"),this.firstSelectedOption?.text??""}disabledChanged(t,e){super.disabledChanged&&super.disabledChanged(t,e),this.ariaDisabled=this.disabled?"true":"false"}formResetCallback(){this.setProxyOptions(),super.setDefaultSelectedOption(),-1===this.selectedIndex&&(this.selectedIndex=0)}clickHandler(t){if(!this.disabled){if(this.open){const e=t.target.closest("option,[role=option]");if(e&&e.disabled)return}return super.clickHandler(t),this.open=this.collapsible&&!this.open,this.open||this.indexWhenOpened===this.selectedIndex||this.updateValue(!0),!0}}focusoutHandler(t){if(super.focusoutHandler(t),!this.open)return!0;const e=t.relatedTarget;this.isSameNode(e)?this.focus():this.options?.includes(e)||(this.open=!1,this.indexWhenOpened!==this.selectedIndex&&this.updateValue(!0))}handleChange(t,e){super.handleChange(t,e),"value"===e&&this.updateValue()}slottedOptionsChanged(t,e){this.options.forEach((t=>{s.y$.getNotifier(t).unsubscribe(this,"value")})),super.slottedOptionsChanged(t,e),this.options.forEach((t=>{s.y$.getNotifier(t).subscribe(this,"value")})),this.setProxyOptions(),this.updateValue()}mousedownHandler(t){return t.offsetX>=0&&t.offsetX<=this.listbox?.scrollWidth?super.mousedownHandler(t):this.collapsible}multipleChanged(t,e){super.multipleChanged(t,e),this.proxy&&(this.proxy.multiple=e)}selectedOptionsChanged(t,e){super.selectedOptionsChanged(t,e),this.options?.forEach(((t,e)=>{const n=this.proxy?.options.item(e);n&&(n.selected=t.selected)}))}setDefaultSelectedOption(){const t=this.options??Array.from(this.children).filter(u.z.slottedOptionFilter),e=t?.findIndex((t=>t.hasAttribute("selected")||t.selected||t.value===this.value));this.selectedIndex=-1===e?0:e}setProxyOptions(){this.proxy instanceof HTMLSelectElement&&this.options&&(this.proxy.options.length=0,this.options.forEach((t=>{const e=t.proxy||(t instanceof HTMLOptionElement?t.cloneNode():null);e&&this.proxy.options.add(e)})))}keydownHandler(t){super.keydownHandler(t);const e=t.key||t.key.charCodeAt(0);switch(e){case l.BI:t.preventDefault(),this.collapsible&&this.typeAheadExpired&&(this.open=!this.open);break;case l.tU:case l.Kh:t.preventDefault();break;case l.kL:t.preventDefault(),this.open=!this.open;break;case l.CX:this.collapsible&&this.open&&(t.preventDefault(),this.open=!1);break;case l.oM:return this.collapsible&&this.open&&(t.preventDefault(),this.open=!1),!0}return this.open||this.indexWhenOpened===this.selectedIndex||(this.updateValue(!0),this.indexWhenOpened=this.selectedIndex),!(e===l.iF||e===l.SB)}connectedCallback(){super.connectedCallback(),this.addEventListener("contentchange",this.updateDisplayValue)}disconnectedCallback(){this.removeEventListener("contentchange",this.updateDisplayValue),this.cleanup?.(),super.disconnectedCallback()}sizeChanged(t,e){super.sizeChanged(t,e),this.proxy&&(this.proxy.size=e)}updateDisplayValue(){this.collapsible&&s.y$.notify(this,"displayValue")}}(0,i.gn)([(0,a.Lj)({attribute:"open",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],m.prototype,"open",void 0),(0,i.gn)([(0,a.Lj)({attribute:"shift-listbox"}),(0,i.w6)("design:type",String)],m.prototype,"shiftListbox",void 0),(0,i.gn)([s.lk,(0,i.w6)("design:type",Boolean),(0,i.w6)("design:paramtypes",[])],m.prototype,"collapsible",null),(0,i.gn)([s.LO,(0,i.w6)("design:type",HTMLElement)],m.prototype,"control",void 0);class b{}(0,i.gn)([s.LO,(0,i.w6)("design:type",Object)],b.prototype,"ariaControls",void 0),(0,h.e)(b,u.x),(0,h.e)(m,d.hW,b);class y extends m{appearanceChanged(t,e){t!==e&&(this.classList.add(e),this.classList.remove(t))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="outline")}}(0,i.gn)([(0,a.Lj)({mode:"fromView"}),(0,i.w6)("design:type",String)],y.prototype,"appearance",void 0)},74358:function(t,e,n){"use strict";n.d(e,{w:function(){return a}});var i=n(78923),r=n(26512),o=n(958),s=n(55135);const a=i.i`
    :host([appearance="filled"]:not([disabled]):active)::after,
    :host([appearance="filled"]:not([disabled]):focus-within:not(:active))::after {
        content: "";
        position: absolute;
        bottom: 0;
        border-bottom: calc(${r.vx} * 1px) solid ${o.Av};
        border-bottom-left-radius: calc(${s.UW} * 1px);
        border-bottom-right-radius: calc(${s.UW} * 1px);
        z-index: 2;
        transition: all 300ms cubic-bezier(0.1, 0.9, 0.2, 1);
    }

    :host([appearance="filled"]:not([disabled]):active)::after {
        left: 50%;
        width: 40%;
        transform: translateX(-50%);
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
    }

    :host([appearance="filled"]:not([disabled]):focus-within:not(:active))::after {
        left: 0;
        width: 100%;
    }
`},4778:function(t,e,n){"use strict";n.d(e,{D:function(){return H}});var i=n(63070),r=n(33940),o=n(38495),s=n(42590);class a extends o.OI{appearanceChanged(t,e){t!==e&&(this.classList.add(e),this.classList.remove(t))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="outline")}}(0,r.gn)([s.Lj,(0,r.w6)("design:type",String)],a.prototype,"appearance",void 0);var c=n(78923),l=n(29717),u=n(22798),d=n(27186),h=n(67739),f=n(24484),p=n(74358),g=n(2658),v=n(82636),m=n(35680),b=n(53131),y=n(42689),w=n(39238),x=n(55135),C=n(26512),k=n(28632),S=n(40009),$=n(27782),T=n(17993),L=n(10970);const O=c.i` :host([appearance="filled"]) .root{background:${m.wF};border-color:transparent}:host([appearance="filled"]:hover:not([disabled])) .root{background:${m.Xi};border-color:transparent}:host([appearance="filled"]:focus-within:not([disabled])) .root{border-color:transparent;box-shadow:none}${p.w}
`.withBehaviors((0,l.vF)(c.i` :host([appearance="filled"]) .root{background:${u.H.Field};border-color:${u.H.FieldText}}:host([appearance="filled"]:hover:not([disabled])) .root,:host([appearance="filled"]:focus-within:not([disabled])) .root{background:${u.H.Field};border-color:${u.H.FieldText}}:host([appearance="filled"]:active:not([disabled])) .root{background:${u.H.Field};border-color:${u.H.FieldText}}:host([appearance="filled"]:not([disabled]):active)::after,:host( [appearance="filled"]:not([disabled]):focus-within:not(:active) )::after{border-bottom-color:${u.H.Highlight}}:host([appearance="filled"][disabled]) .root{border-color:${u.H.GrayText};background:${u.H.Field}}`)),I=c.i` ${(0,d.j)("inline-block")} :host{font-family:${b.S};outline:none;user-select:none;position:relative}.root{box-sizing:border-box;position:relative;display:flex;flex-direction:row;color:${y.C};background:${w._B};border-radius:calc(${x.UW} * 1px);border:calc(${C.H} * 1px) solid ${k.ak};height:calc(${g.i} * 1px)}.control{-webkit-appearance:none;background:transparent;border:0;height:calc(100% - 4px);width:100%;margin-top:auto;margin-bottom:auto;border:none;padding:0 calc(${S._5} * 2px + 1px);color:${y.C};font-family:inherit;font-size:${$.c};line-height:${$.R}}.control:hover,.control:${h.b},.control:disabled,.control:active{outline:none}.label{display:block;color:${y.C};cursor:pointer;font-size:${$.c};line-height:${$.R};margin-bottom:4px}.label__hidden{display:none;visibility:hidden}::slotted([slot="start"]),::slotted([slot="end"]){display:flex;margin:auto;fill:currentcolor}::slotted(svg){${""} width:16px;height:16px}::slotted([slot="start"]){margin-inline-start:11px}::slotted([slot="end"]){margin-inline-end:11px}:host(:hover:not([disabled])) .root{background:${w.Tm};border-color:${k.QP}}:host(:focus-within:not([disabled])) .root{border-color:${T.yG};box-shadow:0 0 0 1px ${T.yG} inset}:host([disabled]) .label,:host([readonly]) .label,:host([readonly]) .control,:host([disabled]) .control{cursor:${f.H}}:host([disabled]){opacity:${L.V}}`.withBehaviors((0,v.H)("filled",O),(0,l.vF)(c.i` .root{forced-color-adjust:none;background:${u.H.Field};border-color:${u.H.FieldText}}:host(:hover:not([disabled])) .root{background:${u.H.Field};border-color:${u.H.Highlight}}::slotted([slot="start"]),::slotted([slot="end"]){fill:${u.H.ButtonText}}:host([disabled]){opacity:1}:host([disabled]) .root{border-color:${u.H.GrayText};background:${u.H.Field}}:host(:focus-within:enabled) .root{border-color:${u.H.Highlight};box-shadow:0 0 0 1px ${u.H.Highlight} inset}.control{color:${u.H.ButtonText}}::placeholder,::-webkit-input-placeholder{color:${u.H.FieldText}}:host([disabled]) ::placeholder,:host([disabled]) ::-webkit-input-placeholder,:host([disabled]) .label{color:${u.H.GrayText}}`));var E=n(49218),D=n(47548),R=n(41472),A=n(71695),B=n(86755);const F=function(t={}){return E.dy`
        <label
            part="label"
            for="control"
            class="${t=>t.defaultSlottedNodes&&t.defaultSlottedNodes.length?"label":"label label__hidden"}"
        >
            <slot
                ${(0,D.Q)({property:"defaultSlottedNodes",filter:B.E})}
            ></slot>
        </label>
        <div class="root" part="root">
            ${(0,A.m9)(t)}
            <input
                class="control"
                part="control"
                id="control"
                @input="${t=>t.handleTextInput()}"
                @change="${t=>t.handleChange()}"
                ?autofocus="${t=>t.autofocus}"
                ?disabled="${t=>t.disabled}"
                list="${t=>t.list}"
                maxlength="${t=>t.maxlength}"
                name="${t=>t.name}"
                minlength="${t=>t.minlength}"
                pattern="${t=>t.pattern}"
                placeholder="${t=>t.placeholder}"
                ?readonly="${t=>t.readOnly}"
                ?required="${t=>t.required}"
                size="${t=>t.size}"
                ?spellcheck="${t=>t.spellcheck}"
                :value="${t=>t.value}"
                type="${t=>t.type}"
                aria-atomic="${t=>t.ariaAtomic}"
                aria-busy="${t=>t.ariaBusy}"
                aria-controls="${t=>t.ariaControls}"
                aria-current="${t=>t.ariaCurrent}"
                aria-describedby="${t=>t.ariaDescribedby}"
                aria-details="${t=>t.ariaDetails}"
                aria-disabled="${t=>t.ariaDisabled}"
                aria-errormessage="${t=>t.ariaErrormessage}"
                aria-flowto="${t=>t.ariaFlowto}"
                aria-haspopup="${t=>t.ariaHaspopup}"
                aria-hidden="${t=>t.ariaHidden}"
                aria-invalid="${t=>t.ariaInvalid}"
                aria-keyshortcuts="${t=>t.ariaKeyshortcuts}"
                aria-label="${t=>t.ariaLabel}"
                aria-labelledby="${t=>t.ariaLabelledby}"
                aria-live="${t=>t.ariaLive}"
                aria-owns="${t=>t.ariaOwns}"
                aria-relevant="${t=>t.ariaRelevant}"
                aria-roledescription="${t=>t.ariaRoledescription}"
                ${(0,R.i)("control")}
            />
            ${(0,A.LC)(t)}
        </div>
    `}(),H=a.compose({name:`${i.H.prefix}-text-field`,template:F,styles:I,shadowOptions:{delegatesFocus:!0}})},82636:function(t,e,n){"use strict";n.d(e,{H:function(){return r}});var i=n(81239);function r(t,e){return new i.w("appearance",t,e)}},77615:function(t,e,n){"use strict";n.d(e,{s:function(){return i}});const i=Object.freeze({prefix:"cs",shadowRootMode:"open",registry:customElements})},91290:function(t,e,n){"use strict";n.d(e,{$f:function(){return a},Qs:function(){return l},RR:function(){return c},di:function(){return s}});var i=n(52704),r=n(78125),o=n(72582);const s=i.L.create("card-base-height",146),a=i.L.create("card-base-width",300),c=i.L.create("card-base-gap",14),l=i.L.create("card-stroke-color",(t=>t(o.q)===r.h.DarkMode?"rgba(0,0,0,0.1)":"rgba(0,0,0,0.0578)"))},55135:function(t,e,n){"use strict";n.d(e,{UW:function(){return o},rS:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("control-corner-radius",4),s=r("layer-corner-radius",4)},40009:function(t,e,n){"use strict";n.d(e,{LQ:function(){return s},_5:function(){return c},hV:function(){return a},nf:function(){return o}});var i=n(52704);const{create:r}=i.L,o=r("base-height-multiplier",8),s=r("base-horizontal-spacing-multiplier",3),a=r("density",0),c=r("design-unit",4)},66779:function(t,e,n){"use strict";n.d(e,{o:function(){return s}});var i=n(52704),r=n(59997);const{create:o}=i.L,s=o("direction",r.N.ltr)},10970:function(t,e,n){"use strict";n.d(e,{V:function(){return o}});var i=n(52704);const{create:r}=i.L,o=r("disabled-opacity",.3)},38492:function(t,e,n){"use strict";n.d(e,{I:function(){return s}});var i=n(52704),r=n(32484);const{create:o}=i.L,s=o("fill-color",(t=>t(r.y9)))},53131:function(t,e,n){"use strict";n.d(e,{S:function(){return o}});var i=n(52704);const{create:r}=i.L,o=r("body-font","Segoe UI, Segoe UI Midlevel, sans-serif")},72582:function(t,e,n){"use strict";n.d(e,{q:function(){return s}});var i=n(52704),r=n(78125);const{create:o}=i.L,s=o("base-layer-luminance",r.h.LightMode)},47999:function(t,e,n){"use strict";n.d(e,{B2:function(){return s},N7:function(){return o},Wo:function(){return a},v$:function(){return c}});var i=n(52704);const{create:r}=i.L,o=r("accent-fill-rest-delta",0),s=r("accent-fill-hover-delta",4),a=r("accent-fill-active-delta",-5),c=r("accent-fill-focus-delta",0)},958:function(t,e,n){"use strict";n.d(e,{UE:function(){return p},D8:function(){return g},OC:function(){return f},Av:function(){return h}});var i=n(52704);var r=n(38492),o=n(20246),s=n(56837),a=n(47999),c=n(30363),l=n(94806);const{create:u}=i.L,d=u({name:"accent-fill-recipe"},{evaluate:(t,e)=>(l.v.normal,(t,e)=>function(t,e,n,i,r,o,s,a,c){const l=t.source,u=t.closestIndexOf(l),d=u+1*i,h=u+1*r,f=u+1*o;return{rest:t.get(u),hover:t.get(d),active:t.get(h),focus:t.get(f)}}(t(c.a),t(s.y),e||t(r.I),t(a.B2),t(a.Wo),t(a.v$),t(o.MY),t(o.jW),t(o.hD)))(t,e)}),h=u("accent-fill-rest",(t=>t(d).evaluate(t).rest)),f=u("accent-fill-hover",(t=>t(d).evaluate(t).hover)),p=u("accent-fill-active",(t=>t(d).evaluate(t).active)),g=u("accent-fill-focus",(t=>t(d).evaluate(t).focus))},18681:function(t,e,n){"use strict";n.d(e,{L8:function(){return s},kb:function(){return a},kp:function(){return o},nd:function(){return c}});var i=n(52704);const{create:r}=i.L,o=r("accent-foreground-rest-delta",0),s=r("accent-foreground-hover-delta",6),a=r("accent-foreground-active-delta",-4),c=r("accent-foreground-focus-delta",0)},26738:function(t,e,n){"use strict";n.d(e,{VN:function(){return p},cZ:function(){return g},D9:function(){return f},go:function(){return h}});var i=n(52704),r=n(64087);var o=n(38492),s=n(18681),a=n(30363),c=n(94806);const{create:l}=i.L,u=t=>(e,n)=>function(t,e,n,i,o,s,a){const c=t.source,l=t.closestIndexOf(c),u=(0,r.a)(e),d=l+(1===u?Math.min(i,o):Math.max(u*i,u*o)),h=t.colorContrast(e,n,d,u),f=t.closestIndexOf(h),p=f+u*Math.abs(i-o);let g,v;return(1===u?i<o:u*i>u*o)?(g=f,v=p):(g=p,v=f),{rest:t.get(g),hover:t.get(v),active:t.get(g+u*s),focus:t.get(g+u*a)}}(e(a.a),n||e(o.I),t,e(s.kp),e(s.L8),e(s.kb),e(s.nd)),d=l({name:"accent-foreground-recipe"},{evaluate:(t,e)=>u(c.v.normal)(t,e)}),h=l("accent-foreground-rest",(t=>t(d).evaluate(t).rest)),f=l("accent-foreground-hover",(t=>t(d).evaluate(t).hover)),p=l("accent-foreground-active",(t=>t(d).evaluate(t).active)),g=l("accent-foreground-focus",(t=>t(d).evaluate(t).focus))},30363:function(t,e,n){"use strict";n.d(e,{a:function(){return a}});var i=n(52704),r=n(89949),o=n(56185);const{create:s}=i.L,a=s({name:"accent-palette"},r.v.create(o.Us))},94806:function(t,e,n){"use strict";n.d(e,{v:function(){return i}});const i={normal:4.5,large:7}},23132:function(t,e,n){"use strict";n.d(e,{Pp:function(){return h},$u:function(){return f},lJ:function(){return d},w4:function(){return u}});var i=n(52704),r=n(958),o=n(94806),s=n(56185);const a=t=>(e,n)=>function(t,e){return t.contrast(s.ix)>=e?s.ix:s.Sn}(n||e(r.Av),t),{create:c}=i.L,l=c({name:"foreground-on-accent-recipe"},{evaluate:(t,e)=>a(o.v.normal)(t,e)}),u=c("foreground-on-accent-rest",(t=>t(l).evaluate(t,t(r.Av)))),d=c("foreground-on-accent-hover",(t=>t(l).evaluate(t,t(r.OC)))),h=c("foreground-on-accent-active",(t=>t(l).evaluate(t,t(r.UE)))),f=c("foreground-on-accent-focus",(t=>t(l).evaluate(t,t(r.D8))))},26741:function(t,e,n){"use strict";n.d(e,{a2:function(){return u}});var i=n(52704),r=n(27153),o=n(38492),s=n(30363),a=n(17993);const{create:c}=i.L,l=c({name:"focus-stroke-inner-recipe"},{evaluate:t=>(0,r.a)(t(s.a),t(o.I),t(a.yG))}),u=c("focus-stroke-inner",(t=>t(l).evaluate(t)))},17993:function(t,e,n){"use strict";n.d(e,{yG:function(){return l}});var i=n(52704),r=n(27153),o=n(38492),s=n(56837);const{create:a}=i.L,c=a({name:"focus-stroke-outer-recipe"},{evaluate:t=>(0,r.y)(t(s.y),t(o.I))}),l=a("focus-stroke-outer",(t=>t(c).evaluate(t)))},20246:function(t,e,n){"use strict";n.d(e,{MY:function(){return o},VQ:function(){return c},hD:function(){return a},jW:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("neutral-fill-rest-delta",7),s=r("neutral-fill-hover-delta",10),a=r("neutral-fill-active-delta",5),c=r("neutral-fill-focus-delta",0)},37939:function(t,e,n){"use strict";n.d(e,{EL:function(){return s},Zb:function(){return c},ef:function(){return o},q_:function(){return a}});var i=n(52704);const{create:r}=i.L,o=r("neutral-fill-input-rest-delta",0),s=r("neutral-fill-input-hover-delta",0),a=r("neutral-fill-input-active-delta",0),c=r("neutral-fill-input-focus-delta",0)},39238:function(t,e,n){"use strict";n.d(e,{Iu:function(){return h},Tm:function(){return d},_B:function(){return u}});var i=n(52704),r=n(64087);var o=n(38492),s=n(37939),a=n(56837);const{create:c}=i.L,l=c({name:"neutral-fill-input-recipe"},{evaluate:(t,e)=>function(t,e,n,i,o,s){const a=(0,r.a)(e),c=t.closestIndexOf(e);return{rest:t.get(c-a*n),hover:t.get(c-a*i),active:t.get(c-a*o),focus:t.get(c-a*s)}}(t(a.y),e||t(o.I),t(s.ef),t(s.EL),t(s.q_),t(s.Zb))}),u=c("neutral-fill-input-rest",(t=>t(l).evaluate(t).rest)),d=c("neutral-fill-input-hover",(t=>t(l).evaluate(t).hover)),h=c("neutral-fill-input-active",(t=>t(l).evaluate(t).active));c("neutral-fill-input-focus",(t=>t(l).evaluate(t).focus))},84659:function(t,e,n){"use strict";n.d(e,{Y:function(){return o}});var i=n(52704);const{create:r}=i.L,o=r("neutral-fill-layer-rest-delta",3)},79148:function(t,e,n){"use strict";n.d(e,{t:function(){return u},ab:function(){return c},RJ:function(){return l}});var i=n(52704);var r=n(38492),o=n(84659),s=n(56837);const{create:a}=i.L,c=a({name:"neutral-fill-layer-recipe"},{evaluate:(t,e)=>function(t,e,n){const i=t.closestIndexOf(e);return t.get(i-n)}(t(s.y),e||t(r.I),t(o.Y))}),l=a("neutral-fill-layer-rest",(t=>t(c).evaluate(t))),u=l},12600:function(t,e,n){"use strict";n.d(e,{IU:function(){return c},fq:function(){return s},gs:function(){return a},qD:function(){return o}});var i=n(52704);const{create:r}=i.L,o=r("neutral-fill-stealth-rest-delta",0),s=r("neutral-fill-stealth-hover-delta",5),a=r("neutral-fill-stealth-active-delta",3),c=r("neutral-fill-stealth-focus-delta",0)},80260:function(t,e,n){"use strict";n.d(e,{sG:function(){return h},Qp:function(){return d},DF:function(){return l},jq:function(){return u}});var i=n(52704);var r=n(38492),o=n(20246),s=n(12600),a=n(56837);const{create:c}=i.L,l=c({name:"neutral-fill-stealth-recipe"},{evaluate:(t,e)=>function(t,e,n,i,r,o,s,a,c,l){const u=Math.max(n,i,r,o,s,a,c,l),d=t.closestIndexOf(e),h=d>=u?-1:1;return{rest:t.get(d+h*n),hover:t.get(d+h*i),active:t.get(d+h*r),focus:t.get(d+h*o)}}(t(a.y),e||t(r.I),t(s.qD),t(s.fq),t(s.gs),t(s.IU),t(o.MY),t(o.jW),t(o.hD),t(o.VQ))}),u=c("neutral-fill-stealth-rest",(t=>t(l).evaluate(t).rest)),d=c("neutral-fill-stealth-hover",(t=>t(l).evaluate(t).hover)),h=c("neutral-fill-stealth-active",(t=>t(l).evaluate(t).active));c("neutral-fill-stealth-focus",(t=>t(l).evaluate(t).focus))},92332:function(t,e,n){"use strict";n.d(e,{Cl:function(){return o},UC:function(){return c},Vt:function(){return a},sc:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("neutral-fill-strong-rest-delta",0),s=r("neutral-fill-strong-hover-delta",8),a=r("neutral-fill-strong-active-delta",-5),c=r("neutral-fill-strong-focus-delta",0)},35680:function(t,e,n){"use strict";n.d(e,{Gy:function(){return d},VN:function(){return h},Xi:function(){return u},At:function(){return c},wF:function(){return l}});var i=n(52704);var r=n(38492),o=n(20246),s=n(56837);const{create:a}=i.L,c=a({name:"neutral-fill-recipe"},{evaluate:(t,e)=>function(t,e,n,i,r,o){const s=t.closestIndexOf(e),a=s>=Math.max(n,i,r,o)?-1:1;return{rest:t.get(s+a*n),hover:t.get(s+a*i),active:t.get(s+a*r),focus:t.get(s+a*o)}}(t(s.y),e||t(r.I),t(o.MY),t(o.jW),t(o.hD),t(o.VQ))}),l=a("neutral-fill-rest",(t=>t(c).evaluate(t).rest)),u=a("neutral-fill-hover",(t=>t(c).evaluate(t).hover)),d=a("neutral-fill-active",(t=>t(c).evaluate(t).active)),h=a("neutral-fill-focus",(t=>t(c).evaluate(t).focus))},74449:function(t,e,n){"use strict";n.d(e,{Q:function(){return c},a:function(){return a}});var i=n(52704);var r=n(38492),o=n(56837);const{create:s}=i.L,a=s({name:"neutral-foreground-hint-recipe"},{evaluate:t=>{return e=t(o.y),n=t(r.I),e.colorContrast(n,4.5);var e,n}}),c=s("neutral-foreground-hint",(t=>t(a).evaluate(t)))},42689:function(t,e,n){"use strict";n.d(e,{C:function(){return c}});var i=n(52704);var r=n(38492),o=n(56837);const{create:s}=i.L,a=s({name:"neutral-foreground-recipe"},{evaluate:t=>{return e=t(o.y),n=t(r.I),e.colorContrast(n,14);var e,n}}),c=s("neutral-foreground-rest",(t=>t(a).evaluate(t)))},32484:function(t,e,n){"use strict";n.d(e,{y9:function(){return l}});var i=n(52704),r=n(78125);var o=n(72582),s=n(56837);const{create:a}=i.L,c=a({name:"neutral-layer-1-recipe"},{evaluate:t=>{return e=t(s.y),n=t(o.q),e.get(e.closestIndexOf((0,r.C)(n)));var e,n}}),l=a("neutral-layer-1",(t=>t(c).evaluate(t)))},27460:function(t,e,n){"use strict";n.d(e,{s:function(){return u}});var i=n(52704),r=n(78125);var o=n(84659),s=n(72582),a=n(56837);const{create:c}=i.L,l=c({name:"neutral-layer-floating-recipe"},{evaluate:t=>function(t,e,n){const i=t.closestIndexOf((0,r.C)(e))-n;return t.get(i-n)}(t(a.y),t(s.q),t(o.Y))}),u=c("neutral-layer-floating",(t=>t(l).evaluate(t)))},56837:function(t,e,n){"use strict";n.d(e,{y:function(){return a}});var i=n(52704),r=n(89949),o=n(56185);const{create:s}=i.L,a=s({name:"neutral-palette"},r.v.create(o.Pc))},59845:function(t,e,n){"use strict";n.d(e,{Bx:function(){return c},IM:function(){return a},fd:function(){return o},rn:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("neutral-stroke-rest-delta",25),s=r("neutral-stroke-hover-delta",40),a=r("neutral-stroke-active-delta",16),c=r("neutral-stroke-focus-delta",25)},14744:function(t,e,n){"use strict";n.d(e,{h:function(){return o}});var i=n(52704);const{create:r}=i.L,o=r("neutral-stroke-divider-rest-delta",8)},71942:function(t,e,n){"use strict";n.d(e,{eN:function(){return d},dt:function(){return u}});var i=n(52704),r=n(64087);var o=n(38492),s=n(14744),a=n(56837);const{create:c}=i.L,l=c({name:"neutral-stroke-divider-recipe"},{evaluate:(t,e)=>function(t,e,n){return t.get(t.closestIndexOf(e)+(0,r.a)(e)*n)}(t(a.y),e||t(o.I),t(s.h))}),u=c("neutral-stroke-divider-rest",(t=>t(l).evaluate(t))),d=u},28632:function(t,e,n){"use strict";n.d(e,{tA:function(){return v},yD:function(){return g},L$:function(){return p},c1:function(){return h},V0:function(){return f},QP:function(){return d},ak:function(){return u}});var i=n(52704),r=n(64087);var o=n(38492),s=n(59845),a=n(56837);const{create:c}=i.L,l=c({name:"neutral-stroke-recipe"},{evaluate:t=>function(t,e,n,i,o,s){const a=t.closestIndexOf(e),c=(0,r.a)(e),l=a+c*n,u=l+c*(i-n),d=l+c*(o-n),h=l+c*(s-n);return{rest:t.get(l),hover:t.get(u),active:t.get(d),focus:t.get(h)}}(t(a.y),t(o.I),t(s.fd),t(s.rn),t(s.IM),t(s.Bx))}),u=c("neutral-stroke-rest",(t=>t(l).evaluate(t).rest)),d=c("neutral-stroke-hover",(t=>t(l).evaluate(t).hover)),h=c("neutral-stroke-active",(t=>t(l).evaluate(t).active)),f=c("neutral-stroke-focus",(t=>t(l).evaluate(t).focus)),p=u,g=d,v=h},26512:function(t,e,n){"use strict";n.d(e,{H:function(){return o},bu:function(){return a},vx:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("stroke-width",1),s=r("focus-stroke-width",2),a=o},27782:function(t,e,n){"use strict";n.d(e,{R:function(){return s},c:function(){return o}});var i=n(52704);const{create:r}=i.L,o=r("type-ramp-base-font-size","14px"),s=r("type-ramp-base-line-height","20px")},16549:function(t,e,n){"use strict";n.d(e,{s:function(){return o},v:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("type-ramp-minus-1-font-size","12px"),s=r("type-ramp-minus-1-line-height","16px")},3490:function(t,e,n){"use strict";n.d(e,{G:function(){return o},M:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("type-ramp-minus-2-font-size","10px"),s=r("type-ramp-minus-2-line-height","14px")},32572:function(t,e,n){"use strict";n.d(e,{P:function(){return o},b:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("type-ramp-plus-1-font-size","16px"),s=r("type-ramp-plus-1-line-height","22px")},94585:function(t,e,n){"use strict";n.d(e,{I:function(){return s},m:function(){return o}});var i=n(52704);const{create:r}=i.L,o=r("type-ramp-plus-2-font-size","20px"),s=r("type-ramp-plus-2-line-height","28px")},68530:function(t,e,n){"use strict";n.d(e,{i:function(){return o},y:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("type-ramp-plus-3-font-size","24px"),s=r("type-ramp-plus-3-line-height","32px")},12499:function(t,e,n){"use strict";n.d(e,{I:function(){return o},i:function(){return s}});var i=n(52704);const{create:r}=i.L,o=r("type-ramp-plus-4-font-size","28px"),s=r("type-ramp-plus-4-line-height","36px")},22674:function(t,e,n){"use strict";n.d(e,{O:function(){return o}});var i=n(58968),r=n(66779);class o{constructor(t,e){this.cache=new WeakMap,this.ltr=t,this.rtl=e}connectedCallback(t){i.H.enqueue((()=>{this.attach(t.source)}))}disconnectedCallback(t){const e=this.cache.get(t.source);e&&r.o.unsubscribe(e)}attach(t){if("object"!=typeof globalThis.process&&!t.isConnected)return;const e=this.cache.get(t)||new s(this.ltr,this.rtl,t),n=r.o.getValueFor(t);r.o.subscribe(e),e.attach(n),this.cache.set(t,e)}}class s{constructor(t,e,n){this.ltr=t,this.rtl=e,this.source=n,this.attached=null}handleChange(t,e){this.attach("default"===e.target?t.default:t.getValueFor(e.target))}attach(t){this.attached!==this[t]&&(null!==this.attached&&this.source.$fastController.removeStyles(this.attached),this.attached=this[t],null!==this.attached&&this.source.$fastController.addStyles(this.attached))}}},62734:function(t,e,n){"use strict";n.d(e,{XC:function(){return i}});const i="box-shadow: 0 0 calc((var(--elevation) * 0.225px) + 2px) rgba(0, 0, 0, calc(.11 * (2 - var(--background-luminance, 1)))), 0 calc(var(--elevation) * 0.4px) calc((var(--elevation) * 0.9px)) rgba(0, 0, 0, calc(.13 * (2 - var(--background-luminance, 1))));"},2658:function(t,e,n){"use strict";n.d(e,{i:function(){return o}});var i=n(78923),r=n(40009);const o=i.i.partial`(${r.nf} + ${r.hV}) * ${r._5}`},16162:function(t,e,n){"use strict";n.d(e,{K:function(){return i}});class i{constructor(t,e,n=!1){this.evaluate=t,this.policy=e,this.isVolatile=n}}},69784:function(t,e,n){"use strict";n.d(e,{k:function(){return a}});var i=n(34959),r=n(16162),o=n(57194),s=n(59502);function a(t){return(0,i.mf)(t)?(0,o.v)(t):t instanceof r.K?t:(0,s.x)((()=>t))}},59502:function(t,e,n){"use strict";n.d(e,{x:function(){return s}});var i=n(89522),r=n(16162);class o extends r.K{createObserver(){return this}bind(t){return this.evaluate(t.source,t.context)}}function s(t,e){return new o(t,e)}(0,i._o)(o)},57194:function(t,e,n){"use strict";n.d(e,{X:function(){return a},v:function(){return s}});var i=n(99452),r=n(16162);class o extends r.K{createObserver(t){return i.y$.binding(this.evaluate,t,this.isVolatile)}}function s(t,e,n=i.y$.isVolatileBinding(t)){return new o(t,e,n)}function a(t,e){const n=new o(t);return n.options=e,n}},42590:function(t,e,n){"use strict";n.d(e,{Ax:function(){return u},Id:function(){return p},Lj:function(){return v},bw:function(){return d},r_:function(){return h},so:function(){return g}});var i=n(99452),r=n(34959),o=n(58968),s=n(31699),a=n(89522);const c="boolean",l="reflect",u=Object.freeze({locate:(0,a.j8)()}),d={toView:t=>t?"true":"false",fromView:t=>null!=t&&"false"!==t&&!1!==t&&0!==t},h={toView:t=>"boolean"==typeof t?t.toString():"",fromView:t=>[null,void 0,void 0].includes(t)?null:d.fromView(t)};function f(t){if(null==t)return null;const e=1*t;return isNaN(e)?null:e}const p={toView(t){const e=f(t);return e?e.toString():e},fromView:f};class g{constructor(t,e,n=e.toLowerCase(),i=l,r){this.guards=new Set,this.Owner=t,this.name=e,this.attribute=n,this.mode=i,this.converter=r,this.fieldName=`_${e}`,this.callbackName=`${e}Changed`,this.hasCallback=this.callbackName in t.prototype,i===c&&void 0===r&&(this.converter=d)}setValue(t,e){const n=t[this.fieldName],i=this.converter;void 0!==i&&(e=i.fromView(e)),n!==e&&(t[this.fieldName]=e,this.tryReflectToAttribute(t),this.hasCallback&&t[this.callbackName](n,e),t.$fastController.notify(this.name))}getValue(t){return i.y$.track(t,this.name),t[this.fieldName]}onAttributeChangedCallback(t,e){this.guards.has(t)||(this.guards.add(t),this.setValue(t,e),this.guards.delete(t))}tryReflectToAttribute(t){const e=this.mode,n=this.guards;n.has(t)||"fromView"===e||o.H.enqueue((()=>{n.add(t);const i=t[this.fieldName];switch(e){case l:const e=this.converter;s.S.setAttribute(t,this.attribute,void 0!==e?e.toView(i):i);break;case c:s.S.setBooleanAttribute(t,this.attribute,i)}n.delete(t)}))}static collect(t,...e){const n=[];e.push(u.locate(t));for(let i=0,o=e.length;i<o;++i){const o=e[i];if(void 0!==o)for(let e=0,i=o.length;e<i;++e){const i=o[e];(0,r.HD)(i)?n.push(new g(t,i)):n.push(new g(t,i.property,i.attribute,i.mode,i.converter))}}return n}}function v(t,e){let n;function i(t,e){arguments.length>1&&(n.property=e),u.locate(t.constructor).push(n)}return arguments.length>1?(n={},void i(t,e)):(n=void 0===t?{}:t,i)}},7279:function(t,e,n){"use strict";n.d(e,{Hi:function(){return C},t3:function(){return g}});var i=n(39579),r=n(99452),o=n(89522),s=n(70885),a=n(62795),c=n(72394),l=n(28521);const u={bubbles:!0,composed:!0,cancelable:!0},d="isConnected",h=new WeakMap;function f(t){var e,n;return null!==(n=null!==(e=t.shadowRoot)&&void 0!==e?e:h.get(t))&&void 0!==n?n:null}let p;class g extends i.A{constructor(t,e){super(t),this.boundObservables=null,this.needsInitialization=!0,this.hasExistingShadowRoot=!1,this._template=null,this.stage=3,this.guardBehaviorConnection=!1,this.behaviors=null,this.behaviorsConnected=!1,this._mainStyles=null,this.$fastController=this,this.view=null,this.source=t,this.definition=e;const n=e.shadowOptions;if(void 0!==n){let e=t.shadowRoot;e?this.hasExistingShadowRoot=!0:(e=t.attachShadow(n),"closed"===n.mode&&h.set(t,e))}const i=r.y$.getAccessors(t);if(i.length>0){const e=this.boundObservables=Object.create(null);for(let n=0,r=i.length;n<r;++n){const r=i[n].name,o=t[r];void 0!==o&&(delete t[r],e[r]=o)}}}get isConnected(){return r.y$.track(this,d),1===this.stage}setNeedsInitialization(t){this.needsInitialization=t}get context(){var t,e;return null!==(e=null===(t=this.view)||void 0===t?void 0:t.context)&&void 0!==e?e:r.rd.default}get isBound(){var t,e;return null!==(e=null===(t=this.view)||void 0===t?void 0:t.isBound)&&void 0!==e&&e}get sourceLifetime(){var t;return null===(t=this.view)||void 0===t?void 0:t.sourceLifetime}get template(){var t;if(null===this._template){const e=this.definition;this.source.resolveTemplate?this._template=this.source.resolveTemplate():e.template&&(this._template=null!==(t=e.template)&&void 0!==t?t:null)}return"function"==typeof this._template&&(this._template=this._template(this.source)),this._template}set template(t){this._template!==t&&(this._template=t,"function"==typeof this._template&&(this._template=this._template(this.source)),this.needsInitialization||this.renderTemplate(this._template))}get mainStyles(){var t;if(null===this._mainStyles){const e=this.definition;this.source.resolveStyles?this._mainStyles=this.source.resolveStyles():e.styles&&(this._mainStyles=null!==(t=e.styles)&&void 0!==t?t:null)}return this._mainStyles}set mainStyles(t){this._mainStyles!==t&&(null!==this._mainStyles&&this.removeStyles(this._mainStyles),this._mainStyles=t,this.needsInitialization||this.addStyles(t))}onUnbind(t){var e;null===(e=this.view)||void 0===e||e.onUnbind(t)}addBehavior(t){var e,n;const i=null!==(e=this.behaviors)&&void 0!==e?e:this.behaviors=new Map,r=null!==(n=i.get(t))&&void 0!==n?n:0;0===r?(i.set(t,1),t.addedCallback&&t.addedCallback(this),!t.connectedCallback||this.guardBehaviorConnection||1!==this.stage&&0!==this.stage||t.connectedCallback(this)):i.set(t,r+1)}removeBehavior(t,e=!1){const n=this.behaviors;if(null===n)return;const i=n.get(t);void 0!==i&&(1===i||e?(n.delete(t),t.disconnectedCallback&&3!==this.stage&&t.disconnectedCallback(this),t.removedCallback&&t.removedCallback(this)):n.set(t,i-1))}addStyles(t){var e;if(!t)return;const n=this.source;if(t instanceof HTMLElement){(null!==(e=f(n))&&void 0!==e?e:this.source).append(t)}else if(!t.isAttachedTo(n)){const e=t.behaviors;if(t.addStylesTo(n),null!==e)for(let t=0,n=e.length;t<n;++t)this.addBehavior(e[t])}}removeStyles(t){var e;if(!t)return;const n=this.source;if(t instanceof HTMLElement){(null!==(e=f(n))&&void 0!==e?e:n).removeChild(t)}else if(t.isAttachedTo(n)){const e=t.behaviors;if(t.removeStylesFrom(n),null!==e)for(let t=0,n=e.length;t<n;++t)this.removeBehavior(e[t])}}connect(){3===this.stage&&(this.stage=0,this.bindObservables(),this.connectBehaviors(),this.needsInitialization?(this.renderTemplate(this.template),this.addStyles(this.mainStyles),this.needsInitialization=!1):null!==this.view&&this.view.bind(this.source),this.stage=1,r.y$.notify(this,d))}bindObservables(){if(null!==this.boundObservables){const t=this.source,e=this.boundObservables,n=Object.keys(e);for(let i=0,r=n.length;i<r;++i){const r=n[i];t[r]=e[r]}this.boundObservables=null}}connectBehaviors(){if(!1===this.behaviorsConnected){const t=this.behaviors;if(null!==t){this.guardBehaviorConnection=!0;for(const e of t.keys())e.connectedCallback&&e.connectedCallback(this);this.guardBehaviorConnection=!1}this.behaviorsConnected=!0}}disconnectBehaviors(){if(!0===this.behaviorsConnected){const t=this.behaviors;if(null!==t)for(const e of t.keys())e.disconnectedCallback&&e.disconnectedCallback(this);this.behaviorsConnected=!1}}disconnect(){1===this.stage&&(this.stage=2,r.y$.notify(this,d),null!==this.view&&this.view.unbind(),this.disconnectBehaviors(),this.stage=3)}onAttributeChangedCallback(t,e,n){const i=this.definition.attributeLookup[t];void 0!==i&&i.onAttributeChangedCallback(this.source,n)}emit(t,e,n){return 1===this.stage&&this.source.dispatchEvent(new CustomEvent(t,{detail:e,...u,...n}))}renderTemplate(t){var e;const n=this.source,i=null!==(e=f(n))&&void 0!==e?e:n;if(null!==this.view)this.view.dispose(),this.view=null;else if(!this.needsInitialization||this.hasExistingShadowRoot){this.hasExistingShadowRoot=!1;for(let t=i.firstChild;null!==t;t=i.firstChild)i.removeChild(t)}t&&(this.view=t.render(n,i,n),this.view.sourceLifetime=r.sG.coupled)}static forCustomElement(t){const e=t.$fastController;if(void 0!==e)return e;const n=c.W.getForInstance(t);if(void 0===n)throw o.Bo.error(1401);return t.$fastController=new p(t,n)}static setStrategy(t){p=t}}function v(t){var e;return"adoptedStyleSheets"in t?t:null!==(e=f(t))&&void 0!==e?e:t.getRootNode()}(0,o._o)(g),g.setStrategy(g);class m{constructor(t){const e=m.styleSheetCache;this.sheets=t.map((t=>{if(t instanceof CSSStyleSheet)return t;let n=e.get(t);return void 0===n&&(n=new CSSStyleSheet,n.replaceSync(t),e.set(t,n)),n}))}addStylesTo(t){const e=v(t);e.adoptedStyleSheets=[...e.adoptedStyleSheets,...this.sheets]}removeStylesFrom(t){const e=v(t),n=this.sheets;e.adoptedStyleSheets=e.adoptedStyleSheets.filter((t=>-1===n.indexOf(t)))}}m.styleSheetCache=new Map;let b=0;function y(t){return t===document?document.body:t}s.X.setDefaultStrategy(s.X.supportsAdoptedStyleSheets?m:class{constructor(t){this.styles=t,this.styleClass="fast-"+ ++b}addStylesTo(t){t=y(v(t));const e=this.styles,n=this.styleClass;for(let i=0;i<e.length;i++){const r=document.createElement("style");r.innerHTML=e[i],r.className=n,t.append(r)}}removeStylesFrom(t){const e=(t=y(v(t))).querySelectorAll(`.${this.styleClass}`);for(let n=0,i=e.length;n<i;++n)t.removeChild(e[n])}});const w="defer-hydration",x="needs-hydration";class C extends g{static hydrationObserverHandler(t){for(const e of t)C.hydrationObserver.unobserve(e.target),e.target.$fastController.connect()}connect(){var t,e;if(void 0===this.needsHydration&&(this.needsHydration=null!==this.source.getAttribute(x)),this.source.hasAttribute(w))return void C.hydrationObserver.observe(this.source,{attributeFilter:[w]});if(!this.needsHydration)return void super.connect();if(3!==this.stage)return;this.stage=0,this.bindObservables(),this.connectBehaviors();const n=this.source,i=null!==(t=f(n))&&void 0!==t?t:n;if(this.template)if((0,l.l$)(this.template)){let t=i.firstChild,r=i.lastChild;null===n.shadowRoot&&(l._M.isElementBoundaryStartMarker(t)&&(t.data="",t=t.nextSibling),l._M.isElementBoundaryEndMarker(r)&&(r.data="",r=r.previousSibling)),this.view=this.template.hydrate(t,r,n),null===(e=this.view)||void 0===e||e.bind(this.source)}else this.renderTemplate(this.template);this.addStyles(this.mainStyles),this.stage=1,this.source.removeAttribute(x),this.needsInitialization=this.needsHydration=!1,r.y$.notify(this,d)}disconnect(){super.disconnect(),C.hydrationObserver.unobserve(this.source)}static install(){g.setStrategy(C)}}C.hydrationObserver=new a.Ag(C.hydrationObserverHandler)},72394:function(t,e,n){"use strict";n.d(e,{W:function(){return h}});var i=n(34959),r=n(99452),o=n(89522),s=n(70885),a=n(42590);const c={mode:"open"},l={},u=new Set,d=o.Bo.getById(i.P6.elementRegistry,(()=>(0,o.LI)()));class h{constructor(t,e=t.definition){var n;this.platformDefined=!1,(0,i.HD)(e)&&(e={name:e}),this.type=t,this.name=e.name,this.template=e.template,this.registry=null!==(n=e.registry)&&void 0!==n?n:customElements;const o=t.prototype,u=a.so.collect(t,e.attributes),h=new Array(u.length),f={},p={};for(let t=0,e=u.length;t<e;++t){const e=u[t];h[t]=e.attribute,f[e.name]=e,p[e.attribute]=e,r.y$.defineProperty(o,e)}Reflect.defineProperty(t,"observedAttributes",{value:h,enumerable:!0}),this.attributes=u,this.propertyLookup=f,this.attributeLookup=p,this.shadowOptions=void 0===e.shadowOptions?c:null===e.shadowOptions?void 0:{...c,...e.shadowOptions},this.elementOptions=void 0===e.elementOptions?l:{...l,...e.elementOptions},this.styles=s.X.normalize(e.styles),d.register(this)}get isDefined(){return this.platformDefined}define(t=this.registry){const e=this.type;return t.get(this.name)||(this.platformDefined=!0,t.define(this.name,e,this.elementOptions)),this}static compose(t,e){return u.has(t)||d.getByType(t)?new h(class extends t{},e):new h(t,e)}static registerBaseType(t){u.add(t)}}h.getByType=d.getByType,h.getForInstance=d.getForInstance},28904:function(t,e,n){"use strict";n.d(e,{H:function(){return c},M:function(){return l}});var i=n(34959),r=n(7279),o=n(72394);function s(t){const e=class extends t{constructor(){super(),r.t3.forCustomElement(this)}$emit(t,e,n){return this.$fastController.emit(t,e,n)}connectedCallback(){this.$fastController.connect()}disconnectedCallback(){this.$fastController.disconnect()}attributeChangedCallback(t,e,n){this.$fastController.onAttributeChangedCallback(t,e,n)}};return o.W.registerBaseType(e),e}function a(t,e){return(0,i.mf)(t)?o.W.compose(t,e).define().type:o.W.compose(this,t).define().type}const c=Object.assign(s(HTMLElement),{from:function(t){return s(t)},define:a,compose:function(t,e){return(0,i.mf)(t)?o.W.compose(t,e):o.W.compose(this,t)}});function l(t){return function(e){a(e,t)}}},28521:function(t,e,n){"use strict";n.d(e,{BL:function(){return p},_M:function(){return u},l$:function(){return g}});const i=/fe-b\$\$start\$\$(\d+)\$\$(.+)\$\$fe-b/,r=/fe-b\$\$end\$\$(\d+)\$\$(.+)\$\$fe-b/,o=/fe-repeat\$\$start\$\$(\d+)\$\$fe-repeat/,s=/fe-repeat\$\$end\$\$(\d+)\$\$fe-repeat/,a=/fe-eb\$\$start\$\$(.+)\$\$fe-eb/,c=/fe-eb\$\$end\$\$(.+)\$\$fe-eb/;function l(t){return t&&t.nodeType===Node.COMMENT_NODE}const u=Object.freeze({attributeMarkerName:"data-fe-b",attributeBindingSeparator:" ",contentBindingStartMarker:(t,e)=>`fe-b$$start$$${t}$$${e}$$fe-b`,contentBindingEndMarker:(t,e)=>`fe-b$$end$$${t}$$${e}$$fe-b`,repeatStartMarker:t=>`fe-repeat$$start$$${t}$$fe-repeat`,repeatEndMarker:t=>`fe-repeat$$end$$${t}$$fe-repeat`,isContentBindingStartMarker:t=>i.test(t),isContentBindingEndMarker:t=>r.test(t),isRepeatViewStartMarker:t=>o.test(t),isRepeatViewEndMarker:t=>s.test(t),isElementBoundaryStartMarker:t=>l(t)&&a.test(t.data),isElementBoundaryEndMarker:t=>l(t)&&c.test(t.data),parseAttributeBinding(t){const e=t.getAttribute(this.attributeMarkerName);return null===e?e:e.split(this.attributeBindingSeparator).map((t=>parseInt(t)))},parseContentBindingStartMarker:t=>f(i,t),parseContentBindingEndMarker:t=>f(r,t),parseRepeatStartMarker:t=>d(o,t),parseRepeatEndMarker:t=>d(s,t),parseElementBoundaryStartMarker:t=>h(a,t),parseElementBoundaryEndMarker:t=>h(c,t)});function d(t,e){const n=t.exec(e);return null===n?n:parseInt(n[1])}function h(t,e){const n=t.exec(e);return null===n?n:n[1]}function f(t,e){const n=t.exec(e);return null===n?n:[parseInt(n[1]),n[2]]}const p=Symbol.for("fe-hydration");function g(t){return t[p]===p}},36e3:function(t,e,n){"use strict";n.d(e,{DI:function(){return y},YM:function(){return j},f3:function(){return k}});var i=n(89522);if(!("metadata"in Reflect)){const t=new Map;Reflect.metadata=function(t,e){return function(n){Reflect.defineMetadata(t,e,n)}},Reflect.defineMetadata=function(e,n,i){let r=t.get(i);void 0===r&&t.set(i,r=new Map),r.set(e,n)},Reflect.getOwnMetadata=function(e,n){const i=t.get(n);if(void 0!==i)return i.get(e)}}const r="annotation:paramtypes",o=Object.freeze({getDesignParamTypes:t=>{var e;return null!==(e=Reflect.getOwnMetadata("design:paramtypes",t))&&void 0!==e?e:i.ow},getAnnotationParamTypes:t=>{var e;return null!==(e=Reflect.getOwnMetadata(r,t))&&void 0!==e?e:i.ow},getOrCreateAnnotationParamTypes(t){let e=this.getAnnotationParamTypes(t);return e===i.ow&&Reflect.defineMetadata(r,e=[],t),e}}),s=new Map,a="context-request";let c;const l=Object.freeze({eventType:a,for(t){let e=s.get(t);return void 0===e&&(e=l.create(t),s.set(t,e)),e},create(t,e){const n=function(t,e,r){if(null==t||void 0!==new.target)throw i.Bo.error(1501,{name:n.name});if(e)l.defineProperty(t,e,n);else{o.getOrCreateAnnotationParamTypes(t)[r]=n}};return n.$isInterface=!0,n.initialValue=e,Reflect.defineProperty(n,"name",{value:t}),n.handle=(t,e)=>l.handle(t,e,n),n.provide=(t,e)=>l.provide(t,n,e),n.get=t=>l.get(t,n),n.request=(t,e,i)=>l.request(t,n,e,i),n.toString=()=>`Context<${n.name}>`,n},setDefaultRequestStrategy(t){c=t},get(t,e){var n;let i;return c(t,e,(t=>i=t),!1),null!==(n=i)&&void 0!==n?n:e.initialValue},request(t,e,n,i=!1){c(t,e,n,i)},dispatch(t,e,n,i=!1){t.dispatchEvent(new u(e,n,i))},provide(t,e,n){l.handle(t,(t=>{t.stopImmediatePropagation(),t.callback(n)}),e)},handle(t,e,n){n?t.addEventListener(a,(t=>{t.context===n&&e(t)})):t.addEventListener(a,e)},defineProperty(t,e,n){const i=Symbol.for(`fast:di:${e}`);Reflect.defineProperty(t,e,{get:function(){var t;return null!==(t=this[i])&&void 0!==t?t:this[i]=l.get(this,n)}})}});l.setDefaultRequestStrategy(l.dispatch);class u extends Event{constructor(t,e,n){super(a,{bubbles:!0,composed:!0}),this.context=t,this.callback=e,this.multiple=n}}class d{constructor(t,e){this.container=t,this.key=e}instance(t){return this.registerResolver(0,t)}singleton(t){return this.registerResolver(1,t)}transient(t){return this.registerResolver(2,t)}callback(t){return this.registerResolver(3,t)}cachedCallback(t){return this.registerResolver(3,P(t))}aliasTo(t){return this.registerResolver(5,t)}registerResolver(t,e){const{container:n,key:i}=this;return this.container=this.key=void 0,n.registerResolver(i,new O(i,t,e))}}function h(t){const e=t.slice(),n=Object.keys(t),i=n.length;let r;for(let o=0;o<i;++o)r=n[o],K(r)||(e[r]=t[r]);return e}const f=Object.freeze({none(t){throw i.Bo.error(1512,{key:t})},singleton:t=>new O(t,1,t),transient:t=>new O(t,2,t)}),p=Object.freeze({default:Object.freeze({parentLocator:()=>null,asyncRegistrationLocator:async()=>null,responsibleForOwnerRequests:!1,defaultResolver:f.singleton})});const g=new Map,v=new WeakMap;let m=null,b=0;const y=Object.freeze({installAsContextRequestStrategy(t){l.setDefaultRequestStrategy(((e,n,i)=>{i(y.findResponsibleContainer(e,t).get(n))}))},createContainer:t=>new M(null,Object.assign({},p.default,t)),findResponsibleContainer(t,e){const n=t.$$container$$;return n&&n.responsibleForOwnerRequests?n:y.findParentContainer(t,e)},findParentContainer(t,e){if(b<1)return e?e():y.getOrCreateDOMContainer();let n;return l.dispatch(t,x,(t=>n=t)),null!=n?n:e?e():y.getOrCreateDOMContainer()},getOrCreateDOMContainer(t,e){if(!t)return"undefined"!=typeof window?(v.has(window)||v.set(window,new M(window,Object.assign({},p.default,e,{parentLocator:()=>null}))),v.get(window)):m||(m=new M(null,Object.assign({},p.default,e,{parentLocator:()=>null})));let n=t.$$container$$;return void 0===n&&(b++,n=new M(t,Object.assign({},p.default,e,{parentLocator:y.findParentContainer}))),n},getDependencies(t){let e=g.get(t);if(void 0===e){const n=t.inject;if(void 0===n){const n=o.getDesignParamTypes(t),r=o.getAnnotationParamTypes(t);if(n===i.ow)if(r===i.ow){const n=Object.getPrototypeOf(t);e="function"==typeof n&&n!==Function.prototype?h(y.getDependencies(n)):[]}else e=h(r);else if(r===i.ow)e=h(n);else{e=h(n);let t,i=r.length;for(let n=0;n<i;++n)t=r[n],void 0!==t&&(e[n]=t);const o=Object.keys(r);let s;i=o.length;for(let t=0;t<i;++t)s=o[t],K(s)||(e[s]=r[s])}}else e=h(n);g.set(t,e)}return e},defineProperty(t,e,n,r=!1){const o=Symbol.for(`fast:di:${e}`);Reflect.defineProperty(t,e,{get:function(){let t=this[o];if(void 0===t){const s=this instanceof Node?y.findResponsibleContainer(this):y.getOrCreateDOMContainer();if(t=s.get(n),this[o]=t,r){const r=this.$fastController;if(!r)throw i.Bo.error(1514);const s=()=>{y.findResponsibleContainer(this).get(n)!==this[o]&&(this[o]=t,r.notify(e))};r.subscribe({handleChange:s},"isConnected")}}return t}})},createContext:function(t,e){const n="function"==typeof t?t:e,r="string"==typeof t?t:t&&"friendlyName"in t&&t.friendlyName||V,s="string"!=typeof t&&(t&&"respectConnection"in t&&t.respectConnection||!1),a=function(t,e,n){if(null==t||void 0!==new.target)throw i.Bo.error(1501,{name:a.name});if(e)y.defineProperty(t,e,a,s);else{o.getOrCreateAnnotationParamTypes(t)[n]=a}};return a.$isInterface=!0,Reflect.defineProperty(a,"name",{value:null!=r?r:V}),null!=n&&(a.register=function(t,e){return n(new d(t,null!=e?e:a))}),a.toString=function(){return`DIContext<${a.name}>`},a},inject:(...t)=>function(e,n,i){if("number"==typeof i){const n=o.getOrCreateAnnotationParamTypes(e),r=t[0];void 0!==r&&(n[i]=r)}else if(n)y.defineProperty(e,n,t[0]);else{const n=i?o.getOrCreateAnnotationParamTypes(i.value):o.getOrCreateAnnotationParamTypes(e);let r;for(let e=0;e<t.length;++e)r=t[e],void 0!==r&&(n[e]=r)}},transient:t=>(t.register=function(e){return j.transient(t,t).register(e)},t.registerInRequestor=!1,t),singleton:(t,e=S)=>(t.register=function(e){return j.singleton(t,t).register(e)},t.registerInRequestor=e.scoped,t)}),w=y.createContext("Container"),x=w;function C(t){return function(e){const n=function(t,e,i){y.inject(n)(t,e,i)};return n.$isResolver=!0,n.resolve=function(n,i){return t(e,n,i)},n}}const k=y.inject;const S={scoped:!1};$=(t,e,n,i)=>n.getAll(t,i);var $;C(((t,e,n)=>()=>n.get(t))),C(((t,e,n)=>n.has(t,!0)?n.get(t):void 0));function T(t,e,n){y.inject(T)(t,e,n)}T.$isResolver=!0,T.resolve=()=>{};C(((t,e,n)=>{const i=L(t,e),r=new O(t,0,i);return n.registerResolver(t,r),i})),C(((t,e,n)=>L(t,e)));function L(t,e){return e.getFactory(t).construct(e)}class O{constructor(t,e,n){this.key=t,this.strategy=e,this.state=n,this.resolving=!1}get $isResolver(){return!0}register(t){return t.registerResolver(this.key,this)}resolveAsync(t,e){switch(this.strategy){case 1:if(this.resolving)throw i.Bo.error(1513,{name:this.state.name});return this.resolving=!0,t.getFactory(this.state).constructAsync(e).then((t=>(this.state=t,this.strategy=0,this.resolving=!1,t)));case 2:{const n=t.getFactory(this.state);if(null===n)throw i.Bo.error(1502,{key:this.key});return n.constructAsync(e)}default:return Promise.resolve(this.resolve(t,e))}}resolve(t,e){switch(this.strategy){case 0:return this.state;case 1:if(this.resolving)throw i.Bo.error(1513,{name:this.state.name});return this.resolving=!0,this.state=t.getFactory(this.state).construct(e),this.strategy=0,this.resolving=!1,this.state;case 2:{const n=t.getFactory(this.state);if(null===n)throw i.Bo.error(1502,{key:this.key});return n.construct(e)}case 3:return this.state(t,e,this);case 4:return this.state[0].resolve(t,e);case 5:return e.get(this.state);default:throw i.Bo.error(1503,{strategy:this.strategy})}}getFactory(t){var e,n,i;switch(this.strategy){case 1:case 2:return t.getFactory(this.state);case 5:return null!==(i=null===(n=null===(e=t.getResolver(this.state))||void 0===e?void 0:e.getFactory)||void 0===n?void 0:n.call(e,t))&&void 0!==i?i:null;default:return null}}}function I(t){return this.get(t)}function E(t,e){return e(t)}class D{constructor(t,e){this.Type=t,this.dependencies=e,this.transformers=null}async constructAsync(t,e){const n=await Promise.all(this.dependencies.map((e=>t.getAsync(e))));return this.constructCore(n,e)}construct(t,e){const n=this.dependencies.map(I,t);return this.constructCore(n,e)}constructCore(t,e){let n;return n=void 0===e?new this.Type(...t):new this.Type(...t,...e),null===this.transformers?n:this.transformers.reduce(E,n)}registerTransformer(t){(this.transformers||(this.transformers=[])).push(t)}}const R={$isResolver:!0,resolve:(t,e)=>e,resolveAsync:function(t,e){return Promise.resolve(e)}};function A(t){return"function"==typeof t.register}function B(t){return function(t){return A(t)&&"boolean"==typeof t.registerInRequestor}(t)&&t.registerInRequestor}const F=new Set(["Array","ArrayBuffer","Boolean","DataView","Date","Error","EvalError","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Number","Object","Promise","RangeError","ReferenceError","RegExp","Set","SharedArrayBuffer","String","SyntaxError","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","URIError","WeakMap","WeakSet"]),H=new Map;class M{constructor(t,e){this.owner=t,this.config=e,this._parent=void 0,this.registerDepth=0,this.isHandlingContextRequests=!1,this.resolvers=new Map,this.resolvers.set(w,R),t&&(t.$$container$$=this,"addEventListener"in t&&l.handle(t,(t=>{if(this.isHandlingContextRequests)try{const e=this.get(t.context);t.stopImmediatePropagation(),t.callback(e)}catch(t){}else t.context===w&&t.composedPath()[0]!==this.owner&&(t.stopImmediatePropagation(),t.callback(this))})))}get parent(){return void 0===this._parent&&(this._parent=this.config.parentLocator(this.owner)),this._parent}get depth(){return null===this.parent?0:this.parent.depth+1}get responsibleForOwnerRequests(){return this.config.responsibleForOwnerRequests}handleContextRequests(t){this.isHandlingContextRequests=t}register(...t){if(100==++this.registerDepth)throw i.Bo.error(1504);let e,n,r,o,s;for(let i=0,a=t.length;i<a;++i)if(e=t[i],U(e))if(A(e))e.register(this);else if(void 0!==e.prototype)j.singleton(e,e).register(this);else for(n=Object.keys(e),o=0,s=n.length;o<s;++o)r=e[n[o]],U(r)&&(A(r)?r.register(this):this.register(r));return--this.registerDepth,this}registerResolver(t,e){_(t);const n=this.resolvers,i=n.get(t);return null==i?n.set(t,e):i instanceof O&&4===i.strategy?i.state.push(e):n.set(t,new O(t,4,[i,e])),e}registerTransformer(t,e){const n=this.getResolver(t);if(null==n)return!1;if(n.getFactory){const t=n.getFactory(this);return null!=t&&(t.registerTransformer(e),!0)}return!1}getResolver(t,e=!0){if(_(t),void 0!==t.resolve)return t;let n,i=this;for(;null!=i;){if(n=i.resolvers.get(t),null!=n)return n;if(null==i.parent){const n=B(t)?this:i;return e?this.jitRegister(t,n):null}i=i.parent}return null}has(t,e=!1){return!!this.resolvers.has(t)||!(!e||null==this.parent)&&this.parent.has(t,!0)}async getAsync(t){if(_(t),t.$isResolver)return t.resolveAsync(this,this);let e,n=this;for(;null!=n;){if(e=n.resolvers.get(t),null!=e)return e.resolveAsync(n,this);if(null==n.parent){const r=await this.config.asyncRegistrationLocator(t);if(!r)throw i.Bo.error(1505,{key:t});const o=B(t)?this:n;return e=r.register(o),e.resolveAsync(n,this)}n=n.parent}throw i.Bo.error(1505,{key:t})}get(t){if(_(t),t.$isResolver)return t.resolve(this,this);let e,n=this;for(;null!=n;){if(e=n.resolvers.get(t),null!=e)return e.resolve(n,this);if(null==n.parent){const i=B(t)?this:n;return e=this.jitRegister(t,i),e.resolve(n,this)}n=n.parent}throw i.Bo.error(1505,{key:t})}getAll(t,e=!1){_(t);const n=this;let r,o=n;if(e){let e=i.ow;for(;null!=o;)r=o.resolvers.get(t),null!=r&&(e=e.concat(z(r,o,n))),o=o.parent;return e}for(;null!=o;){if(r=o.resolvers.get(t),null!=r)return z(r,o,n);if(o=o.parent,null==o)return i.ow}return i.ow}getFactory(t){let e=H.get(t);if(void 0===e){if(q(t))throw i.Bo.error(1506,{name:t.name});H.set(t,e=new D(t,y.getDependencies(t)))}return e}registerFactory(t,e){H.set(t,e)}createChild(t){return new M(null,Object.assign({},this.config,t,{parentLocator:()=>this}))}jitRegister(t,e){if("function"!=typeof t)throw i.Bo.error(1507,{value:t});if(F.has(t.name))throw i.Bo.error(1508,{value:t.name});if(A(t)){const n=t.register(e);if(!(n instanceof Object)||null==n.resolve){const n=e.resolvers.get(t);if(null!=n)return n;throw i.Bo.error(1510)}return n}if(t.$isInterface)throw i.Bo.error(1509,{value:t.name});{const n=this.config.defaultResolver(t,e);return e.resolvers.set(t,n),n}}}const N=new WeakMap;function P(t){return function(e,n,i){if(N.has(i))return N.get(i);const r=t(e,n,i);return N.set(i,r),r}}const j=Object.freeze({instance:(t,e)=>new O(t,0,e),singleton:(t,e)=>new O(t,1,e),transient:(t,e)=>new O(t,2,e),callback:(t,e)=>new O(t,3,e),cachedCallback:(t,e)=>new O(t,3,P(e)),aliasTo:(t,e)=>new O(e,5,t)});function _(t){if(null==t)throw i.Bo.error(1511)}function z(t,e,n){if(t instanceof O&&4===t.strategy){const i=t.state;let r=i.length;const o=new Array(r);for(;r--;)o[r]=i[r].resolve(e,n);return o}return[t.resolve(e,n)]}const V="(anonymous)";function U(t){return"object"==typeof t&&null!==t||"function"==typeof t}const q=function(){const t=new WeakMap;let e=!1,n="",i=0;return function(r){return e=t.get(r),void 0===e&&(n=r.toString(),i=n.length,e=i>=29&&i<=100&&125===n.charCodeAt(i-1)&&n.charCodeAt(i-2)<=32&&93===n.charCodeAt(i-3)&&101===n.charCodeAt(i-4)&&100===n.charCodeAt(i-5)&&111===n.charCodeAt(i-6)&&99===n.charCodeAt(i-7)&&32===n.charCodeAt(i-8)&&101===n.charCodeAt(i-9)&&118===n.charCodeAt(i-10)&&105===n.charCodeAt(i-11)&&116===n.charCodeAt(i-12)&&97===n.charCodeAt(i-13)&&110===n.charCodeAt(i-14)&&88===n.charCodeAt(i-15),t.set(r,e)),e}}(),W={};function K(t){switch(typeof t){case"number":return t>=0&&(0|t)===t;case"string":{const e=W[t];if(void 0!==e)return e;const n=t.length;if(0===n)return W[t]=!1;let i=0;for(let e=0;e<n;++e)if(i=t.charCodeAt(e),0===e&&48===i&&n>1||i<48||i>57)return W[t]=!1;return W[t]=!0}default:return!1}}},31393:function(t,e,n){"use strict";n.d(e,{K:function(){return p}});var i=n(31699),r=n(34959),o=n(89522);function s(t,e,n,i){return(t,e,n,...o)=>{(0,r.HD)(n)&&(n=n.replace("javascript:","")),i(t,e,n,...o)}}function a(t,e,n,i){throw o.Bo.error(1209,{aspectName:n,tagName:null!=t?t:"text"})}const c={onabort:a,onauxclick:a,onbeforeinput:a,onbeforematch:a,onblur:a,oncancel:a,oncanplay:a,oncanplaythrough:a,onchange:a,onclick:a,onclose:a,oncontextlost:a,oncontextmenu:a,oncontextrestored:a,oncopy:a,oncuechange:a,oncut:a,ondblclick:a,ondrag:a,ondragend:a,ondragenter:a,ondragleave:a,ondragover:a,ondragstart:a,ondrop:a,ondurationchange:a,onemptied:a,onended:a,onerror:a,onfocus:a,onformdata:a,oninput:a,oninvalid:a,onkeydown:a,onkeypress:a,onkeyup:a,onload:a,onloadeddata:a,onloadedmetadata:a,onloadstart:a,onmousedown:a,onmouseenter:a,onmouseleave:a,onmousemove:a,onmouseout:a,onmouseover:a,onmouseup:a,onpaste:a,onpause:a,onplay:a,onplaying:a,onprogress:a,onratechange:a,onreset:a,onresize:a,onscroll:a,onsecuritypolicyviolation:a,onseeked:a,onseeking:a,onselect:a,onslotchange:a,onstalled:a,onsubmit:a,onsuspend:a,ontimeupdate:a,ontoggle:a,onvolumechange:a,onwaiting:a,onwebkitanimationend:a,onwebkitanimationiteration:a,onwebkitanimationstart:a,onwebkittransitionend:a,onwheel:a},l={elements:{a:{[i.O.attribute]:{href:s},[i.O.property]:{href:s}},area:{[i.O.attribute]:{href:s},[i.O.property]:{href:s}},button:{[i.O.attribute]:{formaction:s},[i.O.property]:{formAction:s}},embed:{[i.O.attribute]:{src:a},[i.O.property]:{src:a}},form:{[i.O.attribute]:{action:s},[i.O.property]:{action:s}},frame:{[i.O.attribute]:{src:s},[i.O.property]:{src:s}},iframe:{[i.O.attribute]:{src:s},[i.O.property]:{src:s,srcdoc:a}},input:{[i.O.attribute]:{formaction:s},[i.O.property]:{formAction:s}},link:{[i.O.attribute]:{href:a},[i.O.property]:{href:a}},object:{[i.O.attribute]:{codebase:a,data:a},[i.O.property]:{codeBase:a,data:a}},script:{[i.O.attribute]:{src:a,text:a},[i.O.property]:{src:a,text:a,innerText:a,textContent:a}},style:{[i.O.property]:{innerText:a,textContent:a}}},aspects:{[i.O.attribute]:{...c},[i.O.property]:{innerHTML:a,...c},[i.O.event]:{...c}}};function u(t,e){const n={};for(const i in e){const r=t[i],o=e[i];switch(r){case null:break;case void 0:n[i]=o;break;default:n[i]=r}}for(const e in t)e in n||(n[e]=t[e]);return Object.freeze(n)}function d(t,e){const n={};for(const i in e){const r=t[i],o=e[i];switch(r){case null:break;case void 0:n[i]=u(o,{});break;default:n[i]=u(r,o)}}for(const e in t)e in n||(n[e]=u(t[e],{}));return Object.freeze(n)}function h(t,e){const n={};for(const i in e){const r=t[i],o=e[i];switch(r){case null:break;case void 0:n[i]=d(r,{});break;default:n[i]=d(r,o)}}for(const e in t)e in n||(n[e]=d(t[e],{}));return Object.freeze(n)}function f(t,e,n,i,r){const o=t[n];if(o){const t=o[i];if(t)return t(e,n,i,r)}}const p=Object.freeze({create(t={}){var e,n;const i=null!==(e=t.trustedType)&&void 0!==e?e:function(){const t=t=>t;return globalThis.trustedTypes?globalThis.trustedTypes.createPolicy("fast-html",{createHTML:t}):{createHTML:t}}(),r=(o=null!==(n=t.guards)&&void 0!==n?n:{},s=l,Object.freeze({elements:o.elements?h(o.elements,s.elements):s.elements,aspects:o.aspects?d(o.aspects,s.aspects):s.aspects}));var o,s;return Object.freeze({createHTML:t=>i.createHTML(t),protect(t,e,n,i){var o;const s=(null!=t?t:"").toLowerCase(),a=r.elements[s];if(a){const r=f(a,t,e,n,i);if(r)return r}return null!==(o=f(r.aspects,t,e,n,i))&&void 0!==o?o:i}})}})},31699:function(t,e,n){"use strict";n.d(e,{O:function(){return r},S:function(){return l}});var i=n(89522);const r=Object.freeze({none:0,attribute:1,booleanAttribute:2,property:3,content:4,tokenList:5,event:6}),o=t=>t,s=globalThis.trustedTypes?globalThis.trustedTypes.createPolicy("fast-html",{createHTML:o}):{createHTML:o};let a=Object.freeze({createHTML:t=>s.createHTML(t),protect:(t,e,n,i)=>i});const c=a,l=Object.freeze({get policy(){return a},setPolicy(t){if(a!==c)throw i.Bo.error(1201);a=t},setAttribute(t,e,n){null==n?t.removeAttribute(e):t.setAttribute(e,n)},setBooleanAttribute(t,e,n){n?t.setAttribute(e,""):t.removeAttribute(e)}})},52166:function(t,e,n){"use strict";n.r(e),n.d(e,{ArrayObserver:function(){return a.K1},AttributeConfiguration:function(){return A.Ax},AttributeDefinition:function(){return A.so},Behavior:function(){return M},Binding:function(){return l.K},CSSBindingDirective:function(){return v.v},CSSDirective:function(){return g.v},ChildrenDirective:function(){return L.p},Compiler:function(){return b.S},Component:function(){return N},ComponentConfigurationRegistry:function(){return F},DOM:function(){return r.S},DOMAspect:function(){return r.O},ElementController:function(){return B.t3},ElementStyles:function(){return f.X},ExecutionContext:function(){return o.rd},FAST:function(){return i.Bo},FASTElement:function(){return D.H},FASTElementDefinition:function(){return R.W},HTMLBindingDirective:function(){return w.R},HTMLDirective:function(){return x.m0},HTMLView:function(){return O.bP},HydratableElementController:function(){return B.Hi},HydrationBindingError:function(){return O.AH},InlineTemplateDirective:function(){return m.Sy},Markup:function(){return y.Lu},NodeObservationDirective:function(){return I.v},Observable:function(){return o.y$},Parser:function(){return y._b},PropertyChangeNotifier:function(){return s.A},RefDirective:function(){return C.o},RenderBehavior:function(){return E.iO},RenderDirective:function(){return E.sH},RepeatBehavior:function(){return S.eN},RepeatDirective:function(){return S.Gx},ServerComponent:function(){return P},SlottedDirective:function(){return T.E},SourceLifetime:function(){return o.sG},Splice:function(){return a.fw},SpliceStrategy:function(){return a.Fk},SpliceStrategySupport:function(){return a.HO},StatelessAttachedAttributeDirective:function(){return x.Pq},SubscriberSet:function(){return s.q},Updates:function(){return c.H},ViewTemplate:function(){return m._H},attr:function(){return A.Lj},booleanConverter:function(){return A.bw},child:function(){return $},children:function(){return L.N},css:function(){return p.i},cssDirective:function(){return g.k},customElement:function(){return D.M},define:function(){return H},elements:function(){return I.R},emptyArray:function(){return i.ow},html:function(){return m.dy},htmlDirective:function(){return x.hG},lengthOf:function(){return a.Tk},listener:function(){return u.X},normalizeBinding:function(){return h.k},nullableBooleanConverter:function(){return A.r_},nullableNumberConverter:function(){return A.Id},observable:function(){return o.LO},oneTime:function(){return d.x},oneWay:function(){return u.v},ref:function(){return C.i},render:function(){return E.sY},repeat:function(){return S.rx},slotted:function(){return T.Q},volatile:function(){return o.lk},when:function(){return k.g}});var i=n(89522),r=n(31699),o=n(99452),s=n(39579),a=n(32883),c=n(58968),l=n(16162),u=n(57194),d=n(59502),h=n(69784),f=n(70885),p=n(78923),g=n(17781),v=n(11845),m=n(49218),b=n(65883),y=n(59833),w=n(19316),x=n(85594),C=n(41472),k=n(93703),S=n(89150);function $(t,e={}){var n;const i=Object.keys(e).map(((t,n)=>`${0==n?"":"' "}${"function"==typeof e[t]?"@":":"}${t}='`)),r=Object.keys(e).map((t=>e[t])),o=`<${t} `;let s=`></${t}>`;r.length>0&&(s="' "+s);const a=function(t){const e=[...t];return e.raw=[...t],e}([o+(null!==(n=i[0])&&void 0!==n?n:""),...i.slice(1),s]);return(0,m.dy)(a,...r)}var T=n(47548),L=n(99026),O=n(49820),I=n(95185),E=n(47170),D=n(28904),R=n(72394),A=n(42590),B=n(7279);const F=new Map,H=t=>{t.component&&t.component.define(t.definition),F.set(t.definition.name,t.definition)};class M{constructor(t){this.target=t}}class N extends D.H{constructor(){super(...arguments),this.behaviors=new Map}getStateInstance(){return this}dispatch(t){for(const e of this.behaviors.values())e.dispatch(t,this.$fastController.context)}addBehavior(t){const e=new t(this);this.behaviors.set(t.name,e)}addBehaviors(t){for(const e of t)this.addBehavior(e)}}class P extends N{connectedCallback(){this.$fastController.setNeedsInitialization(!1),this.resume(),super.connectedCallback()}resume(){var t;const e=this.$fastController.template;if(!e)throw new Error("No CSR template found for the SSR component.");const n=Object.keys(e.factories).map((t=>e.factories[t])),i=this.shadowRoot;if(!i)throw new Error("No shadow root found for the SSR component.");const r=document.createTreeWalker(i,NodeFilter.SHOW_COMMENT);let o=0,s=r.nextNode();for(;s;){if(null===(t=s.nodeValue)||void 0===t?void 0:t.includes("v2")){const[t,e,i,r]=s.nodeValue.split("-"),a=s.nextSibling;if(a)if("b"===e){const t=n[o];if(!t)throw new Error("No binding directive found for the SSR component.");t.handleChange=(t,e)=>{a.textContent=t(this,this.$fastController.context)};t.dataBinding.createObserver(t,t).bind({source:this,context:this.$fastController.context,isBound:!1,onUnbind:()=>{}}),o++}else if("e"===e){const t=n[o];if(!t)throw new Error("No binding directive found for the SSR component.");const e=s.nextElementSibling;Array.from(e.attributes).filter((t=>t.name.startsWith("@"))).forEach((n=>{const[i,r]=n.name.split("@");if(!t)throw new Error("No binding directive found for the SSR component.");e.addEventListener(r,(t=>{this.dispatch(n.value)}))})),o++}else if("w"===e)if("s"===r){const t=n[o];if(!t)throw new Error("No binding directive found for the SSR component.");t.handleChange=(e,n)=>{let r=a;for(;r&&r!==s&&(r.nodeType!==Node.COMMENT_NODE||r.nodeValue!=`v2-w-${i}-e`);){const t=r.nextSibling;if(r.remove(),!t)break;r=t}const o=m.dy`
                                    ${e}
                                `.create();o.bind(this),o.insertBefore(r),t.handleChange=()=>{}};t.dataBinding.createObserver(t,t).bind({source:this,context:this.$fastController.context,isBound:!1,onUnbind:()=>{}})}else o++;else if("r"===e)if("s"===r){const t=n[o];if(t.handleChange=(t,e)=>{},!t)throw new Error("No binding directive found for the SSR component.");const e=t.dataBinding.createObserver(t,t);e.bind({source:this,context:this.$fastController.context,isBound:!1,onUnbind:()=>{}});const r={handleChange:(e,n)=>{let o=a;for(;o&&o!==s&&(o.nodeType!==Node.COMMENT_NODE||o.nodeValue!=`v2-r-${i}-e`);){const t=o.nextSibling;if(o.remove(),!t)break;o=t}const c=m.dy`
                                        ${t}
                                    `.create();c.bind(this),c.insertBefore(o),t.handleChange=()=>{},n.unsubscribe(r)}};e.subscribe(r)}else o++}s=r.nextNode()}}}},34959:function(t,e,n){"use strict";let i;n.d(e,{HD:function(){return a},P6:function(){return o},ZT:function(){return c},mf:function(){return s}});const r="fast-kernel";try{if(document.currentScript)i=document.currentScript.getAttribute(r);else{const t=document.getElementsByTagName("script");i=t[t.length-1].getAttribute(r)}}catch(t){i="isolate"}let o;switch(i){case"share":o=Object.freeze({updateQueue:1,observable:2,contextEvent:3,elementRegistry:4});break;case"share-v2":o=Object.freeze({updateQueue:1.2,observable:2.2,contextEvent:3.2,elementRegistry:4.2});break;default:const t=`-${Math.random().toString(36).substring(2,8)}`;o=Object.freeze({updateQueue:`1.2${t}`,observable:`2.2${t}`,contextEvent:`3.2${t}`,elementRegistry:`4.2${t}`})}const s=t=>"function"==typeof t,a=t=>"string"==typeof t,c=()=>{}},32883:function(t,e,n){"use strict";n.d(e,{Fk:function(){return p},HO:function(){return c},K1:function(){return b},Tk:function(){return y},fw:function(){return a}});var i=n(89522),r=n(39579),o=n(99452),s=n(58968);class a{constructor(t,e,n){this.index=t,this.removed=e,this.addedCount=n}adjustTo(t){let e=this.index;const n=t.length;return e>n?e=n-this.addedCount:e<0&&(e=n+this.removed.length+e-this.addedCount),this.index=e<0?0:e,this}}const c=Object.freeze({reset:1,splice:2,optimized:3}),l=new a(0,i.ow,0);l.reset=!0;const u=[l];function d(t,e,n,r,o,s){let c=0,l=0;const u=Math.min(n-e,s-o);if(0===e&&0===o&&(c=function(t,e,n){for(let i=0;i<n;++i)if(t[i]!==e[i])return i;return n}(t,r,u)),n===t.length&&s===r.length&&(l=function(t,e,n){let i=t.length,r=e.length,o=0;for(;o<n&&t[--i]===e[--r];)o++;return o}(t,r,u-c)),o+=c,s-=l,(n-=l)-(e+=c)==0&&s-o==0)return i.ow;if(e===n){const t=new a(e,[],0);for(;o<s;)t.removed.push(r[o++]);return[t]}if(o===s)return[new a(e,[],n-e)];const d=function(t){let e=t.length-1,n=t[0].length-1,i=t[e][n];const r=[];for(;e>0||n>0;){if(0===e){r.push(2),n--;continue}if(0===n){r.push(3),e--;continue}const o=t[e-1][n-1],s=t[e-1][n],a=t[e][n-1];let c;c=s<a?s<o?s:o:a<o?a:o,c===o?(o===i?r.push(0):(r.push(1),i=o),e--,n--):c===s?(r.push(3),e--,i=s):(r.push(2),n--,i=a)}return r.reverse()}(function(t,e,n,i,r,o){const s=o-r+1,a=n-e+1,c=new Array(s);let l,u;for(let t=0;t<s;++t)c[t]=new Array(a),c[t][0]=t;for(let t=0;t<a;++t)c[0][t]=t;for(let n=1;n<s;++n)for(let o=1;o<a;++o)t[e+o-1]===i[r+n-1]?c[n][o]=c[n-1][o-1]:(l=c[n-1][o]+1,u=c[n][o-1]+1,c[n][o]=l<u?l:u);return c}(t,e,n,r,o,s)),h=[];let f,p=e,g=o;for(let t=0;t<d.length;++t)switch(d[t]){case 0:void 0!==f&&(h.push(f),f=void 0),p++,g++;break;case 1:void 0===f&&(f=new a(p,[],0)),f.addedCount++,p++,f.removed.push(r[g]),g++;break;case 2:void 0===f&&(f=new a(p,[],0)),f.addedCount++,p++;break;case 3:void 0===f&&(f=new a(p,[],0)),f.removed.push(r[g]),g++}return void 0!==f&&h.push(f),h}function h(t,e){let n=!1,i=0;for(let c=0;c<e.length;c++){const l=e[c];if(l.index+=i,n)continue;const u=(r=t.index,o=t.index+t.removed.length,s=l.index,a=l.index+l.addedCount,o<s||a<r?-1:o===s||a===r?0:r<s?o<a?o-s:a-s:a<o?a-r:o-r);if(u>=0){e.splice(c,1),c--,i-=l.addedCount-l.removed.length,t.addedCount+=l.addedCount-u;const r=t.removed.length+l.removed.length-u;if(t.addedCount||r){let e=l.removed;if(t.index<l.index){const n=t.removed.slice(0,l.index-t.index);n.push(...e),e=n}if(t.index+t.removed.length>l.index+l.addedCount){const n=t.removed.slice(l.index+l.addedCount-t.index);e.push(...n)}t.removed=e,l.index<t.index&&(t.index=l.index)}else n=!0}else if(t.index<l.index){n=!0,e.splice(c,0,t),c++;const r=t.addedCount-t.removed.length;l.index+=r,i+=r}}var r,o,s,a;n||e.push(t)}let f=Object.freeze({support:c.optimized,normalize:(t,e,n)=>void 0===t?void 0===n?i.ow:function(t,e){let n=[];const i=[];for(let t=0,n=e.length;t<n;t++)h(e[t],i);for(let e=0,r=i.length;e<r;++e){const r=i[e];1!==r.addedCount||1!==r.removed.length?n=n.concat(d(t,r.index,r.index+r.addedCount,r.removed,0,r.removed.length)):r.removed[0]!==t[r.index]&&n.push(r)}return n}(e,n):u,pop(t,e,n,i){const r=t.length>0,o=n.apply(t,i);return r&&e.addSplice(new a(t.length,[o],0)),o},push(t,e,n,i){const r=n.apply(t,i);return e.addSplice(new a(t.length-i.length,[],i.length).adjustTo(t)),r},reverse(t,e,n,i){const r=n.apply(t,i);return e.reset(t),r},shift(t,e,n,i){const r=t.length>0,o=n.apply(t,i);return r&&e.addSplice(new a(0,[o],0)),o},sort(t,e,n,i){const r=n.apply(t,i);return e.reset(t),r},splice(t,e,n,i){const r=n.apply(t,i);return e.addSplice(new a(+i[0],r,i.length>2?i.length-2:0).adjustTo(t)),r},unshift(t,e,n,i){const r=n.apply(t,i);return e.addSplice(new a(0,[],i.length).adjustTo(t)),r}});const p=Object.freeze({reset:u,setDefaultStrategy(t){f=t}});function g(t,e,n){Reflect.defineProperty(t,e,{value:n,enumerable:!1})}class v extends r.q{constructor(t){super(t),this.oldCollection=void 0,this.splices=void 0,this.needsQueue=!0,this._strategy=null,this._lengthObserver=void 0,this.call=this.flush,g(t,"$fastController",this)}get strategy(){return this._strategy}set strategy(t){this._strategy=t}get lengthObserver(){let t=this._lengthObserver;if(void 0===t){const e=this.subject;this._lengthObserver=t={length:e.length,handleChange(){this.length!==e.length&&(this.length=e.length,o.y$.notify(t,"length"))}},this.subscribe(t)}return t}subscribe(t){this.flush(),super.subscribe(t)}addSplice(t){void 0===this.splices?this.splices=[t]:this.splices.push(t),this.enqueue()}reset(t){this.oldCollection=t,this.enqueue()}flush(){var t;const e=this.splices,n=this.oldCollection;void 0===e&&void 0===n||(this.needsQueue=!0,this.splices=void 0,this.oldCollection=void 0,this.notify((null!==(t=this._strategy)&&void 0!==t?t:f).normalize(n,this.subject,e)))}enqueue(){this.needsQueue&&(this.needsQueue=!1,s.H.enqueue(this))}}let m=!1;const b=Object.freeze({enable(){if(m)return;m=!0,o.y$.setArrayObserverFactory((t=>new v(t)));const t=Array.prototype;t.$fastPatch||(g(t,"$fastPatch",1),[t.pop,t.push,t.reverse,t.shift,t.sort,t.splice,t.unshift].forEach((e=>{t[e.name]=function(...t){var n;const i=this.$fastController;return void 0===i?e.apply(this,t):(null!==(n=i.strategy)&&void 0!==n?n:f)[e.name](this,i,e,t)}})))}});function y(t){if(!t)return 0;let e=t.$fastController;return void 0===e&&(b.enable(),e=o.y$.getNotifier(t)),o.y$.track(e.lengthObserver,"length"),t.length}},39579:function(t,e,n){"use strict";n.d(e,{A:function(){return r},q:function(){return i}});class i{constructor(t,e){this.sub1=void 0,this.sub2=void 0,this.spillover=void 0,this.subject=t,this.sub1=e}has(t){return void 0===this.spillover?this.sub1===t||this.sub2===t:-1!==this.spillover.indexOf(t)}subscribe(t){const e=this.spillover;if(void 0===e){if(this.has(t))return;if(void 0===this.sub1)return void(this.sub1=t);if(void 0===this.sub2)return void(this.sub2=t);this.spillover=[this.sub1,this.sub2,t],this.sub1=void 0,this.sub2=void 0}else{-1===e.indexOf(t)&&e.push(t)}}unsubscribe(t){const e=this.spillover;if(void 0===e)this.sub1===t?this.sub1=void 0:this.sub2===t&&(this.sub2=void 0);else{const n=e.indexOf(t);-1!==n&&e.splice(n,1)}}notify(t){const e=this.spillover,n=this.subject;if(void 0===e){const e=this.sub1,i=this.sub2;void 0!==e&&e.handleChange(n,t),void 0!==i&&i.handleChange(n,t)}else for(let i=0,r=e.length;i<r;++i)e[i].handleChange(n,t)}}class r{constructor(t){this.subscribers={},this.subjectSubscribers=null,this.subject=t}notify(t){var e,n;null===(e=this.subscribers[t])||void 0===e||e.notify(t),null===(n=this.subjectSubscribers)||void 0===n||n.notify(t)}subscribe(t,e){var n,r;let o;o=e?null!==(n=this.subscribers[e])&&void 0!==n?n:this.subscribers[e]=new i(this.subject):null!==(r=this.subjectSubscribers)&&void 0!==r?r:this.subjectSubscribers=new i(this.subject),o.subscribe(t)}unsubscribe(t,e){var n,i;e?null===(n=this.subscribers[e])||void 0===n||n.unsubscribe(t):null===(i=this.subjectSubscribers)||void 0===i||i.unsubscribe(t)}}},99452:function(t,e,n){"use strict";n.d(e,{LO:function(){return l},lk:function(){return u},rd:function(){return h},sG:function(){return a},y$:function(){return c}});var i=n(34959),r=n(89522),o=n(58968),s=n(39579);const a=Object.freeze({unknown:void 0,coupled:1}),c=r.Bo.getById(i.P6.observable,(()=>{const t=o.H.enqueue,e=/(:|&&|\|\||if|\?\.)/,n=new WeakMap;let c,l=t=>{throw r.Bo.error(1101)};function u(t){var e;let i=null!==(e=t.$fastController)&&void 0!==e?e:n.get(t);return void 0===i&&(Array.isArray(t)?i=l(t):n.set(t,i=new s.A(t))),i}const d=(0,r.j8)();class h{constructor(t){this.name=t,this.field=`_${t}`,this.callback=`${t}Changed`}getValue(t){return void 0!==c&&c.watch(t,this.name),t[this.field]}setValue(t,e){const n=this.field,r=t[n];if(r!==e){t[n]=e;const o=t[this.callback];(0,i.mf)(o)&&o.call(t,r,e),u(t).notify(this.name)}}}class f extends s.q{constructor(t,e,n=!1){super(t,e),this.expression=t,this.isVolatileBinding=n,this.needsRefresh=!0,this.needsQueue=!0,this.isAsync=!0,this.first=this,this.last=null,this.propertySource=void 0,this.propertyName=void 0,this.notifier=void 0,this.next=void 0}setMode(t){this.isAsync=this.needsQueue=t}bind(t){this.controller=t;const e=this.observe(t.source,t.context);return!t.isBound&&this.requiresUnbind(t)&&t.onUnbind(this),e}requiresUnbind(t){return t.sourceLifetime!==a.coupled||this.first!==this.last||this.first.propertySource!==t.source}unbind(t){this.dispose()}observe(t,e){this.needsRefresh&&null!==this.last&&this.dispose();const n=c;let i;c=this.needsRefresh?this:void 0,this.needsRefresh=this.isVolatileBinding;try{i=this.expression(t,e)}finally{c=n}return i}disconnect(){this.dispose()}dispose(){if(null!==this.last){let t=this.first;for(;void 0!==t;)t.notifier.unsubscribe(this,t.propertyName),t=t.next;this.last=null,this.needsRefresh=this.needsQueue=this.isAsync}}watch(t,e){const n=this.last,i=u(t),r=null===n?this.first:{};if(r.propertySource=t,r.propertyName=e,r.notifier=i,i.subscribe(this,e),null!==n){if(!this.needsRefresh){let e;c=void 0,e=n.propertySource[n.propertyName],c=this,t===e&&(this.needsRefresh=!0)}n.next=r}this.last=r}handleChange(){this.needsQueue?(this.needsQueue=!1,t(this)):this.isAsync||this.call()}call(){null!==this.last&&(this.needsQueue=this.isAsync,this.notify(this))}*records(){let t=this.first;for(;void 0!==t;)yield t,t=t.next}}return(0,r._o)(f),Object.freeze({setArrayObserverFactory(t){l=t},getNotifier:u,track(t,e){c&&c.watch(t,e)},trackVolatile(){c&&(c.needsRefresh=!0)},notify(t,e){u(t).notify(e)},defineProperty(t,e){(0,i.HD)(e)&&(e=new h(e)),d(t).push(e),Reflect.defineProperty(t,e.name,{enumerable:!0,get(){return e.getValue(this)},set(t){e.setValue(this,t)}})},getAccessors:d,binding(t,e,n=this.isVolatileBinding(t)){return new f(t,e,n)},isVolatileBinding:t=>e.test(t.toString())})}));function l(t,e){c.defineProperty(t,e)}function u(t,e,n){return Object.assign({},n,{get(){return c.trackVolatile(),n.get.apply(this)}})}const d=r.Bo.getById(i.P6.contextEvent,(()=>{let t=null;return{get:()=>t,set(e){t=e}}})),h=Object.freeze({default:{index:0,length:0,get event(){return h.getEvent()},eventDetail(){return this.event.detail},eventTarget(){return this.event.target}},getEvent:()=>d.get(),setEvent(t){d.set(t)}})},58968:function(t,e,n){"use strict";n.d(e,{H:function(){return r}});var i=n(34959);const r=n(89522).Bo.getById(i.P6.updateQueue,(()=>{const t=[],e=[];let n=!0;function i(){if(e.length)throw e.shift()}function r(r){try{r.call()}catch(r){if(!n)throw t.length=0,r;e.push(r),setTimeout(i,0)}}function o(){let e=0;for(;e<t.length;)if(r(t[e]),e++,e>1024){for(let n=0,i=t.length-e;n<i;n++)t[n]=t[n+e];t.length-=e,e=0}t.length=0}function s(e){t.push(e),t.length<2&&(n?globalThis.requestAnimationFrame(o):o())}return Object.freeze({enqueue:s,next:()=>new Promise(s),process:o,setMode:t=>n=t})}))},89522:function(t,e,n){"use strict";n.d(e,{Bo:function(){return s},j8:function(){return l},LI:function(){return c},ow:function(){return a},_o:function(){return u}});var i=n(34959);!function(){if("undefined"==typeof globalThis)if("undefined"!=typeof global)global.globalThis=global;else if("undefined"!=typeof self)self.globalThis=self;else if("undefined"!=typeof window)window.globalThis=window;else{const t=new Function("return this")();t.globalThis=t}}();const r={configurable:!1,enumerable:!1,writable:!1},o=globalThis.window||globalThis;void 0===o.FAST&&Reflect.defineProperty(o,"FAST",{value:Object.create(null),...r});const s=o.FAST;if(void 0===s.getById){const t=Object.create(null);Reflect.defineProperty(s,"getById",{value(e,n){let i=t[e];return void 0===i&&(i=n?t[e]=n():null),i},...r})}void 0===s.error&&Object.assign(s,{warn(){},error:t=>new Error(`Error ${t}`),addMessages(){}});const a=Object.freeze([]);function c(){const t=new WeakMap;return Object.freeze({register:e=>!t.has(e.type)&&(t.set(e.type,e),!0),getByType:e=>t.get(e),getForInstance(e){if(null!=e)return t.get(e.constructor)}})}function l(){const t=new WeakMap;return function(e){let n=t.get(e);if(void 0===n){let i=Reflect.getPrototypeOf(e);for(;void 0===n&&null!==i;)n=t.get(i),i=Reflect.getPrototypeOf(i);n=void 0===n?[]:n.slice(0),t.set(e,n)}return n}}function u(t){t.prototype.toJSON=i.ZT}},11845:function(t,e,n){"use strict";n.d(e,{v:function(){return o}});var i=n(17781);function r(t,e,n){e.source.style.setProperty(t.targetAspect,n.bind(e))}class o{constructor(t,e){this.dataBinding=t,this.targetAspect=e}createCSS(t){return t(this),`var(${this.targetAspect})`}addedCallback(t){var e;const n=t.source;if(!n.$cssBindings){n.$cssBindings=new Map;const t=n.setAttribute;n.setAttribute=(e,i)=>{t.call(n,e,i),"style"===e&&n.$cssBindings.forEach(((t,e)=>r(e,t.controller,t.observer)))}}const i=null!==(e=t[this.targetAspect])&&void 0!==e?e:t[this.targetAspect]=this.dataBinding.createObserver(this,this);i.controller=t,t.source.$cssBindings.set(this,{controller:t,observer:i})}connectedCallback(t){r(this,t,t[this.targetAspect])}removedCallback(t){t.source.$cssBindings&&t.source.$cssBindings.delete(this)}handleChange(t,e){r(this,e.controller,e)}}i.v.define(o)},17781:function(t,e,n){"use strict";n.d(e,{k:function(){return o},v:function(){return r}});const i=(0,n(89522).LI)(),r=Object.freeze({getForInstance:i.getForInstance,getByType:i.getByType,define:t=>(i.register({type:t}),t)});function o(){return function(t){r.define(t)}}},78923:function(t,e,n){"use strict";n.d(e,{i:function(){return f}});var i=n(34959),r=n(16162),o=n(57194),s=n(17781),a=n(70885),c=n(11845);const l=`${Math.random().toString(36).substring(2,8)}`;let u=0;const d=()=>`--v${l}${++u}`;function h(t,e){const n=[];let l="";const u=[],h=t=>{u.push(t)};for(let u=0,f=t.length-1;u<f;++u){l+=t[u];let f=e[u];(0,i.mf)(f)?f=new c.v((0,o.v)(f),d()).createCSS(h):f instanceof r.K?f=new c.v(f,d()).createCSS(h):void 0!==s.v.getForInstance(f)&&(f=f.createCSS(h)),f instanceof a.X||f instanceof CSSStyleSheet?(""!==l.trim()&&(n.push(l),l=""),n.push(f)):l+=f}return l+=t[t.length-1],""!==l.trim()&&n.push(l),{styles:n,behaviors:u}}const f=(t,...e)=>{const{styles:n,behaviors:i}=h(t,e),r=new a.X(n);return i.length?r.withBehaviors(...i):r};class p{constructor(t,e){this.behaviors=e,this.css="";const n=t.reduce(((t,e)=>((0,i.HD)(e)?this.css+=e:t.push(e),t)),[]);n.length&&(this.styles=new a.X(n))}createCSS(t){return this.behaviors.forEach(t),this.styles&&t(this),this.css}addedCallback(t){t.addStyles(this.styles)}removedCallback(t){t.removeStyles(this.styles)}}s.v.define(p),f.partial=(t,...e)=>{const{styles:n,behaviors:i}=h(t,e);return new p(n,i)}},70885:function(t,e,n){"use strict";var i;let r;function o(t){return t.map((t=>t instanceof s?o(t.styles):[t])).reduce(((t,e)=>t.concat(e)),[])}n.d(e,{X:function(){return s}});class s{constructor(t){this.styles=t,this.targets=new WeakSet,this._strategy=null,this.behaviors=t.map((t=>t instanceof s?t.behaviors:null)).reduce(((t,e)=>null===e?t:null===t?e:t.concat(e)),null)}get strategy(){return null===this._strategy&&this.withStrategy(r),this._strategy}addStylesTo(t){this.strategy.addStylesTo(t),this.targets.add(t)}removeStylesFrom(t){this.strategy.removeStylesFrom(t),this.targets.delete(t)}isAttachedTo(t){return this.targets.has(t)}withBehaviors(...t){return this.behaviors=null===this.behaviors?t:this.behaviors.concat(t),this}withStrategy(t){return this._strategy=new t(o(this.styles)),this}static setDefaultStrategy(t){r=t}static normalize(t){return void 0===t?void 0:Array.isArray(t)?new s(t):t instanceof s?t:new s([t])}}s.supportsAdoptedStyleSheets=!(null===(i=globalThis.CSS)||void 0===i?void 0:i.supports("background","-webkit-named-image(i)"))&&Array.isArray(document.adoptedStyleSheets)&&"replace"in CSSStyleSheet.prototype},99026:function(t,e,n){"use strict";n.d(e,{N:function(){return a},p:function(){return s}});var i=n(34959),r=n(85594),o=n(95185);class s extends o.v{constructor(t){super(t),this.observerProperty=Symbol(),this.handleEvent=(t,e)=>{const n=e.target;this.updateTarget(this.getSource(n),this.computeNodes(n))},t.childList=!0}observe(t){let e=t[this.observerProperty];e||(e=new MutationObserver(this.handleEvent),e.toJSON=i.ZT,t[this.observerProperty]=e),e.target=t,e.observe(t,this.options)}disconnect(t){const e=t[this.observerProperty];e.target=null,e.disconnect()}getNodes(t){return"selector"in this.options?Array.from(t.querySelectorAll(this.options.selector)):Array.from(t.childNodes)}}function a(t){return(0,i.HD)(t)&&(t={property:t}),new s(t)}r.m0.define(s)},65883:function(t,e,n){"use strict";n.d(e,{S:function(){return C}});var i=n(31699),r=n(34959),o=n(89522),s=n(59502),a=n(57194),c=n(19316),l=n(49820),u=n(59833),d=n(85594);const h=(t,e)=>`${t}.${e}`,f={},p={index:0,node:null};function g(t){t.startsWith("fast-")||o.Bo.warn(1204,{name:t})}const v=new Proxy(document.createElement("div"),{get(t,e){g(e);const n=Reflect.get(t,e);return(0,r.mf)(n)?n.bind(t):n},set:(t,e,n)=>(g(e),Reflect.set(t,e,n))});class m{constructor(t,e,n){this.fragment=t,this.directives=e,this.policy=n,this.proto=null,this.nodeIds=new Set,this.descriptors={},this.factories=[]}addFactory(t,e,n,i,r){var o,s;this.nodeIds.has(n)||(this.nodeIds.add(n),this.addTargetDescriptor(e,n,i)),t.id=null!==(o=t.id)&&void 0!==o?o:(0,u.To)(),t.targetNodeId=n,t.targetTagName=r,t.policy=null!==(s=t.policy)&&void 0!==s?s:this.policy,this.factories.push(t)}freeze(){return this.proto=Object.create(null,this.descriptors),this}addTargetDescriptor(t,e,n){const i=this.descriptors;if("r"===e||"h"===e||i[e])return;if(!i[t]){const e=t.lastIndexOf("."),n=t.substring(0,e),i=parseInt(t.substring(e+1));this.addTargetDescriptor(n,t,i)}let r=f[e];if(!r){const i=`_${e}`;f[e]=r={get(){var e;return null!==(e=this[i])&&void 0!==e?e:this[i]=this[t].childNodes[n]}}}i[e]=r}createView(t){const e=this.fragment.cloneNode(!0),n=Object.create(this.proto);n.r=e,n.h=null!=t?t:v;for(const t of this.nodeIds)n[t];return new l.bP(e,this.factories,n)}}function b(t,e,n,i,r,o=!1){const a=n.attributes,l=t.directives;for(let h=0,f=a.length;h<f;++h){const p=a[h],g=p.value,v=u._b.parse(g,l);let m=null;null===v?o&&(m=new c.R((0,s.x)((()=>g),t.policy)),d.m0.assignAspect(m,p.name)):m=C.aggregate(v,t.policy),null!==m&&(n.removeAttributeNode(p),h--,f--,t.addFactory(m,e,i,r,n.tagName))}}function y(t,e,n){let i=0,r=e.firstChild;for(;r;){const e=w(t,n,r,i);r=e.node,i=e.index}}function w(t,e,n,i){const o=h(e,i);switch(n.nodeType){case 1:b(t,e,n,o,i),y(t,n,o);break;case 3:return function(t,e,n,i,o){const s=u._b.parse(e.textContent,t.directives);if(null===s)return p.node=e.nextSibling,p.index=o+1,p;let a,c=a=e;for(let e=0,l=s.length;e<l;++e){const l=s[e];0!==e&&(o++,i=h(n,o),a=c.parentNode.insertBefore(document.createTextNode(""),c.nextSibling)),(0,r.HD)(l)?a.textContent=l:(a.textContent=" ",d.m0.assignAspect(l),t.addFactory(l,n,i,o,null)),c=a}return p.index=o+1,p.node=c.nextSibling,p}(t,n,e,o,i);case 8:const s=u._b.parse(n.data,t.directives);null!==s&&t.addFactory(C.aggregate(s),e,o,i,null)}return p.index=i+1,p.node=n.nextSibling,p}const x="TEMPLATE",C={compile(t,e,n=i.S.policy,o){let s;if((0,r.HD)(t)){s=document.createElement(x),s.innerHTML=n.createHTML(t);const e=s.content.firstElementChild;null!==e&&e.tagName===x&&(s=e)}else s=t;s.content.firstChild||s.content.lastChild||s.content.appendChild(document.createComment(""));const a=document.adoptNode(s.content),c=new m(a,e,n);var l,d;return b(c,"",s,"h",0,!0),l=a.firstChild,d=e,(l&&8==l.nodeType&&null!==u._b.parse(l.data,d)||1===a.childNodes.length&&Object.keys(e).length>0)&&a.insertBefore(document.createComment(""),a.firstChild),y(c,a,"r"),p.node=null,c.freeze()},setDefaultStrategy(t){this.compile=t},aggregate(t,e=i.S.policy){if(1===t.length)return t[0];let n,o,s=!1;const l=t.length,u=t.map((t=>(0,r.HD)(t)?()=>t:(n=t.sourceAspect||n,s=s||t.dataBinding.isVolatile,o=o||t.dataBinding.policy,t.dataBinding.evaluate))),h=new c.R((0,a.v)(((t,e)=>{let n="";for(let i=0;i<l;++i)n+=u[i](t,e);return n}),null!=o?o:e,s));return d.m0.assignAspect(h,n),h}}},19316:function(t,e,n){"use strict";n.d(e,{R:function(){return d}});var i=n(28521),r=n(31699),o=n(99452),s=n(89522),a=n(85594),c=n(59833),l=n(49820);const u={[r.O.attribute]:r.S.setAttribute,[r.O.booleanAttribute]:r.S.setBooleanAttribute,[r.O.property]:(t,e,n)=>t[e]=n,[r.O.content]:function(t,e,n,r){if(null==n&&(n=""),function(t){return void 0!==t.create}(n)){t.textContent="";let e=t.$fastView;if(void 0===e)if((0,i.l$)(r)&&(0,i.l$)(n)&&void 0!==r.bindingViewBoundaries[this.targetNodeId]&&r.hydrationStage!==l.BH.hydrated){const t=r.bindingViewBoundaries[this.targetNodeId];e=n.hydrate(t.first,t.last)}else e=n.create();else t.$fastTemplate!==n&&(e.isComposed&&(e.remove(),e.unbind()),e=n.create());e.isComposed?e.needsBindOnly&&(e.needsBindOnly=!1,e.bind(r.source,r.context)):(e.isComposed=!0,e.bind(r.source,r.context),e.insertBefore(t),t.$fastView=e,t.$fastTemplate=n)}else{const e=t.$fastView;void 0!==e&&e.isComposed&&(e.isComposed=!1,e.remove(),e.needsBindOnly?e.needsBindOnly=!1:e.unbind()),t.textContent=n}},[r.O.tokenList]:function(t,e,n){var i;const r=`${this.id}-t`,o=null!==(i=t[r])&&void 0!==i?i:t[r]={v:0,cv:Object.create(null)},s=o.cv;let a=o.v;const c=t[e];if(null!=n&&n.length){const t=n.split(/\s+/);for(let e=0,n=t.length;e<n;++e){const n=t[e];""!==n&&(s[n]=a,c.add(n))}}if(o.v=a+1,0!==a){a-=1;for(const t in s)s[t]===a&&c.remove(t)}},[r.O.event]:()=>{}};class d{constructor(t){this.dataBinding=t,this.updateTarget=null,this.aspectType=r.O.content}createHTML(t){return c.Lu.interpolation(t(this))}createBehavior(){var t;if(null===this.updateTarget){const e=u[this.aspectType],n=null!==(t=this.dataBinding.policy)&&void 0!==t?t:this.policy;if(!e)throw s.Bo.error(1205);this.data=`${this.id}-d`,this.updateTarget=n.protect(this.targetTagName,this.aspectType,this.targetAspect,e)}return this}bind(t){var e;const n=t.targets[this.targetNodeId],o=(0,i.l$)(t)&&t.hydrationStage&&t.hydrationStage!==l.BH.hydrated;switch(this.aspectType){case r.O.event:n[this.data]=t,n.addEventListener(this.targetAspect,this,this.dataBinding.options);break;case r.O.content:t.onUnbind(this);default:const i=null!==(e=n[this.data])&&void 0!==e?e:n[this.data]=this.dataBinding.createObserver(this,this);if(i.target=n,i.controller=t,o&&(this.aspectType===r.O.attribute||this.aspectType===r.O.booleanAttribute)){i.bind(t);break}this.updateTarget(n,this.targetAspect,i.bind(t),t)}}unbind(t){const e=t.targets[this.targetNodeId].$fastView;void 0!==e&&e.isComposed&&(e.unbind(),e.needsBindOnly=!0)}handleEvent(t){const e=t.currentTarget[this.data];if(e.isBound){o.rd.setEvent(t);const n=this.dataBinding.evaluate(e.source,e.context);o.rd.setEvent(null),!0!==n&&t.preventDefault()}}handleChange(t,e){const n=e.target,i=e.controller;this.updateTarget(n,this.targetAspect,e.bind(i),i)}}a.m0.define(d,{aspected:!0})},85594:function(t,e,n){"use strict";n.d(e,{Pq:function(){return l},hG:function(){return c},m0:function(){return a}});var i=n(31699),r=n(89522),o=n(59833);const s=(0,r.LI)(),a=Object.freeze({getForInstance:s.getForInstance,getByType:s.getByType,define:(t,e)=>((e=e||{}).type=t,s.register(e),t),assignAspect(t,e){if(e)switch(t.sourceAspect=e,e[0]){case":":t.targetAspect=e.substring(1),t.aspectType="classList"===t.targetAspect?i.O.tokenList:i.O.property;break;case"?":t.targetAspect=e.substring(1),t.aspectType=i.O.booleanAttribute;break;case"@":t.targetAspect=e.substring(1),t.aspectType=i.O.event;break;default:t.targetAspect=e,t.aspectType=i.O.attribute}else t.aspectType=i.O.content}});function c(t){return function(e){a.define(e,t)}}class l{constructor(t){this.options=t}createHTML(t){return o.Lu.attribute(t(this))}createBehavior(){return this}}(0,r._o)(l)},10863:function(t,e,n){"use strict";var i=n(28521),r=n(49218),o=n(49820);Object.defineProperties(r._H.prototype,{[i.BL]:{value:i.BL,enumerable:!1,configurable:!1},hydrate:{value:function(t,e,n){return new o.Bq(t,e,this,n)},enumerable:!0,configurable:!1}})},59833:function(t,e,n){"use strict";n.d(e,{Lu:function(){return l},To:function(){return c},_b:function(){return u}});const i=`fast-${Math.random().toString(36).substring(2,8)}`,r=`${i}{`,o=`}${i}`,s=o.length;let a=0;const c=()=>`${i}-${++a}`,l=Object.freeze({interpolation:t=>`${r}${t}${o}`,attribute:t=>`${c()}="${r}${t}${o}"`,comment:t=>`\x3c!--${r}${t}${o}--\x3e`}),u=Object.freeze({parse(t,e){const n=t.split(r);if(1===n.length)return null;const i=[];for(let t=0,r=n.length;t<r;++t){const r=n[t],a=r.indexOf(o);let c;if(-1===a)c=r;else{const t=r.substring(0,a);i.push(e[t]),c=r.substring(a+s)}""!==c&&i.push(c)}return i}})},95185:function(t,e,n){"use strict";n.d(e,{R:function(){return s},v:function(){return a}});var i=n(89522),r=n(85594);const o=t=>1===t.nodeType,s=t=>t?e=>1===e.nodeType&&e.matches(t):o;class a extends r.Pq{get id(){return this._id}set id(t){this._id=t,this._controllerProperty=`${t}-c`}bind(t){const e=t.targets[this.targetNodeId];e[this._controllerProperty]=t,this.updateTarget(t.source,this.computeNodes(e)),this.observe(e),t.onUnbind(this)}unbind(t){const e=t.targets[this.targetNodeId];this.updateTarget(t.source,i.ow),this.disconnect(e),e[this._controllerProperty]=null}getSource(t){return t[this._controllerProperty].source}updateTarget(t,e){t[this.options.property]=e}computeNodes(t){let e=this.getNodes(t);return"filter"in this.options&&(e=e.filter(this.options.filter)),e}}},41472:function(t,e,n){"use strict";n.d(e,{i:function(){return o},o:function(){return r}});var i=n(85594);class r extends i.Pq{bind(t){t.source[this.options]=t.targets[this.targetNodeId]}}i.m0.define(r);const o=t=>new r(t)},47170:function(t,e,n){"use strict";n.d(e,{iO:function(){return p},sH:function(){return g},sY:function(){return I}});var i=n(72394),r=n(28521),o=n(34959),s=n(16162),a=n(59502),c=n(57194),l=n(69784),u=n(85594),d=n(59833),h=n(49218),f=n(49820);class p{constructor(t){this.directive=t,this.location=null,this.controller=null,this.view=null,this.data=null,this.dataBindingObserver=t.dataBinding.createObserver(this,t),this.templateBindingObserver=t.templateBinding.createObserver(this,t)}bind(t){if(this.location=t.targets[this.directive.targetNodeId],this.controller=t,this.data=this.dataBindingObserver.bind(t),this.template=this.templateBindingObserver.bind(t),t.onUnbind(this),(0,r.l$)(this.template)&&(0,r.l$)(t)&&t.hydrationStage!==f.BH.hydrated&&!this.view){const e=t.bindingViewBoundaries[this.directive.targetNodeId];e&&(this.view=this.template.hydrate(e.first,e.last),this.bindView(this.view))}else this.refreshView()}unbind(t){const e=this.view;null!==e&&e.isComposed&&(e.unbind(),e.needsBindOnly=!0)}handleChange(t,e){e===this.dataBindingObserver&&(this.data=this.dataBindingObserver.bind(this.controller)),(this.directive.templateBindingDependsOnData||e===this.templateBindingObserver)&&(this.template=this.templateBindingObserver.bind(this.controller)),this.refreshView()}bindView(t){t.isComposed?t.needsBindOnly&&(t.needsBindOnly=!1,t.bind(this.data)):(t.isComposed=!0,t.bind(this.data),t.insertBefore(this.location),t.$fastTemplate=this.template)}refreshView(){let t=this.view;const e=this.template;null===t?(this.view=t=e.create(),this.view.context.parent=this.controller.source,this.view.context.parentContext=this.controller.context):t.$fastTemplate!==e&&(t.isComposed&&(t.remove(),t.unbind()),this.view=t=e.create(),this.view.context.parent=this.controller.source,this.view.context.parentContext=this.controller.context),this.bindView(t)}}class g{constructor(t,e,n){this.dataBinding=t,this.templateBinding=e,this.templateBindingDependsOnData=n}createHTML(t){return d.Lu.comment(t(this))}createBehavior(){return new p(this)}}u.m0.define(g);const v=new Map,m={":model":t=>t},b=Symbol("RenderInstruction"),y="default-view",w=h.dy`
    &nbsp;
`;function x(t){return void 0===t?w:t.template}function C(t,e){const n=[],i=[],{attributes:r,directives:s,content:a,policy:c}=null!=e?e:{};if(n.push(`<${t}`),r){const t=Object.getOwnPropertyNames(r);for(let e=0,o=t.length;e<o;++e){const o=t[e];0===e?n[0]=`${n[0]} ${o}="`:n.push(`" ${o}="`),i.push(r[o])}n.push('"')}if(s){n[n.length-1]+=" ";for(let t=0,e=s.length;t<e;++t){const e=s[t];n.push(t>0?"":" "),i.push(e)}}if(n[n.length-1]+=">",a&&(0,o.mf)(a.create))i.push(a),n.push(`</${t}>`);else{const e=n.length-1;n[e]=`${n[e]}${null!=a?a:""}</${t}>`}return h._H.create(n,i,c)}function k(t){var e;const n=null!==(e=t.name)&&void 0!==e?e:y;let r;if((o=t).element||o.tagName){let e=t.tagName;if(!e){const n=i.W.getByType(t.element);if(!n)throw new Error("Invalid element for model rendering.");e=n.name}t.attributes||(t.attributes=m),r=C(e,t)}else r=t.template;var o;return{brand:b,type:t.type,name:n,template:r}}function S(t){return t&&t.brand===b}function $(t){let e=v.get(t.type);void 0===e&&v.set(t.type,e=Object.create(null));const n=S(t)?t:k(t);return e[n.name]=n}function T(t,e){const n=v.get(t);if(void 0!==n)return n[null!=e?e:y]}function L(t,e){if(t)return T(t.constructor,e)}Object.freeze({instanceOf:S,create:k,createElementTemplate:C,register:$,getByType:T,getForInstance:L});class O{constructor(t){this.node=t,t.$fastTemplate=this}get context(){return this}bind(t){}unbind(){}insertBefore(t){t.parentNode.insertBefore(this.node,t)}remove(){this.node.parentNode.removeChild(this.node)}create(){return this}hydrate(t,e){return this}}function I(t,e){let n,i;n=void 0===t?(0,a.x)((t=>t)):(0,l.k)(t);let r=!1;if(void 0===e)r=!0,i=(0,a.x)(((t,e)=>{var i;const r=n.evaluate(t,e);return r instanceof Node?null!==(i=r.$fastTemplate)&&void 0!==i?i:new O(r):x(L(r))}));else if((0,o.mf)(e))i=(0,c.v)(((t,i)=>{var r;let s=e(t,i);return(0,o.HD)(s)?s=x(L(n.evaluate(t,i),s)):s instanceof Node&&(s=null!==(r=s.$fastTemplate)&&void 0!==r?r:new O(s)),s}),void 0,!0);else if((0,o.HD)(e))r=!0,i=(0,a.x)(((t,i)=>{var r;const o=n.evaluate(t,i);return o instanceof Node?null!==(r=o.$fastTemplate)&&void 0!==r?r:new O(o):x(L(o,e))}));else if(e instanceof s.K){const t=e.evaluate;e.evaluate=(e,i)=>{var r;let s=t(e,i);return(0,o.HD)(s)?s=x(L(n.evaluate(e,i),s)):s instanceof Node&&(s=null!==(r=s.$fastTemplate)&&void 0!==r?r:new O(s)),s},i=e}else i=(0,a.x)(((t,n)=>e));return new g(n,i,r)}},89150:function(t,e,n){"use strict";n.d(e,{Gx:function(){return m},eN:function(){return v},rx:function(){return b}});var i=n(28521),r=n(32883),o=n(99452),s=n(89522),a=n(69784),c=n(85594),l=n(59833),u=n(49820);const d=Object.freeze({positioning:!1,recycle:!0});function h(t,e,n,i){t.context.parent=i.source,t.context.parentContext=i.context,t.bind(e[n])}function f(t,e,n,i){t.context.parent=i.source,t.context.parentContext=i.context,t.context.length=e.length,t.context.index=n,t.bind(e[n])}function p(t){return t.nodeType===Node.COMMENT_NODE}class g extends Error{constructor(t,e){super(t),this.propertyBag=e}}class v{constructor(t){this.directive=t,this.items=null,this.itemsObserver=null,this.bindView=h,this.views=[],this.itemsBindingObserver=t.dataBinding.createObserver(this,t),this.templateBindingObserver=t.templateBinding.createObserver(this,t),t.options.positioning&&(this.bindView=f)}bind(t){this.location=t.targets[this.directive.targetNodeId],this.controller=t,this.items=this.itemsBindingObserver.bind(t),this.template=this.templateBindingObserver.bind(t),this.observeItems(!0),(0,i.l$)(this.template)&&(0,i.l$)(t)&&t.hydrationStage!==u.BH.hydrated?this.hydrateViews(this.template):this.refreshAllViews(),t.onUnbind(this)}unbind(){null!==this.itemsObserver&&this.itemsObserver.unsubscribe(this),this.unbindAllViews()}handleChange(t,e){if(e===this.itemsBindingObserver)this.items=this.itemsBindingObserver.bind(this.controller),this.observeItems(),this.refreshAllViews();else if(e===this.templateBindingObserver)this.template=this.templateBindingObserver.bind(this.controller),this.refreshAllViews(!0);else{if(!e[0])return;e[0].reset?this.refreshAllViews():this.updateViews(e)}}observeItems(t=!1){if(!this.items)return void(this.items=s.ow);const e=this.itemsObserver,n=this.itemsObserver=o.y$.getNotifier(this.items),i=e!==n;i&&null!==e&&e.unsubscribe(this),(i||t)&&n.subscribe(this)}updateViews(t){const e=this.views,n=this.bindView,i=this.items,r=this.template,o=this.controller,s=this.directive.options.recycle,a=[];let c=0,l=0;for(let u=0,d=t.length;u<d;++u){const d=t[u],h=d.removed;let f=0,p=d.index;const g=p+d.addedCount,v=e.splice(d.index,h.length),m=l=a.length+v.length;for(;p<g;++p){const t=e[p],u=t?t.firstChild:this.location;let d;s&&l>0?(f<=m&&v.length>0?(d=v[f],f++):(d=a[c],c++),l--):d=r.create(),e.splice(p,0,d),n(d,i,p,o),d.insertBefore(u)}v[f]&&a.push(...v.slice(f))}for(let t=c,e=a.length;t<e;++t)a[t].dispose();if(this.directive.options.positioning)for(let t=0,n=e.length;t<n;++t){const i=e[t].context;i.length=n,i.index=t}}refreshAllViews(t=!1){const e=this.items,n=this.template,i=this.location,r=this.bindView,o=this.controller;let s=e.length,a=this.views,c=a.length;if(0!==s&&!t&&this.directive.options.recycle||(u.bP.disposeContiguousBatch(a),c=0),0===c){this.views=a=new Array(s);for(let t=0;t<s;++t){const s=n.create();r(s,e,t,o),a[t]=s,s.insertBefore(i)}}else{let t=0;for(;t<s;++t)if(t<c){const i=a[t];if(!i){const e=new XMLSerializer;throw new g(`View is null or undefined inside ${this.location.getRootNode().host.nodeName}. This is a SSR software defect`,{index:t,hydrationStage:this.controller.hydrationStage,itemsLength:s,viewsState:a.map((t=>t?"hydrated":"empty")),viewTemplateString:e.serializeToString(n.create().fragment),rootNodeContent:e.serializeToString(this.location.getRootNode())})}r(i,e,t,o)}else{const s=n.create();r(s,e,t,o),a.push(s),s.insertBefore(i)}const l=a.splice(t,c-t);for(t=0,s=l.length;t<s;++t)l[t].dispose()}}unbindAllViews(){const t=this.views;for(let e=0,n=t.length;e<n;++e){const n=t[e];if(!n){const n=new XMLSerializer;throw new g(`View is null or undefined inside ${this.location.getRootNode().host.nodeName}. This is a SSR software defect`,{index:e,hydrationStage:this.controller.hydrationStage,viewsState:t.map((t=>t?"hydrated":"empty")),rootNodeContent:n.serializeToString(this.location.getRootNode())})}n.unbind()}}hydrateViews(t){if(!this.items)return;this.views=new Array(this.items.length);let e=this.location.previousSibling;for(;null!==e;){if(!p(e)){e=e.previousSibling;continue}const n=i._M.parseRepeatEndMarker(e.data);if(null===n){e=e.previousSibling;continue}e.data="";const r=e.previousSibling;if(!r)throw new Error(`Error when hydrating inside ${this.location.getRootNode().host.nodeName}: end should never be null. This is a SSR software defect`);let o=r,s=0;for(;null!==o;){if(p(o))if(i._M.isRepeatViewEndMarker(o.data))s++;else if(i._M.isRepeatViewStartMarker(o.data)){if(!s){if(i._M.parseRepeatStartMarker(o.data)!==n)throw new Error(`Error when hydrating inside ${this.location.getRootNode().host.nodeName}: Mismatched start and end markers. This is a SSR software defect`);o.data="",e=o.previousSibling,o=o.nextSibling;const s=t.hydrate(o,r);this.views[n]=s,this.bindView(s,this.items,n,this.controller);break}s--}o=o.previousSibling}if(!o)throw new Error(`Error when hydrating inside ${this.location.getRootNode().host.nodeName}:start should never be null. This is a SSR software defect`)}}}class m{constructor(t,e,n){this.dataBinding=t,this.templateBinding=e,this.options=n,r.K1.enable()}createHTML(t){return l.Lu.comment(t(this))}createBehavior(){return new v(this)}}function b(t,e,n=d){const i=(0,a.k)(t),r=(0,a.k)(e);return new m(i,r,{...d,...n})}c.m0.define(m)},47548:function(t,e,n){"use strict";n.d(e,{E:function(){return a},Q:function(){return c}});var i=n(34959),r=n(85594),o=n(95185);const s="slotchange";class a extends o.v{observe(t){t.addEventListener(s,this)}disconnect(t){t.removeEventListener(s,this)}getNodes(t){return t.assignedNodes(this.options)}handleEvent(t){const e=t.currentTarget;this.updateTarget(this.getSource(e),this.computeNodes(e))}}function c(t){return(0,i.HD)(t)&&(t={property:t}),new a(t)}r.m0.define(a)},49218:function(t,e,n){"use strict";n.d(e,{Sy:function(){return p},_H:function(){return v},dy:function(){return m}});var i=n(89522),r=n(34959),o=n(16162),s=n(59502),a=n(57194),c=n(19316),l=n(59833),u=n(65883),d=n(85594);const h=/([ \x09\x0a\x0c\x0d])([^\0-\x1F\x7F-\x9F "'>=/]+)([ \x09\x0a\x0c\x0d]*=[ \x09\x0a\x0c\x0d]*(?:[^ \x09\x0a\x0c\x0d"'`<>=]*|"[^"]*|'[^']*))$/,f=Object.create(null);class p{constructor(t,e=f){this.html=t,this.factories=e}createHTML(t){const e=this.factories;for(const n in e)t(e[n]);return this.html}}function g(t,e,n,i=d.m0.getForInstance(t)){if(i.aspected){const n=h.exec(e);null!==n&&d.m0.assignAspect(t,n[2])}return t.createHTML(n)}p.empty=new p(""),d.m0.define(p);class v{constructor(t,e={},n,i){this.policy=i,this.result=null,this.html=t,this.factories=e,this.opCodes=n}compile(){return null===this.result&&(this.result=u.S.compile(this.html,this.factories,this.policy,this.opCodes)),this.result}create(t){return this.compile().createView(t)}inline(){return new p((0,r.HD)(this.html)?this.html:this.html.innerHTML,this.factories)}withPolicy(t){if(this.result)throw i.Bo.error(1208);if(this.policy)throw i.Bo.error(1207);return this.policy=t,this}render(t,e,n){const i=this.create(n);return i.bind(t),i.appendTo(e),i}static createFromOpCodes(t){return new v("",void 0,t,void 0)}static create(t,e,n){let i="";const r=Object.create(null),o=t=>{var e;const n=null!==(e=t.id)&&void 0!==e?e:t.id=(0,l.To)();return r[n]=t,n};for(let n=0,r=t.length-1;n<r;++n){const r=t[n],s=e[n];let a;i+=r;let c=v.createBindingDirectiveFromExpression(s);c||(a=d.m0.getForInstance(s),c=s),i+=g(c,r,o,a)}return new v(i+t[t.length-1],r,void 0,n)}static createBindingDirective(t,e){let n;const i=t=>{var e;const i=null!==(e=t.id)&&void 0!==e?e:t.id=(0,l.To)();return n=t,i};{const n=t,r=e;let o,s=v.createBindingDirectiveFromExpression(r);s||(o=d.m0.getForInstance(r),s=r),g(s,n,i,o)}return n}static createBindingDirectiveFromExpression(t){let e;if((0,r.mf)(t))e=new c.R((0,a.v)(t));else if(t instanceof o.K)e=new c.R(t);else if(!d.m0.getForInstance(t)){const n=t;e=new c.R((0,s.x)((()=>n)))}return e}}(0,i._o)(v);const m=(t,...e)=>{if(Array.isArray(t)&&Array.isArray(t.raw))return v.create(t,e);throw i.Bo.error(1206)};m.partial=t=>new p(t)},49820:function(t,e,n){"use strict";n.d(e,{bP:function(){return v},AH:function(){return b},BH:function(){return m},Bq:function(){return y}});var i=n(28521);class r extends Error{constructor(t,e,n){super(t),this.factories=e,this.node=n}}function o(t){return t.nodeType===Node.COMMENT_NODE}function s(t){return t.nodeType===Node.TEXT_NODE}function a(t,e){const n=document.createRange();return n.setStart(t,0),n.setEnd(e,o(e)||s(e)?e.data.length:e.childNodes.length),n}function c(t,e,n){const o=i._M.parseAttributeBinding(t);if(null!==o){for(const i of o){if(!e[i])throw new r(`HydrationView was unable to successfully target factory on ${t.nodeName} inside ${t.getRootNode().host.nodeName}. This likely indicates a template mismatch between SSR rendering and hydration.`,e,t);u(e[i],t,n)}t.removeAttribute(i._M.attributeMarkerName)}}function l(t,e,n,a,c){if(i._M.isElementBoundaryStartMarker(t))!function(t,e){const n=i._M.parseElementBoundaryStartMarker(t.data);let r=e.nextSibling();for(;null!==r;){if(o(r)){const t=i._M.parseElementBoundaryEndMarker(r.data);if(t&&t===n)break}r=e.nextSibling()}}(t,e);else if(i._M.isContentBindingStartMarker(t.data)){const l=i._M.parseContentBindingStartMarker(t.data);if(null===l)return;const[d,h]=l,f=n[d];if(!f)throw new r(`HydrationView was unable to successfully target factory on ${t.nodeName} for ${t.data}. This likely indicates a template mismatch between SSR rendering and hydration.`,n,e.root);const p=[];let g=e.nextSibling();t.data="";const v=g;for(;null!==g;){if(o(g)){const t=i._M.parseContentBindingEndMarker(g.data);if(t&&t[1]===h)break}p.push(g),g=e.nextSibling()}if(null===g){const e=t.getRootNode();throw new Error(`Error when hydrating inside ${function(t){return t instanceof DocumentFragment&&"mode"in t}(e)?e.host.nodeName:e.nodeName}: current should never be null. This is a SSR software defect`)}if(g.data="",1===p.length&&s(p[0]))u(f,p[0],a);else{g!==v&&null!==g.previousSibling&&(c[f.targetNodeId]={first:v,last:g.previousSibling});u(f,g.parentNode.insertBefore(document.createTextNode(""),g),a)}}}function u(t,e,n){if(void 0===t.targetNodeId)throw new Error("Factory could not be target to the node");n[t.targetNodeId]=e}var d,h=n(99452),f=n(89522);function p(t,e){const n=t.parentNode;let i,r=t;for(;r!==e;){if(i=r.nextSibling,!i)throw new Error(`Unmatched first/last child inside ${e.getRootNode().host.nodeName}. This is a SSR software defect`);n.removeChild(r),r=i}n.removeChild(e)}class g{constructor(){this.index=0,this.length=0}get event(){return h.rd.getEvent()}get isEven(){return this.index%2==0}get isOdd(){return this.index%2!=0}get isFirst(){return 0===this.index}get isInMiddle(){return!this.isFirst&&!this.isLast}get isLast(){return this.index===this.length-1}eventDetail(){return this.event.detail}eventTarget(){return this.event.target}}class v extends g{constructor(t,e,n){super(),this.fragment=t,this.factories=e,this.targets=n,this.behaviors=null,this.unbindables=[],this.source=null,this.isBound=!1,this.sourceLifetime=h.sG.unknown,this.context=this,this.firstChild=t.firstChild,this.lastChild=t.lastChild}appendTo(t){t.appendChild(this.fragment)}insertBefore(t){if(this.fragment.hasChildNodes())t.parentNode.insertBefore(this.fragment,t);else{const e=this.lastChild;if(t.previousSibling===e)return;const n=t.parentNode;let i,r=this.firstChild;for(;r!==e;)i=r.nextSibling,n.insertBefore(r,t),r=i;n.insertBefore(e,t)}}remove(){const t=this.fragment,e=this.lastChild;let n,i=this.firstChild;for(;i!==e;)n=i.nextSibling,t.appendChild(i),i=n;t.appendChild(e)}dispose(){p(this.firstChild,this.lastChild),this.unbind()}onUnbind(t){this.unbindables.push(t)}bind(t,e=this){if(this.source===t)return;let n=this.behaviors;if(null===n){this.source=t,this.context=e,this.behaviors=n=new Array(this.factories.length);const i=this.factories;for(let t=0,e=i.length;t<e;++t){const e=i[t].createBehavior();e.bind(this),n[t]=e}}else{null!==this.source&&this.evaluateUnbindables(),this.isBound=!1,this.source=t,this.context=e;for(let t=0,e=n.length;t<e;++t)n[t].bind(this)}this.isBound=!0}unbind(){this.isBound&&null!==this.source&&(this.evaluateUnbindables(),this.source=null,this.context=this,this.isBound=!1)}evaluateUnbindables(){const t=this.unbindables;for(let e=0,n=t.length;e<n;++e)t[e].unbind(this);t.length=0}static disposeContiguousBatch(t){if(0!==t.length){p(t[0].firstChild,t[t.length-1].lastChild);for(let e=0,n=t.length;e<n;++e)t[e].unbind()}}}(0,f._o)(v),h.y$.defineProperty(v.prototype,"index"),h.y$.defineProperty(v.prototype,"length");const m={unhydrated:"unhydrated",hydrating:"hydrating",hydrated:"hydrated"};class b extends Error{constructor(t,e,n,i){super(t),this.factory=e,this.fragment=n,this.templateString=i}}class y extends g{constructor(t,e,n,r){super(),this.firstChild=t,this.lastChild=e,this.sourceTemplate=n,this.hostBindingTarget=r,this[d]=i.BL,this.context=this,this.source=null,this.isBound=!1,this.sourceLifetime=h.sG.unknown,this.unbindables=[],this.fragment=null,this.behaviors=null,this._hydrationStage=m.unhydrated,this._bindingViewBoundaries={},this._targets={},this.factories=n.compile().factories}get hydrationStage(){return this._hydrationStage}get targets(){return this._targets}get bindingViewBoundaries(){return this._bindingViewBoundaries}insertBefore(t){if(null!==this.fragment)if(this.fragment.hasChildNodes())t.parentNode.insertBefore(this.fragment,t);else{const e=this.lastChild;if(t.previousSibling===e)return;const n=t.parentNode;let i,r=this.firstChild;for(;r!==e;)i=r.nextSibling,n.insertBefore(r,t),r=i;n.insertBefore(e,t)}}appendTo(t){null!==this.fragment&&t.appendChild(this.fragment)}remove(){const t=this.fragment||(this.fragment=document.createDocumentFragment()),e=this.lastChild;let n,i=this.firstChild;for(;i!==e;){if(n=i.nextSibling,!n)throw new Error(`Unmatched first/last child inside ${e.getRootNode().host.nodeName}. This is a SSR software defect`);t.appendChild(i),i=n}t.appendChild(e)}bind(t,e=this){if(this.hydrationStage!==m.hydrated&&(this._hydrationStage=m.hydrating),this.source===t)return;let n=this.behaviors;if(null===n){this.source=t,this.context=e;try{const{targets:t,boundaries:e}=function(t,e,n){const i=a(t,e),r=i.commonAncestorContainer,o=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT+NodeFilter.SHOW_COMMENT+NodeFilter.SHOW_TEXT,{acceptNode:t=>0===i.comparePoint(t,0)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}),s={},u={};let d=o.currentNode=t;for(;null!==d;){switch(d.nodeType){case Node.ELEMENT_NODE:c(d,n,s);break;case Node.COMMENT_NODE:l(d,o,n,s,u)}d=o.nextNode()}return i.detach(),{targets:s,boundaries:u}}(this.firstChild,this.lastChild,this.factories);this._targets=t,this._bindingViewBoundaries=e}catch(t){if(t instanceof r){let e=this.sourceTemplate.html;"string"!=typeof e&&(e=e.innerHTML),t.templateString=e}throw t}this.behaviors=n=new Array(this.factories.length);const i=this.factories;for(let t=0,e=i.length;t<e;++t){const e=i[t];if("h"===e.targetNodeId&&this.hostBindingTarget&&u(e,this.hostBindingTarget,this._targets),!(e.targetNodeId in this.targets)){let t=this.sourceTemplate.html;throw"string"!=typeof t&&(t=t.innerHTML),new b(`HydrationView was unable to successfully target bindings inside ${this.firstChild.getRootNode().host.nodeName}.This is likely a hydration software defect.`,e,a(this.firstChild,this.lastChild).cloneContents(),t)}{const i=e.createBehavior();i.bind(this),n[t]=i}}}else{null!==this.source&&this.evaluateUnbindables(),this.isBound=!1,this.source=t,this.context=e;for(let t=0,e=n.length;t<e;++t)n[t].bind(this)}this.isBound=!0,this._hydrationStage=m.hydrated}unbind(){this.isBound&&null!==this.source&&(this.evaluateUnbindables(),this.source=null,this.context=this,this.isBound=!1)}dispose(){p(this.firstChild,this.lastChild),this.unbind()}onUnbind(t){this.unbindables.push(t)}evaluateUnbindables(){const t=this.unbindables;for(let e=0,n=t.length;e<n;++e)t[e].unbind(this);t.length=0}}d=i.BL,(0,f._o)(y)},93703:function(t,e,n){"use strict";n.d(e,{g:function(){return s}});var i=n(34959);const r=()=>null;function o(t){return void 0===t?r:(0,i.mf)(t)?t:()=>t}function s(t,e,n){const r=(0,i.mf)(t)?t:()=>t,s=o(e),a=o(n);return(t,e)=>r(t,e)?s(t,e):a(t,e)}},62795:function(t,e,n){"use strict";n.d(e,{Ag:function(){return c},Jp:function(){return l},MP:function(){return a},TC:function(){return s}});var i=n(31699),r=n(99452),o=n(59833);function s(t){const e=t.parentElement;if(e)return e;{const e=t.getRootNode();if(e.host instanceof HTMLElement)return e.host}return null}function a(t,e){let n=e;for(;null!==n;){if(n===t)return!0;n=s(n)}return!1}class c extends MutationObserver{constructor(t){super((function(t){this.callback.call(null,t.filter((t=>this.observedNodes.has(t.target))))})),this.callback=t,this.observedNodes=new Set}observe(t,e){this.observedNodes.add(t),super.observe(t,e)}unobserve(t){this.observedNodes.delete(t),this.observedNodes.size<1&&this.disconnect()}}const l=Object.freeze({create(t){const e=[],n={};let s=null,a=!1;return{source:t,context:r.rd.default,targets:n,get isBound(){return a},addBehaviorFactory(t,e){var n,r,s,a;const c=t;c.id=null!==(n=c.id)&&void 0!==n?n:(0,o.To)(),c.targetNodeId=null!==(r=c.targetNodeId)&&void 0!==r?r:(0,o.To)(),c.targetTagName=null!==(s=e.tagName)&&void 0!==s?s:null,c.policy=null!==(a=c.policy)&&void 0!==a?a:i.S.policy,this.addTarget(c.targetNodeId,e),this.addBehavior(c.createBehavior())},addTarget(t,e){n[t]=e},addBehavior(t){e.push(t),a&&t.bind(this)},onUnbind(t){null===s&&(s=[]),s.push(t)},connectedCallback(t){a||(a=!0,e.forEach((t=>t.bind(this))))},disconnectedCallback(t){a&&(a=!1,null!==s&&s.forEach((t=>t.unbind(this))))}}}})},35114:function(t,e,n){"use strict";n.d(e,{Kx:function(){return a},Mr:function(){return c},_R:function(){return l}});var i=n(33940),r=n(28904),o=n(42590),s=n(99452);const a={C1:"C1",C2:"C2",C3:"C3",C4:"C4",C5:"C5",C6:"C6"};class c extends r.H{constructor(){super(...arguments),this.layout=a.C4,this.childrenLayout=[],this.disabledKeyListeners=[]}layoutChanged(){this.updateLayout()}childTemplateMapChanged(){this.updateLayout()}dataChanged(){this.updateLayout()}layoutStylesChanged(t,e){t&&this.$fastController.removeStyles(t),this.$fastController.addStyles(e)}selectTemplate(t){if(t.childTemplateType)return this.childTemplateMap?.[t.childTemplateType]}updateLayout(){if(void 0===this.data)return;const t=this.data[this.layout];this.childrenLayout=this.data.defaultLayout.map((e=>({...e,...t?.find((t=>t.id===e.id))}))).sort(((t,e)=>t.childDOMOrder&&e.childDOMOrder?t.childDOMOrder-e.childDOMOrder:0))}handleKeyDown(t){const e=Array.from(this.getRootNode().querySelectorAll(this.tagName)).reduce(((t,e)=>t.concat(Array.from(e.shadowRoot?.children||[]))),[]).filter((t=>t.offsetWidth>0&&t.offsetHeight>0));return!(!this.disabledKeyListeners||!this.disabledKeyListeners.find((e=>e===t.key)))||l(t,e)}}function l(t,e){const n=!!["ArrowDown","ArrowLeft","ArrowRight","ArrowUp","End","Home","PageUp","PageDown"].find((e=>e===t.key));if(t.defaultPrevented||!n||!e)return!0;const i=e.reduce(((t,e,n)=>{const{offsetLeft:i,offsetHeight:r,offsetTop:o,offsetWidth:s}=e;return t.push({x:i,y:o,x2:i+s,y2:o+r,index:n}),t}),[]),r=e.findIndex((e=>t.composedPath().find((t=>t===e))))??0,o=t=>{const e=t.currentTarget;e&&(e.removeAttribute("tabindex"),e.removeEventListener("blur",o))},s=i.reduce(((t,e,n)=>{const o=i[r||0],s={x:["ArrowLeft","ArrowRight"],y:["ArrowUp","ArrowDown"]},a=t=>Math.min(e[`${t}2`],o[`${t}2`])-Math.max(e[t],o[t]),c=t=>Math.abs(e[t]>=o[`${t}2`]?e[t]-o[`${t}2`]:o[t]-e[`${t}2`]);for(const i in s){const l=i,u=a("x"===l?"y":"x"),d=c(l);if(u>0){const i=e[l]>=o[`${l}2`]?1:0,a=s[l][i];n!==r&&(void 0===t[a]||(t[a]?.distance||0)>d)&&(t[a]=Object.assign({},e,{overlap:u,distance:d})),"y"===l&&("ArrowUp"===a?(!t.PageUp||t.PageUp.y>e.y)&&(t.PageUp=e):(!t.PageDown||t.PageDown.y2<e.y2)&&(t.PageDown=e))}}return(!t.Home||t.Home.x>e.x||t.Home.y>e.y)&&(t.Home=e),(!t.End||t.End.x<e.x||t.End.y<e.y)&&(t.End=e),t}),{})[t.key];var a;return void 0!==s&&(a=s.index,e&&e.length>a&&(e[a].setAttribute("tabindex","0"),e[a].addEventListener("blur",o),e[a].focus())),!0}(0,i.gn)([o.Lj,(0,i.w6)("design:type",String)],c.prototype,"layout",void 0),(0,i.gn)([s.LO,(0,i.w6)("design:type",Array)],c.prototype,"childrenLayout",void 0),(0,i.gn)([s.LO,(0,i.w6)("design:type",Object)],c.prototype,"childTemplateMap",void 0),(0,i.gn)([s.LO,(0,i.w6)("design:type",Object)],c.prototype,"data",void 0),(0,i.gn)([s.LO,(0,i.w6)("design:type",Object)],c.prototype,"layoutStyles",void 0),(0,i.gn)([s.LO,(0,i.w6)("design:type",Array)],c.prototype,"disabledKeyListeners",void 0)},20284:function(t,e,n){"use strict";n.d(e,{P:function(){return i}});const i={_self:"_self",_blank:"_blank",_parent:"_parent",_top:"_top"}},35210:function(t,e,n){"use strict";n.d(e,{v:function(){return r}});var i=n(28904);class r extends i.H{}},35928:function(t,e,n){"use strict";n.d(e,{O:function(){return r}});var i=n(49218);function r(){return i.dy`
        <slot></slot>
    `}},52704:function(t,e,n){"use strict";n.d(e,{L:function(){return k}});var i=n(33940),r=n(99452),o=n(39579),s=n(28904),a=n(17781),c=n(62795);class l{constructor(t){this.value=t,this.notifier=r.y$.getNotifier(this),this.dependencies=new Set,this.binding=r.y$.binding(t,this),this.binding.setMode(!1)}static getOrCreate(t){let e=l.cache.get(t);return e||(e=new l(t),l.cache.set(t,e),e)}evaluate(t,e){return this.binding.observe((n=>{if(this.dependencies.add(n),e===n){if(t.parent)return t.parent.getTokenValue(n);throw new Error("DesignTokenNode has encountered a circular token reference. Avoid this by setting the token value for an ancestor node.")}return t.getTokenValue(n)}))}handleChange(){this.notifier.notify(void 0)}}l.cache=new WeakMap;class u{constructor(t,e,n,i){this.token=t,this.evaluator=e,this.node=n,this.subscriber=i,this.value=e.evaluate(n,t),this.subscriber&&r.y$.getNotifier(this.evaluator).subscribe(this.subscriber)}dispose(){this.subscriber&&r.y$.getNotifier(this.evaluator).unsubscribe(this.subscriber)}update(){return this.value=this.evaluator.evaluate(this.node,this.token),this}}class d{constructor(t,e,n,i){this.target=t,this.type=e,this.token=n,this.value=i}notify(){r.y$.getNotifier(this.token).notify(this)}}class h{constructor(){this._parent=null,this._children=new Set,this._values=new Map,this._derived=new Map,this.dependencyGraph=new Map}static isDerivedTokenValue(t){return"function"==typeof t}static isDerivedFor(t,e){return t._derived.has(e)}static collectDerivedContext(t){const e=new Map;if(null===t.parent)return e;let n=h.getAssignedTokensForNode(t),i=t.parent;do{const t=h.getAssignedTokensForNode(i);for(let r=0,o=t.length;r<o;r++){const o=t[r];!n.includes(o)&&h.isDerivedFor(i,o)&&e.set(o,i._derived.get(o))}n=Array.from(new Set(n.concat(t))),i=i.parent}while(null!==i);return e}static getLocalTokenValue(t,e){return h.isAssigned(t,e)?h.isDerivedFor(t,e)?t._derived.get(e).value:t._values.get(e):void 0}static getOrCreateDependencyGraph(t,e){let n=t.dependencyGraph.get(e);return n||(n=new Set,t.dependencyGraph.set(e,n),n)}static notify(){const t=this._notifications;this._notifications=[];for(const e of t)e.notify()}static queueNotification(...t){this._notifications.push(...t)}static getAssignedTokensForNode(t){return Array.from(t._values.keys())}static composeAssignedTokensForNode(t){const e=new Set(h.getAssignedTokensForNode(t));let n=t.parent;for(;null!==n;){const t=h.getAssignedTokensForNode(n);for(const n of t)e.add(n);n=n.parent}return Array.from(e)}static isAssigned(t,e){return t._values.has(e)}get parent(){return this._parent}get children(){return Array.from(this._children)}appendChild(t){let e=null;null!==t.parent&&(e=h.composeAssignedTokensForNode(t.parent),t.parent._children.delete(t));const n=h.composeAssignedTokensForNode(this),i=h.collectDerivedContext(this);t._parent=this,this._children.add(t);for(const r of n){let n=0;if(null!==e){const t=e.indexOf(r);-1!==t&&(n=1,e.splice(t,1))}t.dispatch(new d(this,n,r,i.get(r)?.evaluator.value))}if(null!==e&&e.length>0)for(const n of e)t.dispatch(new d(this,2,n,i.get(n)?.evaluator.value));h.notify()}removeChild(t){if(t.parent===this){const e=h.composeAssignedTokensForNode(this);t._parent=null,this._children.delete(t);for(const n of e)t.dispatch(new d(this,2,n));h.notify()}}dispose(){this.parent&&(this.parent._children.delete(this),this._parent=null);for(const[,t]of this._derived)t.dispose()}setTokenValue(t,e){const n=h.isAssigned(this,t)||h.isDerivedFor(this,t)?1:0,i=h.getLocalTokenValue(this,t);this._values.set(t,e),h.isDerivedFor(this,t)&&this.tearDownDerivedTokenValue(t);const r=h.isDerivedTokenValue(e),o=h.collectDerivedContext(this);let s;if(r){s=this.setupDerivedTokenValue(t,e,!0).value}else s=e;i!==s&&h.queueNotification(new d(this,n,t,e)),this.dispatch(new d(this,n,t,e)),o.forEach(((t,e)=>{if(!h.isDerivedFor(this,e)){h.getLocalTokenValue(this,e)!==(t=this.setupDerivedTokenValue(e,t.evaluator.value)).value&&h.queueNotification(new d(this,1,e,t.evaluator.value)),this.dispatch(new d(this,0,e,t.evaluator.value))}})),h.notify()}getTokenValue(t){let e,n=this;for(;null!==n;){if(h.isDerivedFor(n,t)){e=n._derived.get(t).value;break}if(h.isAssigned(n,t)){e=n._values.get(t);break}n=n._parent}if(void 0!==e)return e;throw new Error(`No value set for token '${t.name??t}' in node tree.`)}deleteTokenValue(t){if(h.isAssigned(this,t)){const e=h.getLocalTokenValue(this,t);let n;this._values.delete(t),this.tearDownDerivedTokenValue(t);try{n=this.getTokenValue(t)}catch(t){n=void 0}h.queueNotification(new d(this,2,t)),e!==n&&this.dispatch(new d(this,2,t)),h.notify()}}dispatch(t){if(this!==t.target){const{token:e}=t,n=h.isAssigned(this,e),i=n&&this._derived.get(e)?.evaluator.dependencies.has(e);if(n&&!i)return;2===t.type&&!n&&h.isDerivedFor(this,e)&&(this.tearDownDerivedTokenValue(e),h.queueNotification(new d(this,2,e))),i&&(t=new d(this,1,e,this._derived.get(e)?.evaluator.value));const{value:r}=t;if(r&&h.isDerivedTokenValue(r)){const n=l.getOrCreate(r).dependencies;let i=!1;for(const t of n)if(h.isAssigned(this,t)){i=!0;break}if(i){const n=this._derived.get(e)?.value,i=this.setupDerivedTokenValue(e,r);if(n!==i.value){const r=new d(this,void 0===n?0:1,e,i.evaluator.value);h.queueNotification(r),t=r}}}}this.collectLocalChangeRecords(t).forEach((t=>{h.queueNotification(t),this.dispatch(t)})),this.notifyChildren(t)}collectLocalChangeRecords(t){const e=new Map;for(const n of h.getOrCreateDependencyGraph(this,t.token))n.value!==n.update().value&&e.set(n.token,new d(this,1,n.token,n.evaluator.value));return e}notifyChildren(...t){if(this.children.length)for(let e=0,n=this.children.length;e<n;e++)for(let n=0;n<t.length;n++)this.children[e].dispatch(t[n])}tearDownDerivedTokenValue(t){if(h.isDerivedFor(this,t)){const e=this._derived.get(t);e.dispose(),this._derived.delete(t),e.evaluator.dependencies.forEach((t=>{h.getOrCreateDependencyGraph(this,t).delete(e)}))}}setupDerivedTokenValue(t,e,n=!1){const i=new u(t,l.getOrCreate(e),this,n?{handleChange:()=>{if(i.value!==i.update().value){const t=new d(this,1,i.token,i.evaluator.value);h.queueNotification(t),this.dispatch(t),h.notify()}}}:void 0);return this._derived.set(t,i),i.evaluator.dependencies.forEach((e=>{e!==t&&h.getOrCreateDependencyGraph(this,e).add(i)})),i}}h._notifications=[];var f=n(58968),p=n(70885);class g{setProperty(t,e){f.H.enqueue((()=>this.target.setProperty(t,e)))}removeProperty(t){f.H.enqueue((()=>this.target.removeProperty(t)))}}class v extends g{constructor(){super();const t=new CSSStyleSheet;this.target=t.cssRules[t.insertRule(":root{}")].style,document.adoptedStyleSheets=[...document.adoptedStyleSheets,t]}}class m extends g{constructor(){super(),this.style=document.createElement("style"),document.head.appendChild(this.style);const{sheet:t}=this.style;if(t){const e=t.insertRule(":root{}",t.cssRules.length);this.target=t.cssRules[e].style}}}class b{constructor(t){this.store=new Map,this.target=null;const e=t.$fastController;this.style=document.createElement("style"),e.addStyles(this.style),r.y$.getNotifier(e).subscribe(this,"isConnected"),this.handleChange(e,"isConnected")}targetChanged(){if(null!==this.target)for(const[t,e]of this.store.entries())this.target.setProperty(t,e)}setProperty(t,e){this.store.set(t,e),f.H.enqueue((()=>{null!==this.target&&this.target.setProperty(t,e)}))}removeProperty(t){this.store.delete(t),f.H.enqueue((()=>{null!==this.target&&this.target.removeProperty(t)}))}handleChange(t,e){const{sheet:n}=this.style;if(n){const t=n.insertRule(":host{}",n.cssRules.length);this.target=n.cssRules[t].style}else this.target=null}}(0,i.gn)([r.LO,(0,i.w6)("design:type",Object)],b.prototype,"target",void 0);class y{setProperty(t,e){y.properties[t]=e;for(const n of y.roots.values())n.setProperty(t,e)}removeProperty(t){delete y.properties[t];for(const e of y.roots.values())e.removeProperty(t)}static registerRoot(t){const{roots:e}=y;if(!e.has(t)){e.add(t);for(const e in y.properties)t.setProperty(e,y.properties[e])}}static unregisterRoot(t){const{roots:e}=y;if(e.has(t)){e.delete(t);for(const e in y.properties)t.removeProperty(e)}}}y.roots=new Set,y.properties={};const w=new WeakMap,x=p.X.supportsAdoptedStyleSheets?class extends g{constructor(t){super();const e=new CSSStyleSheet;this.target=e.cssRules[e.insertRule(":host{}")].style,t.$fastController.addStyles(new p.X([e]))}}:b,C=Object.freeze({getOrCreate(t){if(w.has(t))return w.get(t);let e;return e=t instanceof Document?p.X.supportsAdoptedStyleSheets?new v:new m:new x(t),w.set(t,e),e}});class k{constructor(t){this.subscriberNotifier={handleChange:(t,e)=>{const n={target:e.target===L.defaultNode||Reflect.get(e.target,T)===T?"default":e.target.target,token:this};this.subscribers.notify(n)}},this.name=t.name,r.y$.getNotifier(this).subscribe(this.subscriberNotifier)}get $value(){return this.default}get default(){return L.defaultOverridesNode.getTokenValue(this)}get subscribers(){return this._subscribers||(this._subscribers=new o.q(this)),this._subscribers}static isCSSDesignTokenConfiguration(t){return"string"==typeof t.cssCustomPropertyName}static create(t,e){let n;return n="string"==typeof t?new S({name:t,cssCustomPropertyName:t}):k.isCSSDesignTokenConfiguration(t)?new S(t):new k(t),void 0!==e&&L.defaultNode.setTokenValue(n,e),n}static withStrategy(t){L.withStrategy(t)}static registerDefaultStyleTarget(t=document){(t instanceof s.H||t instanceof Document)&&(t=C.getOrCreate(t)),y.registerRoot(t)}static unregisterDefaultStyleTarget(t=document){(t instanceof s.H||t instanceof Document)&&(t=C.getOrCreate(t)),y.unregisterRoot(t)}static clear(){L.clear()}getValueFor(t){return L.getOrCreate(t).getTokenValue(this)}setValueFor(t,e){L.getOrCreate(t).setTokenValue(this,this.normalizeValue(e))}deleteValueFor(t){return L.getOrCreate(t).deleteTokenValue(this),this}withDefault(t){return t=this.normalizeValue(t),h.isAssigned(L.defaultNode,this)||L.defaultNode.setTokenValue(this,t),L.defaultOverridesNode.setTokenValue(this,t),this}subscribe(t){this.subscribers.subscribe(t)}unsubscribe(t){this.subscribers.unsubscribe(t)}alias(t){return e=>e(t)}normalizeValue(t){return t instanceof k&&(t=this.alias(t)),t}}let S=class extends k{constructor(t){super(t),this.cssReflector={handleChange:(t,e)=>{const n=e.target===L.defaultNode||Reflect.get(e.target,T)===T?L.rootStyleSheetTarget:e.target instanceof L?C.getOrCreate(e.target.target):null;n&&(2===e.type?n.removeProperty(this.cssCustomProperty):n.setProperty(this.cssCustomProperty,this.resolveCSSValue(e.target.getTokenValue(this))))}},this.cssCustomProperty=`--${t.cssCustomPropertyName}`,this.cssVar=`var(${this.cssCustomProperty})`,r.y$.getNotifier(this).subscribe(this.cssReflector)}createCSS(){return this.cssVar}resolveCSSValue(t){return t&&"function"==typeof t.createCSS?t.createCSS():t}};S=(0,i.gn)([(0,a.k)(),(0,i.w6)("design:paramtypes",[Object])],S);const $={contains:c.MP,parent(t){let e=(0,c.TC)(t);for(;null!==e;){if(e instanceof s.H)return e;e=(0,c.TC)(e)}return null}},T=Symbol();class L extends h{constructor(t){super(),this.target=t,this.setTokenValue=this.lazyAttachToDefault(super.setTokenValue),this.getTokenValue=this.lazyAttachToDefault(this.getTokenValue),this.deleteTokenValue=this.lazyAttachToDefault(super.deleteTokenValue)}static get strategy(){return void 0===this._strategy&&L.withStrategy($),this._strategy}static get defaultOverridesNode(){let t=L.overridesNodes.get(document);return t||(t=new h,L.overridesNodes.set(document,t),L.defaultNode.appendChild(t),Reflect.defineProperty(t,T,{value:T,enumerable:!1,configurable:!1}),t)}static clear(){!function t(e){for(const n of e.children)t(n);e.dispose()}(L.defaultOverridesNode),L.overridesNodes.delete(document)}connectedCallback(t){let e=L.findParent(t.source);if(null===e&&(e=L.defaultOverridesNode),e!==this.parent){const n=[];for(const i of e.children)i instanceof L&&L.strategy.contains(t.source,i.target)&&n.push(i);e.appendChild(this);for(const t of n)this.appendChild(t)}}disconnectedCallback(t){this.dispose()}static getOrCreate(t){let e=L.cache.get(t);return e||(e=new L(t),L.cache.set(t,e),t.$fastController.addBehavior(L.strategy),t.$fastController.addBehavior(e),e)}static withStrategy(t){this._strategy=t}static findParent(t){let e=L.strategy.parent(t);for(;null!==e;){const t=L.cache.get(e);if(t)return t;e=L.strategy.parent(e)}return null}lazyAttachToDefault(t){return(...e)=>(null===this.parent&&L.defaultOverridesNode.appendChild(this),t.apply(this,e))}getTokenValue(t){try{return super.getTokenValue(t)}catch(t){const e=(t?.message??"Unknown error")+`Target node: ${this.target?.tagName}.  IsFastConnected: ${this.target?.$fastController?.isConnected}. IsConnected: ${this.target?.isConnected}`;throw new Error(e)}}}L.overridesNodes=new WeakMap,L.defaultNode=new h,L.rootStyleSheetTarget=new y,L.cache=new WeakMap},36585:function(t,e,n){"use strict";n.d(e,{U:function(){return x}});var i=n(33940),r=n(28904),o=n(58968),s=n(99452),a=n(42590),c=n(94537),l=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"],u=l.join(","),d="undefined"==typeof Element,h=d?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,f=!d&&Element.prototype.getRootNode?function(t){return t.getRootNode()}:function(t){return t.ownerDocument},p=function(t,e){return t.tabIndex<0&&(e||/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||t.isContentEditable)&&isNaN(parseInt(t.getAttribute("tabindex"),10))?0:t.tabIndex},g=function(t){return"INPUT"===t.tagName},v=function(t){return function(t){return g(t)&&"radio"===t.type}(t)&&!function(t){if(!t.name)return!0;var e,n=t.form||f(t),i=function(t){return n.querySelectorAll('input[type="radio"][name="'+t+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)e=i(window.CSS.escape(t.name));else try{e=i(t.name)}catch(t){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",t.message),!1}var r=function(t,e){for(var n=0;n<t.length;n++)if(t[n].checked&&t[n].form===e)return t[n]}(e,t.form);return!r||r===t}(t)},m=function(t){var e=t.getBoundingClientRect(),n=e.width,i=e.height;return 0===n&&0===i},b=function(t,e){return!(e.disabled||function(t){return g(t)&&"hidden"===t.type}(e)||function(t,e){var n=e.displayCheck,i=e.getShadowRoot;if("hidden"===getComputedStyle(t).visibility)return!0;var r=h.call(t,"details>summary:first-of-type")?t.parentElement:t;if(h.call(r,"details:not([open]) *"))return!0;var o=f(t).host,s=(null==o?void 0:o.ownerDocument.contains(o))||t.ownerDocument.contains(t);if(n&&"full"!==n){if("non-zero-area"===n)return m(t)}else{if("function"==typeof i){for(var a=t;t;){var c=t.parentElement,l=f(t);if(c&&!c.shadowRoot&&!0===i(c))return m(t);t=t.assignedSlot?t.assignedSlot:c||l===t.ownerDocument?c:l.host}t=a}if(s)return!t.getClientRects().length}return!1}(e,t)||function(t){return"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some((function(t){return"SUMMARY"===t.tagName}))}(e)||function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var e=t.parentElement;e;){if("FIELDSET"===e.tagName&&e.disabled){for(var n=0;n<e.children.length;n++){var i=e.children.item(n);if("LEGEND"===i.tagName)return!!h.call(e,"fieldset[disabled] *")||!i.contains(t)}return!0}e=e.parentElement}return!1}(e))},y=function(t,e){return!(v(e)||p(e)<0||!b(t,e))},w=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==h.call(t,u)&&y(e,t)};class x extends r.H{constructor(){super(...arguments),this.modal=!1,this.hidden=!1,this.noFocusTrap=!1,this.noFocusTrapChanged=()=>{this.$fastController.isConnected&&this.updateTrapFocus()},this.isTrappingFocus=!1,this.handleDocumentKeydown=t=>{if(!t.defaultPrevented&&!this.hidden)switch(t.key){case c.CX:this.dismiss(),t.preventDefault();break;case c.oM:this.handleTabKeyDown(t)}},this.handleDocumentFocus=t=>{!t.defaultPrevented&&this.shouldForceFocus(t.target)&&(this.focusFirstElement(),t.preventDefault())},this.handleTabKeyDown=t=>{if(this.noFocusTrap||this.hidden)return;const e=this.getTabQueueBounds();return 0!==e.length?1===e.length?(e[0].focus(),void t.preventDefault()):void(t.shiftKey&&t.target===e[0]?(e[e.length-1].focus(),t.preventDefault()):t.shiftKey||t.target!==e[e.length-1]||(e[0].focus(),t.preventDefault())):void 0},this.getTabQueueBounds=()=>x.reduceTabbableItems([],this),this.focusFirstElement=()=>{const t=this.getTabQueueBounds();t.length>0?t[0].focus():this.dialog instanceof HTMLElement&&this.dialog.focus()},this.shouldForceFocus=t=>this.isTrappingFocus&&!this.contains(t),this.shouldTrapFocus=()=>!this.noFocusTrap&&!this.hidden,this.updateTrapFocus=t=>{const e=void 0===t?this.shouldTrapFocus():t;e&&!this.isTrappingFocus?(this.isTrappingFocus=!0,document.addEventListener("focusin",this.handleDocumentFocus),o.H.enqueue((()=>{this.shouldForceFocus(document.activeElement)&&this.focusFirstElement()}))):!e&&this.isTrappingFocus&&(this.isTrappingFocus=!1,document.removeEventListener("focusin",this.handleDocumentFocus))}}dismiss(){this.$emit("dismiss"),this.$emit("cancel")}show(){this.hidden=!1}hide(){this.hidden=!0,this.$emit("close")}connectedCallback(){super.connectedCallback(),document.addEventListener("keydown",this.handleDocumentKeydown),this.notifier=s.y$.getNotifier(this),this.notifier.subscribe(this,"hidden"),this.updateTrapFocus()}disconnectedCallback(){super.disconnectedCallback(),document.removeEventListener("keydown",this.handleDocumentKeydown),this.updateTrapFocus(!1),this.notifier.unsubscribe(this,"hidden")}handleChange(t,e){if("hidden"===e)this.updateTrapFocus()}static reduceTabbableItems(t,e){return"-1"===e.getAttribute("tabindex")?t:w(e)||x.isFocusableFastElement(e)&&x.hasTabbableShadow(e)?(t.push(e),t):e.childElementCount?t.concat(Array.from(e.children).reduce(x.reduceTabbableItems,[])):t}static isFocusableFastElement(t){return!!t.$fastController?.definition.shadowOptions?.delegatesFocus}static hasTabbableShadow(t){return Array.from(t.shadowRoot?.querySelectorAll("*")??[]).some((t=>w(t)))}}(0,i.gn)([(0,a.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],x.prototype,"modal",void 0),(0,i.gn)([(0,a.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],x.prototype,"hidden",void 0),(0,i.gn)([(0,a.Lj)({attribute:"no-focus-trap",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],x.prototype,"noFocusTrap",void 0),(0,i.gn)([(0,a.Lj)({attribute:"aria-describedby"}),(0,i.w6)("design:type",String)],x.prototype,"ariaDescribedby",void 0),(0,i.gn)([(0,a.Lj)({attribute:"aria-labelledby"}),(0,i.w6)("design:type",String)],x.prototype,"ariaLabelledby",void 0),(0,i.gn)([(0,a.Lj)({attribute:"aria-label"}),(0,i.w6)("design:type",String)],x.prototype,"ariaLabel",void 0)},23526:function(t,e,n){"use strict";n.d(e,{Um:function(){return h},V2:function(){return f}});var i=n(89522),r=n(58968),o=n(42590),s=n(99452),a=n(94537);const c="form-associated-proxy",l="ElementInternals",u=l in window&&"setFormValue"in window[l].prototype,d=new WeakMap;function h(t){const e=class extends t{constructor(...t){super(...t),this.dirtyValue=!1,this.disabled=!1,this.proxyEventsToBlock=["change","click"],this.proxyInitialized=!1,this.required=!1,this.initialValue=this.initialValue||"",this.elementInternals||(this.formResetCallback=this.formResetCallback.bind(this))}static get formAssociated(){return u}get validity(){return this.elementInternals?this.elementInternals.validity:this.proxy.validity}get form(){return this.elementInternals?this.elementInternals.form:this.proxy.form}get validationMessage(){return this.elementInternals?this.elementInternals.validationMessage:this.proxy.validationMessage}get willValidate(){return this.elementInternals?this.elementInternals.willValidate:this.proxy.willValidate}get labels(){if(this.elementInternals)return Object.freeze(Array.from(this.elementInternals.labels));if(this.proxy instanceof HTMLElement&&this.proxy.ownerDocument&&this.id){const t=this.proxy.labels,e=Array.from(this.proxy.getRootNode().querySelectorAll(`[for='${this.id}']`)),n=t?e.concat(Array.from(t)):e;return Object.freeze(n)}return i.ow}valueChanged(t,e){this.dirtyValue=!0,this.proxy instanceof HTMLElement&&(this.proxy.value=this.value),this.currentValue=this.value,this.setFormValue(this.value),this.validate()}currentValueChanged(){this.value=this.currentValue}initialValueChanged(t,e){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}disabledChanged(t,e){this.proxy instanceof HTMLElement&&(this.proxy.disabled=this.disabled),r.H.enqueue((()=>this.classList.toggle("disabled",this.disabled)))}nameChanged(t,e){this.proxy instanceof HTMLElement&&(this.proxy.name=this.name)}requiredChanged(t,e){this.proxy instanceof HTMLElement&&(this.proxy.required=this.required),r.H.enqueue((()=>this.classList.toggle("required",this.required))),this.validate()}get elementInternals(){if(!u)return null;let t=d.get(this);return t||(t=this.attachInternals(),d.set(this,t)),t}connectedCallback(){super.connectedCallback(),this.addEventListener("keypress",this._keypressHandler),this.value||(this.value=this.initialValue,this.dirtyValue=!1),this.elementInternals||(this.attachProxy(),this.form&&this.form.addEventListener("reset",this.formResetCallback))}disconnectedCallback(){super.disconnectedCallback(),this.proxyEventsToBlock.forEach((t=>this.proxy.removeEventListener(t,this.stopPropagation))),!this.elementInternals&&this.form&&this.form.removeEventListener("reset",this.formResetCallback)}checkValidity(){return this.elementInternals?this.elementInternals.checkValidity():this.proxy.checkValidity()}reportValidity(){return this.elementInternals?this.elementInternals.reportValidity():this.proxy.reportValidity()}setValidity(t,e,n){this.elementInternals?this.elementInternals.setValidity(t,e,n):"string"==typeof e&&this.proxy.setCustomValidity(e)}formDisabledCallback(t){this.disabled=t}formResetCallback(){this.value=this.initialValue,this.dirtyValue=!1}attachProxy(){this.proxyInitialized||(this.proxyInitialized=!0,this.proxy.style.display="none",this.proxyEventsToBlock.forEach((t=>this.proxy.addEventListener(t,this.stopPropagation))),this.proxy.disabled=this.disabled,this.proxy.required=this.required,"string"==typeof this.name&&(this.proxy.name=this.name),"string"==typeof this.value&&(this.proxy.value=this.value),this.proxy.setAttribute("slot",c),this.proxySlot=document.createElement("slot"),this.proxySlot.setAttribute("name",c)),this.shadowRoot?.appendChild(this.proxySlot),this.appendChild(this.proxy)}detachProxy(){this.removeChild(this.proxy),this.shadowRoot?.removeChild(this.proxySlot)}validate(t){this.proxy instanceof HTMLElement&&this.setValidity(this.proxy.validity,this.proxy.validationMessage,t)}setFormValue(t,e){this.elementInternals&&this.elementInternals.setFormValue(t,e||t)}_keypressHandler(t){if(t.key===a.kL)if(this.form instanceof HTMLFormElement){const t=this.form.querySelector("[type=submit]");t?.click()}}stopPropagation(t){t.stopPropagation()}};return(0,o.Lj)({mode:"boolean"})(e.prototype,"disabled"),(0,o.Lj)({mode:"fromView",attribute:"value"})(e.prototype,"initialValue"),(0,o.Lj)({attribute:"current-value"})(e.prototype,"currentValue"),(0,o.Lj)(e.prototype,"name"),(0,o.Lj)({mode:"boolean"})(e.prototype,"required"),(0,s.LO)(e.prototype,"value"),e}function f(t){class e extends(h(t)){}class n extends e{constructor(...t){super(t),this.dirtyChecked=!1,this.checkedAttribute=!1,this.checked=!1,this.dirtyChecked=!1}checkedAttributeChanged(){this.defaultChecked=this.checkedAttribute}defaultCheckedChanged(){this.dirtyChecked||(this.checked=this.defaultChecked,this.dirtyChecked=!1)}checkedChanged(t,e){this.dirtyChecked||(this.dirtyChecked=!0),this.currentChecked=this.checked,this.updateForm(),this.proxy instanceof HTMLInputElement&&(this.proxy.checked=this.checked),void 0!==t&&this.$emit("change"),this.validate()}currentCheckedChanged(t,e){this.checked=this.currentChecked}updateForm(){const t=this.checked?this.value:null;this.setFormValue(t,t)}connectedCallback(){super.connectedCallback(),this.updateForm()}formResetCallback(){super.formResetCallback(),this.checked=!!this.checkedAttribute,this.dirtyChecked=!1}}return(0,o.Lj)({attribute:"checked",mode:"boolean"})(n.prototype,"checkedAttribute"),(0,o.Lj)({attribute:"current-checked",converter:o.bw})(n.prototype,"currentChecked"),(0,s.LO)(n.prototype,"defaultChecked"),(0,s.LO)(n.prototype,"checked"),n}},65799:function(t,e,n){"use strict";n.d(e,{n:function(){return d}});var i=n(33940),r=n(28904),o=n(58968),s=n(42590),a=n(99452),c=n(31289),l=n(71695);const u="ease-in-out";class d extends r.H{constructor(){super(...arguments),this.framesPerSecond=60,this.updatingItems=!1,this.speed=600,this.easing=u,this.flippersHiddenFromAT=!1,this.scrolling=!1,this.resizeDetector=null}get frameTime(){return 1e3/this.framesPerSecond}scrollingChanged(t,e){if(this.scrollContainer){const t=1==this.scrolling?"scrollstart":"scrollend";this.$emit(t,this.scrollContainer.scrollLeft)}}get isRtl(){return this.scrollItems.length>1&&this.scrollItems[0].offsetLeft>this.scrollItems[1].offsetLeft}connectedCallback(){super.connectedCallback(),this.initializeResizeDetector()}disconnectedCallback(){this.disconnectResizeDetector(),super.disconnectedCallback()}scrollItemsChanged(t,e){e&&!this.updatingItems&&o.H.enqueue((()=>this.setStops()))}disconnectResizeDetector(){this.resizeDetector&&(this.resizeDetector.disconnect(),this.resizeDetector=null)}initializeResizeDetector(){this.disconnectResizeDetector(),this.resizeDetector=new window.ResizeObserver(this.resized.bind(this)),this.resizeDetector.observe(this)}updateScrollStops(){this.updatingItems=!0;const t=this.scrollItems.reduce(((t,e)=>e instanceof HTMLSlotElement?t.concat(e.assignedElements()):(t.push(e),t)),[]);this.scrollItems=t,this.updatingItems=!1}setStops(){this.updateScrollStops();const{scrollContainer:t}=this,{scrollLeft:e}=t,{width:n,left:i}=t.getBoundingClientRect();this.width=n;let r=0,o=this.scrollItems.map(((t,n)=>{const{left:o,width:s}=t.getBoundingClientRect(),a=Math.round(o+e-i),c=Math.round(a+s);return this.isRtl?-c:(r=c,0===n?0:a)})).concat(r);o=this.fixScrollMisalign(o),o.sort(((t,e)=>Math.abs(t)-Math.abs(e))),this.scrollStops=o,this.setFlippers()}validateStops(t=!0){const e=()=>!!this.scrollStops.find((t=>Math.abs(t)>0));return!e()&&t&&this.setStops(),e()}fixScrollMisalign(t){if(this.isRtl&&t.some((t=>t>0))){t.sort(((t,e)=>e-t));const e=t[0];t=t.map((t=>t-e))}return t}setFlippers(){const t=this.scrollContainer.scrollLeft;this.previousFlipperContainer?.classList.toggle("disabled",0===t),this.scrollStops&&this.nextFlipperContainer?.classList.toggle("disabled",this.validateStops(!1)&&Math.abs(t)+this.width>=this.content?.offsetWidth||(this.scrollContainer?.offsetWidth??0)>=(this.content?.offsetWidth??0))}scrollInView(t,e=0,n){if("number"!=typeof t&&t&&(t=this.scrollItems.findIndex((e=>e===t||e.contains(t)))),void 0!==t){n=n??e;const{scrollContainer:i,scrollStops:r,scrollItems:o}=this,{scrollLeft:s}=this.scrollContainer,{width:a}=i.getBoundingClientRect(),c=r[t],{width:l}=o[t].getBoundingClientRect(),u=c+l,d=s+e>c;if(d||s+a-n<u){const t=[...r].sort(((t,e)=>d?e-t:t-e)).find((t=>d?t+e<c:t+a-(n??0)>u))??0;this.scrollToPosition(t)}}}keyupHandler(t){switch(t.key){case"ArrowLeft":this.scrollToPrevious();break;case"ArrowRight":this.scrollToNext()}}scrollToPrevious(){this.validateStops();const t=this.scrollContainer.scrollLeft;let e=0;e=this.isRtl?this.scrollStops.findIndex(((e,n)=>e<=t&&(n===this.scrollStops.length-1||this.scrollStops[n+1]<t))):this.scrollStops.findIndex(((e,n)=>e>=t&&(n===this.scrollStops.length-1||this.scrollStops[n+1]>t)));const n=Math.abs(this.scrollStops[e+1]);let i=this.scrollStops.findIndex((t=>Math.abs(t)+this.width>n));(i>=e||-1===i)&&(i=e>0?e-1:0),this.scrollToPosition(this.scrollStops[i],t)}scrollToNext(){this.validateStops();const t=this.scrollContainer.scrollLeft,e=this.scrollStops.findIndex((e=>Math.abs(e)>=Math.abs(t))),n=this.scrollStops.findIndex((e=>Math.abs(t)+this.width<=Math.abs(e)));let i=e;n>e+2?i=n-2:e<this.scrollStops.length-2&&(i=e+1),this.scrollToPosition(this.scrollStops[i],t)}scrollToPosition(t,e=this.scrollContainer.scrollLeft){if(this.scrolling)return;this.scrolling=!0;const n=this.duration??Math.abs(t-e)/this.speed+"s";this.content.style.setProperty("transition-duration",n);const i=parseFloat(getComputedStyle(this.content).getPropertyValue("transition-duration")),r=e=>{e&&e.target!==e.currentTarget||(this.content.style.setProperty("transition-duration","0s"),this.content.style.removeProperty("transform"),this.scrollContainer.style.setProperty("scroll-behavior","auto"),this.scrollContainer.scrollLeft=t,this.setFlippers(),this.content.removeEventListener("transitionend",r),this.scrolling=!1)};if(0===i)return void r();this.content.addEventListener("transitionend",r);const o=this.scrollContainer.scrollWidth-this.scrollContainer.clientWidth;let s=this.scrollContainer.scrollLeft-Math.min(t,o);this.isRtl&&(s=this.scrollContainer.scrollLeft+Math.min(Math.abs(t),o)),this.content.style.setProperty("transition-property","transform"),this.content.style.setProperty("transition-timing-function",this.easing),this.content.style.setProperty("transform",`translateX(${s}px)`)}resized(){this.resizeTimeout&&(this.resizeTimeout=clearTimeout(this.resizeTimeout)),this.resizeTimeout=setTimeout((()=>{this.width=this.scrollContainer.offsetWidth,this.setFlippers()}),this.frameTime)}scrolled(){this.scrollTimeout&&(this.scrollTimeout=clearTimeout(this.scrollTimeout)),this.scrollTimeout=setTimeout((()=>{this.setFlippers()}),this.frameTime)}getScrollStops(){return this.scrollStops}getWidth(){return this.width}}(0,i.gn)([(0,s.Lj)({converter:s.Id}),(0,i.w6)("design:type",Number)],d.prototype,"speed",void 0),(0,i.gn)([s.Lj,(0,i.w6)("design:type",String)],d.prototype,"duration",void 0),(0,i.gn)([s.Lj,(0,i.w6)("design:type",String)],d.prototype,"easing",void 0),(0,i.gn)([(0,s.Lj)({attribute:"flippers-hidden-from-at",converter:s.bw}),(0,i.w6)("design:type",Boolean)],d.prototype,"flippersHiddenFromAT",void 0),(0,i.gn)([a.LO,(0,i.w6)("design:type",Boolean)],d.prototype,"scrolling",void 0),(0,i.gn)([a.LO,(0,i.w6)("design:type",Array)],d.prototype,"scrollItems",void 0),(0,i.gn)([(0,s.Lj)({attribute:"view"}),(0,i.w6)("design:type",String)],d.prototype,"view",void 0),(0,c.e)(d,l.hW)},3193:function(t,e,n){"use strict";n.d(e,{Qm:function(){return h},v2:function(){return d}});var i=n(33940),r=n(28904),o=n(99452),s=n(42590),a=n(7986),c=n(33714),l=n(71695),u=n(31289);function d(t){return(0,a.Re)(t)&&("option"===t.getAttribute("role")||t instanceof HTMLOptionElement)}class h extends r.H{constructor(t,e,n,i){super(),this.defaultSelected=!1,this.dirtySelected=!1,this.selected=this.defaultSelected,this.dirtyValue=!1,t&&(this.textContent=t),e&&(this.initialValue=e),n&&(this.defaultSelected=n),i&&(this.selected=i),this.proxy=new Option(`${this.textContent}`,this.initialValue,this.defaultSelected,this.selected),this.proxy.disabled=this.disabled}checkedChanged(t,e){this.ariaChecked="boolean"!=typeof e?null:e?"true":"false"}contentChanged(t,e){this.proxy instanceof HTMLOptionElement&&(this.proxy.textContent=this.textContent),this.$emit("contentchange",null,{bubbles:!0})}defaultSelectedChanged(){this.dirtySelected||(this.selected=this.defaultSelected,this.proxy instanceof HTMLOptionElement&&(this.proxy.selected=this.defaultSelected))}disabledChanged(t,e){this.ariaDisabled=this.disabled?"true":"false",this.proxy instanceof HTMLOptionElement&&(this.proxy.disabled=this.disabled)}selectedAttributeChanged(){this.defaultSelected=this.selectedAttribute,this.proxy instanceof HTMLOptionElement&&(this.proxy.defaultSelected=this.defaultSelected)}selectedChanged(){this.ariaSelected=this.selected?"true":"false",this.dirtySelected||(this.dirtySelected=!0),this.proxy instanceof HTMLOptionElement&&(this.proxy.selected=this.selected)}initialValueChanged(t,e){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}get label(){return this.value??this.text}get text(){return this.textContent?.replace(/\s+/g," ").trim()??""}set value(t){const e=`${t??""}`;this._value=e,this.dirtyValue=!0,this.proxy instanceof HTMLOptionElement&&(this.proxy.value=e),o.y$.notify(this,"value")}get value(){return o.y$.track(this,"value"),this._value??this.text}get form(){return this.proxy?this.proxy.form:null}}(0,i.gn)([o.LO,(0,i.w6)("design:type",Boolean)],h.prototype,"checked",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Array)],h.prototype,"content",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Boolean)],h.prototype,"defaultSelected",void 0),(0,i.gn)([(0,s.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],h.prototype,"disabled",void 0),(0,i.gn)([(0,s.Lj)({attribute:"selected",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],h.prototype,"selectedAttribute",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Boolean)],h.prototype,"selected",void 0),(0,i.gn)([(0,s.Lj)({attribute:"value",mode:"fromView"}),(0,i.w6)("design:type",String)],h.prototype,"initialValue",void 0);class f{}(0,i.gn)([o.LO,(0,i.w6)("design:type",Object)],f.prototype,"ariaChecked",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Object)],f.prototype,"ariaPosInSet",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Object)],f.prototype,"ariaSelected",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Object)],f.prototype,"ariaSetSize",void 0),(0,u.e)(f,c.v),(0,u.e)(h,l.hW,f)},36393:function(t,e,n){"use strict";n.d(e,{T:function(){return s}});var i=n(49218),r=n(47548),o=n(71695);function s(t={}){return i.dy`
        <template
            aria-checked="${t=>t.ariaChecked}"
            aria-disabled="${t=>t.ariaDisabled}"
            aria-posinset="${t=>t.ariaPosInSet}"
            aria-selected="${t=>t.ariaSelected}"
            aria-setsize="${t=>t.ariaSetSize}"
            role="option"
        >
            ${(0,o.m9)(t)}
            <span class="content" part="content">
                <slot ${(0,r.Q)("content")}></slot>
            </span>
            ${(0,o.LC)(t)}
        </template>
    `}},13031:function(t,e,n){"use strict";n.d(e,{b:function(){return u}});var i=n(33940),r=n(58968),o=n(99452),s=n(42590),a=n(97108),c=n(94537),l=n(98296);class u extends l.z{constructor(){super(...arguments),this.activeIndex=-1,this.rangeStartIndex=-1}get activeOption(){return this.options[this.activeIndex]}get checkedOptions(){return this.options?.filter((t=>t.checked))}get firstSelectedOptionIndex(){return this.options.indexOf(this.firstSelectedOption)}activeIndexChanged(t,e){this.ariaActiveDescendant=this.options[e]?.id??"",this.focusAndScrollOptionIntoView()}checkActiveIndex(){if(!this.multiple)return;const t=this.activeOption;t&&(t.checked=!0)}checkFirstOption(t=!1){t?(-1===this.rangeStartIndex&&(this.rangeStartIndex=this.activeIndex+1),this.options.forEach(((t,e)=>{t.checked=(0,a.Z2)(e,this.rangeStartIndex)}))):this.uncheckAllOptions(),this.activeIndex=0,this.checkActiveIndex()}checkLastOption(t=!1){t?(-1===this.rangeStartIndex&&(this.rangeStartIndex=this.activeIndex),this.options.forEach(((t,e)=>{t.checked=(0,a.Z2)(e,this.rangeStartIndex,this.options.length)}))):this.uncheckAllOptions(),this.activeIndex=this.options.length-1,this.checkActiveIndex()}connectedCallback(){super.connectedCallback(),this.addEventListener("focusout",this.focusoutHandler)}disconnectedCallback(){this.removeEventListener("focusout",this.focusoutHandler),super.disconnectedCallback()}checkNextOption(t=!1){t?(-1===this.rangeStartIndex&&(this.rangeStartIndex=this.activeIndex),this.options.forEach(((t,e)=>{t.checked=(0,a.Z2)(e,this.rangeStartIndex,this.activeIndex+1)}))):this.uncheckAllOptions(),this.activeIndex+=this.activeIndex<this.options.length-1?1:0,this.checkActiveIndex()}checkPreviousOption(t=!1){t?(-1===this.rangeStartIndex&&(this.rangeStartIndex=this.activeIndex),1===this.checkedOptions.length&&(this.rangeStartIndex+=1),this.options.forEach(((t,e)=>{t.checked=(0,a.Z2)(e,this.activeIndex,this.rangeStartIndex)}))):this.uncheckAllOptions(),this.activeIndex-=this.activeIndex>0?1:0,this.checkActiveIndex()}clickHandler(t){if(!this.multiple)return super.clickHandler(t);const e=t.target?.closest("[role=option]");return e&&!e.disabled?(this.uncheckAllOptions(),this.activeIndex=this.options.indexOf(e),this.checkActiveIndex(),this.toggleSelectedForAllCheckedOptions(),!0):void 0}focusAndScrollOptionIntoView(){super.focusAndScrollOptionIntoView(this.activeOption)}focusinHandler(t){if(!this.multiple)return super.focusinHandler(t);this.shouldSkipFocus||t.target!==t.currentTarget||(this.uncheckAllOptions(),-1===this.activeIndex&&(this.activeIndex=-1!==this.firstSelectedOptionIndex?this.firstSelectedOptionIndex:0),this.checkActiveIndex(),this.setSelectedOptions(),this.focusAndScrollOptionIntoView()),this.shouldSkipFocus=!1}focusoutHandler(t){this.multiple&&this.uncheckAllOptions()}keydownHandler(t){if(!this.multiple)return super.keydownHandler(t);if(this.disabled)return!0;const{key:e,shiftKey:n}=t;switch(this.shouldSkipFocus=!1,e){case c.tU:return void this.checkFirstOption(n);case c.iF:return void this.checkNextOption(n);case c.SB:return void this.checkPreviousOption(n);case c.Kh:return void this.checkLastOption(n);case c.oM:return this.focusAndScrollOptionIntoView(),!0;case c.CX:return this.uncheckAllOptions(),this.checkActiveIndex(),!0;case c.BI:if(t.preventDefault(),this.typeAheadExpired)return void this.toggleSelectedForAllCheckedOptions();default:return 1===e.length&&this.handleTypeAhead(`${e}`),!0}}mousedownHandler(t){if(t.offsetX>=0&&t.offsetX<=this.scrollWidth)return super.mousedownHandler(t)}multipleChanged(t,e){this.ariaMultiSelectable=e?"true":null,this.options?.forEach((t=>{t.checked=!e&&void 0})),this.setSelectedOptions()}setSelectedOptions(){this.multiple?this.$fastController.isConnected&&this.options&&(this.selectedOptions=this.options.filter((t=>t.selected)),this.focusAndScrollOptionIntoView()):super.setSelectedOptions()}sizeChanged(t,e){const n=Math.max(0,parseInt(e?.toFixed()??"",10));n!==e&&r.H.enqueue((()=>{this.size=n}))}toggleSelectedForAllCheckedOptions(){const t=this.checkedOptions.filter((t=>!t.disabled)),e=!t.every((t=>t.selected));t.forEach((t=>t.selected=e)),this.selectedIndex=this.options.indexOf(t[t.length-1]),this.setSelectedOptions()}typeaheadBufferChanged(t,e){if(this.multiple){if(this.$fastController.isConnected){const t=this.getTypeaheadMatches(),e=this.options.indexOf(t[0]);e>-1&&(this.activeIndex=e,this.uncheckAllOptions(),this.checkActiveIndex()),this.typeAheadExpired=!1}}else super.typeaheadBufferChanged(t,e)}uncheckAllOptions(t=!1){this.options.forEach((t=>t.checked=!this.multiple&&void 0)),t||(this.rangeStartIndex=-1)}}(0,i.gn)([o.LO,(0,i.w6)("design:type",Number)],u.prototype,"activeIndex",void 0),(0,i.gn)([(0,s.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],u.prototype,"multiple",void 0),(0,i.gn)([(0,s.Lj)({converter:s.Id}),(0,i.w6)("design:type",Number)],u.prototype,"size",void 0)},98296:function(t,e,n){"use strict";n.d(e,{x:function(){return f},z:function(){return h}});var i=n(33940),r=n(28904),o=n(99452),s=n(42590),a=n(94537);var c=n(62512),l=n(3193),u=n(33714),d=n(31289);class h extends r.H{constructor(){super(...arguments),this._options=[],this.selectedIndex=-1,this.selectedOptions=[],this.shouldSkipFocus=!1,this.typeaheadBuffer="",this.typeaheadExpired=!0,this.typeaheadTimeout=-1}get firstSelectedOption(){return this.selectedOptions[0]??null}get hasSelectableOptions(){return this.options.length>0&&!this.options.every((t=>t.disabled))}get length(){return this.options?.length??0}get options(){return o.y$.track(this,"options"),this._options}set options(t){this._options=t,o.y$.notify(this,"options")}get typeAheadExpired(){return this.typeaheadExpired}set typeAheadExpired(t){this.typeaheadExpired=t}clickHandler(t){const e=t.target.closest("option,[role=option]");if(e&&!e.disabled)return this.selectedIndex=this.options.indexOf(e),!0}focusAndScrollOptionIntoView(t=this.firstSelectedOption){this.contains(document.activeElement)&&null!==t&&(t.focus(),requestAnimationFrame((()=>{t.scrollIntoView({block:"nearest"})})))}focusinHandler(t){this.shouldSkipFocus||t.target!==t.currentTarget||(this.setSelectedOptions(),this.focusAndScrollOptionIntoView()),this.shouldSkipFocus=!1}getTypeaheadMatches(){const t=this.typeaheadBuffer.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&"),e=new RegExp(`^${t}`,"gi");return this.options.filter((t=>t.text.trim().match(e)))}getSelectableIndex(t=this.selectedIndex,e){const n=t>e?-1:t<e?1:0,i=t+n;let r=null;switch(n){case-1:r=this.options.reduceRight(((t,e,n)=>!t&&!e.disabled&&n<i?e:t),r);break;case 1:r=this.options.reduce(((t,e,n)=>!t&&!e.disabled&&n>i?e:t),r)}return this.options.indexOf(r)}handleChange(t,e){if("selected"===e)h.slottedOptionFilter(t)&&(this.selectedIndex=this.options.indexOf(t)),this.setSelectedOptions()}handleTypeAhead(t){this.typeaheadTimeout&&window.clearTimeout(this.typeaheadTimeout),this.typeaheadTimeout=window.setTimeout((()=>this.typeaheadExpired=!0),h.TYPE_AHEAD_TIMEOUT_MS),t.length>1||(this.typeaheadBuffer=`${this.typeaheadExpired?"":this.typeaheadBuffer}${t}`)}keydownHandler(t){if(this.disabled)return!0;this.shouldSkipFocus=!1;const e=t.key;switch(e){case a.tU:t.shiftKey||(t.preventDefault(),this.selectFirstOption());break;case a.iF:t.shiftKey||(t.preventDefault(),this.selectNextOption());break;case a.SB:t.shiftKey||(t.preventDefault(),this.selectPreviousOption());break;case a.Kh:t.preventDefault(),this.selectLastOption();break;case a.oM:return this.focusAndScrollOptionIntoView(),!0;case a.kL:case a.CX:return!0;case a.BI:if(this.typeaheadExpired)return!0;default:return 1===e.length&&this.handleTypeAhead(`${e}`),!0}}mousedownHandler(t){return this.shouldSkipFocus=!this.contains(document.activeElement),!0}multipleChanged(t,e){this.ariaMultiSelectable=e?"true":null}selectedIndexChanged(t,e){if(this.hasSelectableOptions){if(this.options[this.selectedIndex]?.disabled&&"number"==typeof t){const n=this.getSelectableIndex(t,e),i=n>-1?n:t;return this.selectedIndex=i,void(e===i&&this.selectedIndexChanged(e,i))}this.setSelectedOptions()}else this.selectedIndex=-1}selectedOptionsChanged(t,e){const n=e.filter(h.slottedOptionFilter);this.options?.forEach((t=>{const e=o.y$.getNotifier(t);e.unsubscribe(this,"selected"),t.selected=n.includes(t),e.subscribe(this,"selected")}))}selectFirstOption(){this.disabled||(this.selectedIndex=this.options?.findIndex((t=>!t.disabled))??-1)}selectLastOption(){this.disabled||(this.selectedIndex=function(t,e){let n=t.length;for(;n--;)if(e(t[n],n,t))return n;return-1}(this.options,(t=>!t.disabled)))}selectNextOption(){!this.disabled&&this.selectedIndex<this.options.length-1&&(this.selectedIndex+=1)}selectPreviousOption(){!this.disabled&&this.selectedIndex>0&&(this.selectedIndex=this.selectedIndex-1)}setDefaultSelectedOption(){this.selectedIndex=this.options?.findIndex((t=>t.defaultSelected))??-1}setSelectedOptions(){this.options?.length&&(this.selectedOptions=[this.options[this.selectedIndex]],this.ariaActiveDescendant=this.firstSelectedOption?.id??"",this.focusAndScrollOptionIntoView())}slottedOptionsChanged(t,e){this.options=e.reduce(((t,e)=>((0,l.v2)(e)&&t.push(e),t)),[]);const n=`${this.options.length}`;this.options.forEach(((t,e)=>{t.id||(t.id=(0,c.EL)("option-")),t.ariaPosInSet=`${e+1}`,t.ariaSetSize=n})),this.$fastController.isConnected&&(this.setSelectedOptions(),this.setDefaultSelectedOption())}typeaheadBufferChanged(t,e){if(this.$fastController.isConnected){const t=this.getTypeaheadMatches();if(t.length){const e=this.options.indexOf(t[0]);e>-1&&(this.selectedIndex=e)}this.typeaheadExpired=!1}}}h.slottedOptionFilter=t=>(0,l.v2)(t)&&!t.hidden,h.TYPE_AHEAD_TIMEOUT_MS=1e3,(0,i.gn)([(0,s.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],h.prototype,"disabled",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Number)],h.prototype,"selectedIndex",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Array)],h.prototype,"selectedOptions",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Array)],h.prototype,"slottedOptions",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",String)],h.prototype,"typeaheadBuffer",void 0);class f{}(0,i.gn)([o.LO,(0,i.w6)("design:type",Object)],f.prototype,"ariaActiveDescendant",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Object)],f.prototype,"ariaDisabled",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Object)],f.prototype,"ariaExpanded",void 0),(0,i.gn)([o.LO,(0,i.w6)("design:type",Object)],f.prototype,"ariaMultiSelectable",void 0),(0,d.e)(f,u.v),(0,d.e)(h,f)},41034:function(t,e,n){"use strict";n.d(e,{WN:function(){return f}});var i=n(33940),r=n(55090),o=n(28904),s=n(58968),a=n(42590),c=n(99452),l=n(94537),u=n(71695),d=n(31289),h=n(12695);class f extends o.H{constructor(){super(...arguments),this.role=h.O.menuitem,this.focusSubmenuOnLoad=!1,this.handleMenuItemKeyDown=t=>{if(t.defaultPrevented)return!1;switch(t.key){case l.kL:case l.BI:return this.invoke(),!1;case l.mr:return this.expandAndFocus(),!1;case l.CX:case l.BE:if(this.expanded)return this.closeSubMenu(),!1}return!0},this.handleMenuItemClick=t=>(t.defaultPrevented||this.disabled||this.invoke(),!1),this.submenuLoaded=()=>{this.focusSubmenuOnLoad&&(this.focusSubmenuOnLoad=!1,this.submenu&&(this.submenu.focus(),this.setAttribute("tabindex","-1")))},this.handleMouseOver=t=>(this.disabled||!this.hasSubmenu||this.expanded||(this.expanded=!0),!1),this.handleMouseOut=t=>(!this.expanded||this.contains(document.activeElement)||(this.expanded=!1),!1),this.closeSubMenu=()=>{this.expanded=!1,this.focus()},this.expandAndFocus=()=>{this.hasSubmenu&&(this.focusSubmenuOnLoad=!0,this.expanded=!0)},this.invoke=()=>{if(!this.disabled)switch(this.role){case h.O.menuitemcheckbox:this.checked=!this.checked;break;case h.O.menuitem:if(this.hasSubmenu){this.expandAndFocus();break}this.$emit("change");break;case h.O.menuitemradio:this.checked||(this.checked=!0)}}}expandedChanged(t,e){this.$fastController.isConnected&&(e&&this.submenu&&this.updateSubmenu(),this.$emit("expanded-change",this,{bubbles:!1}))}checkedChanged(t,e){this.$fastController.isConnected&&this.$emit("change")}get hasSubmenu(){return!!this.submenu}slottedSubmenuChanged(t,e){e.length&&(this.submenu=e[0],this.updateSubmenu())}disconnectedCallback(){this.cleanup?.(),super.disconnectedCallback()}updateSubmenu(){this.cleanup?.(),this.submenu&&this.expanded&&s.H.enqueue((()=>{this.cleanup=(0,r.Me)(this,this.submenuContainer,(async()=>{const t=["left-start","right-start"],{x:e,y:n}=await(0,r.oo)(this,this.submenuContainer,{middleware:[(0,r.uY)(),(0,r.dp)({apply:({availableWidth:e,rects:n})=>{e<n.floating.width&&t.push("bottom-end","top-end")}}),(0,r.RR)({fallbackPlacements:t})],placement:"right-start",strategy:"fixed"});Object.assign(this.submenuContainer.style,{left:`${e}px`,position:"fixed",top:`${n}px`}),this.submenuLoaded()}))}))}}(0,i.gn)([(0,a.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],f.prototype,"disabled",void 0),(0,i.gn)([(0,a.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],f.prototype,"expanded",void 0),(0,i.gn)([a.Lj,(0,i.w6)("design:type",String)],f.prototype,"role",void 0),(0,i.gn)([(0,a.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],f.prototype,"checked",void 0),(0,i.gn)([(0,a.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],f.prototype,"hidden",void 0),(0,i.gn)([c.LO,(0,i.w6)("design:type",Array)],f.prototype,"slottedSubmenu",void 0),(0,i.gn)([c.LO,(0,i.w6)("design:type",Object)],f.prototype,"submenu",void 0),(0,d.e)(f,u.hW)},12695:function(t,e,n){"use strict";n.d(e,{O:function(){return i}});const i={menuitem:"menuitem",menuitemcheckbox:"menuitemcheckbox",menuitemradio:"menuitemradio"};i.menuitem,i.menuitemcheckbox,i.menuitemradio},33714:function(t,e,n){"use strict";n.d(e,{v:function(){return o}});var i=n(33940),r=n(42590);class o{}(0,i.gn)([(0,r.Lj)({attribute:"aria-atomic"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaAtomic",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-busy"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaBusy",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-controls"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaControls",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-current"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaCurrent",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-describedby"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaDescribedby",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-details"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaDetails",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-disabled"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaDisabled",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-errormessage"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaErrormessage",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-flowto"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaFlowto",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-haspopup"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaHaspopup",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-hidden"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaHidden",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-invalid"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaInvalid",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-keyshortcuts"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaKeyshortcuts",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-label"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaLabel",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-labelledby"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaLabelledby",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-live"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaLive",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-owns"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaOwns",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-relevant"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaRelevant",void 0),(0,i.gn)([(0,r.Lj)({attribute:"aria-roledescription"}),(0,i.w6)("design:type",Object)],o.prototype,"ariaRoledescription",void 0)},71695:function(t,e,n){"use strict";n.d(e,{LC:function(){return a},hW:function(){return s},m9:function(){return c}});var i=n(49218),r=n(41472),o=n(17503);class s{}function a(t){return i.dy`
        <slot name="end" ${(0,r.i)("end")}>${(0,o.A)(t.end)}</slot>
    `.inline()}function c(t){return i.dy`
        <slot name="start" ${(0,r.i)("start")}>${(0,o.A)(t.start)}</slot>
    `.inline()}},3923:function(t,e,n){"use strict";n.d(e,{U:function(){return u}});var i=n(49218),r=n(93703),o=n(41472),s=n(47548),a=n(98296),c=n(71695),l=n(17503);function u(t={}){return i.dy`
        <template
            aria-activedescendant="${t=>t.ariaActiveDescendant}"
            aria-controls="${t=>t.ariaControls}"
            aria-disabled="${t=>t.ariaDisabled}"
            aria-expanded="${t=>t.ariaExpanded}"
            aria-haspopup="${t=>t.collapsible?"listbox":null}"
            aria-multiselectable="${t=>t.ariaMultiSelectable}"
            ?open="${t=>t.open}"
            role="combobox"
            tabindex="${t=>t.disabled?null:"0"}"
            @click="${(t,e)=>t.clickHandler(e.event)}"
            @focusin="${(t,e)=>t.focusinHandler(e.event)}"
            @focusout="${(t,e)=>t.focusoutHandler(e.event)}"
            @keydown="${(t,e)=>t.keydownHandler(e.event)}"
            @mousedown="${(t,e)=>t.mousedownHandler(e.event)}"
        >
            ${(0,r.g)((t=>t.collapsible),i.dy`
                    <div
                        class="control"
                        part="control"
                        ?disabled="${t=>t.disabled}"
                        ${(0,o.i)("control")}
                    >
                        ${(0,c.m9)(t)}
                        <slot name="button-container">
                            <div class="selected-value" part="selected-value">
                                <slot name="selected-value">${t=>t.displayValue}</slot>
                            </div>
                            <div aria-hidden="true" class="indicator" part="indicator">
                                <slot name="indicator">
                                    ${(0,l.A)(t.indicator)}
                                </slot>
                            </div>
                        </slot>
                        ${(0,c.LC)(t)}
                    </div>
                `)}
            <div
                class="listbox"
                id="${t=>t.listboxId}"
                part="listbox"
                role="listbox"
                ?disabled="${t=>t.disabled}"
                ?hidden="${t=>!!t.collapsible&&!t.open}"
                ${(0,o.i)("listbox")}
            >
                <slot
                    ${(0,s.Q)({filter:a.z.slottedOptionFilter,flatten:!0,property:"slottedOptions"})}
                ></slot>
            </div>
        </template>
    `}},38495:function(t,e,n){"use strict";n.d(e,{yd:function(){return v},OI:function(){return g}});var i=n(33940),r=n(58968),o=n(42590),s=n(99452),a=n(33714),c=n(71695),l=n(31289),u=n(28904),d=n(23526);class h extends u.H{}class f extends((0,d.Um)(h)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}const p="text";class g extends f{constructor(){super(...arguments),this.type=p}readOnlyChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.readOnly=this.readOnly,this.validate())}autofocusChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.autofocus=this.autofocus,this.validate())}placeholderChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.placeholder=this.placeholder)}typeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.type=this.type,this.validate())}listChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.setAttribute("list",this.list),this.validate())}maxlengthChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.maxLength=this.maxlength,this.validate())}minlengthChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.minLength=this.minlength,this.validate())}patternChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.pattern=this.pattern,this.validate())}sizeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.size=this.size)}spellcheckChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.spellcheck=this.spellcheck)}connectedCallback(){super.connectedCallback(),this.proxy.setAttribute("type",this.type),this.validate(),this.autofocus&&r.H.enqueue((()=>{this.focus()}))}select(){this.control.select(),this.$emit("select")}handleTextInput(){this.value=this.control.value}handleChange(){this.$emit("change")}validate(){super.validate(this.control)}}(0,i.gn)([(0,o.Lj)({attribute:"readonly",mode:"boolean"}),(0,i.w6)("design:type",Boolean)],g.prototype,"readOnly",void 0),(0,i.gn)([(0,o.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],g.prototype,"autofocus",void 0),(0,i.gn)([o.Lj,(0,i.w6)("design:type",String)],g.prototype,"placeholder",void 0),(0,i.gn)([o.Lj,(0,i.w6)("design:type",String)],g.prototype,"type",void 0),(0,i.gn)([o.Lj,(0,i.w6)("design:type",String)],g.prototype,"list",void 0),(0,i.gn)([(0,o.Lj)({converter:o.Id}),(0,i.w6)("design:type",Number)],g.prototype,"maxlength",void 0),(0,i.gn)([(0,o.Lj)({converter:o.Id}),(0,i.w6)("design:type",Number)],g.prototype,"minlength",void 0),(0,i.gn)([o.Lj,(0,i.w6)("design:type",String)],g.prototype,"pattern",void 0),(0,i.gn)([(0,o.Lj)({converter:o.Id}),(0,i.w6)("design:type",Number)],g.prototype,"size",void 0),(0,i.gn)([(0,o.Lj)({mode:"boolean"}),(0,i.w6)("design:type",Boolean)],g.prototype,"spellcheck",void 0),(0,i.gn)([s.LO,(0,i.w6)("design:type",Array)],g.prototype,"defaultSlottedNodes",void 0);class v{}(0,l.e)(v,a.v),(0,l.e)(g,c.hW,v)},31289:function(t,e,n){"use strict";n.d(e,{e:function(){return r}});var i=n(42590);function r(t,...e){const n=i.Ax.locate(t);e.forEach((e=>{Object.getOwnPropertyNames(e.prototype).forEach((n=>{"constructor"!==n&&Object.defineProperty(t.prototype,n,Object.getOwnPropertyDescriptor(e.prototype,n))}));i.Ax.locate(e).forEach((t=>n.push(t)))}))}},5977:function(t,e,n){"use strict";n.d(e,{M:function(){return r}});var i=n(59997);const r=t=>"rtl"===t.closest("[dir]")?.dir?i.N.rtl:i.N.ltr},29717:function(t,e,n){"use strict";n.d(e,{KJ:function(){return r},Uu:function(){return s},vF:function(){return o},zw:function(){return a}});class i{constructor(t){this.listenerCache=new WeakMap,this.query=t}connectedCallback(t){const{query:e}=this;let n=this.listenerCache.get(t);n||(n=this.constructListener(t),this.listenerCache.set(t,n)),n.bind(e)(),e.addEventListener("change",n)}disconnectedCallback(t){const e=this.listenerCache.get(t);e&&this.query.removeEventListener("change",e)}}class r extends i{constructor(t,e){super(t),this.styles=e}static with(t){return e=>new r(t,e)}constructListener(t){let e=!1;const n=this.styles;return function(){const{matches:i}=this;i&&!e?(t.addStyles(n),e=i):!i&&e&&(t.removeStyles(n),e=i)}}removedCallback(t){t.removeStyles(this.styles)}}const o=r.with(window.matchMedia("(forced-colors)")),s=r.with(window.matchMedia("(prefers-color-scheme: dark)")),a=r.with(window.matchMedia("(prefers-color-scheme: light)"))},81239:function(t,e,n){"use strict";n.d(e,{w:function(){return r}});var i=n(99452);class r{constructor(t,e,n){this.propertyName=t,this.value=e,this.styles=n}addedCallback(t){i.y$.getNotifier(t.source).subscribe(this,this.propertyName),this.handleChange(t.source,this.propertyName)}removedCallback(t){i.y$.getNotifier(t.source).unsubscribe(this,this.propertyName),t.removeStyles(this.styles)}handleChange(t,e){const n=t.$fastController;t[e]===this.value?n.addStyles(this.styles):n.removeStyles(this.styles)}}},24484:function(t,e,n){"use strict";n.d(e,{H:function(){return i}});const i="not-allowed"},27186:function(t,e,n){"use strict";n.d(e,{j:function(){return r}});const i=":host([hidden]){display:none}";function r(t){return`${i}:host{display:${t}}`}},67739:function(t,e,n){"use strict";n.d(e,{b:function(){return i}});const i="focus-visible"},17503:function(t,e,n){"use strict";n.d(e,{A:function(){return r}});var i=n(49218);function r(t){return t?"string"==typeof t?new i.Sy(t):"inline"in t?t.inline():t:i.Sy.empty}},86755:function(t,e,n){"use strict";n.d(e,{E:function(){return i}});const i=t=>t.nodeType!==Node.TEXT_NODE||!!t.nodeValue?.trim().length},60279:function(t,e,n){"use strict";n.d(e,{$2:function(){return C},DR:function(){return x},KW:function(){return k},T8:function(){return v},hM:function(){return d},hP:function(){return g},iI:function(){return m},lw:function(){return p},rD:function(){return y},rp:function(){return u},v1:function(){return w},wo:function(){return f},zP:function(){return b}});var i=n(9366),r=n(38316),o=n(447),s=n(83343),a=n(2696),c=n(40272),l=n(82917);function u(t){return.2126*t.r+.7152*t.g+.0722*t.b}function d(t){function e(t){return t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4)}return u(new a.h(e(t.r),e(t.g),e(t.b),1))}const h=(t,e)=>(t+.05)/(e+.05);function f(t,e){const n=d(t),i=d(e);return n>i?h(n,i):h(i,n)}function p(t){const e=Math.max(t.r,t.g,t.b),n=Math.min(t.r,t.g,t.b),r=e-n;let o=0;0!==r&&(o=e===t.r?(t.g-t.b)/r%6*60:e===t.g?60*((t.b-t.r)/r+2):60*((t.r-t.g)/r+4)),o<0&&(o+=360);const s=(e+n)/2;let a=0;return 0!==r&&(a=r/(1-Math.abs(2*s-1))),new i.H(o,a,s)}function g(t,e=1){const n=(1-Math.abs(2*t.l-1))*t.s,i=n*(1-Math.abs(t.h/60%2-1)),r=t.l-n/2;let o=0,s=0,c=0;return t.h<60?(o=n,s=i,c=0):t.h<120?(o=i,s=n,c=0):t.h<180?(o=0,s=n,c=i):t.h<240?(o=0,s=i,c=n):t.h<300?(o=i,s=0,c=n):t.h<360&&(o=n,s=0,c=i),new a.h(o+r,s+r,c+r,e)}function v(t){const e=Math.max(t.r,t.g,t.b),n=e-Math.min(t.r,t.g,t.b);let i=0;0!==n&&(i=e===t.r?(t.g-t.b)/n%6*60:e===t.g?60*((t.b-t.r)/n+2):60*((t.r-t.g)/n+4)),i<0&&(i+=360);let o=0;return 0!==e&&(o=n/e),new r.T(i,o,e)}function m(t,e=1){const n=t.s*t.v,i=n*(1-Math.abs(t.h/60%2-1)),r=t.v-n;let o=0,s=0,c=0;return t.h<60?(o=n,s=i,c=0):t.h<120?(o=i,s=n,c=0):t.h<180?(o=0,s=n,c=i):t.h<240?(o=0,s=i,c=n):t.h<300?(o=i,s=0,c=n):t.h<360&&(o=n,s=0,c=i),new a.h(o+r,s+r,c+r,e)}function b(t){function e(t){return t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}const n=e(t.r),i=e(t.g),r=e(t.b),o=.4124564*n+.3575761*i+.1804375*r,s=.2126729*n+.7151522*i+.072175*r,a=.0193339*n+.119192*i+.9503041*r;return new c.x(o,s,a)}function y(t,e=1){function n(t){return t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055}const i=n(3.2404542*t.x-1.5371385*t.y-.4985314*t.z),r=n(-.969266*t.x+1.8760108*t.y+.041556*t.z),o=n(.0556434*t.x-.2040259*t.y+1.0572252*t.z);return new a.h(i,r,o,e)}function w(t){return function(t){function e(t){return t>o.R.epsilon?Math.pow(t,1/3):(o.R.kappa*t+16)/116}const n=e(t.x/c.x.whitePoint.x),i=e(t.y/c.x.whitePoint.y),r=116*i-16,s=500*(n-i),a=200*(i-e(t.z/c.x.whitePoint.z));return new o.R(r,s,a)}(b(t))}function x(t,e=1){return y(function(t){const e=(t.l+16)/116,n=e+t.a/500,i=e-t.b/200,r=Math.pow(n,3),s=Math.pow(e,3),a=Math.pow(i,3);let l=0;l=r>o.R.epsilon?r:(116*n-16)/o.R.kappa;let u=0;u=t.l>o.R.epsilon*o.R.kappa?s:t.l/o.R.kappa;let d=0;return d=a>o.R.epsilon?a:(116*i-16)/o.R.kappa,l=c.x.whitePoint.x*l,u=c.x.whitePoint.y*u,d=c.x.whitePoint.z*d,new c.x(l,u,d)}(t),e)}function C(t){return function(t){let e=0;(Math.abs(t.b)>.001||Math.abs(t.a)>.001)&&(e=(0,l.vi)(Math.atan2(t.b,t.a))),e<0&&(e+=360);const n=Math.sqrt(t.a*t.a+t.b*t.b);return new s.t(t.l,n,e)}(w(t))}function k(t,e=1){return x(function(t){let e=0,n=0;return 0!==t.h&&(e=Math.cos((0,l.Ht)(t.h))*t.c,n=Math.sin((0,l.Ht)(t.h))*t.c),new o.R(t.l,e,n)}(t),e)}},9366:function(t,e,n){"use strict";n.d(e,{H:function(){return r}});var i=n(82917);class r{constructor(t,e,n){this.h=t,this.s=e,this.l=n}static fromObject(t){return!t||isNaN(t.h)||isNaN(t.s)||isNaN(t.l)?null:new r(t.h,t.s,t.l)}equalValue(t){return this.h===t.h&&this.s===t.s&&this.l===t.l}roundToPrecision(t){return new r((0,i.fZ)(this.h,t),(0,i.fZ)(this.s,t),(0,i.fZ)(this.l,t))}toObject(){return{h:this.h,s:this.s,l:this.l}}}},38316:function(t,e,n){"use strict";n.d(e,{T:function(){return r}});var i=n(82917);class r{constructor(t,e,n){this.h=t,this.s=e,this.v=n}static fromObject(t){return!t||isNaN(t.h)||isNaN(t.s)||isNaN(t.v)?null:new r(t.h,t.s,t.v)}equalValue(t){return this.h===t.h&&this.s===t.s&&this.v===t.v}roundToPrecision(t){return new r((0,i.fZ)(this.h,t),(0,i.fZ)(this.s,t),(0,i.fZ)(this.v,t))}toObject(){return{h:this.h,s:this.s,v:this.v}}}},447:function(t,e,n){"use strict";n.d(e,{R:function(){return r}});var i=n(82917);class r{constructor(t,e,n){this.l=t,this.a=e,this.b=n}static fromObject(t){return!t||isNaN(t.l)||isNaN(t.a)||isNaN(t.b)?null:new r(t.l,t.a,t.b)}equalValue(t){return this.l===t.l&&this.a===t.a&&this.b===t.b}roundToPrecision(t){return new r((0,i.fZ)(this.l,t),(0,i.fZ)(this.a,t),(0,i.fZ)(this.b,t))}toObject(){return{l:this.l,a:this.a,b:this.b}}}r.epsilon=216/24389,r.kappa=24389/27},83343:function(t,e,n){"use strict";n.d(e,{t:function(){return r}});var i=n(82917);class r{constructor(t,e,n){this.l=t,this.c=e,this.h=n}static fromObject(t){return!t||isNaN(t.l)||isNaN(t.c)||isNaN(t.h)?null:new r(t.l,t.c,t.h)}equalValue(t){return this.l===t.l&&this.c===t.c&&this.h===t.h}roundToPrecision(t){return new r((0,i.fZ)(this.l,t),(0,i.fZ)(this.c,t),(0,i.fZ)(this.h,t))}toObject(){return{l:this.l,c:this.c,h:this.h}}}},2696:function(t,e,n){"use strict";n.d(e,{h:function(){return r}});var i=n(82917);class r{constructor(t,e,n,i){this.r=t,this.g=e,this.b=n,this.a="number"!=typeof i||isNaN(i)?1:i}static fromObject(t){return!t||isNaN(t.r)||isNaN(t.g)||isNaN(t.b)?null:new r(t.r,t.g,t.b,t.a)}equalValue(t){return this.r===t.r&&this.g===t.g&&this.b===t.b&&this.a===t.a}toStringHexRGB(){return"#"+[this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringHexRGBA(){return this.toStringHexRGB()+this.formatHexValue(this.a)}toStringHexARGB(){return"#"+[this.a,this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringWebRGB(){return`rgb(${Math.round((0,i.cY)(this.r,0,255))},${Math.round((0,i.cY)(this.g,0,255))},${Math.round((0,i.cY)(this.b,0,255))})`}toStringWebRGBA(){return`rgba(${Math.round((0,i.cY)(this.r,0,255))},${Math.round((0,i.cY)(this.g,0,255))},${Math.round((0,i.cY)(this.b,0,255))},${(0,i.uZ)(this.a,0,1)})`}roundToPrecision(t){return new r((0,i.fZ)(this.r,t),(0,i.fZ)(this.g,t),(0,i.fZ)(this.b,t),(0,i.fZ)(this.a,t))}clamp(){return new r((0,i.uZ)(this.r,0,1),(0,i.uZ)(this.g,0,1),(0,i.uZ)(this.b,0,1),(0,i.uZ)(this.a,0,1))}toObject(){return{r:this.r,g:this.g,b:this.b,a:this.a}}formatHexValue(t){return(0,i.yi)((0,i.cY)(t,0,255))}}},40272:function(t,e,n){"use strict";n.d(e,{x:function(){return r}});var i=n(82917);class r{constructor(t,e,n){this.x=t,this.y=e,this.z=n}static fromObject(t){return!t||isNaN(t.x)||isNaN(t.y)||isNaN(t.z)?null:new r(t.x,t.y,t.z)}equalValue(t){return this.x===t.x&&this.y===t.y&&this.z===t.z}roundToPrecision(t){return new r((0,i.fZ)(this.x,t),(0,i.fZ)(this.y,t),(0,i.fZ)(this.z,t))}toObject(){return{x:this.x,y:this.y,z:this.z}}}r.whitePoint=new r(.95047,1,1.08883)},82917:function(t,e,n){"use strict";function i(t,e,n){return isNaN(t)||t<=e?e:t>=n?n:t}function r(t,e,n){return isNaN(t)||t<=e?0:t>=n?1:t/(n-e)}function o(t,e,n){return isNaN(t)?e:e+t*(n-e)}function s(t){return t*(Math.PI/180)}function a(t){return t*(180/Math.PI)}function c(t){const e=Math.round(i(t,0,255)).toString(16);return 1===e.length?"0"+e:e}function l(t,e,n){return isNaN(t)||t<=0?e:t>=1?n:e+t*(n-e)}function u(t,e,n){if(t<=0)return e%360;if(t>=1)return n%360;const i=(e-n+360)%360;return i<=(n-e+360)%360?(e-i*t+360)%360:(e+i*t+360)%360}n.d(e,{AG:function(){return u},Fv:function(){return r},Ht:function(){return s},cY:function(){return o},fZ:function(){return d},t7:function(){return l},uZ:function(){return i},vi:function(){return a},yi:function(){return c}});Math.PI;function d(t,e){const n=Math.pow(10,e);return Math.round(t*n)/n}},97186:function(t,e,n){"use strict";n.d(e,{pJ:function(){return u},b4:function(){return h},lu:function(){return g},in:function(){return f},hg:function(){return p}});var i=n(2696),r=n(82917);const o={aliceblue:{r:.941176,g:.972549,b:1},antiquewhite:{r:.980392,g:.921569,b:.843137},aqua:{r:0,g:1,b:1},aquamarine:{r:.498039,g:1,b:.831373},azure:{r:.941176,g:1,b:1},beige:{r:.960784,g:.960784,b:.862745},bisque:{r:1,g:.894118,b:.768627},black:{r:0,g:0,b:0},blanchedalmond:{r:1,g:.921569,b:.803922},blue:{r:0,g:0,b:1},blueviolet:{r:.541176,g:.168627,b:.886275},brown:{r:.647059,g:.164706,b:.164706},burlywood:{r:.870588,g:.721569,b:.529412},cadetblue:{r:.372549,g:.619608,b:.627451},chartreuse:{r:.498039,g:1,b:0},chocolate:{r:.823529,g:.411765,b:.117647},coral:{r:1,g:.498039,b:.313725},cornflowerblue:{r:.392157,g:.584314,b:.929412},cornsilk:{r:1,g:.972549,b:.862745},crimson:{r:.862745,g:.078431,b:.235294},cyan:{r:0,g:1,b:1},darkblue:{r:0,g:0,b:.545098},darkcyan:{r:0,g:.545098,b:.545098},darkgoldenrod:{r:.721569,g:.52549,b:.043137},darkgray:{r:.662745,g:.662745,b:.662745},darkgreen:{r:0,g:.392157,b:0},darkgrey:{r:.662745,g:.662745,b:.662745},darkkhaki:{r:.741176,g:.717647,b:.419608},darkmagenta:{r:.545098,g:0,b:.545098},darkolivegreen:{r:.333333,g:.419608,b:.184314},darkorange:{r:1,g:.54902,b:0},darkorchid:{r:.6,g:.196078,b:.8},darkred:{r:.545098,g:0,b:0},darksalmon:{r:.913725,g:.588235,b:.478431},darkseagreen:{r:.560784,g:.737255,b:.560784},darkslateblue:{r:.282353,g:.239216,b:.545098},darkslategray:{r:.184314,g:.309804,b:.309804},darkslategrey:{r:.184314,g:.309804,b:.309804},darkturquoise:{r:0,g:.807843,b:.819608},darkviolet:{r:.580392,g:0,b:.827451},deeppink:{r:1,g:.078431,b:.576471},deepskyblue:{r:0,g:.74902,b:1},dimgray:{r:.411765,g:.411765,b:.411765},dimgrey:{r:.411765,g:.411765,b:.411765},dodgerblue:{r:.117647,g:.564706,b:1},firebrick:{r:.698039,g:.133333,b:.133333},floralwhite:{r:1,g:.980392,b:.941176},forestgreen:{r:.133333,g:.545098,b:.133333},fuchsia:{r:1,g:0,b:1},gainsboro:{r:.862745,g:.862745,b:.862745},ghostwhite:{r:.972549,g:.972549,b:1},gold:{r:1,g:.843137,b:0},goldenrod:{r:.854902,g:.647059,b:.12549},gray:{r:.501961,g:.501961,b:.501961},green:{r:0,g:.501961,b:0},greenyellow:{r:.678431,g:1,b:.184314},grey:{r:.501961,g:.501961,b:.501961},honeydew:{r:.941176,g:1,b:.941176},hotpink:{r:1,g:.411765,b:.705882},indianred:{r:.803922,g:.360784,b:.360784},indigo:{r:.294118,g:0,b:.509804},ivory:{r:1,g:1,b:.941176},khaki:{r:.941176,g:.901961,b:.54902},lavender:{r:.901961,g:.901961,b:.980392},lavenderblush:{r:1,g:.941176,b:.960784},lawngreen:{r:.486275,g:.988235,b:0},lemonchiffon:{r:1,g:.980392,b:.803922},lightblue:{r:.678431,g:.847059,b:.901961},lightcoral:{r:.941176,g:.501961,b:.501961},lightcyan:{r:.878431,g:1,b:1},lightgoldenrodyellow:{r:.980392,g:.980392,b:.823529},lightgray:{r:.827451,g:.827451,b:.827451},lightgreen:{r:.564706,g:.933333,b:.564706},lightgrey:{r:.827451,g:.827451,b:.827451},lightpink:{r:1,g:.713725,b:.756863},lightsalmon:{r:1,g:.627451,b:.478431},lightseagreen:{r:.12549,g:.698039,b:.666667},lightskyblue:{r:.529412,g:.807843,b:.980392},lightslategray:{r:.466667,g:.533333,b:.6},lightslategrey:{r:.466667,g:.533333,b:.6},lightsteelblue:{r:.690196,g:.768627,b:.870588},lightyellow:{r:1,g:1,b:.878431},lime:{r:0,g:1,b:0},limegreen:{r:.196078,g:.803922,b:.196078},linen:{r:.980392,g:.941176,b:.901961},magenta:{r:1,g:0,b:1},maroon:{r:.501961,g:0,b:0},mediumaquamarine:{r:.4,g:.803922,b:.666667},mediumblue:{r:0,g:0,b:.803922},mediumorchid:{r:.729412,g:.333333,b:.827451},mediumpurple:{r:.576471,g:.439216,b:.858824},mediumseagreen:{r:.235294,g:.701961,b:.443137},mediumslateblue:{r:.482353,g:.407843,b:.933333},mediumspringgreen:{r:0,g:.980392,b:.603922},mediumturquoise:{r:.282353,g:.819608,b:.8},mediumvioletred:{r:.780392,g:.082353,b:.521569},midnightblue:{r:.098039,g:.098039,b:.439216},mintcream:{r:.960784,g:1,b:.980392},mistyrose:{r:1,g:.894118,b:.882353},moccasin:{r:1,g:.894118,b:.709804},navajowhite:{r:1,g:.870588,b:.678431},navy:{r:0,g:0,b:.501961},oldlace:{r:.992157,g:.960784,b:.901961},olive:{r:.501961,g:.501961,b:0},olivedrab:{r:.419608,g:.556863,b:.137255},orange:{r:1,g:.647059,b:0},orangered:{r:1,g:.270588,b:0},orchid:{r:.854902,g:.439216,b:.839216},palegoldenrod:{r:.933333,g:.909804,b:.666667},palegreen:{r:.596078,g:.984314,b:.596078},paleturquoise:{r:.686275,g:.933333,b:.933333},palevioletred:{r:.858824,g:.439216,b:.576471},papayawhip:{r:1,g:.937255,b:.835294},peachpuff:{r:1,g:.854902,b:.72549},peru:{r:.803922,g:.521569,b:.247059},pink:{r:1,g:.752941,b:.796078},plum:{r:.866667,g:.627451,b:.866667},powderblue:{r:.690196,g:.878431,b:.901961},purple:{r:.501961,g:0,b:.501961},red:{r:1,g:0,b:0},rosybrown:{r:.737255,g:.560784,b:.560784},royalblue:{r:.254902,g:.411765,b:.882353},saddlebrown:{r:.545098,g:.270588,b:.07451},salmon:{r:.980392,g:.501961,b:.447059},sandybrown:{r:.956863,g:.643137,b:.376471},seagreen:{r:.180392,g:.545098,b:.341176},seashell:{r:1,g:.960784,b:.933333},sienna:{r:.627451,g:.321569,b:.176471},silver:{r:.752941,g:.752941,b:.752941},skyblue:{r:.529412,g:.807843,b:.921569},slateblue:{r:.415686,g:.352941,b:.803922},slategray:{r:.439216,g:.501961,b:.564706},slategrey:{r:.439216,g:.501961,b:.564706},snow:{r:1,g:.980392,b:.980392},springgreen:{r:0,g:1,b:.498039},steelblue:{r:.27451,g:.509804,b:.705882},tan:{r:.823529,g:.705882,b:.54902},teal:{r:0,g:.501961,b:.501961},thistle:{r:.847059,g:.74902,b:.847059},tomato:{r:1,g:.388235,b:.278431},transparent:{r:0,g:0,b:0,a:0},turquoise:{r:.25098,g:.878431,b:.815686},violet:{r:.933333,g:.509804,b:.933333},wheat:{r:.960784,g:.870588,b:.701961},white:{r:1,g:1,b:1},whitesmoke:{r:.960784,g:.960784,b:.960784},yellow:{r:1,g:1,b:0},yellowgreen:{r:.603922,g:.803922,b:.196078}},s=/^rgb\(\s*((?:(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*,\s*){2}(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*)\)$/i,a=/^rgba\(\s*((?:(?:25[0-5]|2[0-4]\d|1\d\d|\d{1,2})\s*,\s*){3}(?:0|1|0?\.\d*)\s*)\)$/i,c=/^#((?:[0-9a-f]{6}|[0-9a-f]{3}))$/i,l=/^#((?:[0-9a-f]{8}|[0-9a-f]{4}))$/i;function u(t){return c.test(t)}function d(t){return function(t){return l.test(t)}(t)}function h(t){return s.test(t)}function f(t){const e=c.exec(t);if(null===e)return null;let n=e[1];if(3===n.length){const t=n.charAt(0),e=n.charAt(1),i=n.charAt(2);n=t.concat(t,e,e,i,i)}const o=parseInt(n,16);return isNaN(o)?null:new i.h((0,r.Fv)((16711680&o)>>>16,0,255),(0,r.Fv)((65280&o)>>>8,0,255),(0,r.Fv)(255&o,0,255),1)}function p(t){const e=s.exec(t);if(null===e)return null;const n=e[1].split(",");return new i.h((0,r.Fv)(Number(n[0]),0,255),(0,r.Fv)(Number(n[1]),0,255),(0,r.Fv)(Number(n[2]),0,255),1)}function g(t){const e=t.toLowerCase();return u(e)?f(e):d(e)?function(t){const e=l.exec(t);if(null===e)return null;let n=e[1];if(4===n.length){const t=n.charAt(0),e=n.charAt(1),i=n.charAt(2),r=n.charAt(3);n=t.concat(t,e,e,i,i,r,r)}const o=parseInt(n,16);return isNaN(o)?null:new i.h((0,r.Fv)((16711680&o)>>>16,0,255),(0,r.Fv)((65280&o)>>>8,0,255),(0,r.Fv)(255&o,0,255),(0,r.Fv)((4278190080&o)>>>24,0,255))}(e):h(e)?p(e):function(t){return a.test(t)}(e)?function(t){const e=a.exec(t);if(null===e)return null;const n=e[1].split(",");return 4===n.length?new i.h((0,r.Fv)(Number(n[0]),0,255),(0,r.Fv)(Number(n[1]),0,255),(0,r.Fv)(Number(n[2]),0,255),Number(n[3])):null}(e):function(t){return o.hasOwnProperty(t)}(e)?function(t){const e=o[t.toLowerCase()];return e?new i.h(e.r,e.g,e.b,e.hasOwnProperty("a")?e.a:void 0):null}(e):null}},97380:function(t,e,n){"use strict";n.d(e,{i:function(){return i}});const i={horizontal:"horizontal",vertical:"vertical"}},54256:function(t,e,n){"use strict";function i(...t){return t.reduce(((t,e)=>{const n=t.length?" ":"",r=Array.isArray(e)&&e[1]?i.call(null,e[0]):"function"==typeof e?e():"string"==typeof e?e:"";return r.length?t+n+r:t}),"")}n.d(e,{A:function(){return i}})},7986:function(t,e,n){"use strict";function i(...t){return t.every((t=>t instanceof HTMLElement))}function r(t,e){if(!t||!e||!i(t))return;return Array.from(t.querySelectorAll(e)).filter((t=>null!==t.offsetParent))}n.d(e,{Re:function(){return i},UM:function(){return r}})},85144:function(t,e,n){"use strict";n.d(e,{AB:function(){return s},Oz:function(){return o},Rl:function(){return i},_m:function(){return r},pu:function(){return a},xG:function(){return c}});const i="focus",r="focusin",o="focusout",s="keydown",a="resize",c="scroll"},94537:function(t,e,n){"use strict";n.d(e,{BE:function(){return s},BI:function(){return v},CX:function(){return d},Kh:function(){return l},L2:function(){return r},Op:function(){return g},SB:function(){return c},hi:function(){return p},iF:function(){return o},kL:function(){return u},mr:function(){return a},ny:function(){return f},oM:function(){return m},tU:function(){return h},uH:function(){return i},uf:function(){return b}});const i="Alt",r="AltGraph",o="ArrowDown",s="ArrowLeft",a="ArrowRight",c="ArrowUp",l="End",u="Enter",d="Escape",h="Home",f="F2",p="PageDown",g="PageUp",v=" ",m="Tab",b={ArrowDown:o,ArrowLeft:s,ArrowRight:a,ArrowUp:c}},59997:function(t,e,n){"use strict";var i;n.d(e,{N:function(){return i}}),function(t){t.ltr="ltr",t.rtl="rtl"}(i||(i={}))},97108:function(t,e,n){"use strict";function i(t,e,n){return n<t?e:n>e?t:n}function r(t,e,n){return Math.min(Math.max(n,t),e)}function o(t,e,n=0){return[e,n]=[e,n].sort(((t,e)=>t-e)),e<=t&&t<n}n.d(e,{Z2:function(){return o},b9:function(){return r},wt:function(){return i}})},62512:function(t,e,n){"use strict";n.d(e,{EL:function(){return r},TF:function(){return s},WU:function(){return o}});let i=0;function r(t=""){return`${t}${i++}`}function o(t,...e){return t.replace(/{(\d+)}/g,(function(t,n){if(n>=e.length)return t;const i=e[n];return"number"==typeof i||i?i:""}))}function s(t){return!t||!t.trim()}},22798:function(t,e,n){"use strict";var i;n.d(e,{H:function(){return i}}),function(t){t.Canvas="Canvas",t.CanvasText="CanvasText",t.LinkText="LinkText",t.VisitedText="VisitedText",t.ActiveText="ActiveText",t.ButtonFace="ButtonFace",t.ButtonText="ButtonText",t.Field="Field",t.FieldText="FieldText",t.Highlight="Highlight",t.HighlightText="HighlightText",t.GrayText="GrayText"}(i||(i={}))},51758:function(t,e,n){"use strict";n.d(e,{W:function(){return g}});var i=n(78923),r=n(24484),o=n(29717),s=n(22798),a=n(38148),c=n(82636),l=n(10970),u=n(35680),d=n(958),h=n(26738),f=n(28632),p=n(80260);const g=i.i` :host([disabled]),:host([disabled]:hover),:host([disabled]:active){opacity:${l.V};background-color:${u.wF};cursor:${r.H}}${a.G6}
`.withBehaviors((0,o.vF)(i.i` :host([disabled]),:host([disabled]:hover),:host([disabled]:active),:host([disabled]) .control,:host([disabled]) .control:hover,:host([appearance="neutral"][disabled]:hover) .control{forced-color-adjust:none;background-color:${s.H.ButtonFace};border-color:${s.H.GrayText};color:${s.H.GrayText};opacity:1}`),(0,c.H)("accent",i.i` :host([appearance="accent"][disabled]),:host([appearance="accent"][disabled]:hover),:host([appearance="accent"][disabled]:active){background:${d.Av}}${a.jQ} `.withBehaviors((0,o.vF)(i.i` :host([appearance="accent"][disabled]) .control,:host([appearance="accent"][disabled]) .control:hover{background:${s.H.ButtonFace};border-color:${s.H.GrayText};color:${s.H.GrayText}}`))),(0,c.H)("lightweight",i.i` :host([appearance="lightweight"][disabled]:hover),:host([appearance="lightweight"][disabled]:active){background-color:transparent;color:${h.go}}:host([appearance="lightweight"][disabled]) .content::before,:host([appearance="lightweight"][disabled]:hover) .content::before,:host([appearance="lightweight"][disabled]:active) .content::before{background:transparent}${a.vt} `.withBehaviors((0,o.vF)(i.i` :host([appearance="lightweight"][disabled]) .control{forced-color-adjust:none;color:${s.H.GrayText}}:host([appearance="lightweight"][disabled]) .control:hover .content::before{background:none}`))),(0,c.H)("outline",i.i` :host([appearance="outline"][disabled]:hover),:host([appearance="outline"][disabled]:active){background:transparent;border-color:${f.ak}}${a.O8} `.withBehaviors((0,o.vF)(i.i` :host([appearance="outline"][disabled]) .control{border-color:${s.H.GrayText}}`))),(0,c.H)("stealth",i.i` :host([appearance="stealth"][disabled]),:host([appearance="stealth"][disabled]:hover),:host([appearance="stealth"][disabled]:active){background:${p.jq}}${a.cg} `.withBehaviors((0,o.vF)(i.i` :host([appearance="stealth"][disabled]),:host([appearance="stealth"][disabled]:hover){background:${s.H.ButtonFace}}:host([appearance="stealth"][disabled]) .control{background:${s.H.ButtonFace};border-color:transparent;color:${s.H.GrayText}}`))))},54721:function(t,e,n){"use strict";n.d(e,{W:function(){return d}});var i=n(78923),r=n(27186),o=n(29717),s=n(22798),a=n(62734),c=n(38492),l=n(42689),u=n(55135);const d=i.i` ${(0,r.j)("block")} :host{--elevation:4;display:block;contain:content;height:var(--card-height,100%);width:var(--card-width,100%);box-sizing:border-box;background:${c.I};color:${l.C};border-radius:calc(${u.rS} * 1px);${a.XC}}:host(:hover){--elevation:8}:host(:focus-within){--elevation:8}:host{content-visibility:auto}`.withBehaviors((0,o.vF)(i.i` :host{forced-color-adjust:none;background:${s.H.Canvas};box-shadow:0 0 0 1px ${s.H.CanvasText}}`))},26254:function(t,e,n){"use strict";n.d(e,{P:function(){return c},W:function(){return l}});var i=n(78923),r=n(27186),o=n(22674);const s=i.i` .scroll-prev{right:auto;left:0}.scroll.scroll-next::before,.scroll-next .scroll-action{left:auto;right:0}.scroll.scroll-next::before{background:linear-gradient(to right,transparent,var(--scroll-fade-next))}.scroll-next .scroll-action{transform:translate(50%,-50%)}`,a=i.i` .scroll.scroll-next{right:auto;left:0}.scroll.scroll-next::before{background:linear-gradient(to right,var(--scroll-fade-next),transparent);left:auto;right:0}.scroll.scroll-prev::before{background:linear-gradient(to right,transparent,var(--scroll-fade-previous))}.scroll-prev .scroll-action{left:auto;right:0;transform:translate(50%,-50%)}`,c=i.i` .scroll-area{position:relative}div.scroll-view{overflow-x:hidden}.scroll{bottom:0;pointer-events:none;position:absolute;right:0;top:0;user-select:none;width:100px}.scroll.disabled{display:none}.scroll::before,.scroll-action{left:0;position:absolute}.scroll::before{background:linear-gradient(to right,var(--scroll-fade-previous),transparent);content:"";display:block;height:100%;width:100%}.scroll-action{pointer-events:auto;right:auto;top:50%;transform:translate(-50%,-50%)}::slotted(fluent-flipper){opacity:0;transition:opacity 0.2s ease-in-out}.scroll-area:hover ::slotted(fluent-flipper){opacity:1}`.withBehaviors(new o.O(s,a)),l=i.i` ${(0,r.j)("block")} :host{--scroll-align:center;--scroll-item-spacing:4px;contain:layout;position:relative}.scroll-view{overflow-x:auto;scrollbar-width:none}::-webkit-scrollbar{display:none}.content-container{align-items:var(--scroll-align);display:inline-flex;flex-wrap:nowrap;position:relative}.content-container ::slotted(*){margin-right:var(--scroll-item-spacing)}.content-container ::slotted(*:last-child){margin-right:0}`},77670:function(t,e,n){"use strict";n.d(e,{W:function(){return C}});var i=n(78923),r=n(27186),o=n(67739),s=n(24484),a=n(29717),c=n(22798),l=n(2658),u=n(53131),d=n(55135),h=n(26512),f=n(42689),p=n(27782),g=n(40009),v=n(26741),m=n(17993),b=n(958),y=n(23132),w=n(35680),x=n(10970);const C=i.i` ${(0,r.j)("inline-flex")} :host{font-family:${u.S};border-radius:calc(${d.UW} * 1px);border:calc(${h.vx} * 1px) solid transparent;box-sizing:border-box;color:${f.C};cursor:pointer;fill:currentcolor;font-size:${p.c};height:calc(${l.i} * 1px);line-height:${p.R};margin:0 calc(${g._5} * 1px);outline:none;overflow:hidden;align-items:center;padding:0 calc(${g._5} * 2.25px);user-select:none;white-space:nowrap}:host(:${o.b}){box-shadow:0 0 0 calc(${h.vx} * 1px) inset ${v.a2};border-color:${m.yG};background:${b.D8};color:${y.$u}}:host([aria-selected="true"]){background:${b.Av};color:${y.w4}}:host(:hover){background:${b.OC};color:${y.lJ}}:host(:active){background:${b.UE};color:${y.Pp}}:host(:not([aria-selected="true"]):hover){background:${w.Xi};color:${f.C}}:host(:not([aria-selected="true"]):active){background:${w.Gy};color:${f.C}}:host([disabled]){cursor:${s.H};opacity:${x.V}}:host([disabled]:hover){background-color:inherit}.content{grid-column-start:2;justify-self:start;overflow:hidden;text-overflow:ellipsis}::slotted([slot="start"]),::slotted([slot="end"]),::slotted(svg){display:flex}::slotted(svg){${""} height:calc(${g._5} * 4px);width:calc(${g._5} * 4px)}::slotted([slot="end"]){margin-inline-start:1ch}::slotted([slot="start"]){margin-inline-end:1ch}`.withBehaviors((0,a.vF)(i.i` :host{border-color:transparent;color:${c.H.ButtonText};forced-color-adjust:none}:host(:not([aria-selected="true"]):hover),:host([aria-selected="true"]){background:${c.H.Highlight};color:${c.H.HighlightText}}:host([disabled]),:host([disabled]:not([aria-selected="true"]):hover){background:${c.H.Canvas};color:${c.H.GrayText};fill:currentcolor;opacity:1}`))},74724:function(t,e,n){"use strict";n.d(e,{Cw:function(){return O},W2:function(){return E},WK:function(){return I}});var i=n(78923),r=n(67739),o=n(27186),s=n(24484),a=n(29717),c=n(22798),l=n(2658),u=n(62734),d=n(82636),h=n(35680),f=n(17993),p=n(39238),g=n(55135),v=n(26512),m=n(28632),b=n(42689),y=n(53131),w=n(27460),x=n(40009),C=n(27782),k=n(26741),S=n(958),$=n(23132),T=n(10970),L=n(80260);const O=i.i` :host([appearance="filled"]){background:${h.wF};border-color:transparent}:host([appearance="filled"]:hover:not([disabled])){background:${h.Xi};border-color:transparent}:host([appearance="filled"]:${r.b}){border-color:${f.yG}}`,I=i.i` ${(0,o.j)("inline-flex")} :host{--elevation:14;background:${p._B};border-radius:calc(${g.UW} * 1px);border:calc(${v.H} * 1px) solid ${m.ak};box-sizing:border-box;color:${b.C};font-family:${y.S};height:calc(${l.i} * 1px);position:relative;user-select:none;min-width:250px;vertical-align:top}.listbox{${u.XC} background:${w.s};border-radius:calc(${g.UW} * 1px);box-sizing:border-box;display:inline-flex;flex-direction:column;left:0;max-height:calc(var(--max-height) - (${l.i} * 1px));padding:calc(${x._5} * 1px) 0;overflow-y:auto;position:fixed;top:0;z-index:1;margin:1px 0}.listbox[hidden]{display:none}.control{align-items:center;box-sizing:border-box;cursor:pointer;display:flex;font-size:${C.c};font-family:inherit;min-height:100%;line-height:${C.R};padding:0 calc(${x._5} * 2.25px);width:100%}:host(:not([disabled]):hover){background:${p.Tm};border-color:${m.QP}}:host(:focus){outline:none}:host(:${r.b}){border-color:${f.yG};outline:none;box-shadow:0 0 0 1px inset ${f.yG}}:host([open]:${r.b}){border-color:${m.ak};outline:none;box-shadow:none}:host(:${r.b}) ::slotted([aria-selected="true"][role="option"]:not([disabled])){box-shadow:0 0 0 calc(${v.vx} * 1px) inset ${k.a2};border-color:${f.yG};background:${S.D8};color:${$.$u}}:host([disabled]){cursor:${s.H};opacity:${T.V}}:host([disabled]) .control{cursor:${s.H};user-select:none}:host([disabled]:hover){background:${L.jq};color:${b.C};fill:currentcolor}:host(:not([disabled])) .control:active{background:${p.Iu};border-color:${m.c1};border-radius:calc(${g.UW} * 1px)}.selected-value{font-family:inherit;flex:1 1 auto;text-align:start}.indicator{flex:0 0 auto;margin-inline-start:1em}::slotted([name="listbox"]){display:none;width:100%}:host([open]) ::slotted([name="listbox"]){display:flex;position:absolute;${u.XC}}::slotted([slot="end"]){margin-inline-start:auto}::slotted([slot="start"]),::slotted([slot="end"]),.indicator,.select-indicator,::slotted(svg){${""} fill:currentcolor;height:1em;min-height:calc(${x._5} * 4px);min-width:calc(${x._5} * 4px);width:1em}::slotted([role="option"]){flex:0 0 auto}`.withBehaviors((0,d.H)("filled",O),(0,a.vF)(i.i` :host([disabled]){border-color:${c.H.GrayText};background-color:${c.H.ButtonFace};color:${c.H.GrayText};opacity:1;forced-color-adjust:none}:host([disabled]:hover){background:${c.H.ButtonFace}}:host([disabled]) .control{color:${c.H.GrayText};border-color:${c.H.GrayText}}:host(:not([disabled]):hover){background:${c.H.ButtonFace};border-color:${c.H.Highlight}}:host(:${r.b}){forced-color-adjust:none;background:${c.H.ButtonFace};border-color:${c.H.Highlight};box-shadow:0 0 0 1px inset ${c.H.Highlight};color:${c.H.ButtonText};fill:currentcolor}:host([open]) .listbox{background:${c.H.ButtonFace};border:1px solid ${c.H.ButtonText}}:host(:${r.b}) ::slotted([aria-selected="true"][role="option"]:not([disabled])){background:${c.H.Highlight};border-color:${c.H.ButtonText};box-shadow:0 0 0 calc(${v.vx} * 1px) inset ${c.H.HighlightText};color:${c.H.HighlightText};fill:currentcolor}::slotted([role="option"]:not([aria-selected="true"]):not([disabled]):hover){forced-color-adjust:none;color:${c.H.ButtonText};background:${c.H.ButtonFace};border-color:${c.H.Highlight};box-shadow:none}`)),E=i.i` ${I}
`},38148:function(t,e,n){"use strict";n.d(e,{G6:function(){return k},O8:function(){return L},Xu:function(){return $},cg:function(){return O},jQ:function(){return S},vt:function(){return T}});var i=n(78923),r=n(27186),o=n(67739),s=n(29717),a=n(22798),c=n(2658),l=n(53131),u=n(27782),d=n(35680),h=n(42689),f=n(55135),p=n(40009),g=n(26512),v=n(17993),m=n(958),b=n(23132),y=n(26741),w=n(26738),x=n(28632),C=n(80260);const k=i.i` ${(0,r.j)("inline-flex")} :host{font-family:${l.S};outline:none;font-size:${u.c};line-height:${u.R};height:calc(${c.i} * 1px);min-width:calc(${c.i} * 1px);background-color:${d.wF};color:${h.C};border-radius:calc(${f.UW} * 1px);fill:currentcolor;cursor:pointer}.control{background:transparent;height:inherit;flex-grow:1;box-sizing:border-box;display:inline-flex;justify-content:center;align-items:center;padding:0 calc((10 + (${p._5} * 2 * ${p.hV})) * 1px);white-space:nowrap;outline:none;text-decoration:none;border:calc(${g.H} * 1px) solid transparent;color:inherit;border-radius:inherit;fill:inherit;cursor:inherit;font-family:inherit}.control,::slotted([slot="end"]),::slotted([slot="start"]){font:inherit}:host([icon-only]) .control,.control.icon-only{padding:0;line-height:0}:host(:hover){background-color:${d.Xi}}:host(:active){background-color:${d.Gy}}${""} .control:${o.b}{border:calc(${g.H} * 1px) solid ${v.yG};box-shadow:0 0 0 calc((${g.vx} - ${g.H}) * 1px) ${v.yG}}.control::-moz-focus-inner{border:0}.content{pointer-events:none}::slotted([slot="start"]),::slotted([slot="end"]){display:flex;pointer-events:none}::slotted(svg){${""} width:16px;height:16px;pointer-events:none}::slotted([slot="start"]){margin-inline-end:11px}::slotted([slot="end"]){margin-inline-start:11px}`.withBehaviors((0,s.vF)(i.i` :host,:host([appearance="neutral"]) .control{background-color:${a.H.ButtonFace};border-color:${a.H.ButtonText};color:${a.H.ButtonText};fill:currentcolor}:host(:not([disabled][href]):hover),:host([appearance="neutral"]:not([disabled]):hover) .control{forced-color-adjust:none;background-color:${a.H.Highlight};color:${a.H.HighlightText}}.control:${o.b},:host([appearance="outline"]) .control:${o.b},:host([appearance="neutral"]:${o.b}) .control{forced-color-adjust:none;background-color:${a.H.Highlight};border-color:${a.H.ButtonText};box-shadow:0 0 0 calc((${g.vx} - ${g.H}) * 1px) ${a.H.ButtonText};color:${a.H.HighlightText}}.control:not([disabled]):hover,:host([appearance="outline"]) .control:hover{border-color:${a.H.ButtonText}}:host([href]) .control{border-color:${a.H.LinkText};color:${a.H.LinkText}}:host([href]) .control:hover,:host(.neutral[href]) .control:hover,:host(.outline[href]) .control:hover,:host([href]) .control:${o.b}{forced-color-adjust:none;background:${a.H.ButtonFace};border-color:${a.H.LinkText};box-shadow:0 0 0 1px ${a.H.LinkText} inset;color:${a.H.LinkText};fill:currentcolor}`)),S=i.i` :host([appearance="accent"]){background:${m.Av};color:${b.w4}}:host([appearance="accent"]:hover){background:${m.OC};color:${b.lJ}}:host([appearance="accent"]:active) .control:active{background:${m.UE};color:${b.Pp}}:host([appearance="accent"]) .control:${o.b}{box-shadow:0 0 0 calc(${g.vx} * 1px) inset ${y.a2},0 0 0 calc((${g.vx} - ${g.H}) * 1px) ${v.yG}}`.withBehaviors((0,s.vF)(i.i` :host([appearance="accent"]) .control{forced-color-adjust:none;background:${a.H.Highlight};color:${a.H.HighlightText}}:host([appearance="accent"]) .control:hover,:host([appearance="accent"]:active) .control:active{background:${a.H.HighlightText};border-color:${a.H.Highlight};color:${a.H.Highlight}}:host([appearance="accent"]) .control:${o.b}{border-color:${a.H.ButtonText};box-shadow:0 0 0 2px ${a.H.HighlightText} inset}:host([appearance="accent"][href]) .control{background:${a.H.LinkText};color:${a.H.HighlightText}}:host([appearance="accent"][href]) .control:hover{background:${a.H.ButtonFace};border-color:${a.H.LinkText};box-shadow:none;color:${a.H.LinkText};fill:currentcolor}:host([appearance="accent"][href]) .control:${o.b}{border-color:${a.H.LinkText};box-shadow:0 0 0 2px ${a.H.HighlightText} inset}`)),$=i.i` :host([appearance="hypertext"]){height:auto;font-size:inherit;line-height:inherit;background:transparent;min-width:0}:host([appearance="hypertext"]) .control{display:inline;padding:0;border:none;box-shadow:none;border-radius:0;line-height:1}:host a.control:not(:link){background-color:transparent;cursor:default}:host([appearance="hypertext"]) .control:link,:host([appearance="hypertext"]) .control:visited{background:transparent;color:${w.go};border-bottom:calc(${g.H} * 1px) solid ${w.go}}:host([appearance="hypertext"]) .control:hover{border-bottom-color:${w.D9}}:host([appearance="hypertext"]) .control:active{border-bottom-color:${w.VN}}:host([appearance="hypertext"]) .control:${o.b}{border-bottom:calc(${g.vx} * 1px) solid ${v.yG};margin-bottom:calc(calc(${g.H} - ${g.vx}) * 1px)}`.withBehaviors((0,s.vF)(i.i` :host([appearance="hypertext"]) .control:${o.b}{color:${a.H.LinkText};border-bottom-color:${a.H.LinkText}}`)),T=i.i` :host([appearance="lightweight"]){background:transparent;color:${w.go}}:host([appearance="lightweight"]) .control{padding:0;height:initial;border:none;box-shadow:none;border-radius:0}:host([appearance="lightweight"]:hover){color:${w.D9}}:host([appearance="lightweight"]:active){color:${w.VN}}:host([appearance="lightweight"]) .content{position:relative}:host([appearance="lightweight"]) .content::before{content:"";display:block;height:calc(${g.H} * 1px);position:absolute;top:calc(1em + 3px);width:100%}:host([appearance="lightweight"]:hover) .content::before{background:${w.D9}}:host([appearance="lightweight"]:active) .content::before{background:${w.VN}}:host([appearance="lightweight"]) .control:${o.b} .content::before{background:${h.C};height:calc(${g.vx} * 1px)}`.withBehaviors((0,s.vF)(i.i` :host([appearance="lightweight"]){color:${a.H.ButtonText}}:host([appearance="lightweight"]) .control:hover,:host([appearance="lightweight"]) .control:${o.b}{forced-color-adjust:none;background:${a.H.ButtonFace};color:${a.H.Highlight}}:host([appearance="lightweight"]) .control:hover .content::before,:host([appearance="lightweight"]) .control:${o.b} .content::before{background:${a.H.Highlight}}:host([appearance="lightweight"][href]) .control:hover,:host([appearance="lightweight"][href]) .control:${o.b}{background:${a.H.ButtonFace};box-shadow:none;color:${a.H.LinkText}}:host([appearance="lightweight"][href]) .control:hover .content::before,:host([appearance="lightweight"][href]) .control:${o.b} .content::before{background:${a.H.LinkText}}`)),L=i.i` :host([appearance="outline"]){background:transparent;border-color:${x.ak}}:host([appearance="outline"]:hover){border-color:${x.QP}}:host([appearance="outline"]:active){border-color:${x.c1}}:host([appearance="outline"]) .control{border-color:inherit}:host([appearance="outline"]) .control:${o.b}{box-shadow:0 0 0 calc((${g.vx} - ${g.H}) * 1px) ${v.yG};border-color:${v.yG}}`.withBehaviors((0,s.vF)(i.i` :host([appearance="outline"]){border-color:${a.H.ButtonText}}:host([appearance="outline"][href]){border-color:${a.H.LinkText}}`)),O=i.i` :host([appearance="stealth"]){background:${C.jq}}:host([appearance="stealth"]:hover){background:${C.Qp}}:host([appearance="stealth"]:active){background:${C.sG}}`.withBehaviors((0,s.vF)(i.i` :host([appearance="stealth"]),:host([appearance="stealth"]) .control{forced-color-adjust:none;background:${a.H.ButtonFace};border-color:transparent;color:${a.H.ButtonText};fill:currentcolor}:host([appearance="stealth"]:hover) .control{background:${a.H.Highlight};border-color:${a.H.Highlight};color:${a.H.HighlightText};fill:currentcolor}:host([appearance="stealth"]:${o.b}) .control{background:${a.H.Highlight};box-shadow:0 0 0 1px ${a.H.Highlight};color:${a.H.HighlightText};fill:currentcolor}:host([appearance="stealth"][href]) .control{color:${a.H.LinkText}}:host([appearance="stealth"]:hover[href]) .control,:host([appearance="stealth"]:${o.b}[href]) .control{background:${a.H.LinkText};border-color:${a.H.LinkText};color:${a.H.HighlightText};fill:currentcolor}:host([appearance="stealth"]:${o.b}[href]) .control{box-shadow:0 0 0 1px ${a.H.LinkText}}`))}}]);