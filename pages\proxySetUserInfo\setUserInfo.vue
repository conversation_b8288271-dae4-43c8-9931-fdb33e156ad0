<template>
  <view class="bkcolor">
    <view class="wd">
      <view class="set-user-info-item">
        <view>用户名</view>
        <view
          ><input
            type="text"
            v-model="userName"
            placeholder="请输入用户名"
            focus
            maxlength="20"
        /></view>
      </view>
      <view style="display: flex; justify-content: center">
        <button
          form-type="submit"
          style="
            margin-top: 20px;
            border-radius: 20px;
            font-size: 15px;
            background: #4089fb;
            color: white;
            width: 80vw;
          "
          @click="setUserName"
        >
          保存
        </button>
      </view>
      <view style="display: flex; justify-content: center">
        <button
          form-type="submit"
          style="
            margin-top: 30px;
            border-radius: 20px;
            font-size: 15px;
            background: #f83131;
            color: white;
            width: 80vw;
          "
          @click="logout"
        >
          退出登录
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { showInformations, setUserName } from "@/api/proxyUser.js";
import { resetInfo, clearProxyLoginStatus } from "@/utils/auth.js";
export default {
  data() {
    return {
      userName: "",
      isSend: false,
    };
  },
  methods: {
    getUserInfo() {
      if (this.isSend) {
        return;
      }
      this.isSend = true;
      showInformations()
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          this.userName = res.data.data.userName;
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    logout() {
      uni.showModal({
        title: "提示",
        content: "确定要退出代理端吗？",
        success: (res) => {
          if (res.confirm) {
            // 清除代理端登录状态
            clearProxyLoginStatus();
            this.$store.commit("proxyUser/DEFAULTUSERINFO");

            // 跳转到统一登录页面
            uni.reLaunch({
              url: "/pages/unifiedLogin/unifiedLogin",
            });
          }
        },
      });
    },
    setUserName() {
      if (this.userName.length === 0) {
        uni.showToast({
          title: "请输入用户名",
          icon: "error",
        });
        return;
      }
      if (this.isSend) {
        return;
      }
      this.isSend = true;
      const data = { username: this.userName };
      console.log(data);
      setUserName(data)
        .then((res) => {
          console.log(res);
          if (res.data.code !== 200) {
            return;
          }
          uni.showToast({
            title: "修改成功",
            icon: "success",
          });
          this.$store.dispatch("proxyUser/getUserInfo");
        })
        .finally(() => {
          this.isSend = false;
        });
    },
  },
  onLoad() {
    this.getUserInfo();
  },
  watch: {
    isSend(c) {
      if (c) {
        uni.showLoading({
          title: "加载中",
          mask: true,
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  onHide() {
    uni.hideLoading();
  },
};
</script>

<style>
.set-user-info-item {
  display: flex;
  justify-content: space-between;
  padding: 25px 0px;
}
</style>
