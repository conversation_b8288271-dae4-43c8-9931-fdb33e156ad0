import App from "./App";
import store from "./store";
import {
  VCONSOLE_ENABLED,
  VCONSOLE_CONFIG,
  isProduction,
} from "@/config/index.js";
import { setupNetworkListener } from "@/utils/requset.js";

// VConsole初始化 - 仅在需要时加载
// #ifdef APP-PLUS || H5
if (VCONSOLE_ENABLED) {
  import("vconsole")
    .then((VConsole) => {
      try {
        // 使用配置初始化VConsole
        const vConsoleConfig = VCONSOLE_CONFIG || {};
        const vConsole = new VConsole.default(vConsoleConfig);

        console.log(
          "VConsole已启用 - 环境:",
          isProduction ? "生产环境" : "开发环境"
        );
        console.log("VConsole配置:", vConsoleConfig);

        // 添加自定义插件或配置
        if (
          vConsoleConfig.onReady &&
          typeof vConsoleConfig.onReady === "function"
        ) {
          vConsoleConfig.onReady();
        }
      } catch (initError) {
        console.error("VConsole初始化失败:", initError);
      }
    })
    .catch((err) => {
      console.warn("VConsole加载失败:", err);
    });
}
// #endif

// 初始化网络状态监听
// #ifdef APP-PLUS || H5
setupNetworkListener();
// #endif

// #ifndef VUE3
import Vue from "vue";
import "./uni.promisify.adaptor";
import uView from "@/uni_modules/uview-ui";
Vue.use(uView);
Vue.config.productionTip = false;
App.mpType = "app";
Vue.prototype.$store = store;

const app = new Vue({
  store,
  ...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";

// VConsole初始化 - Vue3版本
// #ifdef APP-PLUS || H5
if (VCONSOLE_ENABLED) {
  import("vconsole")
    .then((VConsole) => {
      // eslint-disable-next-line no-unused-vars
      const vConsole = new VConsole.default(); // 初始化VConsole，无需保存引用
      console.log(
        "VConsole已启用 - 环境:",
        isProduction ? "生产环境" : "开发环境"
      );
    })
    .catch((err) => {
      console.warn("VConsole加载失败:", err);
    });
}
// #endif

export function createApp() {
  const app = createSSRApp(App);
  return {
    app,
  };
}
// #endif
