<template>
	<view v-if="isShow" class="bm-wrapper" @click.stop="closeMessage">
		<!-- 白色区域 -->
		<view class="bm-main" @click.stop>
			<!-- 标题 -->
			<view class="bm-title">
				<view>
					{{ title }}
				</view>
				<view>
					<uni-icons type="closeempty" @click="closeMessage"></uni-icons>
				</view>
			</view>
			<!-- 内容区 -->
			<view>
				<slot></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		props: {
			isShow: {
				type: Boolean,
				default: false
			},
			title: {
				type: String,
				default: 'test'
			}
		},
		methods: {
			closeMessage() {
				console.log('#')
				this.$emit('closeMsg')
			}
		}
	}
</script>

<style>
	.bm-wrapper {
		height: 100vh;
		width: 100vw;
		position: fixed;
		z-index: 9999;
		top: 0;
		left: 0;
		background-color: rgba(0, 0, 0, 0.2);
		display: flex;
		align-items: flex-end;
	}
	.bm-main {
		background-color: white;
		width: 100vw;
		border-radius: 8px 8px 0 0;
		padding: 15px 12px;
		box-sizing: border-box;
	}
	.bm-title {
		margin-bottom: 10px;
		display: flex;
		justify-content: space-between;
		font-weight: bolder;
	}
</style>