<template>
	<view>
		<image src="@/static/wecome/wecome.png" style="width: 100vw; height: 100vh;" @click="toMy"></image>


		<!-- <button v-if="phone == ''" open-type="getPhoneNumber" class="getphone-button"
				@getphonenumber="getPhoneNumber">点击授权获取手机号</button> -->

	</view>
</template>

<script>
	import {
		ifGivePage,
		updUser,
		showInformations,
		getPhoneNoInfo,
		userPhoneLogin,
		checkUserInvitation
	} from '@/api/user.js'

	export default {
		data() {
			return {

			}
		},
		methods: {
			toMy() {
				uni.switchTab({
					url: '/pages/my/my'
				})
				// if (uni.getStorageSync("token") != '' && uni.getStorageSync("token") != null && uni.getStorageSync(
				// 		"token") != undefined) {
				// 	uni.switchTab({
				// 		url: '/pages/my/my'
				// 	})
				// }
				// return
			},
			// getPhoneNumber(e) {
			// 	let that = this;
			// 	console.log(e)
			// 	//console.log("3 缓存的用户信息",  uni.getStorageSync("sessionUser"));
			// 	if (e.detail.errMsg == "getPhoneNumber:ok") { // 用户允许或去手机号
			// 		//获取到手机号码
			// 		let params = {
			// 			code: e.detail.code,
			// 			// encryptedData: e.detail.encryptedData,
			// 			// ivStr: e.detail.iv
			// 		};
			// 		console.log(params)
			// 		//调用后端接口getPhoneNoInfo方法
			// 		getPhoneNoInfo(params).then(res => {
			// 			if (res.data.code != 200) {
			// 				uni.showModal({
			// 					title: "登录失败"
			// 				})
			// 				return
			// 			}
			// 			//存入Storage
			// 			// uni.setStorageSync("sessionPhone",result.phoneNumber);
			// 			that.phone = res.data.data
			// 			// 调用登录接口
			// 			let login_data = {
			// 				phone: that.phone
			// 			}
			// 			userPhoneLogin(login_data).then(res => {
			// 				if (res.data.code != 200) {
			// 					uni.showToast({
			// 						icon: "error",
			// 						title: "登录失败"
			// 					})
			// 					return
			// 				}
			// 				console.log(res)

			// 				uni.setStorageSync("token", res.data.data.token)
			// 				uni.setStorageSync("userId", res.data.data.userId)
			// 				uni.setStorageSync("phone", res.data.data.phone)

			// 				let invitation = uni.getStorageSync("invitationCode")
			// 				if (invitation == null || invitation == undefined || invitation == "") {
			// 					uni.switchTab({
			// 						url: "/pages/my/my"
			// 					})
			// 				} else {
			// 					// 调用接口，获取用户信息，并绑定code
			// 					let useData = {
			// 						userId: res.data.data.userId,
			// 						invitation: invitation
			// 					}

			// 					console.log("开始发送检查请求")
			// 					checkUserInvitation(useData).then(res => {
			// 						console.log(res)
			// 						uni.switchTab({
			// 							url: "/pages/my/my"
			// 						})
			// 					})
			// 				}

			// 			})
			// 			console.log(that.phone)
			// 		})
			// 	}
			// },

		}
	}
</script>

<style>

</style>