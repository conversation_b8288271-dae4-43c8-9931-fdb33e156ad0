{
	"pages": [
		//pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/unifiedLogin/unifiedLogin",
			"style": {
				"navigationBarTitleText": "登录",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/wecome/wecome",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "uni-app"
			}
		},
		{
			"path": "pages/equities/equities",
			"style": {
				"navigationBarTitleText": "ETC商城"
			}
		},
		{
			"path": "pages/ETCService/ETCService",
			"style": {
				"navigationBarTitleText": "ETC服务"
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "登录",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#F4F4F4"
			}
		},
		{
			"path": "pages/setUserInfo/setUserInfo",
			"style": {
				"navigationBarTitleText": "个人信息",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/service/service",
			"style": {
				"navigationBarTitleText": "客服",
				"enablePullDownRefresh": false,
				"app-plus": {
					"softinputmode": "adjustResize"
				}
			}
		},
		{
			"path": "pages/myOrder/myOrder",
			"style": {
				"navigationBarTitleText": "我的订单",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/buy/buy",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#3E80FE",
				"navigationBarTextStyle": "white"
			}
		},
		{
			"path": "pages/webPage/webPage",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/goodInfo/goodInfo",
			"style": {
				"navigationBarTitleText": "商品信息",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/flOrder/flOrder",
			"style": {
				"navigationBarTitleText": "权益订单",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/bindingCarId/bindingCarId",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/withdrawal/withdrawal",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/credit/credit",
			"style": {
				"navigationBarTitleText": "话费快充"
			}
		},
		{
			"path": "pages/credit_warn/credit_warn",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/credit_orders/credit_orders",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/my/etcHandle",
			"style": {
				"navigationBarTitleText": "ETC办理"
			}
		},
		{
			"path": "pages/goodsfilter/goodsList",
			"style": {
				"navigationBarTitleText": "自营商城"
			}
		},
		{
			"path": "pages/goodsfilter/goodsDetails",
			"style": {
				"navigationBarTitleText": "商品详情"
			}
		},
		{
			"path": "pages/selfOrderBuy/selfOrderBuy",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/selfOrderBuy/addressList",
			"style": {
				"navigationBarTitleText": "收货人信息"
			}
		},
		{
			"path": "pages/selfOrderBuy/editAddress",
			"style": {
				"navigationBarTitleText": "编辑收货信息"
			}
		},
		{
			"path": "pages/proxyLogin/proxyLogin",
			"style": {
				"navigationBarTitleText": "代理端登录",
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/proxyHome/proxyHome",
			"style": {
				"navigationBarTitleText": "代理端",
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/proxyIndex/index",
			"style": {
				"navigationBarTitleText": "代理端首页",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/proxyRegister/register",
			"style": {
				"navigationBarTitleText": "代理端注册",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/proxySetUserInfo/setUserInfo",
			"style": {
				"navigationBarTitleText": "代理端个人信息",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/proxyUserList/userList",
			"style": {
				"navigationBarTitleText": "用户列表",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/proxyDevelopment/development",
			"style": {
				"navigationBarTitleText": "发展成员",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/proxyPerformance/performance",
			"style": {
				"navigationBarTitleText": "业绩管理",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}
		},
		{
			"path": "pages/proxyProxyPerformance/proxyPerformance",
			"style": {
				"navigationBarTitleText": "代理业绩",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/proxyOrderList/orderList",
			"style": {
				"navigationBarTitleText": "订单列表",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/proxyUserProtocol/userProtocol",
			"style": {
				"navigationBarTitleText": "用户协议",
				"enablePullDownRefresh": false
			}
		}
	],
	"tabBar": {
		"color": "#828282",
		"selectedColor": "#387CF4",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/my/my",
				"iconPath": "static/tab/index.png",
				"selectedIconPath": "static/tab/index_check.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/goodsfilter/goodsList",
				"iconPath": "static/tab/equities.png",
				"selectedIconPath": "static/tab/equities_check.png",
				"text": "ETC自营商品"
			}
			// }, {
			// 	"pagePath": "pages/service/service",
			// 	"iconPath": "static/tab/my.png",
			// 	"selectedIconPath": "static/tab/my_check.png",
			// 	"text": "客服"
			// }
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {},
	"easycom": {
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
	}
}