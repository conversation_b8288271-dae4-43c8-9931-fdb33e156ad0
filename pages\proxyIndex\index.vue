<template>
  <view class="bkcolor">
    <view class="index-top-wrap">
      <!-- 顶部功能区 -->
      <view class="top-functions">
        <!-- 切换到用户端 -->
        <view class="function-btn" @click="switchToUser">
          <view class="btn-circle">
            <uni-icons type="person" color="#4089fb" size="16"></uni-icons>
          </view>
          <text class="btn-text">用户端</text>
        </view>

        <!-- 扫一扫 -->
        <view class="function-btn" @click="openScan">
          <view class="btn-circle">
            <uni-icons type="scan" color="#4089fb" size="16"></uni-icons>
          </view>
          <text class="btn-text">扫一扫</text>
        </view>
      </view>
      <view class="user_info_box">
        <view>
          <image
            v-if="userInfo.headPic === ''"
            src="@/static/index/photo.png"
            style="
              height: 20vw;
              width: 20vw;
              border-radius: 45px;
              margin-left: 50px;
            "
          ></image>
          <image
            v-else
            :src="userInfo.headPic"
            style="
              height: 20vw;
              width: 20vw;
              border-radius: 45px;
              margin-left: 50px;
            "
          >
          </image>
        </view>
        <view
          style="
            color: white;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 60px;
            margin-left: 15px;
            flex: 1;
          "
          v-if="isLogin"
        >
          <view
            @click="toUserInfoSet"
            style="display: flex; align-items: center; margin-bottom: 2px"
          >
            <view style="font-size: 16px; font-weight: 600">{{
              displayUserName
            }}</view>
            <uni-icons
              type="right"
              size="14"
              color="white"
              style="margin-left: 5px"
            ></uni-icons>
          </view>
          <view style="font-size: 12px; opacity: 0.9; margin-bottom: 2px">
            手机号：{{ formatPhone(userInfo.phone) }}
          </view>
          <view style="display: flex; align-items: center; gap: 8px">
            <view
              style="
                font-size: 13px;
                background: rgba(255, 255, 255, 0.2);
                padding: 2px 8px;
                border-radius: 10px;
              "
            >
              {{ rankDict[userInfo.adminRabk] || "代理商" }}
            </view>
            <view style="font-size: 12px; opacity: 0.8">
              积分：{{ userInfo.integral || 0 }}
            </view>
          </view>
        </view>
        <view
          style="
            color: white;
            font-size: 17px;
            font-weight: 500;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 50px;
            margin-left: 17px;
          "
          v-else
        >
          <view
            style="
              display: flex;
              margin-left: 25px;
              font-size: 14px;
              color: white;
              font-weight: 500;
              font-size: 16px;
            "
            @click="toLogin"
          >
            登录
            <view>
              <uni-icons type="right" color="white" size="15"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 第二排 -->
    <view class="wd index-second">
      <view
        v-for="(data, index) in navbar"
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 25%;
        "
        @click="toUrl(data.url)"
      >
        <view>
          <image :src="data.icon" style="height: 30px; width: 30px"></image>
        </view>
        <view style="margin-top: 10px">{{ data.text }}</view>
      </view>
      <view
        @click="toProxyPerformance"
        style="
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 25%;
        "
      >
        <view>
          <image
            src="https://zk.yaoxuankeji.club:8199/images/2024/10/10/7634546c982b4d20b8bc0b0a2e491f61.png"
            style="height: 30px; width: 30px"
          ></image>
        </view>
        <view style="margin-top: 10px">个人业绩</view>
      </view>
    </view>
  </view>
</template>

<script>
import { isLogin, isProxyLogin } from "@/utils/auth.js";
import { getDateByDay } from "@/utils/dateUtil.js";
import { mapGetters } from "vuex";
import { checkCurrentPageAccess } from "@/utils/routeGuard.js";
import { logout } from "@/utils/logout.js";
const navbar = [
  {
    text: "用户列表",
    icon: "https://zk.yaoxuankeji.club:8199/images/2024/10/10/d2a8e7ba3a694b9fa4017702be5a3fda.png",
    url: "/pages/proxyUserList/userList",
  },
  {
    text: "业绩管理",
    icon: "https://zk.yaoxuankeji.club:8199/images/2024/10/10/1cc328e92b7746018aa0063369945664.png",
    url: "/pages/proxyPerformance/performance",
  },
  {
    text: "发展成员",
    icon: "https://zk.yaoxuankeji.club:8199/images/2024/10/10/e65008a16f5e48c1bb1184588dee3a3c.png",
    url: "/pages/proxyDevelopment/development",
  },
];
const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      topBarHeight: 0,
      isLogin: isLogin(),
      isProxyLogin: isProxyLogin(),
      navbar,
      rankDict: ["", "1级代理", "2级代理", "总代理"],
    };
  },
  onLoad(options) {
    // 检查页面访问权限
    if (!checkCurrentPageAccess()) {
      return; // 如果无权限访问，路由守卫会自动重定向
    }

    // 检查登录状态（确保是代理端用户）
    if (isLogin() && !isProxyLogin()) {
      uni.showToast({
        title: "普通用户请使用用户端页面",
        icon: "none",
        duration: 2000,
      });
      setTimeout(() => {
        uni.reLaunch({
          url: "/pages/my/my",
        });
      }, 2000);
      return;
    }

    this.topBarHeight = this.tabbarHeigth =
      uni.getSystemInfoSync().statusBarHeight;
  },
  methods: {
    openScan() {
      uni.scanCode({
        success({ result }) {
          console.log(result);
          const data = JSON.parse(result);
          if (
            data.adminRabk === undefined ||
            data.myInvitationCode === undefined
          ) {
            uni.showToast({
              title: "二维码有误",
              icon: "error",
            });
            return;
          }
          uni.navigateTo({
            url: "/pages/proxyRegister/register?data=" + result,
          });
        },
      });
    },
    toUserInfoSet() {
      uni.navigateTo({
        url: "/pages/proxySetUserInfo/setUserInfo",
      });
    },
    toLogin() {
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
    },
    toUrl(url) {
      uni.navigateTo({
        url,
      });
    },
    toProxyPerformance() {
      const data = {
        proxy: this.userInfo,
        checkNav: 0,
        range: [
          toLocaleDateString(getDateByDay(0)),
          toLocaleDateString(getDateByDay(0)),
        ],
      };
      uni.navigateTo({
        url:
          "/pages/proxyProxyPerformance/proxyPerformance?data=" +
          JSON.stringify(data),
      });
    },
    // 格式化手机号，中间4位用*号隐藏
    formatPhone(phone) {
      if (!phone || phone.length !== 11) {
        return phone || "";
      }
      return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    },

    // 切换到用户端
    switchToUser() {
      uni.showModal({
        title: "切换到用户端",
        content: "将退出代理端登录并切换到用户端登录页面，是否继续？",
        confirmText: "继续",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            // 用户确认，退出登录并跳转到统一登录页面
            logout({
              showMessage: false,
            });

            // 延迟一点时间再跳转，确保登出完成
            setTimeout(() => {
              uni.reLaunch({
                url: "/pages/unifiedLogin/unifiedLogin",
              });
            }, 500);
          }
        },
      });
    },
  },
  onShow() {
    this.isLogin = isLogin();
    this.isProxyLogin = isProxyLogin();

    // 检查代理端登录状态
    if (!this.isLogin || !this.isProxyLogin) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }

    if (this.isLogin) {
      this.$store.dispatch("proxyUser/getUserInfo");
    }
  },
  computed: {
    ...mapGetters("proxyUser", ["userInfo"]),
    // 根据优先级显示用户名：realName > userName > phone
    displayUserName() {
      // 确保userInfo存在且不为空对象
      if (!this.userInfo || Object.keys(this.userInfo).length === 0) {
        return "加载中...";
      }

      if (
        this.userInfo.realName &&
        this.userInfo.realName !== null &&
        this.userInfo.realName.trim() !== "" &&
        this.userInfo.realName !== "null"
      ) {
        return this.userInfo.realName;
      }

      if (
        this.userInfo.userName &&
        this.userInfo.userName !== null &&
        this.userInfo.userName.trim() !== "" &&
        this.userInfo.userName !== "null"
      ) {
        return this.userInfo.userName;
      }

      if (this.userInfo.phone) {
        return this.userInfo.phone;
      }

      return "未设置昵称";
    },
  },
};
</script>

<style>
.index-top-wrap {
  padding-top: 34vw;
  height: 38vw;
  background-image: url("https://zk.yaoxuankeji.club:8199/images/2024/10/10/397c0d64ff8d463ab9355fbc0c8865c7.png");
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
}

/* 顶部功能区样式 */
.top-functions {
  position: absolute;
  top: 60px;
  left: 24px;
  display: flex;
  gap: 20px;
}

.function-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-circle {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.btn-circle:active {
  transform: scale(0.95);
}

.btn-text {
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin-top: 4px;
}

.index-second {
  width: 90%;
  margin: 0 auto;
  background-color: white;
  margin-top: 20px;
  border-radius: 5px;
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
}

.index-scan {
  background-color: rgba(255, 255, 255, 0.6);
  height: 35px;
  width: 35px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 30px;
}

.user_info_box {
  width: 90%;
  height: 35vw;
  margin: 0 auto;
  display: flex;
  align-items: center;
  background-image: url("https://zk.yaoxuankeji.club:8199/images/2024/10/10/208cc1d6ee3e4415acbfa8500d1d0d47.png");
  background-size: 100%;
  background-repeat: no-repeat;
}
</style>
