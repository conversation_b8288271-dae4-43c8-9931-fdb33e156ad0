(function(){function kt(c,h,y,b){var R=Ge(Ce(1&b?c.prototype:c),h,y);return 2&b&&typeof R=="function"?function(J){return R.apply(y,J)}:R}function Ge(){return Ge=typeof Reflect!="undefined"&&Reflect.get?Reflect.get.bind():function(c,h,y){var b=Ot(c,h);if(b){var R=Object.getOwnPropertyDescriptor(b,h);return R.get?R.get.call(arguments.length<3?c:y):R.value}},Ge.apply(null,arguments)}function Ot(c,h){for(;!{}.hasOwnProperty.call(c,h)&&(c=Ce(c))!==null;);return c}function st(c,h){var y=Object.keys(c);if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(c);h&&(b=b.filter(function(R){return Object.getOwnPropertyDescriptor(c,R).enumerable})),y.push.apply(y,b)}return y}function M(c){for(var h=1;h<arguments.length;h++){var y=arguments[h]!=null?arguments[h]:{};h%2?st(Object(y),!0).forEach(function(b){a(c,b,y[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(y)):st(Object(y)).forEach(function(b){Object.defineProperty(c,b,Object.getOwnPropertyDescriptor(y,b))})}return c}function it(c,h,y,b,R,J,B){try{var $=c[J](B),U=$.value}catch(d){return void y(d)}$.done?h(U):Promise.resolve(U).then(b,R)}function ot(c){return function(){var h=this,y=arguments;return new Promise(function(b,R){var J=c.apply(h,y);function B(U){it(J,b,R,B,$,"next",U)}function $(U){it(J,b,R,B,$,"throw",U)}B(void 0)})}}function Ee(){"use strict";Ee=function(){return h};var c,h={},y=Object.prototype,b=y.hasOwnProperty,R=Object.defineProperty||function(m,f,g){m[f]=g.value},J=typeof Symbol=="function"?Symbol:{},B=J.iterator||"@@iterator",$=J.asyncIterator||"@@asyncIterator",U=J.toStringTag||"@@toStringTag";function d(m,f,g){return Object.defineProperty(m,f,{value:g,enumerable:!0,configurable:!0,writable:!0}),m[f]}try{d({},"")}catch(m){d=function(g,C,O){return g[C]=O}}function te(m,f,g,C){var O=f&&f.prototype instanceof l?f:l,S=Object.create(O.prototype),F=new Se(C||[]);return R(S,"_invoke",{value:K(m,g,F)}),S}function q(m,f,g){try{return{type:"normal",arg:m.call(f,g)}}catch(C){return{type:"throw",arg:C}}}h.wrap=te;var de="suspendedStart",ne="suspendedYield",oe="executing",X="completed",Z={};function l(){}function ae(){}function G(){}var ve={};d(ve,B,function(){return this});var re=Object.getPrototypeOf,ge=re&&re(re(Ie([])));ge&&ge!==y&&b.call(ge,B)&&(ve=ge);var pe=G.prototype=l.prototype=Object.create(ve);function D(m){["next","throw","return"].forEach(function(f){d(m,f,function(g){return this._invoke(f,g)})})}function he(m,f){function g(O,S,F,H){var Q=q(m[O],m,S);if(Q.type!=="throw"){var ue=Q.arg,se=ue.value;return se&&Y(se)=="object"&&b.call(se,"__await")?f.resolve(se.__await).then(function(ce){g("next",ce,F,H)},function(ce){g("throw",ce,F,H)}):f.resolve(se).then(function(ce){ue.value=ce,F(ue)},function(ce){return g("throw",ce,F,H)})}H(Q.arg)}var C;R(this,"_invoke",{value:function(S,F){function H(){return new f(function(Q,ue){g(S,F,Q,ue)})}return C=C?C.then(H,H):H()}})}function K(m,f,g){var C=de;return function(O,S){if(C===oe)throw Error("Generator is already running");if(C===X){if(O==="throw")throw S;return{value:c,done:!0}}for(g.method=O,g.arg=S;;){var F=g.delegate;if(F){var H=ke(F,g);if(H){if(H===Z)continue;return H}}if(g.method==="next")g.sent=g._sent=g.arg;else if(g.method==="throw"){if(C===de)throw C=X,g.arg;g.dispatchException(g.arg)}else g.method==="return"&&g.abrupt("return",g.arg);C=oe;var Q=q(m,f,g);if(Q.type==="normal"){if(C=g.done?X:ne,Q.arg===Z)continue;return{value:Q.arg,done:g.done}}Q.type==="throw"&&(C=X,g.method="throw",g.arg=Q.arg)}}}function ke(m,f){var g=f.method,C=m.iterator[g];if(C===c)return f.delegate=null,g==="throw"&&m.iterator.return&&(f.method="return",f.arg=c,ke(m,f),f.method==="throw")||g!=="return"&&(f.method="throw",f.arg=new TypeError("The iterator does not provide a '"+g+"' method")),Z;var O=q(C,m.iterator,f.arg);if(O.type==="throw")return f.method="throw",f.arg=O.arg,f.delegate=null,Z;var S=O.arg;return S?S.done?(f[m.resultName]=S.value,f.next=m.nextLoc,f.method!=="return"&&(f.method="next",f.arg=c),f.delegate=null,Z):S:(f.method="throw",f.arg=new TypeError("iterator result is not an object"),f.delegate=null,Z)}function Pe(m){var f={tryLoc:m[0]};1 in m&&(f.catchLoc=m[1]),2 in m&&(f.finallyLoc=m[2],f.afterLoc=m[3]),this.tryEntries.push(f)}function we(m){var f=m.completion||{};f.type="normal",delete f.arg,m.completion=f}function Se(m){this.tryEntries=[{tryLoc:"root"}],m.forEach(Pe,this),this.reset(!0)}function Ie(m){if(m||m===""){var f=m[B];if(f)return f.call(m);if(typeof m.next=="function")return m;if(!isNaN(m.length)){var g=-1,C=function O(){for(;++g<m.length;)if(b.call(m,g))return O.value=m[g],O.done=!1,O;return O.value=c,O.done=!0,O};return C.next=C}}throw new TypeError(Y(m)+" is not iterable")}return ae.prototype=G,R(pe,"constructor",{value:G,configurable:!0}),R(G,"constructor",{value:ae,configurable:!0}),ae.displayName=d(G,U,"GeneratorFunction"),h.isGeneratorFunction=function(m){var f=typeof m=="function"&&m.constructor;return!!f&&(f===ae||(f.displayName||f.name)==="GeneratorFunction")},h.mark=function(m){return Object.setPrototypeOf?Object.setPrototypeOf(m,G):(m.__proto__=G,d(m,U,"GeneratorFunction")),m.prototype=Object.create(pe),m},h.awrap=function(m){return{__await:m}},D(he.prototype),d(he.prototype,$,function(){return this}),h.AsyncIterator=he,h.async=function(m,f,g,C,O){O===void 0&&(O=Promise);var S=new he(te(m,f,g,C),O);return h.isGeneratorFunction(f)?S:S.next().then(function(F){return F.done?F.value:S.next()})},D(pe),d(pe,U,"Generator"),d(pe,B,function(){return this}),d(pe,"toString",function(){return"[object Generator]"}),h.keys=function(m){var f=Object(m),g=[];for(var C in f)g.push(C);return g.reverse(),function O(){for(;g.length;){var S=g.pop();if(S in f)return O.value=S,O.done=!1,O}return O.done=!0,O}},h.values=Ie,Se.prototype={constructor:Se,reset:function(f){if(this.prev=0,this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method="next",this.arg=c,this.tryEntries.forEach(we),!f)for(var g in this)g.charAt(0)==="t"&&b.call(this,g)&&!isNaN(+g.slice(1))&&(this[g]=c)},stop:function(){this.done=!0;var f=this.tryEntries[0].completion;if(f.type==="throw")throw f.arg;return this.rval},dispatchException:function(f){if(this.done)throw f;var g=this;function C(ue,se){return F.type="throw",F.arg=f,g.next=ue,se&&(g.method="next",g.arg=c),!!se}for(var O=this.tryEntries.length-1;O>=0;--O){var S=this.tryEntries[O],F=S.completion;if(S.tryLoc==="root")return C("end");if(S.tryLoc<=this.prev){var H=b.call(S,"catchLoc"),Q=b.call(S,"finallyLoc");if(H&&Q){if(this.prev<S.catchLoc)return C(S.catchLoc,!0);if(this.prev<S.finallyLoc)return C(S.finallyLoc)}else if(H){if(this.prev<S.catchLoc)return C(S.catchLoc,!0)}else{if(!Q)throw Error("try statement without catch or finally");if(this.prev<S.finallyLoc)return C(S.finallyLoc)}}}},abrupt:function(f,g){for(var C=this.tryEntries.length-1;C>=0;--C){var O=this.tryEntries[C];if(O.tryLoc<=this.prev&&b.call(O,"finallyLoc")&&this.prev<O.finallyLoc){var S=O;break}}S&&(f==="break"||f==="continue")&&S.tryLoc<=g&&g<=S.finallyLoc&&(S=null);var F=S?S.completion:{};return F.type=f,F.arg=g,S?(this.method="next",this.next=S.finallyLoc,Z):this.complete(F)},complete:function(f,g){if(f.type==="throw")throw f.arg;return f.type==="break"||f.type==="continue"?this.next=f.arg:f.type==="return"?(this.rval=this.arg=f.arg,this.method="return",this.next="end"):f.type==="normal"&&g&&(this.next=g),Z},finish:function(f){for(var g=this.tryEntries.length-1;g>=0;--g){var C=this.tryEntries[g];if(C.finallyLoc===f)return this.complete(C.completion,C.afterLoc),we(C),Z}},catch:function(f){for(var g=this.tryEntries.length-1;g>=0;--g){var C=this.tryEntries[g];if(C.tryLoc===f){var O=C.completion;if(O.type==="throw"){var S=O.arg;we(C)}return S}}throw Error("illegal catch attempt")},delegateYield:function(f,g,C){return this.delegate={iterator:Ie(f),resultName:g,nextLoc:C},this.method==="next"&&(this.arg=c),Z}},h}function Fe(c){return Tt(c)||_t(c)||ct(c)||Vt()}function Vt(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _t(c){if(typeof Symbol!="undefined"&&c[Symbol.iterator]!=null||c["@@iterator"]!=null)return Array.from(c)}function Tt(c){if(Array.isArray(c))return He(c)}function a(c,h,y){return(h=ut(h))in c?Object.defineProperty(c,h,{value:y,enumerable:!0,configurable:!0,writable:!0}):c[h]=y,c}function at(c,h){for(var y=0;y<h.length;y++){var b=h[y];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(c,ut(b.key),b)}}function A(c,h,y){return h&&at(c.prototype,h),y&&at(c,y),Object.defineProperty(c,"prototype",{writable:!1}),c}function ut(c){var h=Pt(c,"string");return Y(h)=="symbol"?h:h+""}function Pt(c,h){if(Y(c)!="object"||!c)return c;var y=c[Symbol.toPrimitive];if(y!==void 0){var b=y.call(c,h||"default");if(Y(b)!="object")return b;throw new TypeError("@@toPrimitive must return a primitive value.")}return(h==="string"?String:Number)(c)}function k(c,h){if(!(c instanceof h))throw new TypeError("Cannot call a class as a function")}function x(c,h,y){return h=Ce(h),Rt(c,Be()?Reflect.construct(h,y||[],Ce(c).constructor):h.apply(c,y))}function Rt(c,h){if(h&&(Y(h)=="object"||typeof h=="function"))return h;if(h!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return w(c)}function w(c){if(c===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return c}function j(c,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function");c.prototype=Object.create(h&&h.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),h&&Te(c,h)}function Ae(c){var h=typeof Map=="function"?new Map:void 0;return Ae=function(b){if(b===null||!Dt(b))return b;if(typeof b!="function")throw new TypeError("Super expression must either be null or a function");if(h!==void 0){if(h.has(b))return h.get(b);h.set(b,R)}function R(){return Nt(b,arguments,Ce(this).constructor)}return R.prototype=Object.create(b.prototype,{constructor:{value:R,enumerable:!1,writable:!0,configurable:!0}}),Te(R,b)},Ae(c)}function Nt(c,h,y){if(Be())return Reflect.construct.apply(null,arguments);var b=[null];b.push.apply(b,h);var R=new(c.bind.apply(c,b));return y&&Te(R,y.prototype),R}function Be(){try{var c=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(h){}return(Be=function(){return!!c})()}function Dt(c){try{return Function.toString.call(c).indexOf("[native code]")!==-1}catch(h){return typeof c=="function"}}function Te(c,h){return Te=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(y,b){return y.__proto__=b,y},Te(c,h)}function Ce(c){return Ce=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(h){return h.__proto__||Object.getPrototypeOf(h)},Ce(c)}function Y(c){return Y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(h){return typeof h}:function(h){return h&&typeof Symbol=="function"&&h.constructor===Symbol&&h!==Symbol.prototype?"symbol":typeof h},Y(c)}function je(c,h){var y=typeof Symbol!="undefined"&&c[Symbol.iterator]||c["@@iterator"];if(!y){if(Array.isArray(c)||(y=ct(c))||h&&c&&typeof c.length=="number"){y&&(c=y);var b=0,R=function(){};return{s:R,n:function(){return b>=c.length?{done:!0}:{done:!1,value:c[b++]}},e:function(d){throw d},f:R}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var J,B=!0,$=!1;return{s:function(){y=y.call(c)},n:function(){var d=y.next();return B=d.done,d},e:function(d){$=!0,J=d},f:function(){try{B||y.return==null||y.return()}finally{if($)throw J}}}}function ct(c,h){if(c){if(typeof c=="string")return He(c,h);var y={}.toString.call(c).slice(8,-1);return y==="Object"&&c.constructor&&(y=c.constructor.name),y==="Map"||y==="Set"?Array.from(c):y==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(y)?He(c,h):void 0}}function He(c,h){(h==null||h>c.length)&&(h=c.length);for(var y=0,b=Array(h);y<h;y++)b[y]=c[y];return b}(function(){var c=Object.defineProperty,h=Object.defineProperties,y=Object.getOwnPropertyDescriptors,b=Object.getOwnPropertySymbols,R=Object.prototype.hasOwnProperty,J=Object.prototype.propertyIsEnumerable,B=function(s,t,e){return t in s?c(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e},$=function(s,t){for(var e in t||(t={}))R.call(t,e)&&B(s,e,t[e]);if(b){var n=je(b(t)),r;try{for(n.s();!(r=n.n()).done;){var e=r.value;J.call(t,e)&&B(s,e,t[e])}}catch(o){n.e(o)}finally{n.f()}}return s},U=function(s,t){return h(s,y(t))},d=function(s,t,e){return B(s,Y(t)!="symbol"?t+"":t,e),e},te=function(i){function s(t){var e;return k(this,s),e=x(this,s,[t]),e.name="DecodingError",e}return j(s,i),A(s)}(Ae(Error)),q=function(i){function s(t){var e;return k(this,s),e=x(this,s,[t]),e.name="EncodingError",e}return j(s,i),A(s)}(Ae(Error)),de=function(i){function s(t){var e;return k(this,s),e=x(this,s,[t]),e.name="GVLError",e}return j(s,i),A(s)}(Ae(Error)),ne=function(i){function s(t,e){var n,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"";return k(this,s),n=x(this,s,["invalid value ".concat(e," passed for ").concat(t," ").concat(r)]),n.name="TCModelError",n}return j(s,i),A(s)}(Ae(Error)),oe=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t){if(!/^[0-1]+$/.test(t))throw new q("Invalid bitField");var e=t.length%this.LCM;t+=e?"0".repeat(this.LCM-e):"";for(var n="",r=0;r<t.length;r+=this.BASIS)n+=this.DICT[parseInt(t.substr(r,this.BASIS),2)];return n}},{key:"decode",value:function(t){if(!/^[A-Za-z0-9\-_]+$/.test(t))throw new te("Invalidly encoded Base64URL string");for(var e="",n=0;n<t.length;n++){var r=this.REVERSE_DICT.get(t[n]).toString(2);e+="0".repeat(this.BASIS-r.length)+r}return e}}])}();d(oe,"DICT","ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"),d(oe,"REVERSE_DICT",new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]])),d(oe,"BASIS",6),d(oe,"LCM",24);var X=function(){function i(){k(this,i)}return A(i,[{key:"has",value:function(t){return X.langSet.has(t)}},{key:"parseLanguage",value:function(t){t=t.toUpperCase();var e=t.split("-")[0];if(t.length>=2&&e.length==2){if(X.langSet.has(t))return t;if(X.langSet.has(e))return e;var n=e+"-"+e;if(X.langSet.has(n))return n;var r=je(X.langSet),o;try{for(r.s();!(o=r.n()).done;){var u=o.value;if(u.indexOf(t)!==-1||u.indexOf(e)!==-1)return u}}catch(p){r.e(p)}finally{r.f()}}throw new Error("unsupported language ".concat(t))}},{key:"forEach",value:function(t){X.langSet.forEach(t)}},{key:"size",get:function(){return X.langSet.size}}])}(),Z=X;d(Z,"langSet",new Set(["AR","BG","BS","CA","CS","CY","DA","DE","EL","EN","ES","ET","EU","FI","FR","GL","HE","HR","HU","ID","IT","JA","KA","KO","LT","LV","MK","MS","MT","NL","NO","PL","PT-BR","PT-PT","RO","RU","SK","SL","SQ","SR-LATN","SR-CYRL","SV","SW","TH","TL","TR","UK","VI","ZH"]));var l=A(function i(){k(this,i)});d(l,"cmpId","cmpId"),d(l,"cmpVersion","cmpVersion"),d(l,"consentLanguage","consentLanguage"),d(l,"consentScreen","consentScreen"),d(l,"created","created"),d(l,"supportOOB","supportOOB"),d(l,"isServiceSpecific","isServiceSpecific"),d(l,"lastUpdated","lastUpdated"),d(l,"numCustomPurposes","numCustomPurposes"),d(l,"policyVersion","policyVersion"),d(l,"publisherCountryCode","publisherCountryCode"),d(l,"publisherCustomConsents","publisherCustomConsents"),d(l,"publisherCustomLegitimateInterests","publisherCustomLegitimateInterests"),d(l,"publisherLegitimateInterests","publisherLegitimateInterests"),d(l,"publisherConsents","publisherConsents"),d(l,"publisherRestrictions","publisherRestrictions"),d(l,"purposeConsents","purposeConsents"),d(l,"purposeLegitimateInterests","purposeLegitimateInterests"),d(l,"purposeOneTreatment","purposeOneTreatment"),d(l,"specialFeatureOptins","specialFeatureOptins"),d(l,"useNonStandardTexts","useNonStandardTexts"),d(l,"vendorConsents","vendorConsents"),d(l,"vendorLegitimateInterests","vendorLegitimateInterests"),d(l,"vendorListVersion","vendorListVersion"),d(l,"vendorsAllowed","vendorsAllowed"),d(l,"vendorsDisclosed","vendorsDisclosed"),d(l,"version","version");var ae=function(){function i(){k(this,i)}return A(i,[{key:"clone",value:function(){var t=this,e=new this.constructor;return Object.keys(this).forEach(function(n){var r=t.deepClone(t[n]);r!==void 0&&(e[n]=r)}),e}},{key:"deepClone",value:function(t){var e=Y(t);if(e==="number"||e==="string"||e==="boolean")return t;if(t!==null&&e==="object"){if(typeof t.clone=="function")return t.clone();if(t instanceof Date)return new Date(t.getTime());if(t[Symbol.iterator]!==void 0){var n=[],r=je(t),o;try{for(r.s();!(o=r.n()).done;){var u=o.value;n.push(this.deepClone(u))}}catch(V){r.e(V)}finally{r.f()}return t instanceof Array?n:new t.constructor(n)}else{var p={};for(var I in t)t.hasOwnProperty(I)&&(p[I]=this.deepClone(t[I]));return p}}}}])}(),G;(function(i){i[i.NOT_ALLOWED=0]="NOT_ALLOWED",i[i.REQUIRE_CONSENT=1]="REQUIRE_CONSENT",i[i.REQUIRE_LI=2]="REQUIRE_LI"})(G||(G={}));var ve=function(i){function s(t,e){var n;return k(this,s),n=x(this,s),a(w(n),"purposeId_",void 0),a(w(n),"restrictionType",void 0),t!==void 0&&(n.purposeId=t),e!==void 0&&(n.restrictionType=e),n}return j(s,i),A(s,[{key:"hash",get:function(){if(!this.isValid())throw new Error("cannot hash invalid PurposeRestriction");return"".concat(this.purposeId).concat(ve.hashSeparator).concat(this.restrictionType)}},{key:"purposeId",get:function(){return this.purposeId_},set:function(e){this.purposeId_=e}},{key:"isValid",value:function(){return Number.isInteger(this.purposeId)&&this.purposeId>0&&(this.restrictionType===G.NOT_ALLOWED||this.restrictionType===G.REQUIRE_CONSENT||this.restrictionType===G.REQUIRE_LI)}},{key:"isSameAs",value:function(e){return this.purposeId===e.purposeId&&this.restrictionType===e.restrictionType}}],[{key:"unHash",value:function(e){var n=e.split(this.hashSeparator),r=new ve;if(n.length!==2)throw new ne("hash",e);return r.purposeId=parseInt(n[0],10),r.restrictionType=parseInt(n[1],10),r}}])}(ae),re=ve;d(re,"hashSeparator","-");var ge=function(i){function s(){var t;k(this,s);for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t=x(this,s,[].concat(n)),a(t,"bitLength",0),a(t,"map",new Map),a(t,"gvl_",void 0),t}return j(s,i),A(s,[{key:"has",value:function(e){return this.map.has(e)}},{key:"isOkToHave",value:function(e,n,r){var o,u=!0;if((o=this.gvl)!==null&&o!==void 0&&o.vendors){var p=this.gvl.vendors[r];if(p)if(e===G.NOT_ALLOWED)u=p.legIntPurposes.includes(n)||p.purposes.includes(n);else if(p.flexiblePurposes.length)switch(e){case G.REQUIRE_CONSENT:u=p.flexiblePurposes.includes(n)&&p.legIntPurposes.includes(n);break;case G.REQUIRE_LI:u=p.flexiblePurposes.includes(n)&&p.purposes.includes(n);break}else u=!1;else u=!1}return u}},{key:"add",value:function(e,n){if(this.isOkToHave(n.restrictionType,n.purposeId,e)){var r=n.hash;this.has(r)||(this.map.set(r,new Set),this.bitLength=0),this.map.get(r).add(e)}}},{key:"restrictPurposeToLegalBasis",value:function(e){var n=Array.from(this.gvl.vendorIds),r=e.hash,o=n[n.length-1],u=Fe(Array(o).keys()).map(function(I){return I+1});if(!this.has(r))this.map.set(r,new Set(u)),this.bitLength=0;else for(var p=1;p<=o;p++)this.map.get(r).add(p)}},{key:"getVendors",value:function(e){var n=[];if(e){var r=e.hash;this.has(r)&&(n=Array.from(this.map.get(r)))}else{var o=new Set;this.map.forEach(function(u){u.forEach(function(p){o.add(p)})}),n=Array.from(o)}return n.sort(function(u,p){return u-p})}},{key:"getRestrictionType",value:function(e,n){var r;return this.getRestrictions(e).forEach(function(o){o.purposeId===n&&(r===void 0||r>o.restrictionType)&&(r=o.restrictionType)}),r}},{key:"vendorHasRestriction",value:function(e,n){for(var r=!1,o=this.getRestrictions(e),u=0;u<o.length&&!r;u++)r=n.isSameAs(o[u]);return r}},{key:"getMaxVendorId",value:function(){var e=0;return this.map.forEach(function(n){e=Math.max(Array.from(n)[n.size-1],e)}),e}},{key:"getRestrictions",value:function(e){var n=[];return this.map.forEach(function(r,o){e?r.has(e)&&n.push(re.unHash(o)):n.push(re.unHash(o))}),n}},{key:"getPurposes",value:function(){var e=new Set;return this.map.forEach(function(n,r){e.add(re.unHash(r).purposeId)}),Array.from(e)}},{key:"remove",value:function(e,n){var r=n.hash,o=this.map.get(r);o&&(o.delete(e),o.size==0&&(this.map.delete(r),this.bitLength=0))}},{key:"gvl",get:function(){return this.gvl_},set:function(e){var n=this;this.gvl_||(this.gvl_=e,this.map.forEach(function(r,o){var u=re.unHash(o);Array.from(r).forEach(function(p){n.isOkToHave(u.restrictionType,u.purposeId,p)||r.delete(p)})}))}},{key:"isEmpty",value:function(){return this.map.size===0}},{key:"numRestrictions",get:function(){return this.map.size}}])}(ae),pe;(function(i){i.COOKIE="cookie",i.WEB="web",i.APP="app"})(pe||(pe={}));var D;(function(i){i.CORE="core",i.VENDORS_DISCLOSED="vendorsDisclosed",i.VENDORS_ALLOWED="vendorsAllowed",i.PUBLISHER_TC="publisherTC"})(D||(D={}));var he=A(function i(){k(this,i)});d(he,"ID_TO_KEY",[D.CORE,D.VENDORS_DISCLOSED,D.VENDORS_ALLOWED,D.PUBLISHER_TC]),d(he,"KEY_TO_ID",a(a(a(a({},D.CORE,0),D.VENDORS_DISCLOSED,1),D.VENDORS_ALLOWED,2),D.PUBLISHER_TC,3));var K=function(i){function s(){var t;k(this,s);for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t=x(this,s,[].concat(n)),a(t,"bitLength",0),a(t,"maxId_",0),a(t,"set_",new Set),t}return j(s,i),A(s,[{key:Symbol.iterator,value:Ee().mark(function t(){var e;return Ee().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:e=1;case 1:if(!(e<=this.maxId)){r.next=7;break}return r.next=4,[e,this.has(e)];case 4:e++,r.next=1;break;case 7:case"end":return r.stop()}},t,this)})},{key:"values",value:function(){return this.set_.values()}},{key:"maxId",get:function(){return this.maxId_}},{key:"has",value:function(e){return this.set_.has(e)}},{key:"unset",value:function(e){var n=this;Array.isArray(e)?e.forEach(function(r){return n.unset(r)}):Y(e)=="object"?this.unset(Object.keys(e).map(function(r){return Number(r)})):(this.set_.delete(Number(e)),this.bitLength=0,e===this.maxId&&(this.maxId_=0,this.set_.forEach(function(r){n.maxId_=Math.max(n.maxId,r)})))}},{key:"isIntMap",value:function(e){var n=this,r=Y(e)=="object";return r=r&&Object.keys(e).every(function(o){var u=Number.isInteger(parseInt(o,10));return u=u&&n.isValidNumber(e[o].id),u=u&&e[o].name!==void 0,u}),r}},{key:"isValidNumber",value:function(e){return parseInt(e,10)>0}},{key:"isSet",value:function(e){var n=!1;return e instanceof Set&&(n=Array.from(e).every(this.isValidNumber)),n}},{key:"set",value:function(e){var n=this;if(Array.isArray(e))e.forEach(function(r){return n.set(r)});else if(this.isSet(e))this.set(Array.from(e));else if(this.isIntMap(e))this.set(Object.keys(e).map(function(r){return Number(r)}));else if(this.isValidNumber(e))this.set_.add(e),this.maxId_=Math.max(this.maxId,e),this.bitLength=0;else throw new ne("set()",e,"must be positive integer array, positive integer, Set<number>, or IntMap")}},{key:"empty",value:function(){this.set_=new Set}},{key:"forEach",value:function(e){for(var n=1;n<=this.maxId;n++)e(this.has(n),n)}},{key:"size",get:function(){return this.set_.size}},{key:"setAll",value:function(e){this.set(e)}}])}(ae),ke,Pe,we,Se,Ie,m,f,g,C,O,S,F,H,Q,ue,se,ce,Qe,v=A(function i(){k(this,i)});ke=l.cmpId,Pe=l.cmpVersion,we=l.consentLanguage,Se=l.consentScreen,Ie=l.created,m=l.isServiceSpecific,f=l.lastUpdated,g=l.policyVersion,C=l.publisherCountryCode,O=l.publisherLegitimateInterests,S=l.publisherConsents,F=l.purposeConsents,H=l.purposeLegitimateInterests,Q=l.purposeOneTreatment,ue=l.specialFeatureOptins,se=l.useNonStandardTexts,ce=l.vendorListVersion,Qe=l.version,d(v,ke,12),d(v,Pe,12),d(v,we,12),d(v,Se,6),d(v,Ie,36),d(v,m,1),d(v,f,36),d(v,g,6),d(v,C,12),d(v,O,24),d(v,S,24),d(v,F,24),d(v,H,24),d(v,Q,1),d(v,ue,12),d(v,se,1),d(v,ce,12),d(v,Qe,6),d(v,"anyBoolean",1),d(v,"encodingType",1),d(v,"maxId",16),d(v,"numCustomPurposes",6),d(v,"numEntries",12),d(v,"numRestrictions",12),d(v,"purposeId",6),d(v,"restrictionType",2),d(v,"segmentType",3),d(v,"singleOrRange",1),d(v,"vendorId",16);var le=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t){return String(Number(t))}},{key:"decode",value:function(t){return t==="1"}}])}(),P=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t,e){var n;if(typeof t=="string"&&(t=parseInt(t,10)),n=t.toString(2),n.length>e||t<0)throw new q("".concat(t," too large to encode into ").concat(e));return n.length<e&&(n="0".repeat(e-n.length)+n),n}},{key:"decode",value:function(t,e){if(e!==t.length)throw new te("invalid bit length");return parseInt(t,2)}}])}(),We=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t,e){return P.encode(Math.round(t.getTime()/100),e)}},{key:"decode",value:function(t,e){if(e!==t.length)throw new te("invalid bit length");var n=new Date;return n.setTime(P.decode(t,e)*100),n}}])}(),fe=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t,e){for(var n="",r=1;r<=e;r++)n+=le.encode(t.has(r));return n}},{key:"decode",value:function(t,e){if(t.length!==e)throw new te("bitfield encoding length mismatch");for(var n=new K,r=1;r<=e;r++)le.decode(t[r-1])&&n.set(r);return n.bitLength=t.length,n}}])}(),ze=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t,e){t=t.toUpperCase();var n=65,r=t.charCodeAt(0)-n,o=t.charCodeAt(1)-n;if(r<0||r>25||o<0||o>25)throw new q("invalid language code: ".concat(t));if(e%2===1)throw new q("numBits must be even, ".concat(e," is not valid"));e=e/2;var u=P.encode(r,e),p=P.encode(o,e);return u+p}},{key:"decode",value:function(t,e){var n;if(e===t.length&&!(t.length%2)){var r=t.length/2,o=P.decode(t.slice(0,r),r)+65,u=P.decode(t.slice(r),r)+65;n=String.fromCharCode(o)+String.fromCharCode(u)}else throw new te("invalid bit length for language");return n}}])}(),lt=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t){var e=P.encode(t.numRestrictions,v.numRestrictions);if(!t.isEmpty()){var n=function(o,u){for(var p=o+1;p<=u;p++)if(t.gvl.vendorIds.has(p))return p;return o};t.getRestrictions().forEach(function(r){e+=P.encode(r.purposeId,v.purposeId),e+=P.encode(r.restrictionType,v.restrictionType);for(var o=t.getVendors(r),u=o.length,p=0,I=0,V="",_=0;_<u;_++){var L=o[_];if(I===0&&(p++,I=L),_===u-1||o[_+1]>n(L,o[u-1])){var N=L!==I;V+=le.encode(N),V+=P.encode(I,v.vendorId),N&&(V+=P.encode(L,v.vendorId)),I=0}}e+=P.encode(p,v.numEntries),e+=V})}return e}},{key:"decode",value:function(t){var e=0,n=new ge,r=P.decode(t.substr(e,v.numRestrictions),v.numRestrictions);e+=v.numRestrictions;for(var o=0;o<r;o++){var u=P.decode(t.substr(e,v.purposeId),v.purposeId);e+=v.purposeId;var p=P.decode(t.substr(e,v.restrictionType),v.restrictionType);e+=v.restrictionType;var I=new re(u,p),V=P.decode(t.substr(e,v.numEntries),v.numEntries);e+=v.numEntries;for(var _=0;_<V;_++){var L=le.decode(t.substr(e,v.anyBoolean));e+=v.anyBoolean;var N=P.decode(t.substr(e,v.vendorId),v.vendorId);if(e+=v.vendorId,L){var ee=P.decode(t.substr(e,v.vendorId),v.vendorId);if(e+=v.vendorId,ee<N)throw new te("Invalid RangeEntry: endVendorId ".concat(ee," is less than ").concat(N));for(var Le=N;Le<=ee;Le++)n.add(Le,I)}else n.add(N,I)}}return n.bitLength=e,n}}])}(),Oe;(function(i){i[i.FIELD=0]="FIELD",i[i.RANGE=1]="RANGE"})(Oe||(Oe={}));var Ve=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t){var e=[],n=[],r=P.encode(t.maxId,v.maxId),o="",u,p=v.maxId+v.encodingType,I=p+t.maxId,V=v.vendorId*2+v.singleOrRange+v.numEntries,_=p+v.numEntries;return t.forEach(function(L,N){o+=le.encode(L),u=t.maxId>V&&_<I,u&&L&&(t.has(N+1)?n.length===0&&(n.push(N),_+=v.singleOrRange,_+=v.vendorId):(n.push(N),_+=v.vendorId,e.push(n),n=[]))}),u?(r+=String(Oe.RANGE),r+=this.buildRangeEncoding(e)):(r+=String(Oe.FIELD),r+=o),r}},{key:"decode",value:function(t,e){var n,r=0,o=P.decode(t.substr(r,v.maxId),v.maxId);r+=v.maxId;var u=P.decode(t.charAt(r),v.encodingType);if(r+=v.encodingType,u===Oe.RANGE){if(n=new K,e===1){if(t.substr(r,1)==="1")throw new te("Unable to decode default consent=1");r++}var p=P.decode(t.substr(r,v.numEntries),v.numEntries);r+=v.numEntries;for(var I=0;I<p;I++){var V=le.decode(t.charAt(r));r+=v.singleOrRange;var _=P.decode(t.substr(r,v.vendorId),v.vendorId);if(r+=v.vendorId,V){var L=P.decode(t.substr(r,v.vendorId),v.vendorId);r+=v.vendorId;for(var N=_;N<=L;N++)n.set(N)}else n.set(_)}}else{var ee=t.substr(r,o);r+=o,n=fe.decode(ee,o)}return n.bitLength=r,n}},{key:"buildRangeEncoding",value:function(t){var e=t.length,n=P.encode(e,v.numEntries);return t.forEach(function(r){var o=r.length===1;n+=le.encode(!o),n+=P.encode(r[0],v.vendorId),o||(n+=P.encode(r[1],v.vendorId))}),n}}])}();function Je(){var i;return i={},a(a(a(a(a(a(a(a(a(a(i,l.version,P),l.created,We),l.lastUpdated,We),l.cmpId,P),l.cmpVersion,P),l.consentScreen,P),l.consentLanguage,ze),l.vendorListVersion,P),l.policyVersion,P),l.isServiceSpecific,le),a(a(a(a(a(a(a(a(a(a(i,l.useNonStandardTexts,le),l.specialFeatureOptins,fe),l.purposeConsents,fe),l.purposeLegitimateInterests,fe),l.purposeOneTreatment,le),l.publisherCountryCode,ze),l.vendorConsents,Ve),l.vendorLegitimateInterests,Ve),l.publisherRestrictions,lt),"segmentType",P),a(a(a(a(a(a(a(i,l.vendorsDisclosed,Ve),l.vendorsAllowed,Ve),l.publisherConsents,fe),l.publisherLegitimateInterests,fe),l.numCustomPurposes,P),l.publisherCustomConsents,fe),l.publisherCustomLegitimateInterests,fe)}var dt=A(function i(){k(this,i),a(this,1,a({},D.CORE,[l.version,l.created,l.lastUpdated,l.cmpId,l.cmpVersion,l.consentScreen,l.consentLanguage,l.vendorListVersion,l.purposeConsents,l.vendorConsents])),a(this,2,a(a(a(a({},D.CORE,[l.version,l.created,l.lastUpdated,l.cmpId,l.cmpVersion,l.consentScreen,l.consentLanguage,l.vendorListVersion,l.policyVersion,l.isServiceSpecific,l.useNonStandardTexts,l.specialFeatureOptins,l.purposeConsents,l.purposeLegitimateInterests,l.purposeOneTreatment,l.publisherCountryCode,l.vendorConsents,l.vendorLegitimateInterests,l.publisherRestrictions]),D.PUBLISHER_TC,[l.publisherConsents,l.publisherLegitimateInterests,l.numCustomPurposes,l.publisherCustomConsents,l.publisherCustomLegitimateInterests]),D.VENDORS_ALLOWED,[l.vendorsAllowed]),D.VENDORS_DISCLOSED,[l.vendorsDisclosed]))}),pt=A(function i(s,t){if(k(this,i),a(this,1,[D.CORE]),a(this,2,[D.CORE]),s.version===2)if(s.isServiceSpecific)this[2].push(D.PUBLISHER_TC);else{var e=!!(t&&t.isForVendors);(!e||s[l.supportOOB]===!0)&&this[2].push(D.VENDORS_DISCLOSED),e&&(s[l.supportOOB]&&s[l.vendorsAllowed].size>0&&this[2].push(D.VENDORS_ALLOWED),this[2].push(D.PUBLISHER_TC))}}),Me=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t,e){var n=this,r;try{r=this.fieldSequence[String(t.version)][e]}catch(p){throw new q("Unable to encode version: ".concat(t.version,", segment: ").concat(e))}var o="";e!==D.CORE&&(o=P.encode(he.KEY_TO_ID[e],v.segmentType));var u=Je();return r.forEach(function(p){var I=t[p],V=u[p],_=v[p];_===void 0&&n.isPublisherCustom(p)&&(_=Number(t[l.numCustomPurposes]));try{o+=V.encode(I,_)}catch(L){throw new q("Error encoding ".concat(e,"->").concat(p,": ").concat(L.message))}}),oe.encode(o)}},{key:"decode",value:function(t,e,n){var r=this,o=oe.decode(t),u=0;n===D.CORE&&(e.version=P.decode(o.substr(u,v[l.version]),v[l.version])),n!==D.CORE&&(u+=v.segmentType);var p=this.fieldSequence[String(e.version)][n],I=Je();return p.forEach(function(V){var _=I[V],L=v[V];if(L===void 0&&r.isPublisherCustom(V)&&(L=Number(e[l.numCustomPurposes])),L!==0){var N=o.substr(u,L);if(_===Ve?e[V]=_.decode(N,e.version):e[V]=_.decode(N,L),Number.isInteger(L))u+=L;else if(Number.isInteger(e[V].bitLength))u+=e[V].bitLength;else throw new te(V)}}),e}},{key:"isPublisherCustom",value:function(t){return t.indexOf("publisherCustom")===0}}])}();d(Me,"fieldSequence",new dt);var Ke=function(){function i(){k(this,i)}return A(i,null,[{key:"process",value:function(t,e){var n=t.gvl;if(!n)throw new q("Unable to encode TCModel without a GVL");if(!n.isReady)throw new q("Unable to encode TCModel tcModel.gvl.readyPromise is not resolved");t=t.clone(),t.consentLanguage=n.language.slice(0,2).toUpperCase(),(e==null?void 0:e.version)>0&&(e==null?void 0:e.version)<=this.processor.length?t.version=e.version:t.version=this.processor.length;var r=t.version-1;if(!this.processor[r])throw new q("Invalid version: ".concat(t.version));return this.processor[r](t,n)}}])}();d(Ke,"processor",[function(i){return i},function(i,s){i.publisherRestrictions.gvl=s,i.purposeLegitimateInterests.unset([1,3,4,5,6]);var t=new Map;return t.set("legIntPurposes",i.vendorLegitimateInterests),t.set("purposes",i.vendorConsents),t.forEach(function(e,n){e.forEach(function(r,o){if(r){var u=s.vendors[o];if(!u||u.deletedDate)e.unset(o);else if(u[n].length===0&&!(n==="legIntPurposes"&&u.purposes.length===0&&u.legIntPurposes.length===0&&u.specialPurposes.length>0))if(i.isServiceSpecific)if(u.flexiblePurposes.length===0)e.unset(o);else{for(var p=i.publisherRestrictions.getRestrictions(o),I=!1,V=0,_=p.length;V<_&&!I;V++)I=p[V].restrictionType===G.REQUIRE_CONSENT&&n==="purposes"||p[V].restrictionType===G.REQUIRE_LI&&n==="legIntPurposes";I||e.unset(o)}else e.unset(o)}})}),i.vendorsDisclosed.set(s.vendors),i}]);var ht=function(){function i(){k(this,i)}return A(i,null,[{key:"absCall",value:function(t,e,n,r){return new Promise(function(o,u){var p=new XMLHttpRequest,I=function(){if(p.readyState==XMLHttpRequest.DONE)if(p.status>=200&&p.status<300){var ee=p.response;if(typeof ee=="string")try{ee=JSON.parse(ee)}catch(Le){}o(ee)}else u(new Error("HTTP Status: ".concat(p.status," response type: ").concat(p.responseType)))},V=function(){u(new Error("error"))},_=function(){u(new Error("aborted"))},L=function(){u(new Error("Timeout "+r+"ms "+t))};p.withCredentials=n,p.addEventListener("load",I),p.addEventListener("error",V),p.addEventListener("abort",_),e===null?p.open("GET",t,!0):p.open("POST",t,!0),p.responseType="json",p.timeout=r,p.ontimeout=L,p.send(e)})}},{key:"post",value:function(t,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;return this.absCall(t,JSON.stringify(e),n,r)}},{key:"fetch",value:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;return this.absCall(t,null,e,n)}}])}(),T=function(i){function s(t,e){var n;k(this,s),n=x(this,s),a(n,"readyPromise",void 0),a(n,"gvlSpecificationVersion",void 0),a(n,"vendorListVersion",void 0),a(n,"tcfPolicyVersion",void 0),a(n,"lastUpdated",void 0),a(n,"purposes",void 0),a(n,"specialPurposes",void 0),a(n,"features",void 0),a(n,"specialFeatures",void 0),a(n,"isReady_",!1),a(n,"vendors_",void 0),a(n,"vendorIds",void 0),a(n,"fullVendorList",void 0),a(n,"byPurposeVendorMap",void 0),a(n,"bySpecialPurposeVendorMap",void 0),a(n,"byFeatureVendorMap",void 0),a(n,"bySpecialFeatureVendorMap",void 0),a(n,"stacks",void 0),a(n,"dataCategories",void 0),a(n,"lang_",void 0),a(n,"cacheLang_",void 0),a(n,"isLatest",!1);var r=T.baseUrl,o=e==null?void 0:e.language;if(o)try{o=T.consentLanguages.parseLanguage(o)}catch(p){throw new de("Error during parsing the language: "+p.message)}if(n.lang_=o||T.DEFAULT_LANGUAGE,n.cacheLang_=o||T.DEFAULT_LANGUAGE,n.isVendorList(t))n.populate(t),n.readyPromise=Promise.resolve();else{if(!r)throw new de("must specify GVL.baseUrl before loading GVL json");if(t>0){var u=t;T.CACHE.has(u)?(n.populate(T.CACHE.get(u)),n.readyPromise=Promise.resolve()):(r+=T.versionedFilename.replace("[VERSION]",String(u)),n.readyPromise=n.fetchJson(r))}else T.CACHE.has(T.LATEST_CACHE_KEY)?(n.populate(T.CACHE.get(T.LATEST_CACHE_KEY)),n.readyPromise=Promise.resolve()):(n.isLatest=!0,n.readyPromise=n.fetchJson(r+T.latestFilename))}return n}return j(s,i),A(s,[{key:"cacheLanguage",value:function(){T.LANGUAGE_CACHE.has(this.cacheLang_)||T.LANGUAGE_CACHE.set(this.cacheLang_,{purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories})}},{key:"fetchJson",value:function(){var t=ot(Ee().mark(function n(r){return Ee().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.prev=0,u.t0=this,u.next=4,ht.fetch(r);case 4:u.t1=u.sent,u.t0.populate.call(u.t0,u.t1),u.next=11;break;case 8:throw u.prev=8,u.t2=u.catch(0),new de(u.t2.message);case 11:case"end":return u.stop()}},n,this,[[0,8]])}));function e(n){return t.apply(this,arguments)}return e}()},{key:"getJson",value:function(){return M(M({gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.clonePurposes(),specialPurposes:this.cloneSpecialPurposes(),features:this.cloneFeatures(),specialFeatures:this.cloneSpecialFeatures(),stacks:this.cloneStacks()},this.dataCategories?{dataCategories:this.cloneDataCategories()}:{}),{},{vendors:this.cloneVendors()})}},{key:"cloneSpecialFeatures",value:function(){for(var e={},n=0,r=Object.keys(this.specialFeatures);n<r.length;n++){var o=r[n];e[o]=T.cloneFeature(this.specialFeatures[o])}return e}},{key:"cloneFeatures",value:function(){for(var e={},n=0,r=Object.keys(this.features);n<r.length;n++){var o=r[n];e[o]=T.cloneFeature(this.features[o])}return e}},{key:"cloneStacks",value:function(){for(var e={},n=0,r=Object.keys(this.stacks);n<r.length;n++){var o=r[n];e[o]=T.cloneStack(this.stacks[o])}return e}},{key:"cloneDataCategories",value:function(){for(var e={},n=0,r=Object.keys(this.dataCategories);n<r.length;n++){var o=r[n];e[o]=T.cloneDataCategory(this.dataCategories[o])}return e}},{key:"cloneSpecialPurposes",value:function(){for(var e={},n=0,r=Object.keys(this.specialPurposes);n<r.length;n++){var o=r[n];e[o]=T.clonePurpose(this.specialPurposes[o])}return e}},{key:"clonePurposes",value:function(){for(var e={},n=0,r=Object.keys(this.purposes);n<r.length;n++){var o=r[n];e[o]=T.clonePurpose(this.purposes[o])}return e}},{key:"cloneVendors",value:function(){for(var e={},n=0,r=Object.keys(this.fullVendorList);n<r.length;n++){var o=r[n];e[o]=T.cloneVendor(this.fullVendorList[o])}return e}},{key:"changeLanguage",value:function(){var t=ot(Ee().mark(function n(r){var o,u,p,I,V;return Ee().wrap(function(L){for(;;)switch(L.prev=L.next){case 0:o=r,L.prev=1,o=T.consentLanguages.parseLanguage(r),L.next=8;break;case 5:throw L.prev=5,L.t0=L.catch(1),new de("Error during parsing the language: "+L.t0.message);case 8:if(u=r.toUpperCase(),!(!(o.toLowerCase()===T.DEFAULT_LANGUAGE.toLowerCase()&&!T.LANGUAGE_CACHE.has(u))&&o!==this.lang_)){L.next=26;break}if(this.lang_=o,!T.LANGUAGE_CACHE.has(u)){L.next=15;break}p=T.LANGUAGE_CACHE.get(u);for(I in p)p.hasOwnProperty(I)&&(this[I]=p[I]);L.next=26;break;case 15:return V=T.baseUrl+T.languageFilename.replace("[LANG]",this.lang_.toLowerCase()),L.prev=16,L.next=19,this.fetchJson(V);case 19:this.cacheLang_=u,this.cacheLanguage(),L.next=26;break;case 23:throw L.prev=23,L.t1=L.catch(16),new de("unable to load language: "+L.t1.message);case 26:case"end":return L.stop()}},n,this,[[1,5],[16,23]])}));function e(n){return t.apply(this,arguments)}return e}()},{key:"language",get:function(){return this.lang_}},{key:"isVendorList",value:function(e){return e!==void 0&&e.vendors!==void 0}},{key:"populate",value:function(e){this.purposes=e.purposes,this.specialPurposes=e.specialPurposes,this.features=e.features,this.specialFeatures=e.specialFeatures,this.stacks=e.stacks,this.dataCategories=e.dataCategories,this.isVendorList(e)&&(this.gvlSpecificationVersion=e.gvlSpecificationVersion,this.tcfPolicyVersion=e.tcfPolicyVersion,this.vendorListVersion=e.vendorListVersion,this.lastUpdated=e.lastUpdated,typeof this.lastUpdated=="string"&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors_=e.vendors,this.fullVendorList=e.vendors,this.mapVendors(),this.isReady_=!0,this.isLatest&&T.CACHE.set(T.LATEST_CACHE_KEY,this.getJson()),T.CACHE.has(this.vendorListVersion)||T.CACHE.set(this.vendorListVersion,this.getJson())),this.cacheLanguage()}},{key:"mapVendors",value:function(e){var n=this;this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach(function(r){n.byPurposeVendorMap[r]={legInt:new Set,consent:new Set,flexible:new Set}}),Object.keys(this.specialPurposes).forEach(function(r){n.bySpecialPurposeVendorMap[r]=new Set}),Object.keys(this.features).forEach(function(r){n.byFeatureVendorMap[r]=new Set}),Object.keys(this.specialFeatures).forEach(function(r){n.bySpecialFeatureVendorMap[r]=new Set}),Array.isArray(e)||(e=Object.keys(this.fullVendorList).map(function(r){return+r})),this.vendorIds=new Set(e),this.vendors_=e.reduce(function(r,o){var u=n.vendors_[String(o)];return u&&u.deletedDate===void 0&&(u.purposes.forEach(function(p){n.byPurposeVendorMap[String(p)].consent.add(o)}),u.specialPurposes.forEach(function(p){n.bySpecialPurposeVendorMap[String(p)].add(o)}),u.legIntPurposes.forEach(function(p){n.byPurposeVendorMap[String(p)].legInt.add(o)}),u.flexiblePurposes&&u.flexiblePurposes.forEach(function(p){n.byPurposeVendorMap[String(p)].flexible.add(o)}),u.features.forEach(function(p){n.byFeatureVendorMap[String(p)].add(o)}),u.specialFeatures.forEach(function(p){n.bySpecialFeatureVendorMap[String(p)].add(o)}),r[o]=u),r},{})}},{key:"getFilteredVendors",value:function(e,n,r,o){var u=this,p=e.charAt(0).toUpperCase()+e.slice(1),I,V={};return e==="purpose"&&r?I=this["by"+p+"VendorMap"][String(n)][r]:I=this["by"+(o?"Special":"")+p+"VendorMap"][String(n)],I.forEach(function(_){V[String(_)]=u.vendors[String(_)]}),V}},{key:"getVendorsWithConsentPurpose",value:function(e){return this.getFilteredVendors("purpose",e,"consent")}},{key:"getVendorsWithLegIntPurpose",value:function(e){return this.getFilteredVendors("purpose",e,"legInt")}},{key:"getVendorsWithFlexiblePurpose",value:function(e){return this.getFilteredVendors("purpose",e,"flexible")}},{key:"getVendorsWithSpecialPurpose",value:function(e){return this.getFilteredVendors("purpose",e,void 0,!0)}},{key:"getVendorsWithFeature",value:function(e){return this.getFilteredVendors("feature",e)}},{key:"getVendorsWithSpecialFeature",value:function(e){return this.getFilteredVendors("feature",e,void 0,!0)}},{key:"vendors",get:function(){return this.vendors_}},{key:"narrowVendorsTo",value:function(e){this.mapVendors(e)}},{key:"isReady",get:function(){return this.isReady_}},{key:"clone",value:function(){var e=new T(this.getJson());return this.lang_!==T.DEFAULT_LANGUAGE&&e.changeLanguage(this.lang_),e}}],[{key:"baseUrl",get:function(){return this.baseUrl_},set:function(e){if(/^https?:\/\/vendorlist\.consensu\.org\//.test(e))throw new de("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");e.length>0&&e[e.length-1]!=="/"&&(e+="/"),this.baseUrl_=e}},{key:"emptyLanguageCache",value:function(e){var n=!1;return e==null&&T.LANGUAGE_CACHE.size>0?(T.LANGUAGE_CACHE=new Map,n=!0):typeof e=="string"&&this.consentLanguages.has(e.toUpperCase())&&(T.LANGUAGE_CACHE.delete(e.toUpperCase()),n=!0),n}},{key:"emptyCache",value:function(e){var n=!1;return Number.isInteger(e)&&e>=0?(T.CACHE.delete(e),n=!0):e===void 0&&(T.CACHE=new Map,n=!0),n}},{key:"clonePurpose",value:function(e){return M(M({id:e.id,name:e.name,description:e.description},e.descriptionLegal?{descriptionLegal:e.descriptionLegal}:{}),e.illustrations?{illustrations:Array.from(e.illustrations)}:{})}},{key:"cloneFeature",value:function(e){return M(M({id:e.id,name:e.name,description:e.description},e.descriptionLegal?{descriptionLegal:e.descriptionLegal}:{}),e.illustrations?{illustrations:Array.from(e.illustrations)}:{})}},{key:"cloneDataCategory",value:function(e){return{id:e.id,name:e.name,description:e.description}}},{key:"cloneStack",value:function(e){return{id:e.id,name:e.name,description:e.description,purposes:Array.from(e.purposes),specialFeatures:Array.from(e.specialFeatures)}}},{key:"cloneDataRetention",value:function(e){return M(M({},typeof e.stdRetention=="number"?{stdRetention:e.stdRetention}:{}),{},{purposes:M({},e.purposes),specialPurposes:M({},e.specialPurposes)})}},{key:"cloneVendorUrls",value:function(e){return e.map(function(n){return M({langId:n.langId,privacy:n.privacy},n.legIntClaim?{legIntClaim:n.legIntClaim}:{})})}},{key:"cloneVendor",value:function(e){return M(M(M(M(M(M(M(M(M(M(M({id:e.id,name:e.name,purposes:Array.from(e.purposes),legIntPurposes:Array.from(e.legIntPurposes),flexiblePurposes:Array.from(e.flexiblePurposes),specialPurposes:Array.from(e.specialPurposes),features:Array.from(e.features),specialFeatures:Array.from(e.specialFeatures)},e.overflow?{overflow:{httpGetLimit:e.overflow.httpGetLimit}}:{}),typeof e.cookieMaxAgeSeconds=="number"||e.cookieMaxAgeSeconds===null?{cookieMaxAgeSeconds:e.cookieMaxAgeSeconds}:{}),e.usesCookies!==void 0?{usesCookies:e.usesCookies}:{}),e.policyUrl?{policyUrl:e.policyUrl}:{}),e.cookieRefresh!==void 0?{cookieRefresh:e.cookieRefresh}:{}),e.usesNonCookieAccess!==void 0?{usesNonCookieAccess:e.usesNonCookieAccess}:{}),e.dataRetention?{dataRetention:this.cloneDataRetention(e.dataRetention)}:{}),e.urls?{urls:this.cloneVendorUrls(e.urls)}:{}),e.dataDeclaration?{dataDeclaration:Array.from(e.dataDeclaration)}:{}),e.deviceStorageDisclosureUrl?{deviceStorageDisclosureUrl:e.deviceStorageDisclosureUrl}:{}),e.deletedDate?{deletedDate:e.deletedDate}:{})}},{key:"isInstanceOf",value:function(e){return Y(e)=="object"&&typeof e.narrowVendorsTo=="function"}}])}(ae),z=T;d(z,"LANGUAGE_CACHE",new Map),d(z,"CACHE",new Map),d(z,"LATEST_CACHE_KEY",0),d(z,"DEFAULT_LANGUAGE","EN"),d(z,"consentLanguages",new Z),d(z,"baseUrl_"),d(z,"latestFilename","vendor-list.json"),d(z,"versionedFilename","archives/vendor-list-v[VERSION].json"),d(z,"languageFilename","purposes-[LANG].json");var Re=function(i){function s(t){var e;return k(this,s),e=x(this,s),a(w(e),"isServiceSpecific_",!1),a(w(e),"supportOOB_",!0),a(w(e),"useNonStandardTexts_",!1),a(w(e),"purposeOneTreatment_",!1),a(w(e),"publisherCountryCode_","AA"),a(w(e),"version_",2),a(w(e),"consentScreen_",0),a(w(e),"policyVersion_",4),a(w(e),"consentLanguage_","EN"),a(w(e),"cmpId_",0),a(w(e),"cmpVersion_",0),a(w(e),"vendorListVersion_",0),a(w(e),"numCustomPurposes_",0),a(w(e),"gvl_",void 0),a(w(e),"created",void 0),a(w(e),"lastUpdated",void 0),a(w(e),"specialFeatureOptins",new K),a(w(e),"purposeConsents",new K),a(w(e),"purposeLegitimateInterests",new K),a(w(e),"publisherConsents",new K),a(w(e),"publisherLegitimateInterests",new K),a(w(e),"publisherCustomConsents",new K),a(w(e),"publisherCustomLegitimateInterests",new K),a(w(e),"customPurposes",void 0),a(w(e),"vendorConsents",new K),a(w(e),"vendorLegitimateInterests",new K),a(w(e),"vendorsDisclosed",new K),a(w(e),"vendorsAllowed",new K),a(w(e),"publisherRestrictions",new ge),t&&(e.gvl=t),e.updated(),e}return j(s,i),A(s,[{key:"gvl",get:function(){return this.gvl_},set:function(e){z.isInstanceOf(e)||(e=new z(e)),this.gvl_=e,this.publisherRestrictions.gvl=e}},{key:"cmpId",get:function(){return this.cmpId_},set:function(e){if(e=Number(e),Number.isInteger(e)&&e>1)this.cmpId_=e;else throw new ne("cmpId",e)}},{key:"cmpVersion",get:function(){return this.cmpVersion_},set:function(e){if(e=Number(e),Number.isInteger(e)&&e>-1)this.cmpVersion_=e;else throw new ne("cmpVersion",e)}},{key:"consentScreen",get:function(){return this.consentScreen_},set:function(e){if(e=Number(e),Number.isInteger(e)&&e>-1)this.consentScreen_=e;else throw new ne("consentScreen",e)}},{key:"consentLanguage",get:function(){return this.consentLanguage_},set:function(e){this.consentLanguage_=e}},{key:"publisherCountryCode",get:function(){return this.publisherCountryCode_},set:function(e){if(/^([A-z]){2}$/.test(e))this.publisherCountryCode_=e.toUpperCase();else throw new ne("publisherCountryCode",e)}},{key:"vendorListVersion",get:function(){return this.gvl?this.gvl.vendorListVersion:this.vendorListVersion_},set:function(e){if(e=Number(e)>>0,e<0)throw new ne("vendorListVersion",e);this.vendorListVersion_=e}},{key:"policyVersion",get:function(){return this.gvl?this.gvl.tcfPolicyVersion:this.policyVersion_},set:function(e){if(this.policyVersion_=parseInt(e,10),this.policyVersion_<0)throw new ne("policyVersion",e)}},{key:"version",get:function(){return this.version_},set:function(e){this.version_=parseInt(e,10)}},{key:"isServiceSpecific",get:function(){return this.isServiceSpecific_},set:function(e){this.isServiceSpecific_=e}},{key:"useNonStandardTexts",get:function(){return this.useNonStandardTexts_},set:function(e){this.useNonStandardTexts_=e}},{key:"supportOOB",get:function(){return this.supportOOB_},set:function(e){this.supportOOB_=e}},{key:"purposeOneTreatment",get:function(){return this.purposeOneTreatment_},set:function(e){this.purposeOneTreatment_=e}},{key:"setAllVendorConsents",value:function(){this.vendorConsents.set(this.gvl.vendors)}},{key:"unsetAllVendorConsents",value:function(){this.vendorConsents.empty()}},{key:"setAllVendorsDisclosed",value:function(){this.vendorsDisclosed.set(this.gvl.vendors)}},{key:"unsetAllVendorsDisclosed",value:function(){this.vendorsDisclosed.empty()}},{key:"setAllVendorsAllowed",value:function(){this.vendorsAllowed.set(this.gvl.vendors)}},{key:"unsetAllVendorsAllowed",value:function(){this.vendorsAllowed.empty()}},{key:"setAllVendorLegitimateInterests",value:function(){this.vendorLegitimateInterests.set(this.gvl.vendors)}},{key:"unsetAllVendorLegitimateInterests",value:function(){this.vendorLegitimateInterests.empty()}},{key:"setAllPurposeConsents",value:function(){this.purposeConsents.set(this.gvl.purposes)}},{key:"unsetAllPurposeConsents",value:function(){this.purposeConsents.empty()}},{key:"setAllPurposeLegitimateInterests",value:function(){this.purposeLegitimateInterests.set(this.gvl.purposes)}},{key:"unsetAllPurposeLegitimateInterests",value:function(){this.purposeLegitimateInterests.empty()}},{key:"setAllSpecialFeatureOptins",value:function(){this.specialFeatureOptins.set(this.gvl.specialFeatures)}},{key:"unsetAllSpecialFeatureOptins",value:function(){this.specialFeatureOptins.empty()}},{key:"setAll",value:function(){this.setAllVendorConsents(),this.setAllPurposeLegitimateInterests(),this.setAllSpecialFeatureOptins(),this.setAllPurposeConsents(),this.setAllVendorLegitimateInterests()}},{key:"unsetAll",value:function(){this.unsetAllVendorConsents(),this.unsetAllPurposeLegitimateInterests(),this.unsetAllSpecialFeatureOptins(),this.unsetAllPurposeConsents(),this.unsetAllVendorLegitimateInterests()}},{key:"numCustomPurposes",get:function(){var e=this.numCustomPurposes_;if(Y(this.customPurposes)=="object"){var n=Object.keys(this.customPurposes).sort(function(r,o){return Number(r)-Number(o)});e=parseInt(n.pop(),10)}return e},set:function(e){if(this.numCustomPurposes_=parseInt(e,10),this.numCustomPurposes_<0)throw new ne("numCustomPurposes",e)}},{key:"updated",value:function(){var e=new Date,n=new Date(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()));this.created=n,this.lastUpdated=n}}])}(ae);d(Re,"consentLanguages",z.consentLanguages);var Ye=function(){function i(){k(this,i)}return A(i,null,[{key:"encode",value:function(t,e){var n="",r;return t=Ke.process(t,e),Array.isArray(e==null?void 0:e.segments)?r=e.segments:r=new pt(t,e)[""+t.version],r.forEach(function(o,u){var p="";u<r.length-1&&(p="."),n+=Me.encode(t,o)+p}),n}},{key:"decode",value:function(t,e){var n=t.split("."),r=n.length;e||(e=new Re);for(var o=0;o<r;o++){var u=n[o],p=oe.decode(u.charAt(0)).substr(0,v.segmentType),I=he.ID_TO_KEY[P.decode(p,v.segmentType).toString()];Me.decode(u,e,I)}return e}}])}(),W;(function(i){i.PING="ping",i.GET_TC_DATA="getTCData",i.GET_IN_APP_TC_DATA="getInAppTCData",i.GET_VENDOR_LIST="getVendorList",i.ADD_EVENT_LISTENER="addEventListener",i.REMOVE_EVENT_LISTENER="removeEventListener"})(W||(W={}));var me;(function(i){i.STUB="stub",i.LOADING="loading",i.LOADED="loaded",i.ERROR="error"})(me||(me={}));var ye;(function(i){i.VISIBLE="visible",i.HIDDEN="hidden",i.DISABLED="disabled"})(ye||(ye={}));var _e;(function(i){i.TC_LOADED="tcloaded",i.CMP_UI_SHOWN="cmpuishown",i.USER_ACTION_COMPLETE="useractioncomplete"})(_e||(_e={}));var Ne=function(){function i(s,t,e,n){k(this,i),a(this,"listenerId",void 0),a(this,"callback",void 0),a(this,"next",void 0),a(this,"param",void 0),a(this,"success",!0),Object.assign(this,{callback:s,listenerId:e,param:t,next:n});try{this.respond()}catch(r){this.invokeCallback(null)}}return A(i,[{key:"invokeCallback",value:function(t){var e=t!==null;typeof this.next=="function"?this.callback(this.next,t,e):this.callback(t,e)}}])}(),De=function(i){function s(){return k(this,s),x(this,s,arguments)}return j(s,i),A(s,[{key:"respond",value:function(){this.throwIfParamInvalid(),this.invokeCallback(new qe(this.param,this.listenerId))}},{key:"throwIfParamInvalid",value:function(){if(this.param!==void 0&&(!Array.isArray(this.param)||!this.param.every(Number.isInteger)))throw new Error("Invalid Parameter")}}])}(Ne),ft=function(){function i(){k(this,i),a(this,"eventQueue",new Map),a(this,"queueNumber",0)}return A(i,[{key:"add",value:function(t){return this.eventQueue.set(this.queueNumber,t),this.queueNumber++}},{key:"remove",value:function(t){return this.eventQueue.delete(t)}},{key:"exec",value:function(){this.eventQueue.forEach(function(t,e){new De(t.callback,t.param,e,t.next)})}},{key:"clear",value:function(){this.queueNumber=0,this.eventQueue.clear()}},{key:"size",get:function(){return this.eventQueue.size}}])}(),E=function(){function i(){k(this,i)}return A(i,null,[{key:"reset",value:function(){delete this.cmpId,delete this.cmpVersion,delete this.eventStatus,delete this.gdprApplies,delete this.tcModel,delete this.tcString,delete this.tcfPolicyVersion,this.cmpStatus=me.LOADING,this.disabled=!1,this.displayStatus=ye.HIDDEN,this.eventQueue.clear()}}])}();d(E,"apiVersion","2"),d(E,"tcfPolicyVersion"),d(E,"eventQueue",new ft),d(E,"cmpStatus",me.LOADING),d(E,"disabled",!1),d(E,"displayStatus",ye.HIDDEN),d(E,"cmpId"),d(E,"cmpVersion"),d(E,"eventStatus"),d(E,"gdprApplies"),d(E,"tcModel"),d(E,"tcString");var Ue=A(function i(){k(this,i),a(this,"cmpId",E.cmpId),a(this,"cmpVersion",E.cmpVersion),a(this,"gdprApplies",E.gdprApplies),a(this,"tcfPolicyVersion",E.tcfPolicyVersion)}),vt=function(i){function s(){var t;k(this,s);for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t=x(this,s,[].concat(n)),a(t,"cmpStatus",me.ERROR),t}return j(s,i),A(s)}(Ue),qe=function(i){function s(t,e){var n;if(k(this,s),n=x(this,s),a(w(n),"tcString",void 0),a(w(n),"listenerId",void 0),a(w(n),"eventStatus",void 0),a(w(n),"cmpStatus",void 0),a(w(n),"isServiceSpecific",void 0),a(w(n),"useNonStandardTexts",void 0),a(w(n),"publisherCC",void 0),a(w(n),"purposeOneTreatment",void 0),a(w(n),"outOfBand",void 0),a(w(n),"purpose",void 0),a(w(n),"vendor",void 0),a(w(n),"specialFeatureOptins",void 0),a(w(n),"publisher",void 0),n.eventStatus=E.eventStatus,n.cmpStatus=E.cmpStatus,n.listenerId=e,E.gdprApplies){var r=E.tcModel;n.tcString=E.tcString,n.isServiceSpecific=r.isServiceSpecific,n.useNonStandardTexts=r.useNonStandardTexts,n.purposeOneTreatment=r.purposeOneTreatment,n.publisherCC=r.publisherCountryCode,n.outOfBand={allowedVendors:n.createVectorField(r.vendorsAllowed,t),disclosedVendors:n.createVectorField(r.vendorsDisclosed,t)},n.purpose={consents:n.createVectorField(r.purposeConsents),legitimateInterests:n.createVectorField(r.purposeLegitimateInterests)},n.vendor={consents:n.createVectorField(r.vendorConsents,t),legitimateInterests:n.createVectorField(r.vendorLegitimateInterests,t)},n.specialFeatureOptins=n.createVectorField(r.specialFeatureOptins),n.publisher={consents:n.createVectorField(r.publisherConsents),legitimateInterests:n.createVectorField(r.publisherLegitimateInterests),customPurpose:{consents:n.createVectorField(r.publisherCustomConsents),legitimateInterests:n.createVectorField(r.publisherCustomLegitimateInterests)},restrictions:n.createRestrictions(r.publisherRestrictions)}}return w(n)}return j(s,i),A(s,[{key:"createRestrictions",value:function(e){var n={};if(e.numRestrictions>0)for(var r=e.getMaxVendorId(),o=function(){var I=u.toString();e.getRestrictions(u).forEach(function(V){var _=V.purposeId.toString();n[_]||(n[_]={}),n[_][I]=V.restrictionType})},u=1;u<=r;u++)o();return n}},{key:"createVectorField",value:function(e,n){return n?n.reduce(function(r,o){return r[String(o)]=e.has(Number(o)),r},{}):Fe(e).reduce(function(r,o){return r[o[0].toString(10)]=o[1],r},{})}}])}(Ue),gt=function(i){function s(t){var e;return k(this,s),e=x(this,s,[t]),delete e.outOfBand,e}return j(s,i),A(s,[{key:"createVectorField",value:function(e){return Fe(e).reduce(function(n,r){return n+=r[1]?"1":"0",n},"")}},{key:"createRestrictions",value:function(e){var n={};if(e.numRestrictions>0){var r=e.getMaxVendorId();e.getRestrictions().forEach(function(p){n[p.purposeId.toString()]="_".repeat(r)});for(var o=function(I){var V=I+1;e.getRestrictions(V).forEach(function(_){var L=_.restrictionType.toString(),N=_.purposeId.toString(),ee=n[N].substr(0,I),Le=n[N].substr(I+1);n[N]=ee+L+Le})},u=0;u<r;u++)o(u)}return n}}])}(qe),mt=function(i){function s(){var t;return k(this,s),t=x(this,s),a(w(t),"cmpLoaded",!0),a(w(t),"cmpStatus",E.cmpStatus),a(w(t),"displayStatus",E.displayStatus),a(w(t),"apiVersion",String(E.apiVersion)),a(w(t),"gvlVersion",void 0),E.tcModel&&E.tcModel.vendorListVersion&&(t.gvlVersion=+E.tcModel.vendorListVersion),t}return j(s,i),A(s)}(Ue),yt=function(i){function s(){return k(this,s),x(this,s,arguments)}return j(s,i),A(s,[{key:"respond",value:function(){this.invokeCallback(new mt)}}])}(Ne),bt=function(i){function s(){return k(this,s),x(this,s,arguments)}return j(s,i),A(s,[{key:"respond",value:function(){this.throwIfParamInvalid(),this.invokeCallback(new gt(this.param))}}])}(De),Et=function(i){function s(){return k(this,s),x(this,s,arguments)}return j(s,i),A(s,[{key:"respond",value:function(){var e=this,n=E.tcModel,r=n.vendorListVersion,o;this.param===void 0&&(this.param=r),this.param===r&&n.gvl?o=n.gvl:o=new z(this.param),o.readyPromise.then(function(){e.invokeCallback(o.getJson())})}}])}(Ne),Ct=function(i){function s(){return k(this,s),x(this,s,arguments)}return j(s,i),A(s,[{key:"respond",value:function(){this.listenerId=E.eventQueue.add({callback:this.callback,param:this.param,next:this.next}),kt(s,"respond",this,3)([])}}])}(De),wt=function(i){function s(){return k(this,s),x(this,s,arguments)}return j(s,i),A(s,[{key:"respond",value:function(){this.invokeCallback(E.eventQueue.remove(this.param))}}])}(Ne),Xe,Ze,$e,et,tt,nt,ie=A(function i(){k(this,i)});Xe=W.PING,Ze=W.GET_TC_DATA,$e=W.GET_IN_APP_TC_DATA,et=W.GET_VENDOR_LIST,tt=W.ADD_EVENT_LISTENER,nt=W.REMOVE_EVENT_LISTENER,d(ie,Xe,yt),d(ie,Ze,De),d(ie,$e,bt),d(ie,et,Et),d(ie,tt,Ct),d(ie,nt,wt);var rt=function(){function i(){k(this,i)}return A(i,null,[{key:"has",value:function(t){return typeof t=="string"&&(t=Number(t)),this.set_.has(t)}}])}();d(rt,"set_",new Set([0,2,void 0,null]));var xe="__tcfapi",St=function(){function i(s){if(k(this,i),a(this,"callQueue",void 0),a(this,"customCommands",void 0),s){var t=W.ADD_EVENT_LISTENER;if(s!=null&&s[t])throw new Error("Built-In Custom Commmand for ".concat(t," not allowed: Use ").concat(W.GET_TC_DATA," instead"));if(t=W.REMOVE_EVENT_LISTENER,s!=null&&s[t])throw new Error("Built-In Custom Commmand for ".concat(t," not allowed"));s!=null&&s[W.GET_TC_DATA]&&(s[W.ADD_EVENT_LISTENER]=s[W.GET_TC_DATA],s[W.REMOVE_EVENT_LISTENER]=s[W.GET_TC_DATA]),this.customCommands=s}try{this.callQueue=window[xe]()||[]}catch(e){this.callQueue=[]}finally{window[xe]=this.apiCall.bind(this),this.purgeQueuedCalls()}}return A(i,[{key:"apiCall",value:function(t,e,n){if(typeof t!="string")n(null,!1);else if(!rt.has(e))n(null,!1);else{var r;if(typeof n!="function")throw new Error("invalid callback function");for(var o=arguments.length,u=new Array(o>3?o-3:0),p=3;p<o;p++)u[p-3]=arguments[p];E.disabled?n(new vt,!1):!this.isCustomCommand(t)&&!this.isBuiltInCommand(t)?n(null,!1):this.isCustomCommand(t)&&!this.isBuiltInCommand(t)?(r=this.customCommands)[t].apply(r,[n].concat(u)):t===W.PING?this.isCustomCommand(t)?new ie[t](this.customCommands[t],u[0],null,n):new ie[t](n,u[0]):E.tcModel===void 0?this.callQueue.push([t,e,n].concat(u)):this.isCustomCommand(t)&&this.isBuiltInCommand(t)?new ie[t](this.customCommands[t],u[0],null,n):new ie[t](n,u[0])}}},{key:"purgeQueuedCalls",value:function(){var t=this.callQueue;this.callQueue=[],t.forEach(function(e){var n;(n=window)[xe].apply(n,Fe(e))})}},{key:"isCustomCommand",value:function(t){return this.customCommands&&typeof this.customCommands[t]=="function"}},{key:"isBuiltInCommand",value:function(t){return ie[t]!==void 0}}])}(),It=function(){function i(s,t){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3?arguments[3]:void 0;k(this,i),a(this,"callResponder",void 0),a(this,"isServiceSpecific",void 0),a(this,"numUpdates",0),this.throwIfInvalidInt(s,"cmpId",2),this.throwIfInvalidInt(t,"cmpVersion",0),E.cmpId=s,E.cmpVersion=t,E.tcfPolicyVersion=4,this.isServiceSpecific=!!e,this.callResponder=new St(n)}return A(i,[{key:"throwIfInvalidInt",value:function(t,e,n){if(!(typeof t=="number"&&Number.isInteger(t)&&t>=n))throw new Error("Invalid ".concat(e,": ").concat(t))}},{key:"update",value:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(E.disabled)throw new Error("CmpApi Disabled");E.cmpStatus=me.LOADED,e?(E.displayStatus=ye.VISIBLE,E.eventStatus=_e.CMP_UI_SHOWN):E.tcModel===void 0?(E.displayStatus=ye.DISABLED,E.eventStatus=_e.TC_LOADED):(E.displayStatus=ye.HIDDEN,E.eventStatus=_e.USER_ACTION_COMPLETE),E.gdprApplies=t!==null,E.gdprApplies?(t===""?(E.tcModel=new Re,E.tcModel.cmpId=E.cmpId,E.tcModel.cmpVersion=E.cmpVersion):E.tcModel=Ye.decode(t),E.tcModel.isServiceSpecific=this.isServiceSpecific,E.tcfPolicyVersion=Number(E.tcModel.policyVersion),E.tcString=t):E.tcModel=null,this.numUpdates===0?this.callResponder.purgeQueuedCalls():E.eventQueue.exec(),this.numUpdates++}},{key:"disable",value:function(){E.disabled=!0,E.cmpStatus=me.ERROR}}])}(),Lt=function(){function i(){k(this,i)}return A(i,[{key:"receiveMessage",value:function(t){var e=typeof t.data=="string",n={};try{e?n=JSON.parse(t.data):n=t.data}catch(V){}if(n&&n.__tcfapiCall){var r=n.__tcfapiCall.callId,o=n.__tcfapiCall.command,u=n.__tcfapiCall.parameter,p=n.__tcfapiCall.version,I=function(_,L){var N={__tcfapiReturn:{returnValue:_,success:L,callId:r,command:o}};t&&t.source&&t.source.postMessage&&t.source.postMessage(e?JSON.stringify(N):N,"*")};window.__tcfapi.apply(window,[o,p,I,u])}else n&&n.hasOwnProperty("OnetrustIABCookies")&&(n.OnetrustIABCookies==="blocked"&&(n.OnetrustIABCookies=null),window.OneTrust.updateConsentFromCookies(n.OnetrustIABCookies))}},{key:"initializeTCF",value:function(){window.removeEventListener("message",window.receiveOTMessage),delete window.receiveOTMessage,(window.attachEvent||window.addEventListener)("message",function(t){return be.receiveMessage(t)},!1)}},{key:"getGVLObject",value:function(t,e,n){if(e){var r=e.substr(e.lastIndexOf("/")+1);z.baseUrl=e.replace("/"+r,""),z.latestFilename=r}return new z(n,{language:t})}},{key:"getTCModelObject",value:function(t){return new Re(t)}},{key:"getTCStringObject",value:function(){return Ye}},{key:"getCmpApi",value:function(t,e,n){var r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return new It(t,e,n,r)}},{key:"getPurposeRestriction",value:function(t,e){return new re(t,e)}}])}(),be=new Lt;be.initializeTCF();var At={gvl:be.getGVLObject,tcModel:be.getTCModelObject,tcString:be.getTCStringObject,cmpApi:be.getCmpApi,purposeRestriction:be.getPurposeRestriction};window.otIabModule=U($({},window.otIabModule),{tcfSdkRef:At})})();})();
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
(()=>{"use strict";var e,t="eupubconsent-v2",n=".msn.cn",r="OptanonAlertBoxClosed",o="MSANFixCount",a=1126;function i(e){var t=Number(localStorage.getItem(o))||0;e?localStorage.removeItem(o):localStorage.setItem(o,(t+1).toString())}function c(e){var t=document.cookie;if(t){var n=new RegExp("\\b"+e+"\\s*=\\s*([^;]*)","i").exec(t);return n&&n.length>1?n[1]:null}return null}function s(){var o=!1,s=window.otIabModule.tcfSdkRef.tcString().decode(e);if(s)if(126!==s.vendorListVersion_||s.vendorConsents.set_.has(a))126!=s.vendorListVersion_?i(o):i(o=!0);else try{window.__tcfapi("getVendorList",2,((e,d)=>{try{d&&(s.gvl=e,!function(e){var t=new Set(["1","10","100","1002","1003","1004","1008","1009","1015","1016","1018","102","1021","1023","1025","1026","1028","1029","1030","1033","1034","1036","1038","1039","104","1042","1047","1048","1050","1051","1053","1054","1057","1059","1063","1064","1067","1069","1071","1076","1077","1078","1079","109","11","111","115","119","120","122","126","127","132","134","136","137","138","139","14","142","145","147","149","15","150","151","152","154","163","165","170","173","177","178","179","18","183","184","185","190","194","195","198","20","200","202","203","205","206","209","21","213","215","217","218","223","224","227","23","230","231","234","235","236","238","239","240","241","242","246","248","249","25","251","252","253","255","256","259","261","263","264","265","273","276","277","278","279","28","280","281","282","284","290","294","295","297","298","299","30","302","308","31","310","312","314","315","318","319","32","321","323","325","33","331","333","335","336","34","343","345","347","349","350","351","354","359","36","360","361","371","373","374","377","378","382","385","39","394","40","402","408","412","418","42","424","428","436","438","44","440","444","448","452","455","458","46","461","462","467","468","469","475","48","482","488","49","490","493","498","50","502","507","508","509","511","52","528","53","530","534","539","541","543","546","554","559","569","57","570","571","573","577","578","579","580","584","587","59","590","593","596","598","6","60","602","606","61","610","612","613","62","620","621","63","630","644","645","647","655","656","657","658","659","66","663","664","665","666","667","67","671","678","68","681","682","684","687","688","69","694","699","702","708","709","71","713","714","720","721","724","728","729","730","736","738","740","741","744","745","746","747","748","749","750","751","753","754","755","756","76","766","77","770","774","776","777","781","783","784","786","787","79","790","792","793","796","797","798","8","80","800","801","802","803","804","806","807","810","812","813","814","819","82","820","821","823","825","827","828","83","830","831","836","837","840","841","842","843","844","845","846","85","850","851","853","856","857","858","859","86","861","862","864","867","869","87","871","875","876","878","879","88","882","884","895","896","905","92","93","932","935","944","946","947","949","95","951","952","967","970","977","978","98","981","986","989","990","994","996","997"]),n=new Set(["196","237","397","473","496","885","907","908","909","910","911","912","913","914","915","916","917","918","919","920","921","922","923","924","925","926","927","933","934","936","937","938","940","941"]),r=new Set(["1094","1097","1098","1100","1101","1103","1104","1105","1106","1107","1108","1110","1111","1112","1113","1116","1119","1121","1122","1124","1126","1127","1130","1132","1133","1134","1135","1136","1137","1138","1139","1141","1142","1144","1146","1148","1149","1151","1153","1154","1155","1156","1157","1159","1162","1163","1164","1165","1167","1168","1173","1174","1175","1176","1177","1178","1180","1181","1182","1183","1184","1185","1188","1189","1190","1193","1195","1196","1198","1199","1201","1202","1205","1206","1207","1208","1209","1210","1211","1212","1213","1214","1216","1218","1220","1221","1222","1224","1226","1227","1229","1230","1231","1232","1234","1235","1236","1237","1238","1240","1241","1242","1243","1244","1245","1246","1248","1249","1251","1252","1254","1255","1257","1258","1260","1261","1262","1263","1264","1266","1267","1268","1269","1270","1271","1272","1273","1274","1276","1279","1280","1281","1282","1283","1286","1287","1288","1289","1290","1291","1292","1293","1294","1295","1296","1297","1298","1299","1301","1303","1304","1305","1310","1312","1313","1314","1315","1316","1317","1318","1319","1320","1321","1322","1323","1325","1326","1327","1328","1329","1330","1332","1334","1335","1336","1337","1338","1339","1340","1341","1342","1343","1345","1346","1347","1348","1350","1351","1352","1353","1354","1355","1356","1357","1358","1359","1360","1364","1365","1366","1367","1368","1370","1371","1372","1373","1374","1375","1377","1378","1379","1381","1382","1383","1384","1385","1386","1387","1388","1390","1391","1393","156","326","409","430","454","55","673","677","939"]);for(var o of Object.keys(e.gvl.vendors))if(!n.has(o)&&0!=e.gvl.vendors[o].purposes.length&&!r.has(o)&&!e.vendorConsents.set_.has(parseInt(o,10))&&t.has(o))return!1;return!0}(s)?o=!0:(s.vendorConsents.set_.add(a),s.vendorConsents.maxId_<a&&(s.vendorConsents.maxId_=a),function(e){try{if(!otIabModule.tcfSdkRef.tcString)return!1;var o=otIabModule.tcfSdkRef.tcString().encode(e);if(o){if(!otIabModule.tcfSdkRef.tcString().decode(o))return!1;var a=(new Date).getTime(),i=c(r);return i&&(a=Date.parse(i)),!!function(e,t,r,o){if(!o&&c(e))return!1;var a=new Date;a.setTime(r+31536e6);var i=`${e}=${t};${"expires="+a.toUTCString()};domain=${n};path=/;SameSite=Lax`;return document.cookie=i,!0}(t,o,a,!0)}}catch(e){return!1}return!1}(s)&&(o=!0))),i(o)}catch(e){}}),"LATEST")}catch(e){}else i(o)}!function(){if(window.otIabModule&&window.otIabModule.tcfSdkRef){var t=0,n=localStorage.getItem(o);if(n&&(t=Number(n)),!n||t<9)try{window.__tcfapi("addEventListener",2,(function(t,n){try{!n||"useractioncomplete"!==t.eventStatus&&"tcloaded"!==t.eventStatus||(e=t.tcString,t.gdprApplies&&e&&s())}catch(e){}}))}catch(e){}}}()})();