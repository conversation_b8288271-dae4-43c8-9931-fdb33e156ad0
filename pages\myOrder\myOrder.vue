<template>
  <view class="bkcolor" style="background-color: #f5f6f8">
    <!-- 导航拦 -->
    <view class="my-order-nav">
      <view
        @click="checkNav(0)"
        :class="
          'my-order-nav-item' +
          (navCheck === 0 ? ' my-order-nav-item-active' : '')
        "
        >权益卡订单</view
      >
      <view
        @click="checkNav(1)"
        :class="
          'my-order-nav-item' +
          (navCheck === 1 ? ' my-order-nav-item-active' : '')
        "
        >ETC商城订单</view
      >
      <view
        @click="checkNav(2)"
        :class="
          'my-order-nav-item' +
          (navCheck === 2 ? ' my-order-nav-item-active' : '')
        "
        >ETC自营商品订单</view
      >
      <!-- <view @click="checkNav(2)" :class="'my-order-nav-item' + (navCheck === 2 ? ' my-order-nav-item-active' : '')">退款</view>
			<view @click="checkNav(3)" :class="'my-order-nav-item' + (navCheck === 3 ? ' my-order-nav-item-active' : '')">已完成</view> -->
    </view>
    <!-- 订单 -->

    <view class="wd">
      <view v-if="navCheck == '0'">
        <view v-if="showData.length != 0">
          <view
            class="my-order-card"
            v-for="(data, index) in showData"
            :key="index"
          >
            <!-- 订单名 -->
            <view style="display: flex; justify-content: space-between">
              <view>
                {{
                  dict[data.equityType] === undefined
                    ? ""
                    : dict[data.equityType]
                }}
              </view>
              <view style="color: red">
                {{ getStatus(data) }}
              </view>
            </view>
            <!-- 订单号 -->
            <view style="color: #7f7f7f; margin-top: 5px">
              订单号: {{ data.orderno }}
            </view>
            <view style="margin-top: 5px">
              {{ formatDate(data.orderTime) }}
            </view>
            <view style="margin-top: 5px"> 实付:￥{{ data.orderMoney }} </view>
          </view>
        </view>
        <view
          v-else
          style="
            position: fixed;
            left: 50%;
            top: 30%;
            transform: translateX(-50%);
          "
        >
          <image
            src="@/static/myOrder/empty.jpg"
            style="width: 150px; height: 150px"
          ></image>
          <view style="text-align: center; color: #7f7f7f">暂无订单数据</view>
        </view>
      </view>

      <view v-else-if="navCheck == '1'">
        <view v-if="qyscOrder.length != 0">
          <view
            class="my-order-card"
            v-for="(data, index) in qyscOrder"
            :key="index"
          >
            <!-- 订单名 -->
            <view style="display: flex; justify-content: space-between">
              <view style="width: 75%">
                {{ data.orderName }}
              </view>
              <view style="color: red">
                {{ makeStatus(data.status) }}
              </view>
            </view>
            <!-- 订单号 -->
            <view style="color: #7f7f7f; margin-top: 5px">
              订单号: {{ data.orderno }}
            </view>
            <view style="margin-top: 5px">
              {{ formatDate1(data.completeDate) }}
            </view>
            <view style="margin-top: 5px">
              实付:￥{{ data.customerPrice }}
            </view>
          </view>
        </view>

        <view
          v-else
          style="
            position: fixed;
            left: 50%;
            top: 30%;
            transform: translateX(-50%);
          "
        >
          <image
            src="@/static/myOrder/empty.jpg"
            style="width: 150px; height: 150px"
          ></image>
          <view style="text-align: center; color: #7f7f7f">暂无订单数据</view>
        </view>
      </view>

      <view v-else-if="navCheck == '2'">
        <view v-if="selfGoodsOrders.length != 0">
          <view
            class="my-order-card"
            v-for="(data, index) in selfGoodsOrders"
            :key="index"
          >
            <!-- 订单名 -->
            <view style="display: flex; justify-content: space-between">
              <view style="width: 75%">
                {{ data.goodsName }} ⅹ {{ data.goodsNum }}
              </view>
              <view style="color: red">
                {{ formatSelfGoodsStatus(data.orderStatus) }}
              </view>
            </view>
            <!-- 订单号 -->
            <view style="color: #7f7f7f; margin-top: 5px">
              订单号: {{ data.outTradeNum }}
            </view>
            <view
              style="
                color: #7f7f7f;
                margin-top: 5px;
                display: flex;
                justify-content: space-between;
              "
            >
              <view>邮寄单号: {{ data.mailNo ? data.mailNo : "无" }}</view>
              <view @click="copy(data.mailNo)" v-if="data.mailNo">复制</view>
            </view>
            <view style="margin-top: 5px">
              {{ formatDate1(data.payTime) }}
            </view>
            <view style="margin-top: 5px; color: red">
              抵扣积分: {{ data.usedIntegral || 0 }}
            </view>
            <view style="margin-top: 5px">
              商品原价: ￥{{ data.facePrice }}
            </view>
            <view style="margin-top: 5px">
              抵扣后商品金额:￥{{
                (data.realPay - data.freight).toFixed(2)
              }}
              运费:￥{{ data.freight.toFixed(2) }}
            </view>
            <view style="margin-top: 5px"> 实际支付:￥{{ data.realPay }} </view>
          </view>
        </view>

        <view
          v-else
          style="
            position: fixed;
            left: 50%;
            top: 30%;
            transform: translateX(-50%);
          "
        >
          <image
            src="@/static/myOrder/empty.jpg"
            style="width: 150px; height: 150px"
          ></image>
          <view style="text-align: center; color: #7f7f7f">暂无订单数据</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrdersInfoByStatus } from "@/api/orderqyk.js";
import { allcommodity } from "@/api/shop.js";
import { getqyscOrder } from "@/api/user.js";
import { getSelfGoodsOrderList } from "@/api/goods.js";
const dict = ["", "黄金卡", "钻石卡"];
export default {
  data() {
    return {
      navCheck: 0,
      orderList: {
        wait_pay: [],
        complete: [],
        refund: {
          wait_refund: [],
          refund_failed: [],
          refund_complete: [],
        },
        eqityType: [],
      },
      showData: [],
      dict: {},
      eqityType: {},
      qyscOrder: [],
      selfGoodsOrders: [],
    };
  },
  methods: {
    checkNav(index) {
      this.navCheck = index;
      this.showData = [];
      if (index === 0) {
        this.showAll();
        return;
      } else if (index === 1) {
        // this.showAll()
        return;
      } else if (index === 2) {
        this.getSelfGoodsOrderList();
        return;
      }
    },
    getOrdersInfoByStatus() {
      getOrdersInfoByStatus().then((res) => {
        res = res.data;
        res.data.eqityType.forEach((o) => {
          this.eqityType[o.id] = o.name;
        });
        if (res.code !== 200) {
          return;
        }
        this.orderList = res.data;
        this.showAll();
      });
    },
    getGoodsOrderInfo() {
      let data = {
        phone: uni.getStorageSync("phone"),
      };
      getqyscOrder(data).then((res) => {
        console.log(res);
        this.qyscOrder = res.data;
      });
    },
    showAll() {
      this.showData = [];
      this.orderList.wait_pay.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.orderList.complete.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.orderList.refund.wait_refund.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.orderList.refund.refund_failed.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.orderList.refund.refund_complete.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.showData = this.showData.sort((a, b) => {
        const aa = new Date(a.orderTime).getTime();
        const bb = new Date(b.orderTime).getTime();
        return bb - aa;
      });
      console.log(this.showData);
    },

    getStatus(obj) {
      if (obj.refundStatus === null) {
        if (obj.orderStatus === "COMPLETE") {
          return "已完成";
        } else if (obj.orderStatus === "FAILED") {
          return "已失败";
        }
        return "已取消";
      }
      if (obj.refundStatus === "WAIT_REFUND") {
        return "待退款";
      } else if (obj.refundStatus === "REFUND") {
        return "已退款";
      }
      return "退款失败";
    },
    formatDate(str = "") {
      const data1 = str.split(" ");
      const data2 = data1[0].split("/");
      return data2[0] + "年" + data2[1] + "月" + data2[2] + "日" + data1[1];
    },
    formatDate1(str = "") {
      const data1 = str.split(" ");
      const data2 = data1[0].split("/");
      return data2[0] + "年" + data2[1] + "月" + data2[2] + "日";
    },
    makeStatus(str) {
      console.log(str);
      if (str == "waitPay") {
        return "未支付";
      }
      if (str == "COMPLETE" || str == "processing" || str == "untreated") {
        return "待处理";
      }
      if (str == "success") {
        return "订单完成";
      }
      if (str == "FAILED") {
        return "取消支付";
      }
      if (str == "failed") {
        return "订单错误";
      }
    },
    allcommodity() {
      allcommodity().then((res) => {
        res = res.data;
        if (res.code !== 200) {
          return;
        }
        res.data.forEach((o) => {
          this.dict[o.id] = o.name;
        });
        this.dict = {
          ...this.dict,
        };
      });
    },
    getSelfGoodsOrderList() {
      const data = {
        userId: uni.getStorageSync("phone"),
      };
      getSelfGoodsOrderList(data)
        .then((res) => {
          this.selfGoodsOrders = res.data.reverse();
        })
        .catch((err) => {
          console.error("获取ETC自营商品订单失败", err);
          this.selfGoodsOrders = [];
        });
    },
    formatSelfGoodsStatus(status) {
      const statusMap = {
        WAIT_PAY: "待支付",
        PAY: "支付成功",
        PAY_FAIL: "支付失败",
        WAIT_TOPUP: "充值中",
        FAIL_ORDER: "充值下单失败",
        SUCCESS: "充值成功",
        "refund-success": "退款成功",
      };
      return statusMap[status] || status;
    },
    copy(e) {
      // 点击复制
      uni.setClipboardData({
        data: e,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
        fail: () => {
          uni.showToast({
            title: "复制失败",
            icon: "none",
          });
        },
      });
    },
  },
  onLoad() {
    this.getGoodsOrderInfo();
    this.getOrdersInfoByStatus();
    this.allcommodity();
    this.getSelfGoodsOrderList();
  },
};
</script>

<style>
.my-order-nav {
  display: flex;
  justify-content: space-around;
}

.my-order-nav-item {
  padding: 5px 15px;
}

.my-order-nav-item-active {
  font-size: 17px;
  font-weight: 700;
}

.my-order-card {
  background-color: white;
  padding: 20px;
  border-radius: 15px;
  margin-top: 15px;
}
</style>
