# 环境配置说明

本项目支持基于环境的配置管理，可以根据不同的运行环境（开发/生产）自动切换相应的配置。

## 文件结构

```
config/
├── index.js              # 环境配置管理器
├── env.development.js     # 开发环境配置
├── env.production.js      # 生产环境配置
└── README.md             # 本说明文件
```

## 环境判断逻辑

系统通过以下方式判断当前环境：

1. **process.env.NODE_ENV** (推荐方式)
   - `development` - 开发环境
   - `production` - 生产环境

2. **UniApp编译条件**
   - 支持不同平台的环境判断
   - App端、H5端、小程序端等

## 配置项说明

### 开发环境 (env.development.js)
- `BASE_URL`: API基础URL，默认使用本地开发服务器
- `DEBUG`: 启用调试模式
- `VCONSOLE_ENABLED`: 关闭VConsole（开发环境通常使用浏览器调试工具）
- `API_TIMEOUT`: 请求超时时间
- `LOG_LEVEL`: 日志级别

### 生产环境 (env.production.js)
- `BASE_URL`: API基础URL，使用生产服务器地址
- `DEBUG`: 关闭调试模式
- `VCONSOLE_ENABLED`: 启用VConsole（便于移动端调试）
- `API_TIMEOUT`: 请求超时时间
- `LOG_LEVEL`: 日志级别

## 使用方法

### 1. 导入配置

```javascript
// 导入完整配置对象
import config from '@/config/index.js'

// 导入特定配置项
import { BASE_URL, DEBUG, VCONSOLE_ENABLED } from '@/config/index.js'

// 导入环境判断函数
import { isDevelopment, isProduction, getCurrentEnv } from '@/config/index.js'
```

### 2. 在代码中使用

```javascript
// 使用BASE_URL
import { BASE_URL } from '@/config/index.js'
console.log('当前API地址:', BASE_URL)

// 环境判断
import { isDevelopment, isProduction } from '@/config/index.js'
if (isDevelopment) {
  console.log('当前为开发环境')
}

// 根据环境执行不同逻辑
import { runByEnv } from '@/utils/env.js'
runByEnv({
  development: () => console.log('开发环境逻辑'),
  production: () => console.log('生产环境逻辑')
})
```

## VConsole集成

VConsole是一个轻量级的移动端调试工具，已集成到项目中：

- **开发环境**: 默认关闭（使用浏览器开发者工具）
- **生产环境**: 默认开启（便于移动端调试）

### 手动控制VConsole

可以通过修改环境配置文件中的 `VCONSOLE_ENABLED` 来控制是否启用VConsole。

## 环境切换

### 开发时切换环境

1. **通过环境变量**（推荐）
   ```bash
   # 开发环境
   NODE_ENV=development

   # 生产环境  
   NODE_ENV=production
   ```

2. **修改配置文件**
   直接修改 `config/env.development.js` 或 `config/env.production.js` 中的配置

### 构建时环境设置

UniApp在构建时会自动设置 `process.env.NODE_ENV`：
- 开发调试时：`development`
- 发行打包时：`production`

## 注意事项

1. **小程序环境**: 某些小程序平台可能不支持 `process.env.NODE_ENV`，系统会自动降级处理
2. **VConsole兼容性**: VConsole主要用于H5和App端，小程序端可能不支持
3. **配置缓存**: 配置在应用启动时加载，运行时修改配置文件不会生效
4. **安全性**: 生产环境配置会被打包到应用中，不要在配置中存储敏感信息

## 扩展配置

如需添加新的配置项，请在对应的环境配置文件中添加，并在 `config/index.js` 中导出：

```javascript
// env.development.js 或 env.production.js
export default {
  // 现有配置...
  NEW_CONFIG: 'value'
}

// config/index.js
export const {
  BASE_URL,
  // 现有导出...
  NEW_CONFIG
} = config
```
